import React from 'react';
import { Mo<PERSON>, But<PERSON> } from 'react-bootstrap';

const ConfirmationModal = ({ show, handleCancelClose, handleConfirmClose }) => {
  return (
    <Modal show={show} onHide={handleCancelClose} centered backdrop="static" keyboard={false}>
      <Modal.Header>
        <Modal.Title>Are you sure?</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        Do you really want to close the form? All changes will be lost.
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={handleCancelClose}>
          Cancel
        </Button>
        <Button variant="danger" onClick={handleConfirmClose}>
          Confirm
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ConfirmationModal;