import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor; // Adjust this path if needed
  return accessToken;
};

// ✅ Create an option
export const createOption = createAsyncThunk(
  "options/create",
  async ({ payload, questionSlug }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);



      // Check if payload contains an image file
      const hasImage = payload.attachments && payload.attachments instanceof File;

      let requestData;
      let headers = {
        Authorization: `Bearer ${token}`,
      };

      if (hasImage) {
        // Use FormData for file uploads
        requestData = new FormData();
        requestData.append('option_text', payload.option_text);
        requestData.append('is_correct', String(payload.is_correct)); // Convert boolean to string
        requestData.append('attachments', payload.attachments);
        // Don't set Content-Type header, let browser set it with boundary for FormData
      } else {
        // Use JSON for text-only options
        requestData = {
          option_text: payload.option_text,
          is_correct: payload.is_correct,
        };
        headers['Content-Type'] = 'application/json';
      }

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}api/questions/${questionSlug}/options/`,
        requestData,
        { headers }
      );

      return response.data;
    } catch (error) {
      console.error("Error creating option:", error);
      console.error("Error response:", error.response?.data);
      return rejectWithValue(error.response?.data?.message || "Failed to create option");
    }
  }
);

// ✅ Get all options for a specific question
export const getAllOptions = createAsyncThunk(
  "options/getAll",
  async ({ questionSlug }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_ALL_OPTIONS}?question=${questionSlug}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching options:", error);
      return rejectWithValue(error.response?.data?.message || "Failed to fetch options");
    }
  }
);

// ✅ Get a specific option
export const getOption = createAsyncThunk(
  "options/get",
  async ({ optionSlug }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_OPTION}${optionSlug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching option:", error);
      return rejectWithValue(error.response?.data?.message || "Failed to fetch option");
    }
  }
);

// ✅ Update an option
export const updateOption = createAsyncThunk(
  "options/update",
  async ({ questionSlug, optionSlug, data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);

      // Check if data contains an image file
      const hasImage = data.attachments && data.attachments instanceof File;

      let requestData;
      let headers = {
        Authorization: `Bearer ${token}`,
      };

      if (hasImage) {
        // Use FormData for file uploads
        requestData = new FormData();
        if (data.option_text !== undefined) requestData.append('option_text', data.option_text);
        if (data.is_correct !== undefined) requestData.append('is_correct', String(data.is_correct)); // Convert boolean to string
        requestData.append('attachments', data.attachments);
        // Don't set Content-Type header, let browser set it with boundary for FormData
      } else {
        // Use JSON for text-only updates
        requestData = data;
        headers['Content-Type'] = 'application/json';
      }

      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}api/questions/${questionSlug}/options/${optionSlug}/`,
        requestData,
        { headers }
      );
      return response.data;
    } catch (error) {
      console.error("Error updating option:", error);
      return rejectWithValue(error.response?.data?.message || "Failed to update option");
    }
  }
);

// ✅ Delete an option
export const deleteOption = createAsyncThunk(
  "options/delete",
  async ({ questionSlug, optionSlug }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}api/questions/${questionSlug}/options/${optionSlug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return { success: true, optionSlug };
    } catch (error) {
      console.error("Error deleting option:", error);
      return rejectWithValue(error.response?.data?.message || "Failed to delete option");
    }
  }
);

// ✅ Options slice
const optionsSlice = createSlice({
  name: "options",
  initialState: { loading: false, error: null },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Create option
      .addCase(createOption.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createOption.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(createOption.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get all options
      .addCase(getAllOptions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllOptions.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(getAllOptions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get a specific option
      .addCase(getOption.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getOption.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(getOption.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update an option
      .addCase(updateOption.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateOption.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(updateOption.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete an option
      .addCase(deleteOption.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteOption.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(deleteOption.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default optionsSlice.reducer;
