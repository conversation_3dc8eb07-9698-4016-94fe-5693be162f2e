import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>dal, <PERSON>, Badge, Row, Col } from "react-bootstrap";


// This is a test note

// Add custom styles for the popup preview modal
const modalStyles = `
  .popup-preview-modal .modal-content {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }
  .popup-preview-modal .modal-dialog {
    max-width: 500px !important;
  }
`;

const PopupCard = ({ popup, onUpdatePopup, onDeletePopup, isLoading }) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editData, setEditData] = useState({
    title: popup.title,
    description: popup.description,
    content_type: popup.content_type,
    text_content: popup.text_content || "",
    link_url: popup.link_url || "",
    link_text: popup.link_text || "",
    anchor_tag: popup.anchor_tag || "",
    priority: popup.priority,
    display_duration: popup.display_duration,
    delay_ms: popup.delay_ms || 0,
    page_target: popup.page_target || ""
  });

  const [showViewModal, setShowViewModal] = useState(false);
  // Render popup preview content for view modal
  const renderPopupPreview = () => {
    // Remove extra overlay, just show the popup content
    const handleMainAction = () => {
      if (popup.link_url) {
        window.open(popup.link_url, '_blank');
      }
    };
    return (
      <div style={{
        background: 'white',
        borderRadius: '16px',
        padding: '2rem',
        maxWidth: '400px',
        width: '100%',
        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
        textAlign: 'center',
        margin: '0 auto'
      }}>
        {/* Title */}
        <h4 style={{
          marginBottom: '1rem',
          color: '#333',
          fontSize: '1.25rem',
          fontWeight: '600',
          lineHeight: '1.4'
        }}>
          {popup.title}
        </h4>

        {/* Content based on type */}
        {popup.content_type === "text_only" && (
          <div style={{
            color: '#666',
            marginBottom: '1.5rem',
            fontSize: '0.95rem',
            lineHeight: '1.5'
          }}>
            {popup.text_content}
          </div>
        )}

        {popup.content_type === "image_only" && popup.image && (
          <div style={{ marginBottom: '1.5rem' }}>
            <img
              src={popup.image}
              alt="Popup"
              style={{
                maxWidth: '100%',
                maxHeight: '200px',
                borderRadius: '8px',
                objectFit: 'cover'
              }}
            />
          </div>
        )}

        {popup.content_type === "text_image" && (
          <>
            <div style={{
              color: '#666',
              marginBottom: '1rem',
              fontSize: '0.95rem',
              lineHeight: '1.5'
            }}>
              {popup.text_content}
            </div>
            {popup.image && (
              <div style={{ marginBottom: '1.5rem' }}>
                <img
                  src={popup.image}
                  alt="Popup"
                  style={{
                    maxWidth: '100%',
                    maxHeight: '180px',
                    borderRadius: '8px',
                    objectFit: 'cover'
                  }}
                />
              </div>
            )}
          </>
        )}

        {popup.content_type === "text_link" && (
          <div style={{
            color: '#666',
            marginBottom: '1.5rem',
            fontSize: '0.95rem',
            lineHeight: '1.5'
          }}>
            {popup.text_content}
          </div>
        )}

        {popup.content_type === "link_anchor" && (
          <div style={{
            color: '#666',
            marginBottom: '1.5rem',
            fontSize: '0.95rem',
            lineHeight: '1.5'
          }}>
            {popup.anchor_tag}
          </div>
        )}

        {/* Action buttons */}
        <div style={{
          display: 'flex',
          gap: '0.5rem',
          justifyContent: 'center',
          marginTop: '1.5rem'
        }}>
          {(popup.content_type === "text_link" || popup.content_type === "link_anchor") && (
            <button
              style={{
                background: '#007AFF',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.9rem',
                fontWeight: '500',
                cursor: 'pointer',
                minWidth: '80px'
              }}
              onClick={handleMainAction}
            >
              {popup.link_text || popup.anchor_tag || 'Continue'}
            </button>
          )}
          <button
            style={{
              background: 'transparent',
              color: '#007AFF',
              border: 'none',
              borderRadius: '8px',
              padding: '0.75rem 1.5rem',
              fontSize: '0.9rem',
              fontWeight: '500',
              cursor: 'pointer',
              minWidth: '80px'
            }}
            onClick={() => setShowViewModal(false)}
          >
            Cancel
          </button>
        </div>
      </div>
    );
  };

  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleEditSubmit = (e) => {
    e.preventDefault();
    onUpdatePopup(popup.id, editData);
    setShowEditModal(false);
  };

  const handleDelete = () => {
    onDeletePopup(popup.id);
    setShowDeleteModal(false);
  };

  const getPriorityVariant = (priority) => {
    switch (priority) {
      case "high": return "danger";
      case "medium": return "warning";
      case "low": return "success";
      default: return "secondary";
    }
  };

  const getStatusVariant = (status) => {
    switch (status) {
      case "approved": return "success";
      case "pending": return "warning";
      case "rejected": return "danger";
      default: return "secondary";
    }
  };

  const renderContentPreview = () => {
    // Always show image if present
    const hasImage = popup.image && popup.image !== "";
    const hasText = popup.text_content && popup.text_content !== "";
    const hasLink = popup.link_url && popup.link_url !== "";
    const hasLinkText = popup.link_text && popup.link_text !== "";
    const hasAnchor = popup.anchor_tag && popup.anchor_tag !== "";

    return (
      <div className="mt-2">
        {/* Show image if present */}
        {hasImage && (
          <div className="mb-2">
            <small className="text-muted">Image:</small>
            <div className="mt-1">
              <img
                src={popup.image}
                alt="Popup"
                style={{ maxWidth: "100%", maxHeight: "120px", objectFit: "cover" }}
                className="img-thumbnail"
              />
            </div>
          </div>
        )}
        {/* Show text content if present */}
        {hasText && (
          <div className="mb-2">
            <small className="text-muted">Text Content:</small>
            <p className="mb-0">{popup.text_content}</p>
          </div>
        )}
        {/* Show link if present */}
        {hasLink && hasLinkText && (
          <div className="mb-2">
            <small className="text-muted">Text + Link:</small>
            <div>
              <a href={popup.link_url} target="_blank" rel="noopener noreferrer" className="text-primary">
                {popup.link_text}
              </a>
            </div>
          </div>
        )}
        {/* Show anchor tag if present */}
        {hasLink && hasAnchor && (
          <div className="mb-2">
            <small className="text-muted">Link Anchor:</small>
            <div>
              <a href={popup.link_url} target="_blank" rel="noopener noreferrer" className="text-primary">
                {popup.anchor_tag}
              </a>
            </div>
          </div>
        )}
        {/* If nothing, show a fallback */}
        {!hasImage && !hasText && !hasLink && !hasAnchor && (
          <div className="text-muted">No content available.</div>
        )}
      </div>
    );
  };

  const renderEditContentFields = () => {
    switch (editData.content_type) {
      case "text_only":
        return (
          <Form.Group className="mb-3">
            <Form.Label>Text Content</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              name="text_content"
              value={editData.text_content}
              onChange={handleEditChange}
            />
          </Form.Group>
        );
      case "text_link":
        return (
          <>
            <Form.Group className="mb-3">
              <Form.Label>Text Content</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="text_content"
                value={editData.text_content}
                onChange={handleEditChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Link URL</Form.Label>
              <Form.Control
                type="url"
                name="link_url"
                value={editData.link_url}
                onChange={handleEditChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Link Text</Form.Label>
              <Form.Control
                type="text"
                name="link_text"
                value={editData.link_text}
                onChange={handleEditChange}
              />
            </Form.Group>
          </>
        );
      case "link_anchor":
        return (
          <>
            <Form.Group className="mb-3">
              <Form.Label>Link URL</Form.Label>
              <Form.Control
                type="url"
                name="link_url"
                value={editData.link_url}
                onChange={handleEditChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Anchor Tag</Form.Label>
              <Form.Control
                type="text"
                name="anchor_tag"
                value={editData.anchor_tag}
                onChange={handleEditChange}
              />
            </Form.Group>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <style>{modalStyles}</style>
      <Card className="shadow rounded-3 h-100">
        <Card.Body>
          <div className="d-flex justify-content-between align-items-start mb-2">
            <Card.Title className="mb-0">{popup.title}</Card.Title>
            <div className="d-flex gap-1">
              <Badge bg={getPriorityVariant(popup.priority)}>
                {popup.priority_display || popup.priority}
              </Badge>
              <Badge bg={getStatusVariant(popup.approval_status)}>
                {popup.approval_status_display || popup.approval_status}
              </Badge>
            </div>
          </div>
          
          <Card.Text className="text-muted small mb-2">
            {popup.description}
          </Card.Text>

          <div className="mb-2">
            <Badge bg="info" className="me-2">
              {popup.content_type_display || popup.content_type.replace('_', ' ')}
            </Badge>
            <Badge bg="secondary" className="me-2">
              {popup.priority_display || popup.priority}
            </Badge>
          </div>

          <div className="mb-2">
            <small className="text-muted d-block">
              <strong>Duration:</strong> {popup.display_duration}ms
            </small>
            <small className="text-muted d-block">
              <strong>Delay:</strong> {popup.delay_ms || 0}ms
            </small>
            <small className="text-muted d-block">
              <strong>Target:</strong> {popup.page_target || 'All pages'}
            </small>
          </div>

          {renderContentPreview()}

          <div className="mt-3">
            <small className="text-muted d-block">
              Created: {new Date(popup.created_at).toLocaleDateString()}
            </small>
            <small className="text-muted d-block">
              By: {popup.created_by_username}
            </small>
            {popup.rejection_reason && (
              <small className="text-danger d-block">
                Rejection: {popup.rejection_reason}
              </small>
            )}
          </div>

          <div className="d-flex justify-content-center mt-3">
            <Button 
              variant="outline-primary" 
              size="sm" 
              onClick={() => setShowEditModal(true)}
              disabled={isLoading}
              className="m-1"
            >
              Edit
            </Button>
            <Button 
              variant="outline-danger" 
              size="sm" 
              onClick={() => setShowDeleteModal(true)}
              disabled={isLoading}
              className="m-1"
            >
              Delete
            </Button>
            <Button 
              variant="outline-info" 
              size="sm" 
              onClick={() => setShowViewModal(true)}
              className="m-1"
            >
              View
            </Button>
          </div>
      {/* View Modal */}
      <Modal
        show={showViewModal}
        onHide={() => setShowViewModal(false)}
        size="lg"
        centered
        backdrop="static"
        dialogClassName="popup-preview-modal"
      >
        <Modal.Body style={{ padding: 0, background: 'transparent', border: 'none' }}>
          {renderPopupPreview()}
        </Modal.Body>
      </Modal>
        </Card.Body>
      </Card>

      {/* Edit Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Edit Popup Banner</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleEditSubmit}>
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Title</Form.Label>
                  <Form.Control
                    type="text"
                    name="title"
                    value={editData.title}
                    onChange={handleEditChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Priority</Form.Label>
                  <Form.Select
                    name="priority"
                    value={editData.priority}
                    onChange={handleEditChange}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                name="description"
                value={editData.description}
                onChange={handleEditChange}
                required
              />
            </Form.Group>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Delay (ms)</Form.Label>
                  <Form.Control
                    type="number"
                    name="delay_ms"
                    value={editData.delay_ms}
                    onChange={handleEditChange}
                    min="0"
                    step="100"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Page Target</Form.Label>
                  <Form.Control
                    type="text"
                    name="page_target"
                    value={editData.page_target}
                    onChange={handleEditChange}
                    placeholder="/dashboard"
                  />
                </Form.Group>
              </Col>
            </Row>

            {renderEditContentFields()}

            <Form.Group className="mb-3">
              <Form.Label>Display Duration (ms)</Form.Label>
              <Form.Control
                type="number"
                name="display_duration"
                value={editData.display_duration}
                onChange={handleEditChange}
                min="1000"
                max="30000"
                step="1000"
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowEditModal(false)}>
              Cancel
            </Button>
            <Button variant="primary" type="submit" disabled={isLoading}>
              {isLoading ? "Updating..." : "Update Popup"}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to delete the popup "{popup.title}"? This action cannot be undone.
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDelete} disabled={isLoading}>
            {isLoading ? "Deleting..." : "Delete"}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default PopupCard;
