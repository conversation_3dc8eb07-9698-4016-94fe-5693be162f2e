annotated-types==0.7.0
anyascii==0.3.2
asgiref==3.8.1
async-timeout==5.0.1
attrs==25.3.0
autobahn==24.4.2
Automat==25.4.16
beautifulsoup4==4.12.3
blinker==1.9.0
blis==1.2.1
CacheControl==0.14.1
cachetools==5.5.0
catalogue==2.0.10
certifi==2024.8.30
cffi==1.17.1
channels==4.0.0
channels-redis==4.1.0
charset-normalizer==3.4.0
click==8.1.8
cloudpathlib==0.21.0
colorama==0.4.6
confection==0.1.5
constantly==23.10.4
cryptography==44.0.0
cymem==2.0.11
daphne==4.2.0
defusedxml==0.7.1
Django==4.2.7
django-allauth==65.7.0
django-bootstrap-v5==1.0.11
django-bootstrap5==25.1
django-cors-headers==4.3.1
django-fcm==0.1.1
django-filter==24.3
django-modelcluster==6.3
django-permissionedforms==0.1
django-ratelimit==4.1.0
django-seo==0.3.5
django-taggit==6.1.0
django-treebeard==4.7.1
djangorestframework==3.15.2
djangorestframework-simplejwt==5.3.1
draftjs-exporter==5.0.0
et_xmlfile==2.0.0
filetype==1.2.0
firebase-admin==6.6.0
Flask==2.3.3
google-ai-generativelanguage==0.6.15
google-api-core==2.24.0
google-api-python-client==2.155.0
google-auth==2.37.0
google-auth-httplib2==0.2.0
google-cloud-core==2.4.1
google-cloud-firestore==2.19.0
google-cloud-storage==2.19.0
google-crc32c==1.6.0
google-generativeai==0.8.5
google-resumable-media==2.7.2
googleapis-common-protos==1.66.0
grpcio==1.68.1
grpcio-status==1.68.1
httplib2==0.22.0
hyperlink==21.0.0
idna==3.10
incremental==24.7.2
itsdangerous==2.2.0
Jinja2==3.1.6
joblib==1.4.2
l18n==2021.3
laces==0.1.1
langcodes==3.5.0
langdetect==1.0.9
language_data==1.3.0
marisa-trie==1.2.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
msgpack==1.1.0
murmurhash==1.0.12
numpy==1.26.4
oauthlib==3.2.2
openpyxl==3.1.5
packaging==24.2
pdfminer.six==20250327
pdfplumber==0.11.6
pillow==10.4.0
pillow_heif==0.20.0
preshed==3.0.9
proto-plus==1.25.0
protobuf==5.29.1
psutil==7.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.11.3
pydantic_core==2.33.1
Pygments==2.19.1
PyJWT==2.9.0
PyMuPDF==1.25.5
pyOpenSSL==25.1.0
pyparsing==3.2.0
PyPDF2==3.0.1
pypdfium2==4.30.1
pyshorteners==1.0.1
python-dateutil==2.9.0.post0
python-decouple==3.8
python-dotenv==1.0.1
python3-openid==3.2.0
pytz==2024.2
qrcode==8.0
razorpay==1.4.2
redis==5.0.1
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
rich==14.0.0
rsa==4.9
scikit-learn==1.6.1
scipy==1.15.2
service-identity==24.2.0
shellingham==1.5.4
six==1.16.0
smart-open==7.1.0
social-auth-app-django==5.4.3
social-auth-core==4.5.6
soupsieve==2.6
spacy==3.8.5
spacy-legacy==3.0.12
spacy-loggers==1.0.5
sqlparse==0.5.1
srsly==2.5.1
telepath==0.3.1
tenacity==9.1.2
thinc==8.3.4
threadpoolctl==3.6.0
tqdm==4.67.1
Twisted==25.5.0
txaio==23.1.1
typer==0.15.2
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2024.1
Unidecode==1.4.0
uritemplate==4.1.1
urllib3==2.2.3
wasabi==1.1.3
weasel==0.4.1
Werkzeug==2.3.7
whitenoise==6.9.0
Willow==1.9.0
wrapt==1.17.2
zope.interface==7.2
