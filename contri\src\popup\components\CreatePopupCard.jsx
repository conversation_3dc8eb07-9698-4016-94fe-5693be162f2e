import React, { useState } from "react";
import { <PERSON>, Button, Form, Row, Col } from "react-bootstrap";

const CreatePopupCard = ({ onCreatePopup, isLoading }) => {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    content_type: "text_only",
    text_content: "",
    image: null,
    link_url: "",
    link_text: "",
    anchor_tag: "",
    priority: "medium",
    display_duration: 5000,
    delay_ms: 3000,
    page_target: "/dashboard"
  });

  const [imagePreview, setImagePreview] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        image: file
      }));
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Create the popup data based on content type
    const popupData = {
      title: formData.title,
      description: formData.description,
      content_type: formData.content_type,
      priority: formData.priority,
      display_duration: parseInt(formData.display_duration),
      delay_ms: parseInt(formData.delay_ms),
      page_target: formData.page_target
    };

    // Add fields based on content type
    switch (formData.content_type) {
      case "text_only":
        popupData.text_content = formData.text_content;
        break;
      case "image_only":
        // Include image for image_only content type
        if (formData.image) {
          popupData.image = formData.image;
        }
        break;
      case "text_image":
        popupData.text_content = formData.text_content;
        // Include image for text_image content type
        if (formData.image) {
          popupData.image = formData.image;
        }
        break;
      case "text_link":
        popupData.text_content = formData.text_content;
        popupData.link_url = formData.link_url;
        popupData.link_text = formData.link_text;
        break;
      case "link_anchor":
        popupData.link_url = formData.link_url;
        popupData.anchor_tag = formData.anchor_tag;
        popupData.link_text = formData.link_text; // Add link_text for link_anchor
        break;
      default:
        break;
    }

    onCreatePopup(popupData);
  };

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      content_type: "text_only",
      text_content: "",
      image: null,
      link_url: "",
      link_text: "",
      anchor_tag: "",
      priority: "medium",
      display_duration: 5000,
      delay_ms: 3000,
      page_target: "/dashboard"
    });
    setImagePreview(null);
  };

  const renderContentFields = () => {
    switch (formData.content_type) {
      case "text_only":
        return (
          <Form.Group className="mb-3">
            <Form.Label>Text Content *</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              name="text_content"
              value={formData.text_content}
              onChange={handleInputChange}
              placeholder="Enter the text content for the popup"
              required
            />
          </Form.Group>
        );

      case "image_only":
        return (
          <Form.Group className="mb-3">
            <Form.Label>Image *</Form.Label>
            <Form.Control
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              required
            />
            {imagePreview && (
              <div className="mt-2">
                <img 
                  src={imagePreview} 
                  alt="Preview" 
                  style={{ maxWidth: "200px", maxHeight: "150px", objectFit: "cover" }}
                  className="img-thumbnail"
                />
              </div>
            )}
          </Form.Group>
        );

      case "text_image":
        return (
          <>
            <Form.Group className="mb-3">
              <Form.Label>Text Content *</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="text_content"
                value={formData.text_content}
                onChange={handleInputChange}
                placeholder="Enter the text content for the popup"
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Image *</Form.Label>
              <Form.Control
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                required
              />
              {imagePreview && (
                <div className="mt-2">
                  <img 
                    src={imagePreview} 
                    alt="Preview" 
                    style={{ maxWidth: "200px", maxHeight: "150px", objectFit: "cover" }}
                    className="img-thumbnail"
                  />
                </div>
              )}
            </Form.Group>
          </>
        );

      case "text_link":
        return (
          <>
            <Form.Group className="mb-3">
              <Form.Label>Text Content *</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="text_content"
                value={formData.text_content}
                onChange={handleInputChange}
                placeholder="Enter the text content for the popup"
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Link URL *</Form.Label>
              <Form.Control
                type="url"
                name="link_url"
                value={formData.link_url}
                onChange={handleInputChange}
                placeholder="https://example.com"
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Link Text *</Form.Label>
              <Form.Control
                type="text"
                name="link_text"
                value={formData.link_text}
                onChange={handleInputChange}
                placeholder="Click here"
                required
              />
            </Form.Group>
          </>
        );

      case "link_anchor":
        return (
          <>
            <Form.Group className="mb-3">
              <Form.Label>Link URL *</Form.Label>
              <Form.Control
                type="url"
                name="link_url"
                value={formData.link_url}
                onChange={handleInputChange}
                placeholder="https://example.com"
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Anchor Tag *</Form.Label>
              <Form.Control
                type="text"
                name="anchor_tag"
                value={formData.anchor_tag}
                onChange={handleInputChange}
                placeholder="Learn More"
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Link Text *</Form.Label>
              <Form.Control
                type="text"
                name="link_text"
                value={formData.link_text}
                onChange={handleInputChange}
                placeholder="Click here"
                required
              />
            </Form.Group>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="mb-3 shadow rounded-3">
      <Card.Body>
        <Card.Title className="text-primary">Create New Popup Banner</Card.Title>
        <Form onSubmit={handleSubmit}>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Title *</Form.Label>
                <Form.Control
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="Enter popup title"
                  required
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Content Type *</Form.Label>
                <Form.Select
                  name="content_type"
                  value={formData.content_type}
                  onChange={handleInputChange}
                  required
                >
                  <option value="text_only">Text Only</option>
                  <option value="image_only">Image Only</option>
                  <option value="text_image">Text + Image</option>
                  <option value="text_link">Text + Link</option>
                  <option value="link_anchor">Link Anchor</option>
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>

          <Form.Group className="mb-3">
            <Form.Label>Description *</Form.Label>
            <Form.Control
              as="textarea"
              rows={2}
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter popup description"
              required
            />
          </Form.Group>

          {renderContentFields()}

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Priority *</Form.Label>
                <Form.Select
                  name="priority"
                  value={formData.priority}
                  onChange={handleInputChange}
                  required
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Display Duration (ms) *</Form.Label>
                <Form.Control
                  type="number"
                  name="display_duration"
                  value={formData.display_duration}
                  onChange={handleInputChange}
                  min="1000"
                  max="30000"
                  step="1000"
                  required
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Delay (ms) *</Form.Label>
                <Form.Control
                  type="number"
                  name="delay_ms"
                  value={formData.delay_ms}
                  onChange={handleInputChange}
                  min="0"
                  max="10000"
                  step="500"
                  placeholder="3000"
                  required
                />
                <Form.Text className="text-muted">
                  Time to wait before showing the popup
                </Form.Text>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Page Target *</Form.Label>
                <Form.Control
                  type="text"
                  name="page_target"
                  value={formData.page_target}
                  onChange={handleInputChange}
                  placeholder="/dashboard"
                  required
                />
                <Form.Text className="text-muted">
                  Page where this popup should appear
                </Form.Text>
              </Form.Group>
            </Col>
          </Row>

          <div className="d-flex gap-2">
            <Button 
              variant="primary" 
              type="submit" 
              disabled={isLoading}
            >
              {isLoading ? "Creating..." : "Create Popup"}
            </Button>
            <Button 
              variant="secondary" 
              type="button" 
              onClick={resetForm}
              disabled={isLoading}
            >
              Reset
            </Button>
          </div>
        </Form>
      </Card.Body>
    </Card>
  );
};

export default CreatePopupCard;
