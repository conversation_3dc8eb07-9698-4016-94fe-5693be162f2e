from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Contributor<PERSON><PERSON><PERSON><PERSON>, <PERSON>, PopupBanner, ContributorEarning, ContributorPoints
from django.contrib.auth import authenticate
from shashtrarth.utils import validate_username, validate_password


# class UserSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = User
#         fields = [
#             "id",
#             "username",
#             "email",
#             "first_name",
#             "last_name",
#             "password",
#         ]
#         extra_kwargs = {"password": {"write_only": True}}


class ContributorProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContributorProfile
        fields = ["security_question", "security_answer"]


class ContributorRegistrationSerializer(serializers.ModelSerializer):
    contributor_profile = ContributorProfileSerializer()

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "password",
            "first_name",
            "last_name",
            "contributor_profile",
        ]
        extra_kwargs = {"password": {"write_only": True}}

    def validate_username(self, value):
        """Validate username using centralized validation utility"""
        return validate_username(value)

    def validate_password(self, value):
        """Validate password using centralized validation utility"""
        return validate_password(value)

    def create(self, validated_data):
        profile_data = validated_data.pop("contributor_profile")
        user = User.objects.create_user(**validated_data)
        ContributorProfile.objects.create(user=user, **profile_data)
        return user

    def update(self, instance, validated_data):
        profile_data = validated_data.pop("contributor_profile", None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        if profile_data:
            profile = instance.contributorprofile
            for attr, value in profile_data.items():
                setattr(profile, attr, value)
            profile.save()

        return instance


class LoginSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True, write_only=True)

    def validate(self, data):
        username = data.get("username")
        password = data.get("password")

        if username and password:
            user = authenticate(username=username, password=password)
            if user:
                if not user.is_active:
                    raise serializers.ValidationError("User account is disabled.")

                # Check if user has a contributor profile
                try:
                    contributor_profile = user.contributor_profile
                except ContributorProfile.DoesNotExist:
                    raise serializers.ValidationError(
                        "Access denied. This account is not authorized for contributor access."
                    )

                # Verify the role is correct
                if contributor_profile.role != "contributor":
                    raise serializers.ValidationError(
                        "Access denied. Invalid role for contributor access."
                    )

                return {"user": user, "role": contributor_profile.role}
            else:
                raise serializers.ValidationError(
                    "Unable to log in with provided credentials."
                )
        else:
            raise serializers.ValidationError("Must include 'username' and 'password'.")

class BannerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Banner
        fields = '__all__'


class PopupBannerBaseSerializer(serializers.ModelSerializer):
    """Base serializer for PopupBanner with common fields"""
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    approved_by_care_username = serializers.CharField(source='approved_by_care.user.username', read_only=True)
    approved_by_admin_username = serializers.CharField(source='approved_by_admin.username', read_only=True)
    rejected_by_username = serializers.CharField(source='rejected_by.username', read_only=True)
    content_type_display = serializers.CharField(source='get_content_type_display', read_only=True)
    approval_status_display = serializers.CharField(source='get_approval_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)

    class Meta:
        model = PopupBanner
        fields = [
            'id', 'title', 'description', 'content_type', 'content_type_display',
            'image', 'text_content', 'link_url', 'link_text', 'anchor_tag', 'page_target',
            'display_duration', 'delay_ms', 'priority', 'priority_display', 'is_active',
            'approval_status', 'approval_status_display', 'rejection_reason',
            'created_by', 'created_by_username', 'approved_by_care', 'approved_by_care_username',
            'approved_by_admin', 'approved_by_admin_username', 'rejected_by', 'rejected_by_username',
            'created_at', 'updated_at', 'approved_at', 'activated_at', 'slug'
        ]
        read_only_fields = [
            'id', 'created_by', 'approved_by_care', 'approved_by_admin', 'rejected_by',
            'created_at', 'updated_at', 'approved_at', 'activated_at', 'slug'
        ]


class PopupBannerContributorSerializer(PopupBannerBaseSerializer):
    """Serializer for contributors - can create and view own banners"""

    class Meta(PopupBannerBaseSerializer.Meta):
        # Contributors cannot modify approval-related fields after creation
        read_only_fields = PopupBannerBaseSerializer.Meta.read_only_fields + [
            'approval_status', 'rejection_reason', 'is_active'
        ]

    def validate(self, data):
        """Validate content based on content type"""
        content_type = data.get('content_type')

        # For updates, use existing content_type if not provided
        if not content_type and self.instance:
            content_type = self.instance.content_type

        # Skip validation if no content_type (partial update)
        if not content_type:
            return data

        # Text only validation
        if content_type == 'text_only':
            if not data.get('text_content') and not (self.instance and self.instance.text_content):
                raise serializers.ValidationError({
                    'text_content': f"Text content is required for content type '{content_type}'"
                })

        # Image validation
        if content_type in ['image_only', 'text_image']:
            if not data.get('image') and not (self.instance and self.instance.image):
                raise serializers.ValidationError({
                    'image': f"Image is required for content type '{content_type}'"
                })

        # Page target validation
        if content_type == 'page_target':
            if not data.get('page_target') and not (self.instance and self.instance.page_target):
                raise serializers.ValidationError({
                    'page_target': f"Page target is required for content type '{content_type}'"
                })

        # Text validation for combined types
        if content_type in ['text_image', 'text_link']:
            if not data.get('text_content') and not (self.instance and self.instance.text_content):
                raise serializers.ValidationError({
                    'text_content': f"Text content is required for content type '{content_type}'"
                })

        # Link validation
        if content_type in ['text_link', 'link_anchor']:
            if not data.get('link_url') and not (self.instance and self.instance.link_url):
                raise serializers.ValidationError({
                    'link_url': f"Link URL is required for content type '{content_type}'"
                })
            if not data.get('link_text') and not (self.instance and self.instance.link_text):
                raise serializers.ValidationError({
                    'link_text': f"Link text is required for content type '{content_type}'"
                })

        # Anchor tag validation
        if content_type == 'link_anchor':
            if not data.get('anchor_tag') and not (self.instance and self.instance.anchor_tag):
                raise serializers.ValidationError({
                    'anchor_tag': "Anchor tag is required for 'Link + Anchor Tag' content type"
                })

        return data

    def create(self, validated_data):
        """Set the created_by field to the current user"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class PopupBannerCustomerCareSerializer(PopupBannerBaseSerializer):
    """Serializer for customer care - can approve/reject banners"""

    class Meta(PopupBannerBaseSerializer.Meta):
        # Customer care can modify approval status and rejection reason
        read_only_fields = [
            'id', 'created_by', 'approved_by_admin', 'rejected_by',
            'created_at', 'updated_at', 'approved_at', 'activated_at', 'slug',
            'title', 'description', 'content_type', 'image', 'text_content',
            'link_url', 'link_text', 'anchor_tag', 'display_duration', 'priority'
        ]

    def update(self, instance, validated_data):
        """Handle approval/rejection logic"""
        approval_status = validated_data.get('approval_status')
        rejection_reason = validated_data.get('rejection_reason')

        if approval_status == 'approved_by_care':
            if instance.can_be_approved_by_care():
                care_profile = self.context['request'].user.customrcare_profile
                instance.approve_by_care(care_profile)
            else:
                raise serializers.ValidationError("Banner cannot be approved at this stage")

        elif approval_status in ['rejected_by_care']:
            if not rejection_reason:
                raise serializers.ValidationError("Rejection reason is required")
            instance.reject(self.context['request'].user, rejection_reason)

        # Handle activation/deactivation
        is_active = validated_data.get('is_active')
        if is_active is not None:
            if is_active:
                if not instance.activate():
                    raise serializers.ValidationError("Banner cannot be activated")
            else:
                instance.deactivate()

        instance.save()
        return instance


class PopupBannerAdminSerializer(PopupBannerBaseSerializer):
    """Serializer for admin - full control over banners"""

    class Meta(PopupBannerBaseSerializer.Meta):
        # Admin can modify most fields except timestamps and slug
        read_only_fields = [
            'id', 'created_by', 'created_at', 'updated_at', 'approved_at', 'activated_at', 'slug'
        ]

    def update(self, instance, validated_data):
        """Handle admin approval/rejection logic"""
        approval_status = validated_data.get('approval_status')
        rejection_reason = validated_data.get('rejection_reason')

        if approval_status == 'approved_by_admin':
            if instance.can_be_approved_by_admin():
                instance.approve_by_admin(self.context['request'].user)
            else:
                raise serializers.ValidationError("Banner cannot be approved at this stage")

        elif approval_status in ['rejected_by_admin']:
            if not rejection_reason:
                raise serializers.ValidationError("Rejection reason is required")
            instance.reject(self.context['request'].user, rejection_reason)

        # Handle activation/deactivation
        is_active = validated_data.get('is_active')
        if is_active is not None:
            if is_active:
                if not instance.activate():
                    raise serializers.ValidationError("Banner cannot be activated")
            else:
                instance.deactivate()

        # Update other fields
        for attr, value in validated_data.items():
            if attr not in ['approval_status', 'is_active']:
                setattr(instance, attr, value)

        instance.save()
        return instance


class PopupBannerPublicSerializer(serializers.ModelSerializer):
    """Serializer for public API - only active banners with minimal fields"""
    content_type_display = serializers.CharField(source='get_content_type_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)

    class Meta:
        model = PopupBanner
        fields = [
            'id', 'title', 'description', 'content_type', 'content_type_display',
            'image', 'text_content', 'link_url', 'link_text', 'anchor_tag', 'page_target',
            'display_duration', 'delay_ms', 'priority', 'priority_display', 'created_at'
        ]
        read_only_fields = [
            'id', 'title', 'description', 'content_type', 'content_type_display',
            'image', 'text_content', 'link_url', 'link_text', 'anchor_tag', 'page_target',
            'display_duration', 'delay_ms', 'priority', 'priority_display', 'created_at'
        ]


# ============================================================================
# EARNING SYSTEM SERIALIZERS
# ============================================================================

class ContributorPointsSerializer(serializers.ModelSerializer):
    """Serializer for ContributorPoints model"""
    assigned_contributors_count = serializers.SerializerMethodField()

    class Meta:
        model = ContributorPoints
        fields = [
            'id', 'name', 'slug', 'normal_questions', 'master_questions',
            'master_options', 'blogs', 'previous_questions', 'last_updated',
            'created_at', 'assigned_contributors_count'
        ]
        read_only_fields = ['id', 'slug', 'last_updated', 'created_at', 'assigned_contributors_count']

    def get_assigned_contributors_count(self, obj):
        return obj.assigned_contributors.count()


class ContributorEarningSerializer(serializers.ModelSerializer):
    """Serializer for ContributorEarning model"""
    contributor_username = serializers.CharField(source='contributor.user.username', read_only=True)
    points_config_name = serializers.CharField(source='points_config_used.name', read_only=True)
    activity_breakdown = serializers.SerializerMethodField()

    class Meta:
        model = ContributorEarning
        fields = [
            'id', 'contributor', 'contributor_username', 'period_type', 'period_start', 'period_end',
            'normal_questions_count', 'master_questions_count', 'master_options_count',
            'blogs_count', 'previous_questions_count',
            'normal_questions_points', 'master_questions_points', 'master_options_points',
            'blogs_points', 'previous_questions_points', 'total_points', 'total_earnings',
            'points_config_used', 'points_config_name', 'is_paid', 'paid_at',
            'created_at', 'updated_at', 'activity_breakdown'
        ]
        read_only_fields = [
            'id', 'contributor_username', 'total_points', 'points_config_name',
            'created_at', 'updated_at', 'activity_breakdown'
        ]

    def get_activity_breakdown(self, obj):
        """Get detailed activity breakdown"""
        return {
            'normal_questions': {
                'count': obj.normal_questions_count,
                'points': float(obj.normal_questions_points),
            },
            'master_questions': {
                'count': obj.master_questions_count,
                'points': float(obj.master_questions_points),
            },
            'master_options': {
                'count': obj.master_options_count,
                'points': float(obj.master_options_points),
            },
            'blogs': {
                'count': obj.blogs_count,
                'points': float(obj.blogs_points),
            },
            'previous_questions': {
                'count': obj.previous_questions_count,
                'points': float(obj.previous_questions_points),
            },
        }


class ContributorEarningSummarySerializer(serializers.Serializer):
    """Serializer for earning summary data"""
    period_type = serializers.CharField()
    period_start = serializers.DateTimeField()
    period_end = serializers.DateTimeField()
    total_points = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_earnings = serializers.DecimalField(max_digits=10, decimal_places=2)
    activity_breakdown = serializers.DictField()
    is_paid = serializers.BooleanField()
    paid_at = serializers.DateTimeField(allow_null=True)


class ContributorProfileEarningSerializer(serializers.ModelSerializer):
    """Enhanced ContributorProfile serializer with earning information"""
    custom_points_name = serializers.CharField(source='custom_points.name', read_only=True)
    points_configuration = serializers.SerializerMethodField()
    current_month_earnings = serializers.SerializerMethodField()
    total_lifetime_earnings = serializers.SerializerMethodField()

    class Meta:
        model = ContributorProfile
        fields = [
            'id', 'user', 'role', 'slug', 'custom_points', 'custom_points_name',
            'points_configuration', 'current_month_earnings', 'total_lifetime_earnings'
        ]
        read_only_fields = [
            'id', 'slug', 'custom_points_name', 'points_configuration',
            'current_month_earnings', 'total_lifetime_earnings'
        ]

    def get_points_configuration(self, obj):
        """Get the points configuration for this contributor"""
        points_config = obj.get_points_config()
        if points_config:
            return ContributorPointsSerializer(points_config).data
        return None

    def get_current_month_earnings(self, obj):
        """Get current month earnings summary"""
        try:
            from .earning_service import EarningCalculationService
            summary = EarningCalculationService.get_earnings_summary(obj, 'monthly')
            return summary
        except Exception:
            return None

    def get_total_lifetime_earnings(self, obj):
        """Get total lifetime earnings"""
        try:
            from .earning_service import EarningCalculationService
            return EarningCalculationService.get_contributor_total_earnings(obj)
        except Exception:
            return None