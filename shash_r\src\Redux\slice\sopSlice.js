import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor;
  return accessToken;
};

// Base API URL for SOPs
const API_URL = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SOP}`;

// Initial state
const initialState = {
  sops: [],
  currentSop: null,
  isLoading: false,
  error: null,
};

// Async thunk to fetch all SOPs
export const getSOP = createAsyncThunk(
  'sop/getSOP',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(API_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching SOPs');
    }
  }
);

// Async thunk to fetch a specific SOP by ID
export const getSopById = createAsyncThunk(
  'sop/getSopById',
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(`${API_URL}${id}/`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching SOP');
    }
  }
);

// Create the slice
const sopSlice = createSlice({
  name: 'sop',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentSop: (state, action) => {
      state.currentSop = action.payload;
    },
    clearCurrentSop: (state) => {
      state.currentSop = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all SOPs
      .addCase(getSOP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSOP.fulfilled, (state, action) => {
        state.isLoading = false;
        state.sops = action.payload;
        state.error = null;
      })
      .addCase(getSOP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Get SOP by ID
      .addCase(getSopById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSopById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentSop = action.payload;
        state.error = null;
      })
      .addCase(getSopById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

// Export actions
export const { clearError, setCurrentSop, clearCurrentSop } = sopSlice.actions;

// Export reducer
export default sopSlice.reducer;
