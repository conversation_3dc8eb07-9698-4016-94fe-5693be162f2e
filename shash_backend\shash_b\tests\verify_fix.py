#!/usr/bin/env python3
"""
Quick verification that the package purchase API fix is working
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from packages_and_subscriptions.models import Package, Subscription
from students.models import Student
from packages_and_subscriptions.payment_service import PaymentService
import requests

def test_backend_functionality():
    """Test the backend functionality directly"""
    print("Testing Backend Functionality")
    print("=" * 35)
    
    try:
        # Get test data
        student = Student.objects.first()
        package = Package.objects.filter(is_active=True, discount_price__gt=0).first()
        
        if not student or not package:
            print("❌ No test data available (student or package)")
            return False
        
        print(f"✅ Test data found:")
        print(f"   Student: {student.user.username}")
        print(f"   Package: {package.name} (₹{package.discount_price})")
        
        # Test payment service
        payment_service = PaymentService()
        
        # Test pricing calculation
        pricing_info = payment_service.calculate_final_price(
            package=package,
            coupon_code=None,
            gift_card_code=None,
            gift_card_pin=None,
            student=student
        )
        print(f"✅ Pricing calculation: ₹{pricing_info['final_price']}")
        
        # Test Razorpay order creation
        razorpay_order = payment_service.create_razorpay_order(
            amount=pricing_info['final_price'],
            student=student,
            package=package
        )
        print(f"✅ Razorpay order created: {razorpay_order['id']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Backend test failed: {e}")
        return False

def test_api_endpoint():
    """Test the API endpoint"""
    print("\nTesting API Endpoint")
    print("=" * 25)
    
    try:
        # Test new API endpoint
        data = {'student': 11, 'package': 1}
        response = requests.post(
            'http://127.0.0.1:8000/api/packages/v2/create-subscription/', 
            json=data,
            timeout=10
        )
        
        print(f"API Response Status: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print("✅ API Success!")
            print(f"   Subscription ID: {result.get('subscription_id')}")
            print(f"   Final Price: ₹{result.get('final_price')}")
            print(f"   Razorpay Order: {result.get('razorpay_order_id', 'N/A')}")
            return True
        else:
            print(f"❌ API Error: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def main():
    print("Package Purchase API Fix Verification")
    print("=" * 45)
    
    backend_ok = test_backend_functionality()
    api_ok = test_api_endpoint()
    
    print("\n" + "=" * 45)
    print("VERIFICATION RESULTS:")
    print(f"Backend Functionality: {'✅ PASS' if backend_ok else '❌ FAIL'}")
    print(f"API Endpoint: {'✅ PASS' if api_ok else '❌ FAIL'}")
    
    if backend_ok and api_ok:
        print("\n🎉 SUCCESS! Package purchase API is working correctly!")
        print("\nKey Points:")
        print("- Razorpay integration is working")
        print("- Payment orders are being created")
        print("- Subscriptions are being saved")
        print("- Email errors no longer crash the API")
    else:
        print("\n⚠️ Some issues remain. Check the errors above.")

if __name__ == "__main__":
    main()
