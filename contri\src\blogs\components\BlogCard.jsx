import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, Modal } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
import defaultImage from "../../assets/blog.png";
import { useDispatch } from "react-redux";
import Swal from "sweetalert2";
import { deleteBlog, updateBlog } from "../../redux/slice/blogSlice";
import EditBlogForm from "./EditBlogForm";
import toast from "react-hot-toast";
import { BsPencilSquare, BsTrash } from "react-icons/bs";


const BlogCard = ({ blog, fetchBlogs }) => {
  const dispatch = useDispatch();
  const [showModal, setShowModal] = useState(false); // State to control the modal visibility
  const [selectedBlog, setSelectedBlog] = useState(null); // State to store the selected blog for editing
  const [loading, setLoading] = useState(false); // Loading state for form submission

  // Function to handle blog deletion
  const handleDelete = async () => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        // Dispatch deleteBlog action with blog.id
        await dispatch(deleteBlog(blog.slug));
        Swal.fire("Deleted!", "Your blog has been deleted.", "success");
        fetchBlogs(); // Call fetchBlogs to update the list
      } catch (error) {
        Swal.fire("Error!", "Something went wrong while deleting the blog.", "error");
      }
    }
  };

  // Function to handle the Edit button click
  const handleEdit = () => {
    setSelectedBlog(blog); // Set the selected blog for editing
    setShowModal(true); // Show the modal
  };

  // Function to close the modal
  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedBlog(null); // Clear the selected blog when closing the modal
  };

  const handleSubmit = async (updatedBlogData) => {
    // Validate required fields before submission
    if (!updatedBlogData.title || !updatedBlogData.author || !updatedBlogData.content) {
      toast.error("Title, Author, and Content are required.");
      return;
    }

    const defaultImage = `${import.meta.env.VITE_BASE_URL}/images/default.png`;

    const title = updatedBlogData.title;
    const slug = title
      .toLowerCase()                    // Convert to lowercase
      .trim()                           // Trim any leading or trailing spaces
      .replace(/\s+/g, '-')             // Replace spaces with dashes
      .replace(/[^a-z0-9\-]/g, '');     // Remove any characters that are not lowercase letters, numbers, or dashes

    // Concatenate the VITE_HOSTED_DOMAIN with the slug
    const canonicalUrl = `http://${import.meta.env.VITE_HOSTED_DOMAIN}/blog/${slug}`;

    console.log(canonicalUrl); // Output the full URL

    let uploadedImageUrl = `${import.meta.env.VITE_WEBSITE_DOMAIN}${selectedBlog.image}`;

    if (updatedBlogData.image) {
      // Generate the dynamic URL based on the file name and extension
      const fileName = updatedBlogData.image.name.replace(/\s+/g, "-").toLowerCase(); // Replace spaces with hyphens
      const fileExtension = fileName.split(".").pop(); // Get the file extension
      uploadedImageUrl = `${import.meta.env.VITE_WEBSITE_DOMAIN}/media/blog_images/${fileName}`;
    }

    const openGraph = {
      "og:title": updatedBlogData.meta_title || updatedBlogData.title,
      "og:description": updatedBlogData.meta_description || updatedBlogData.introduction,
      "og:image": uploadedImageUrl,
    };

    const twitterCards = {
      "twitter:title": updatedBlogData.meta_title || updatedBlogData.title,
      "twitter:description": updatedBlogData.meta_description || updatedBlogData.introduction,
      "twitter:image": uploadedImageUrl,
    };

    const breadcrumbSchema = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        { "@type": "ListItem", position: 1, name: "Home", item: `${import.meta.env.VITE_HOSTED_DOMAIN}/` },
        { "@type": "ListItem", position: 2, name: "Blog", item: `${import.meta.env.VITE_HOSTED_DOMAIN}/blogs` },
        { "@type": "ListItem", position: 3, name: updatedBlogData.title, item: canonicalUrl },
      ],
    };

    const articleSchema = {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": updatedBlogData.meta_title || updatedBlogData.title,
      "image": [uploadedImageUrl],
      "author": updatedBlogData.author || "Anonymous",
      "publisher": {
        "@type": "Organization",
        "name": "Your Website",
        "logo": { "@type": "ImageObject", url: `https://${import.meta.env.VITE_WEBSITE_DOMAIN}/images/logo.png` },
      },
      "datePublished": new Date().toISOString(),
      "dateModified": new Date().toISOString(),
    };

    const blogData = {
      ...updatedBlogData,
      canonical_url: canonicalUrl,
      open_graph: JSON.stringify(openGraph),
      twitter_cards: JSON.stringify(twitterCards),
      breadcrumb_schema: JSON.stringify(breadcrumbSchema),
      article_schema: JSON.stringify(articleSchema),
    };

    setLoading(true);
    try {
      await dispatch(updateBlog({ slug: selectedBlog.slug, updatedData: blogData })).unwrap();
      toast.success("Blog updated successfully!");
      fetchBlogs();  // Refresh the list after update
    } catch (error) {
      toast.error(error.message || "Error updating blog.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="d-flex flex-column align-items-start">
      <Card className="mb-4 w-100">
        <Card.Img
          variant="top"
          src={blog.image ? `${import.meta.env.VITE_BASE_URL}/${blog.image}` : defaultImage}
          alt={blog.image_caption || "Blog Image"}
          style={{
            maxWidth: "100%",
            height: "10rem",
            objectFit: "cover",
          }}
        />
        <Card.Body>
          <Card.Title>{blog.title}</Card.Title>
          <Card.Subtitle className="my-2">
            <small className="text-muted">Published on: {new Date(blog.published_date).toLocaleDateString()}</small> <br />
            <small> By Author: {blog?.author_first_name} {blog?.author_last_name} </small>
          </Card.Subtitle>
          <Card.Text className="text-truncate-4">{blog.short_content}</Card.Text>
          <div className="d-flex justify-content-center mt-2">

            <Link to={`/current_affairs_normal_questions/${blog.slug}`}>
              <Button variant="outline-success" style={{ fontSize: "0.7rem" }}>
                Normal Ques.
              </Button>
            </Link>


            <Link to={`/current_affairs_master_questions/${blog.slug}`}>
              <Button variant="outline-success" className="mx-1" style={{ fontSize: "0.7rem" }}>
                Master Ques.
              </Button>
            </Link>


            <Link to={`/current_affairs_master_options/${blog.slug}`}>
              <Button variant="outline-success" style={{ fontSize: "0.7rem" }}>
                Master Opt.
              </Button>
            </Link>

          </div>

          <div className="d-flex justify-content-center mt-2">

            {/* View Button */}
            <a
              href={`${import.meta.env.VITE_SHASHTRATH_BLOG}blog/${blog.slug}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              <Button variant="outline-secondary" className="mx-1" style={{ fontSize: "0.7rem" }}>
                Public View
              </Button>
            </a>       
            <Link to={`/blog/${blog.slug}`}>
            <Button variant="outline-info" className="mx-1" style={{fontSize: "0.7rem"}}>
              Internal View
            </Button>
            </Link>

                 {/* Edit Button */}
            <Button variant="outline-primary" className="mx-1" onClick={handleEdit} style={{ fontSize: "0.7rem" }}>
              <BsPencilSquare />
            </Button>

            {/* Delete Button */}
            <Button variant="outline-danger" onClick={handleDelete} style={{ fontSize: "0.7rem" }}>
              <BsTrash />
            </Button>
          </div>
        </Card.Body>
      </Card>

      {/* Modal for Editing Blog */}
      <Modal show={showModal} onHide={handleCloseModal} size="xl">
        <Modal.Header closeButton>
          <Modal.Title>Edit Blog</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedBlog && (
            <>
              {/* BlogForm for editing */}
              <EditBlogForm
                initialValues={{
                  ...selectedBlog,
                  imageUrl: selectedBlog.image ? `${import.meta.env.VITE_BASE_URL}/${selectedBlog.image}` : '', // Pass current image URL
                }}
                onSubmit={handleSubmit} // Pass handleSubmit for form submission
                loading={loading} // Set loading state as needed
              />
            </>
          )}
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default BlogCard;
