import React, { useState, useEffect } from "react";
import { Modal, Form, Button } from "react-bootstrap";
import RichTextEditor from "./RichTextEditor";
import MathEditor from "./MathEditor";
import MathTextRenderer from "./MathTextRenderer";

const OptionEditModal = ({  show,  handleClose,  updatedOptionData,  setUpdatedOptionData,  handleSubmitOption,}) => {
  // Math editor state
  const [localMathContent, setLocalMathContent] = useState("");
  const [showOptionMathEditor, setShowOptionMathEditor] = useState(false);

  // Reset math content when modal closes
  useEffect(() => {
    if (!show) {
      setLocalMathContent("");
      setShowOptionMathEditor(false);
    }
  }, [show]);

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit Option</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmitOption}>
          <Form.Group controlId="optionText">
            <RichTextEditor
              label="Option Text"
              name="option_text"
              value={updatedOptionData.option_text}
              onChange={(e) =>
                setUpdatedOptionData({
                  ...updatedOptionData,
                  option_text: e.target.value,
                })
              }
              placeholder="Enter option text"
              rows={3}
              required
            />
          </Form.Group>

          {/* Math Editor Section for Option Text */}
          <Form.Group className="mb-3">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <Form.Label>Mathematical Content for Option (Optional)</Form.Label>
              <Button
                variant="outline-primary"
                size="sm"
                onClick={() => setShowOptionMathEditor(!showOptionMathEditor)}
              >
                {showOptionMathEditor ? 'Hide' : 'Add'} Math
              </Button>
            </div>

            {showOptionMathEditor && (
              <MathEditor
                value={localMathContent}
                onChange={setLocalMathContent}
                label="Mathematical Expression for Option"
                placeholder="Enter mathematical expressions for this option..."
                showPreview={true}
                showRawLatex={false}
                displayMode={false}
                embeddedMode={true}
                textContent={updatedOptionData.option_text}
                onTextContentChange={(newText) =>
                  setUpdatedOptionData({
                    ...updatedOptionData,
                    option_text: newText,
                  })
                }
                className="mb-2"
              />
            )}

            {/* Preview of option text with embedded math */}
            {updatedOptionData.option_text && (
              <div className="mt-2">
                <Form.Label>Option Preview:</Form.Label>
                <div className="border rounded p-2 bg-light small">
                  <MathTextRenderer text={updatedOptionData.option_text} />
                </div>
              </div>
            )}
          </Form.Group>

          <Form.Group controlId="isCorrect">
            <Form.Label>Is Correct</Form.Label>
            <Form.Check
              type="checkbox"
              name="is_correct"
              checked={updatedOptionData.is_correct}
              onChange={(e) =>
                setUpdatedOptionData({
                  ...updatedOptionData,
                  is_correct: e.target.checked,
                })
              }
            />
          </Form.Group>
          <Button variant="outline-primary" type="submit" className="mt-3 w-100">
            Save Changes
          </Button>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default OptionEditModal;
