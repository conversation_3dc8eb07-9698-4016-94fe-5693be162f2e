import { useEffect } from 'react';
import { AppState } from 'react-native';
import { useDispatch } from 'react-redux';
import { resetPopupState } from '../redux/popupSlice';

export const AppStateManager = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (nextAppState === 'inactive' || nextAppState === 'background') {
        // Reset popup state when app goes to background or becomes inactive
        dispatch(resetPopupState());
      }
    });

    return () => {
      subscription.remove();
    };
  }, [dispatch]);

  return null; // This component doesn't render anything
};
