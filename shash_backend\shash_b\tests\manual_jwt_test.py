#!/usr/bin/env python3
"""
Manual JWT Test - Quick verification of JWT functionality
"""

import os
import sys
import django
import requests
import json

# Setup Django
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from authentication.jwt_utils import generate_tokens_for_user

def test_jwt_system():
    print("=== Manual JWT System Test ===\n")
    
    # Create a test user
    try:
        # Clean up existing test user
        User.objects.filter(username='jwttest123').delete()
        
        # Create new test user
        user = User.objects.create_user(
            username='jwttest123',
            email='<EMAIL>',
            password='TestPass123!'
        )
        
        Student.objects.create(
            user=user,
            phone=f'jwt{user.id}543210',
            role='student'
        )
        
        print("✓ Test user created successfully")
        
        # Test token generation
        token_data = generate_tokens_for_user(user)
        print("✓ Tokens generated successfully")
        print(f"  Access token length: {len(token_data['access'])}")
        print(f"  Refresh token length: {len(token_data['refresh'])}")
        print(f"  Expires in: {token_data['expires_in']} seconds")
        
        # Test login via API
        login_response = requests.post(
            'http://localhost:8000/api/students/login/',
            json={'username': 'jwttest123', 'password': 'TestPass123!'}
        )
        
        if login_response.status_code == 200:
            print("✓ Login API works")
            login_data = login_response.json()
            jwt_tokens = login_data.get('JWT_Token', {})
            
            # Test token refresh
            refresh_response = requests.post(
                'http://localhost:8000/api/students/token/refresh/',
                json={'refresh': jwt_tokens.get('refresh')}
            )
            
            if refresh_response.status_code == 200:
                print("✓ Token refresh works")
                refresh_data = refresh_response.json()
                print(f"  New access token received: {len(refresh_data.get('access', ''))} chars")
            else:
                print(f"✗ Token refresh failed: {refresh_response.status_code}")
                print(f"  Response: {refresh_response.text}")
            
            # Test authenticated request
            headers = {'Authorization': f"Bearer {jwt_tokens.get('access')}"}
            auth_response = requests.get(
                'http://localhost:8000/api/students/list/',
                headers=headers
            )
            
            if auth_response.status_code == 200:
                print("✓ Authenticated request works")
            else:
                print(f"✗ Authenticated request failed: {auth_response.status_code}")
                print(f"  Response: {auth_response.text}")
                
        else:
            print(f"✗ Login failed: {login_response.status_code}")
            print(f"  Response: {login_response.text}")
        
        # Cleanup
        User.objects.filter(username='jwttest123').delete()
        print("✓ Test user cleaned up")
        
        print("\n=== JWT System Test Complete ===")
        
    except Exception as e:
        print(f"✗ Test failed with error: {str(e)}")
        # Cleanup on error
        try:
            User.objects.filter(username='jwttest123').delete()
        except:
            pass

if __name__ == '__main__':
    test_jwt_system()
