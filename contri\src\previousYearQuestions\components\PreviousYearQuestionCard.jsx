import React from 'react';

// Component to display each previous year question
const PreviousYearQuestionCard = ({ questionData }) => {
  // Destructure questionData to handle undefined fields safely
  const { question_details = {}, exam_list = [], year, month, note } = questionData;
  const { content, subject_name, topic_name, sub_topic_name, options = [], previous_year_questions = [] } = question_details;

  return (
    <div className="question-card">
      {/* Ensure content exists before rendering */}
      {content ? <h3>{content}</h3> : <h3>No content available</h3>}
      
      {/* Ensure subject_name, topic_name, sub_topic_name exist before rendering */}
      <p><strong>Subject:</strong> {subject_name || 'N/A'}</p>
      <p><strong>Topic:</strong> {topic_name || 'N/A'}</p>
      <p><strong>Sub-topic:</strong> {sub_topic_name || 'N/A'}</p>

      <div className="exam-details">
        {/* Ensure exam_list is an array and render */}
        <p><strong>Exams:</strong> {exam_list.length > 0 ? exam_list.join(', ') : 'No exams listed'}</p>
        <p><strong>Year:</strong> {year || 'N/A'}</p>
        <p><strong>Month:</strong> {month || 'N/A'}</p>
        <p><strong>Note:</strong> {note || 'No additional notes'}</p>
      </div>

      <div className="options">
        <h4>Options:</h4>
        {/* Ensure options is an array and has elements */}
        <ul>
          {options.length > 0 ? options.map((option) => (
            <li key={option.option_id}>
              {option.option_text} - {option.is_correct ? 'Correct' : 'Incorrect'}
            </li>
          )) : <li>No options available</li>}
        </ul>
      </div>

      <div className="previous-year-exams">
        <h4>Previous Year Questions:</h4>
        {/* Ensure previous_year_questions is an array and has elements */}
        <ul>
          {previous_year_questions.length > 0 ? previous_year_questions.map((exam) => (
            <li key={exam.id}>
              {exam.exams} - {exam.year} {exam.month} - {exam.note}
            </li>
          )) : <li>No previous year questions available</li>}
        </ul>
      </div>
    </div>
  );
};

export default PreviousYearQuestionCard;
