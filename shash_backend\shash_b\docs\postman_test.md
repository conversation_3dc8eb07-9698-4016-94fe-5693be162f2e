# Customer Care Questions API Testing Guide

## Overview
This document provides comprehensive testing instructions for the Customer Care Questions API endpoints, including attachment functionality for Normal Questions, Master Questions, Master Options, and their options.

## Base URL
```
https://api.shashtrarth.com
```

## 🚨 DEPLOYMENT STATUS
**NEW ENDPOINT STATUS:** The new `/api/customrcare/questions/` endpoint has been created but **NOT YET DEPLOYED** to production.

**CURRENTLY WORKING ENDPOINTS:**
- ✅ `/api/customrcare/questions/search/` - Search questions by subject
- ✅ `/api/customrcare/questions/status-update/` - Update question status
- ✅ `/api/questions/questions/` - Questions CRUD operations
- ✅ `/api/questions/master-questions/` - Master questions CRUD
- ✅ `/api/questions/master-options/` - Master options CRUD

**PENDING DEPLOYMENT:**
- ⏳ `/api/customrcare/questions/` - New general questions list endpoint

## Authentication
All customer care endpoints require authentication. You'll need to login first to get an access token.

### 1. Customer Care Login
```bash
curl -X POST "https://api.shashtrarth.com/api/customrcare/login/" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }'
```

**Expected Response:**
```json
{
  "access": "your_access_token",
  "refresh": "your_refresh_token",
  "user": {
    "username": "your_username",
    "email": "your_email"
  }
}
```

## Main Endpoints

### 2. Get All Questions (NEW ENDPOINT)
**Endpoint:** `GET /api/customrcare/questions/`

```bash
curl -X GET "https://api.shashtrarth.com/api/customrcare/questions/" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "NormalQuestions": [
    {
      "question_id": 29,
      "slug": "test-question-content",
      "content": "Test Question Content",
      "attachments": null,
      "explanation_attachment": null,
      "reason_document": null,
      "options": [
        {
          "option_id": 1,
          "option_text": "True",
          "attachments": null,
          "is_correct": true
        },
        {
          "option_id": 2,
          "option_text": "False",
          "attachments": null,
          "is_correct": false
        }
      ]
    }
  ],
  "MasterQuestions": [
    {
      "master_question_id": 6,
      "title": "Test Title for master Question",
      "attachments": null,
      "reason_document": null,
      "questions": []
    }
  ],
  "MasterOptions": [
    {
      "master_option_id": 10,
      "title": "SSC Chemistry Master Option",
      "attachments": null,
      "reason_document": null,
      "related_questions": []
    }
  ],
  "Blogs": []
}
```

### 3. Search Questions by Subject
**Endpoint:** `GET /api/customrcare/questions/search/`

```bash
curl -X GET "https://api.shashtrarth.com/api/customrcare/questions/search/?keyword=Mathematics" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json"
```

### 4. Update Question Status
**Endpoint:** `POST /api/customrcare/questions/status-update/`

```bash
curl -X POST "https://api.shashtrarth.com/api/customrcare/questions/status-update/" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json" \
  -d '{
    "question_type": "normal",
    "question_id": 29,
    "approval_status": "accepted",
    "reason": "Question approved after review"
  }'
```

## Testing Attachment Functionality

### 5. Create Question with Attachments (Contributor Endpoint)
**Endpoint:** `POST /api/questions/questions/`

```bash
curl -X POST "https://api.shashtrarth.com/api/questions/questions/" \
  -H "Authorization: Bearer contributor_access_token" \
  -F "content=Test question with image attachment" \
  -F "difficulty=1" \
  -F "author=1" \
  -F "subject=[1]" \
  -F "topic=[1]" \
  -F "course=[1]" \
  -F "explanation=Test explanation with image" \
  -F "reason=Test reason with image" \
  -F "attachments=@/path/to/question_image.jpg" \
  -F "explanation_attachment=@/path/to/explanation_image.jpg" \
  -F "reason_document=@/path/to/reason_image.jpg"
```

### 6. Create Option with Attachment (Contributor Endpoint)
**Endpoint:** `POST /api/questions/{question_slug}/options/`

```bash
curl -X POST "https://api.shashtrarth.com/api/questions/test-question-slug/options/" \
  -H "Authorization: Bearer contributor_access_token" \
  -F "question=29" \
  -F "option_text=Test option with image" \
  -F "is_correct=true" \
  -F "attachments=@/path/to/option_image.jpg"
```

### 7. Create Master Question with Attachments (Contributor Endpoint)
**Endpoint:** `POST /api/questions/master-questions/`

```bash
curl -X POST "https://api.shashtrarth.com/api/questions/master-questions/" \
  -H "Authorization: Bearer contributor_access_token" \
  -F "title=Test Master Question with Image" \
  -F "passage_content=This is a test passage for master question" \
  -F "author=1" \
  -F "reason=Test reason for master question" \
  -F "attachments=@/path/to/master_question_image.jpg" \
  -F "reason_document=@/path/to/master_reason_image.jpg"
```

### 8. Create Master Option with Attachments (Contributor Endpoint)
**Endpoint:** `POST /api/questions/master-options/`

```bash
curl -X POST "https://api.shashtrarth.com/api/questions/master-options/" \
  -H "Authorization: Bearer contributor_access_token" \
  -F "title=Test Master Option with Image" \
  -F "option_content=This is a test option content for master option" \
  -F "conditions=Test conditions for master option" \
  -F "author=1" \
  -F "reason=Test reason for master option" \
  -F "attachments=@/path/to/master_option_image.jpg" \
  -F "reason_document=@/path/to/master_option_reason_image.jpg"
```

## Postman Collection Setup

### Environment Variables
Create these environment variables in Postman:
- `base_url`: `https://api.shashtrarth.com`
- `access_token`: (Set after login)
- `contributor_token`: (Set after contributor login)

### Pre-request Script for Authentication
Add this to requests that need authentication:
```javascript
pm.request.headers.add({
    key: 'Authorization',
    value: 'Bearer ' + pm.environment.get('access_token')
});
```

## Test Scenarios

### Scenario 1: Basic Question Retrieval
1. Login as customer care user
2. Get all questions using `/api/customrcare/questions/`
3. Verify response includes NormalQuestions, MasterQuestions, MasterOptions, and Blogs
4. Check that attachment fields are present (even if null)

### Scenario 2: Question Search
1. Search for questions by subject using `/api/customrcare/questions/search/?keyword=Mathematics`
2. Verify filtered results
3. Check attachment fields in response

### Scenario 3: Attachment Verification
1. Create questions/options with attachments (as contributor)
2. Retrieve questions via customer care endpoint
3. Verify attachment URLs are properly returned
4. Test image accessibility

### Scenario 4: Status Update
1. Update question approval status
2. Verify status change
3. Check updated_by_custmorcare field

## Expected Attachment Fields

### Normal Questions
- `attachments`: Main question image
- `explanation_attachment`: Explanation image
- `reason_document`: Reason document image

### Options
- `attachments`: Option image

### Master Questions
- `attachments`: Master question image
- `reason_document`: Reason document image

### Master Options
- `attachments`: Master option image
- `reason_document`: Reason document image

## Error Handling

### Common Error Responses
```json
{
  "detail": "Authentication required."
}
```

```json
{
  "error": "Customer care profile not found"
}
```

```json
{
  "error": "An error occurred: [error_message]"
}
```

## Notes
- All attachment fields support image uploads (JPEG, PNG, etc.)
- Attachments are optional (can be null)
- File uploads require `multipart/form-data` content type
- Customer care endpoints require customer care user authentication
- Contributor endpoints require contributor user authentication
