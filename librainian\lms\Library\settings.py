"""
Django settings for Library project.

Generated by 'django-admin startproject' using Django 5.0.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

from pathlib import Path
import os
from dotenv import load_dotenv
from decouple import config

# Load environment variables from .env file
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "SECRET_KEY"

# SECURITY WARNING: don't run with debug turned on in production!

DEBUG = True

ALLOWED_HOSTS = [
    "librainian.com",
    "www.librainian.com",
    "**************",
    "localhost",
    "127.0.0.1",
    "localhost",
    "127.0.0.1",
    "*************",
]


# Application definition

INSTALLED_APPS = [
    # "jazzmin",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "libraryCommander",
    "manager",
    "librarian",
    "subLibrarian",
    "studentsData",
    "blogs",
    "visitorsData",
    "wallet_and_transactions",
    "django_bootstrap5",
    "membership",
    "advertisements",
    "corsheaders",
    "django.contrib.sites",
    "django.contrib.sitemaps",
    "adInvestor",
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "allauth.socialaccount.providers.google",
    # "captcha",
]

# GOOGLE_RECAPTCHA_SECRET_KEY = "6LeTqe8pAAAAAEWa2IwHttmlbSWSk93WYc6LWeTw"
# RECAPTCHA_PUBLIC_KEY = "6LeTqe8pAAAAAFiiFIMpq1vU08jTWfxKLR4H_4-B"
# RECAPTCHA_PRIVATE_KEY = "6LeTqe8pAAAAAEWa2IwHttmlbSWSk93WYc6LWeTw"

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.sites.middleware.CurrentSiteMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "Library.middleware.SessionTimeoutMiddleware",
    "Library.middleware.URLMatchingMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "allauth.account.middleware.AccountMiddleware",
]

ROOT_URLCONF = "Library.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR, "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "Library.wsgi.application"


# Database
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3",  # Path to the SQLite database file
    }
}

# DATABASES = {
#     "default": {
#         "ENGINE": "django.db.backends.postgresql",
#         "NAME": os.getenv("DB_NAME"),
#         "USER": os.getenv("DB_USER"),
#         "PASSWORD": os.getenv("DB_PASSWORD"),
#         "HOST": os.getenv("DB_HOST"),
#         "PORT": os.getenv("DB_PORT"),
#     }
# }

# Production Database
# ==========================================================


# from dotenv import load_dotenv

# # Set the path to your .env file
# env_path = Path("/one/lms/.env")

# # Load the .env file
# load_dotenv(dotenv_path=env_path)

# # Use the environment variables in your settings
# DATABASES = {
#     "default": {
#         "ENGINE": "django.db.backends.postgresql",
#         "NAME": os.environ.get("DB_NAME"),
#         "USER": os.environ.get("DB_USER"),
#         "PASSWORD": os.environ.get("DB_PASSWORD"),
#         "HOST": os.environ.get("DB_HOST"),
#         "PORT": os.environ.get("DB_PORT"),
#     }
# }
# ==============================================================

# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Asia/Kolkata"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

# STATIC_URL = "/static/"
# STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")
# STATICFILES_DIRS = (os.path.join(BASE_DIR, "static"),)

STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")

STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
]


# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

MEDIA_URL = "/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")


EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = os.getenv("EMAIL_HOST")
EMAIL_PORT = os.getenv("EMAIL_PORT")
EMAIL_USE_SSL = os.getenv("EMAIL_USE_SSL")
EMAIL_HOST_USER = os.getenv("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = os.getenv("EMAIL_HOST_PASSWORD")
DEFAULT_FROM_EMAIL = os.getenv("DEFAULT_FROM_EMAIL")


# Set session timeout to 30 minutes (1800 seconds)
SESSION_COOKIE_AGE = 136000

# Set to True to use browser-length sessions (expire when the user closes the browser)
SESSION_EXPIRE_AT_BROWSER_CLOSE = True

# Set to True to save the session data on every request
SESSION_SAVE_EVERY_REQUEST = True


# Date formatting
DATE_FORMAT = "d-m-Y"
DATE_INPUT_FORMATS = ["%Y-%m-%d", "%d-%m-%Y"]  # Accept both y-m-d and d-m-y

# Datetime formatting (if needed)
DATETIME_FORMAT = "d-m-Y H:i:s"
DATETIME_INPUT_FORMATS = ["%Y-%m-%d %H:%M:%S", "%d-%m-%Y %H:%M:%S"]


TIME_ZONE = "Asia/Kolkata"
USE_TZ = True

# Admin interface formatting
from django.conf.locale.en import formats as en_formats

en_formats.DATE_FORMAT = "d-m-Y"
en_formats.DATETIME_FORMAT = "d-m-Y H:i:s"


CORS_ALLOWED_ORIGINS = [
    "https://librainian.com",
    "https://www.librainian.com",
    "http://*************",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
]

CSRF_TRUSTED_ORIGINS = [
    "https://librainian.com",
    "https://www.librainian.com",
    "http://*************",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    # Add any other trusted origins here
]

# Razorpay keys
RAZORPAY_KEY_ID = os.getenv("RAZORPAY_KEY_ID")
RAZORPAY_KEY_SECRET = os.getenv("RAZORPAY_KEY_SECRET")


# Define the path to your backup folder from the .env file
BACKUP_FOLDER = os.getenv("BACKUP_FOLDER", "")


AUTHENTICATION_BACKENDS = [
    "django.contrib.auth.backends.ModelBackend",
    "allauth.account.auth_backends.AuthenticationBackend",
]

SITE_ID = 1

GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_SECRET = os.getenv("GOOGLE_SECRET")

SOCIALACCOUNT_PROVIDERS = {
    "google": {
        "SCOPE": [
            "profile",
            "email",
        ],
        "AUTH_PARAMS": {
            "access_type": "online",
        },
        "APP": {
            "client_id": GOOGLE_CLIENT_ID,
            "secret": GOOGLE_SECRET,
            "key": "",
        },
    }
}

SOCIALACCOUNT_LOGIN_ON_GET = True
SOCIALACCOUNT_AUTO_SIGNUP = True
LOGIN_REDIRECT_URL = "lib_profile"
LOGIN_URL = "lib_login"
LOGOUT_REDIRECT_URL = "lib_login"

# Specify the custom adapters
ACCOUNT_ADAPTER = "librarian.adapters.CustomAccountAdapter"
SOCIALACCOUNT_ADAPTER = "librarian.adapters.CustomSocialAccountAdapter"

# SECURE_SSL_REDIRECT = True
# SESSION_COOKIE_SECURE = True
# CSRF_COOKIE_SECURE = True

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'PerReloadPage',
    }
}


# Fetch values from .envdadadafgdgdg
BULKSMS_API_ID = os.getenv("BULKSMS_API_ID")
BULKSMS_API_PASSWORD = os.getenv("BULKSMS_API_PASSWORD")
BULKSMS_SENDER = os.getenv("BULKSMS_SENDER")
BULKSMS_SMS_TYPE = os.getenv("BULKSMS_SMS_TYPE")

# Firebase Configuration
FIREBASE_PROJECT_ID = os.getenv("FIREBASE_PROJECT_ID", "librainian-app")
FIREBASE_SERVICE_ACCOUNT_JSON = os.getenv("FIREBASE_SERVICE_ACCOUNT_JSON")
BULKSMS_SMS_ENCODING = os.getenv("BULKSMS_SMS_ENCODING")
