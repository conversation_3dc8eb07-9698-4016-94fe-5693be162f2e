from django.core.management.base import BaseCommand
from django.utils import timezone
from librarian.models import Librarian_param, AnalyticsCache
from librarian.signals import invalidate_analytics_cache


class Command(BaseCommand):
    help = 'Manage analytics cache - refresh, clear, or check status'

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=['refresh', 'clear', 'status', 'refresh-all'],
            default='status',
            help='Action to perform on analytics cache'
        )
        parser.add_argument(
            '--librarian-id',
            type=int,
            help='Specific librarian ID to target (optional)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force action even if cache is fresh'
        )

    def handle(self, *args, **options):
        action = options['action']
        librarian_id = options.get('librarian_id')
        force = options.get('force', False)

        if action == 'status':
            self.show_cache_status(librarian_id)
        elif action == 'refresh':
            self.refresh_cache(librarian_id, force)
        elif action == 'refresh-all':
            self.refresh_all_caches(force)
        elif action == 'clear':
            self.clear_cache(librarian_id)

    def show_cache_status(self, librarian_id=None):
        """Show status of analytics caches"""
        self.stdout.write(self.style.SUCCESS('📊 Analytics Cache Status'))
        self.stdout.write('=' * 50)

        if librarian_id:
            try:
                librarian = Librarian_param.objects.get(id=librarian_id)
                librarians = [librarian]
            except Librarian_param.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Librarian with ID {librarian_id} not found')
                )
                return
        else:
            librarians = Librarian_param.objects.filter(is_librarian=True)

        for librarian in librarians:
            try:
                cache = AnalyticsCache.objects.get(librarian=librarian)
                needs_refresh = cache.needs_recalculation()
                
                self.stdout.write(f'\n🏛️  {librarian.library_name} (ID: {librarian.id})')
                self.stdout.write(f'   Last calculated: {cache.last_calculated}')
                self.stdout.write(f'   Needs refresh: {"Yes" if needs_refresh else "No"}')
                self.stdout.write(f'   Force flag: {"Yes" if cache.force_recalculate else "No"}')
                self.stdout.write(f'   Students this month: {cache.students_this_month}')
                self.stdout.write(f'   Today\'s collection: ₹{cache.todays_collection}')
                
                if needs_refresh:
                    self.stdout.write(self.style.WARNING('   ⚠️  Cache needs refresh'))
                else:
                    self.stdout.write(self.style.SUCCESS('   ✅ Cache is fresh'))
                    
            except AnalyticsCache.DoesNotExist:
                self.stdout.write(f'\n🏛️  {librarian.library_name} (ID: {librarian.id})')
                self.stdout.write(self.style.WARNING('   ⚠️  No cache found'))

    def refresh_cache(self, librarian_id=None, force=False):
        """Refresh analytics cache for specific librarian or all"""
        if librarian_id:
            try:
                librarian = Librarian_param.objects.get(id=librarian_id)
                self.stdout.write(f'🔄 Refreshing cache for {librarian.library_name}...')
                
                if force:
                    invalidate_analytics_cache(librarian)
                    self.stdout.write(self.style.SUCCESS('✅ Cache invalidated and will refresh on next access'))
                else:
                    cache, created = AnalyticsCache.objects.get_or_create(librarian=librarian)
                    if cache.needs_recalculation() or created:
                        invalidate_analytics_cache(librarian)
                        self.stdout.write(self.style.SUCCESS('✅ Cache refreshed'))
                    else:
                        self.stdout.write(self.style.WARNING('⚠️  Cache is already fresh. Use --force to refresh anyway.'))
                        
            except Librarian_param.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Librarian with ID {librarian_id} not found')
                )
        else:
            self.stdout.write('Please specify --librarian-id or use refresh-all')

    def refresh_all_caches(self, force=False):
        """Refresh all analytics caches"""
        librarians = Librarian_param.objects.filter(is_librarian=True)
        total = librarians.count()
        refreshed = 0
        
        self.stdout.write(f'🔄 Refreshing analytics cache for {total} librarians...')
        
        for librarian in librarians:
            try:
                if force:
                    invalidate_analytics_cache(librarian)
                    refreshed += 1
                else:
                    cache, created = AnalyticsCache.objects.get_or_create(librarian=librarian)
                    if cache.needs_recalculation() or created:
                        invalidate_analytics_cache(librarian)
                        refreshed += 1
                        
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error refreshing cache for {librarian.library_name}: {e}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Refreshed {refreshed} out of {total} caches')
        )

    def clear_cache(self, librarian_id=None):
        """Clear analytics cache"""
        if librarian_id:
            try:
                librarian = Librarian_param.objects.get(id=librarian_id)
                AnalyticsCache.objects.filter(librarian=librarian).delete()
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Cache cleared for {librarian.library_name}')
                )
            except Librarian_param.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Librarian with ID {librarian_id} not found')
                )
        else:
            count = AnalyticsCache.objects.count()
            AnalyticsCache.objects.all().delete()
            self.stdout.write(
                self.style.SUCCESS(f'✅ Cleared {count} analytics caches')
            )
