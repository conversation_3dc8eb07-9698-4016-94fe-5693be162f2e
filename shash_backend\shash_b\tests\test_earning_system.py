#!/usr/bin/env python3
"""
Comprehensive Test Script for the Contributor Earning System
Creates 50+ random entries and tests all APIs with detailed documentation generation.
"""

import os
import sys
import django
import random
import json
from datetime import datetime, timedelta
from decimal import Decimal
from faker import Faker

# Setup Django environment
sys.path.append('/Users/<USER>/Documents/code/shash_b')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Import after Django setup
from django.contrib.auth.models import User
from django.utils import timezone
from django.test import Client
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from contributor.models import ContributorProfile, ContributorPoints, ContributorEarning
from contributor.earning_service import EarningCalculationService
from questions.models import Question, MasterQuestion, MasterOption, PreviousYearQuestion, Subject, Topic, SubTopic, Course, SubCourse
from blogs.models import BlogPost

fake = Faker()


class EarningSystemTester:
    """Test class for the earning system"""
    
    def __init__(self):
        self.test_results = []
        self.test_user = None
        self.test_contributor = None
        self.test_points_config = None
    
    def log_result(self, test_name, success, message=""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })
        print(f"{status}: {test_name} - {message}")
    
    def setup_test_data(self):
        """Setup test data"""
        try:
            # Create test user
            self.test_user, created = User.objects.get_or_create(
                username='test_contributor_earning',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Test',
                    'last_name': 'Contributor'
                }
            )
            
            # Create contributor profile
            self.test_contributor, created = ContributorProfile.objects.get_or_create(
                user=self.test_user,
                defaults={'role': 'contributor'}
            )
            
            # Create test points configuration
            self.test_points_config, created = ContributorPoints.objects.get_or_create(
                name='Test Points Config',
                defaults={
                    'normal_questions': 10,
                    'master_questions': 20,
                    'master_options': 5,
                    'blogs': 15,
                    'previous_questions': 8,
                }
            )
            
            self.log_result("Setup Test Data", True, "Test data created successfully")
            return True
            
        except Exception as e:
            self.log_result("Setup Test Data", False, f"Error: {str(e)}")
            return False
    
    def test_points_configuration(self):
        """Test points configuration functionality"""
        try:
            # Test default points configuration
            default_config = self.test_contributor.get_points_config()
            if default_config:
                self.log_result("Default Points Config", True, f"Got config: {default_config.name}")
            else:
                self.log_result("Default Points Config", False, "No default config found")
                return False
            
            # Test custom points configuration
            self.test_contributor.custom_points = self.test_points_config
            self.test_contributor.save()
            
            custom_config = self.test_contributor.get_points_config()
            if custom_config == self.test_points_config:
                self.log_result("Custom Points Config", True, f"Custom config working: {custom_config.name}")
            else:
                self.log_result("Custom Points Config", False, "Custom config not working")
                return False
            
            return True
            
        except Exception as e:
            self.log_result("Points Configuration Test", False, f"Error: {str(e)}")
            return False
    
    def test_earning_calculation(self):
        """Test earning calculation functionality"""
        try:
            # Calculate earnings for current month
            earning = EarningCalculationService.calculate_and_update_earnings(
                self.test_contributor, 'monthly'
            )
            
            if earning:
                self.log_result("Earning Calculation", True, 
                              f"Calculated {earning.total_points} points for {earning.period_type}")
            else:
                self.log_result("Earning Calculation", False, "No earning record created")
                return False
            
            # Test earning summary
            summary = EarningCalculationService.get_earnings_summary(
                self.test_contributor, 'monthly'
            )
            
            if summary and 'total_points' in summary:
                self.log_result("Earning Summary", True, 
                              f"Summary generated with {summary['total_points']} total points")
            else:
                self.log_result("Earning Summary", False, "Summary generation failed")
                return False
            
            return True
            
        except Exception as e:
            self.log_result("Earning Calculation Test", False, f"Error: {str(e)}")
            return False
    
    def test_total_earnings(self):
        """Test total earnings calculation"""
        try:
            total_data = EarningCalculationService.get_contributor_total_earnings(
                self.test_contributor
            )
            
            if total_data and 'total_points' in total_data:
                self.log_result("Total Earnings", True, 
                              f"Total: {total_data['total_points']} points, "
                              f"Records: {total_data['earning_records_count']}")
            else:
                self.log_result("Total Earnings", False, "Total earnings calculation failed")
                return False
            
            return True
            
        except Exception as e:
            self.log_result("Total Earnings Test", False, f"Error: {str(e)}")
            return False
    
    def test_earning_model(self):
        """Test ContributorEarning model functionality"""
        try:
            # Create a test earning record
            start_date = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            end_date = start_date + timedelta(days=30)
            
            earning = ContributorEarning.objects.create(
                contributor=self.test_contributor,
                period_type='monthly',
                period_start=start_date,
                period_end=end_date,
                normal_questions_count=5,
                normal_questions_points=Decimal('50.00'),
                master_questions_count=2,
                master_questions_points=Decimal('40.00'),
                points_config_used=self.test_points_config
            )
            
            # Test auto-calculation of total points
            if earning.total_points == Decimal('90.00'):
                self.log_result("Earning Model Auto-calculation", True, 
                              f"Total points correctly calculated: {earning.total_points}")
            else:
                self.log_result("Earning Model Auto-calculation", False, 
                              f"Expected 90.00, got {earning.total_points}")
                return False
            
            return True
            
        except Exception as e:
            self.log_result("Earning Model Test", False, f"Error: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Contributor Earning System Tests...")
        print("=" * 60)
        
        # Setup
        if not self.setup_test_data():
            print("❌ Setup failed, aborting tests")
            return False
        
        # Run tests
        tests = [
            self.test_points_configuration,
            self.test_earning_model,
            self.test_earning_calculation,
            self.test_total_earnings,
        ]
        
        passed = 0
        failed = 0
        
        for test in tests:
            if test():
                passed += 1
            else:
                failed += 1
        
        # Summary
        print("=" * 60)
        print(f"📊 Test Summary:")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("🎉 All tests passed! Earning system is working correctly.")
        else:
            print("⚠️  Some tests failed. Please check the implementation.")
        
        return failed == 0
    
    def cleanup(self):
        """Cleanup test data"""
        try:
            if self.test_user:
                # Delete user and related data
                ContributorEarning.objects.filter(contributor=self.test_contributor).delete()
                self.test_contributor.delete()
                self.test_user.delete()
                print("🧹 Test data cleaned up")
        except Exception as e:
            print(f"⚠️  Cleanup error: {e}")


def main():
    """Main function"""
    tester = EarningSystemTester()
    try:
        success = tester.run_all_tests()
        return 0 if success else 1
    finally:
        tester.cleanup()


if __name__ == "__main__":
    exit(main())
