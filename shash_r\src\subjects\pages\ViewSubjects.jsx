import React, { useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import {
  Row,
  Col,
  Card,
  Button,
  Container,
  Form,
  Pagination,
  Modal,
  Dropdown,
} from "react-bootstrap";
import { deleteTopic, updateTopic } from "../../redux/slice/topicsSlice.js";
import { getSubject } from "../../redux/slice/subjectSlice.js";
import AddTopic from "../components/AddTopic.jsx";
import NavigationBar from "../../commonComponents/NavigationBar.jsx";
import ViewModal from "../../commonComponents/ViewModal.jsx";
import { BsPencilSquare, BsTrash } from "react-icons/bs";

import toast, { Toaster } from "react-hot-toast";
import Swal from "sweetalert2";
import CourseCardsSkeleton from "../../commonComponents/CourseCardsSkeleton.jsx";
import Skeleton from "react-loading-skeleton";

const ViewSubjects = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { slug } = useParams();
  const accessToken = useSelector(
    (state) => state.contributor.accessToken || null
  );

  useEffect(() => {
    if (!accessToken) {
      toast.error("Please log in as a contributor!");
      const timer = setTimeout(() => {
        navigate("/contributor_login");
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [accessToken, navigate]);

  useEffect(() => {
    if (slug && accessToken) {
      dispatch(getSubject(slug));
    }
  }, [dispatch, slug, accessToken]);

  const { subject, isLoading, error } = useSelector((state) => state.subject);
  const topics = subject?.data?.topics || [];

  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [topicsPerPage, setTopicsPerPage] = useState(6); // Default to 6 cards per page
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);

  const currentTopics = useMemo(() => {
    const filtered = topics.filter(
      (topic) =>
        topic.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        topic.description.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const indexOfLastTopic = currentPage * topicsPerPage;
    const indexOfFirstTopic = indexOfLastTopic - topicsPerPage;

    return filtered.slice(indexOfFirstTopic, indexOfLastTopic);
  }, [topics, searchQuery, currentPage, topicsPerPage]);

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleTopicsPerPageChange = (value) => {
    setTopicsPerPage(value);
    setCurrentPage(1); // Reset to the first page
  };

  const handleViewtIopic = (topic) => {
    setSelectedTopic(topic);
    setShowViewModal(true);
  };

  const handleDeleteTopic = async (topicSlug) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        await dispatch(deleteTopic({ topicSlug }));
        dispatch(getSubject(slug)); // Reload the subject data
        toast.success("Topic deleted successfully!");
      } catch (error) {
        console.error("Error deleting topic", error);
        toast.error("Failed to delete the topic. Please try again.");
      }
    }
  };

  const handleEditTopic = (topic) => {
    setSelectedTopic(topic);
    setEditModalOpen(true);
  };

  const handleSaveTopic = async () => {
    if (selectedTopic) {
      try {
        await dispatch(
          updateTopic({
            topicSlug: selectedTopic.slug,
            updatedData: {
              subject: slug,
              name: selectedTopic.name,
              description: selectedTopic.description,
            },
          })
        );
        dispatch(getSubject(slug));
        setEditModalOpen(false);
        setSelectedTopic(null);
        toast.success("Topic updated successfully!");
      } catch (error) {
        console.error("Error updating topic", error);
        toast.error("Failed to update the topic. Please try again.");
      }
    }
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    window.scrollTo({ top: 0, behavior: "smooth" }); // Scroll to top
  };

  const handleViewTopic = (slug) => {
    navigate(`/topic/${slug}`);
  };

  const handleTopicAdded = () => {
    dispatch(getSubject(slug));
  };

  // if (isLoading) {
  //   return (
  //     <div className="text-center mt-5">
  //       <div className="spinner-border text-success" role="status">
  //         <span className="visually-hidden">Loading...</span>
  //       </div>
  //     </div>
  //   );
  // }

  if (error) {
    return (
      <div className="text-center text-danger mt-5">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <>
      <NavigationBar />
      <Container fluid="md" className="d-flex flex-column">
        <Row className="mb-4">
          <Col xs={12} md={4} lg={3}>
            <AddTopic slug={slug} onTopicAdded={handleTopicAdded} />
          </Col>
          <Col xs={12} md={8} lg={9}>
            <div className="view-subject-container">
              <h2
                className="text-center text-success mt-3 mb-2"
                style={{ fontSize: "1.9rem" }}
              >
                Subject / Topic,{" "}
                <small className="h4"> See live updated here. </small>
              </h2>

              <Row className="mb-3 justify-content-center">
                <Col>
                  <Card className="shadow-lg rounded-3 mb-3">
                    <Card.Body>
                      {isLoading ? (
                        <>
                          <Skeleton
                            height={20}
                            width="60%"
                            baseColor="#e6ffe6"
                            highlightColor="#c4f7c4"
                          />
                          <Skeleton
                            height={15}
                            width="90%"
                            baseColor="#e6ffe6"
                            highlightColor="#c4f7c4"
                            className="mt-2"
                          />
                        </>
                      ) : (
                        <>
                          <Card.Title className="text-success">
                            {subject?.data?.name}
                          </Card.Title>
                          <Card.Text>{subject?.data?.description}</Card.Text>
                        </>
                      )}
                    </Card.Body>
                  </Card>
                </Col>
              </Row>

              <Row className="mb-4 justify-content-center">
                <Col
                  xs={12}
                  sm={8}
                  md={6}
                  lg={5}
                  className="d-flex justify-content-between"
                >
                  <Form.Control
                    type="text"
                    placeholder="Search topics..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                  />
                  <Dropdown>
                    <Dropdown.Toggle variant="success" style={{ margin: "0rem 0.2rem" }}>
                      {topicsPerPage === topics.length ? "All" : `${topicsPerPage} per page`}
                    </Dropdown.Toggle>
                    <Dropdown.Menu>
                      {[5, 25, 50, 100].map((value) => (
                        <Dropdown.Item
                          key={value}
                          onClick={() => handleTopicsPerPageChange(value)}
                        >
                          {value} per page
                        </Dropdown.Item>
                      ))}
                      <Dropdown.Item onClick={() => handleTopicsPerPageChange(topics.length)}>
                        All
                      </Dropdown.Item>
                    </Dropdown.Menu>
                  </Dropdown>
                </Col>
              </Row>

              {isLoading ? (
                <CourseCardsSkeleton number={6} />
              ) : (
                <Row className="d-flex justify-content-center">
                  {currentTopics.length > 0 ? (
                    currentTopics.map((topic) => (
                      <Col key={topic.topic_id} xs={12} sm={6} md={6} lg={4}>
                        <Card className="mb-4 shadow-lg rounded-3">
                          <Card.Body>
                            <Card.Title className="text-success text-truncate w-100">
                              {topic.name}
                            </Card.Title>
                            <Card.Text className="text-truncate w-100">
                              {topic.description || "No description available"}
                            </Card.Text>
                            <div className="d-flex flex-wrap justify-content-center mt-2">
                              <Button
                                variant="outline-primary"
                                className="m-1 fs-6 "
                                onClick={() => handleViewTopic(topic.slug)}
                              >
                                SubTopic
                              </Button>
                              <Button
                                variant="outline-info"
                                className="m-1 fs-6 "
                                onClick={() => handleViewtIopic(topic)}
                              >
                                View {/* View Icon */}
                              </Button>
                              <Button
                                variant="outline-success"
                                className="m-1 fs-6"
                                onClick={() => handleEditTopic(topic)}
                              >
                                <BsPencilSquare />
                              </Button>
                              <Button
                                variant="outline-danger"
                                className="m-1 fs-6"
                                onClick={() => handleDeleteTopic(topic.slug)}
                              >
                                <BsTrash />
                              </Button>
                            </div>
                          </Card.Body>
                        </Card>
                      </Col>
                    ))
                  ) : (
                    <div className="col-12 text-center text-muted">
                      No topics available for this subject.
                    </div>
                  )}
                </Row>
              )}

              {topics.length > topicsPerPage && (
                <div className="d-flex justify-content-center mt-3">
                  <Pagination>
                    <Pagination.First
                      onClick={() => handlePageChange(1)}
                      disabled={currentPage === 1}
                    />
                    <Pagination.Prev
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    />
                    {Array.from({
                      length: Math.ceil(topics.length / topicsPerPage),
                    }).map((_, index) => (
                      <Pagination.Item
                        key={index + 1}
                        active={index + 1 === currentPage}
                        onClick={() => handlePageChange(index + 1)}
                      >
                        {index + 1}
                      </Pagination.Item>
                    ))}
                    <Pagination.Next
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={
                        currentPage === Math.ceil(topics.length / topicsPerPage)
                      }
                    />
                    <Pagination.Last
                      onClick={() =>
                        handlePageChange(
                          Math.ceil(topics.length / topicsPerPage)
                        )
                      }
                      disabled={
                        currentPage === Math.ceil(topics.length / topicsPerPage)
                      }
                    />
                  </Pagination>
                </div>
              )}
            </div>
          </Col>
        </Row>

        {/* Edit Topic Modal */}
        <Modal
          show={editModalOpen}
          onHide={() => setEditModalOpen(false)}
          centered
        >
          <Modal.Header closeButton>
            <Modal.Title>Edit Topic</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form>
              <Form.Group controlId="topicName">
                <Form.Label>Topic Name</Form.Label>
                <Form.Control
                  type="text"
                  value={selectedTopic?.name}
                  onChange={(e) =>
                    setSelectedTopic({ ...selectedTopic, name: e.target.value })
                  }
                />
              </Form.Group>
              <Form.Group controlId="topicDescription" className="mt-3">
                <Form.Label>Topic Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  value={selectedTopic?.description}
                  onChange={(e) =>
                    setSelectedTopic({
                      ...selectedTopic,
                      description: e.target.value,
                    })
                  }
                />
              </Form.Group>
              <div className="d-flex justify-content-between mt-4">
                <Button
                  variant="secondary"
                  onClick={() => setEditModalOpen(false)}
                >
                  Close
                </Button>
                <Button variant="primary" onClick={handleSaveTopic}>
                  Save Changes
                </Button>
              </div>
            </Form>
          </Modal.Body>
        </Modal>

        <ViewModal
          show={showViewModal}
          onHide={() => setShowViewModal(false)}
          content={selectedTopic}
        />

        <Toaster />
      </Container>
    </>
  );
};

export default ViewSubjects;
