import React, { useEffect, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { getMasterOption, deleteMasterOption, updateMasterOption } from '../../../redux/slice/masterOptionSlice';
import { deleteOption, updateOption } from '../../../redux/slice/optionsSlice';
import { deleteQuestion, updateQuestion } from "../../../redux/slice/questionSlice";
import { Container, Card, Button, Modal, Form } from 'react-bootstrap';
import { FaEdit, FaTrashAlt, FaMinus } from 'react-icons/fa';
import Swal from 'sweetalert2';
import { toast, Toaster } from 'react-hot-toast';
import RichTextEditor from '../../../CommonComponents/RichTextEditor';
import MathTextRenderer from '../../../CommonComponents/MathTextRenderer';

export default function BlogViewMasterOptionQuestions({ searchTerm, optionAddedFlag }) {

  const {BlogSlug, slug } = useParams();
  const dispatch = useDispatch();
  const contributorProfileId = useSelector((state) => state.contributor.contributorProfileId || null);

  const [allOptions, setAllOptions] = useState([]);
  const [filteredOptions, setFilteredOptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showQuestionModal, setShowQuestionModal] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  
  const [updatedData, setUpdatedData] = useState({
    author: contributorProfileId,
    title: '',
    option_content: '',
    conditions: '',
  });

  // Fetch master options
  const fetchMasterOptions = async () => {
    setIsLoading(true);
    try {
      const response = await dispatch(getMasterOption(slug));
      if (response && response.payload) {
        const option = response.payload;
        const relatedQuestions = option.related_questions || [];
        setAllOptions([option]);
        filterOptions(relatedQuestions);
      } else {
        console.error("Unexpected response structure:", response);
        toast.error('Failed to load options: Invalid data structure');
      }
    } catch (error) {
      console.error('Error fetching master options:', error);
      toast.error('Failed to load options');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter options based on search term
  const filterOptions = (questions) => {
    const filtered = questions.filter((question) =>
      question.content.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredOptions(filtered);
  };

  useEffect(() => {
    fetchMasterOptions();
  }, [optionAddedFlag]);

  const handleEdit = (option) => {
    setSelectedOption(option);
    setUpdatedData({
      author: contributorProfileId,
      title: option.title,
      option_content: option.option_content,
      conditions: option.conditions,
    });
    setShowModal(true);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUpdatedData((prevData) => ({
      ...prevData,
      [name]: value,
      author: contributorProfileId,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (updatedData.title && updatedData.option_content && updatedData.conditions) {
      try {
        await dispatch(updateMasterOption({ id: selectedOption.slug, updatedData }));
        setShowModal(false);
        fetchMasterOptions();
        toast.success('Option updated successfully!');
      } catch (error) {
        console.error('Error updating option', error);
        toast.error('Failed to update the option.');
      }
    }
  };

  const handleDelete = async (id) => {
    if (!id) {
      toast.error('ID is missing, cannot delete the item.');
      return;
    }
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: 'This action cannot be undone!',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ff0000',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!',
    });
    if (result.isConfirmed) {
      try {
        await dispatch(deleteMasterOption(id));
        fetchMasterOptions();
        Swal.fire('Deleted!', 'The option has been deleted.', 'success');
      } catch (error) {
        console.error('Error deleting the option', error);
        toast.error('Failed to delete the option.');
      }
    }
  };

  // States for option edit modal
  const [showOptionModal, setShowOptionModal] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [updatedOptionData, setUpdatedOptionData] = useState({
    option_text: "",
    is_correct: false,
  });
  const [selectedQuestionId, setSelectedQuestionId] = useState(null); // Initialize the selected question ID state


  // state for updaing the question
  const [updatedQuestionData, setQuestionUpdatedQuestionData] = useState({
    content: '',
    difficulty: 3,
    author: contributorProfileId,
    status: 'active',
    current_affairs: BlogSlug,
    is_current_affairs: true,
    approval_status: 'pending',
    average_score: 0.0,
    times_attempted: 0,
    subject: '',
    subject_name: '',
    topic: '',
    topic_name: '',
    sub_topic: '',
    sub_topic_name: '',
    language: '',
    course: [],
    subcourse: [],
    master_question: null,
    is_master: false,
    master_option: slug,
    is_master_option: true,
    options: [],
  });


  // FUNCTION FOR EDIT THE QUESTION

  const handleQuestionInputChange = (e) => {
    const { name, value } = e.target;
    setQuestionUpdatedQuestionData((prevData) => ({ ...prevData, [name]: value }));
  };

  // FUNCTION FOR EDIT THE QUESTION

  const handleEditQuestion = (question) => {
    setSelectedQuestion(question);
    setQuestionUpdatedQuestionData({
      content: question.content, // Only the question content is editable
      subject: question.subject, // Pre-set subject, topic, and subtopic
      subject_name: question.subject_name,
      topic: question.topic,
      topic_name: question.topic_name,
      sub_topic: question.sub_topic,
      sub_topic_name: question.sub_topic_name,
    });
    setShowQuestionModal(true); // Show modal for editing
  };



  const handleSubmitQuestion = async (e) => {
    e.preventDefault();

    try {
      // Dispatch the updateQuestion action with the correct structure
      await dispatch(
        updateQuestion({
          question_id: selectedQuestion.question_id, // Question ID to update
          data: {
            content: updatedQuestionData.content, // Only update the content
            subject: selectedQuestion.subject, // Keep the original subject
            subject_name: selectedQuestion.subject_name, // Keep the original subject name
            topic: selectedQuestion.topic, // Keep the original topic
            topic_name: selectedQuestion.topic_name, // Keep the original topic name
            sub_topic: selectedQuestion.sub_topic, // Keep the original sub-topic
            sub_topic_name: selectedQuestion.sub_topic_name, // Keep the original sub-topic name
            author: selectedQuestion.author,
            current_affairs: selectedQuestion.current_affairs,
            is_current_affairs: selectedQuestion.is_current_affairs,
            status: selectedQuestion.status,
            approval_status: selectedQuestion.approval_status,
            average_score: selectedQuestion.average_score,
            times_attempted: selectedQuestion.times_attempted,
            attachments: selectedQuestion.attachments,
            language: selectedQuestion.language,
            course: selectedQuestion.course,
            subcourse: selectedQuestion.subcourse,
            master_question: selectedQuestion.master_question,
            is_master: selectedQuestion.is_master,
            master_option: selectedQuestion.master_option,
            is_master_option: selectedQuestion.is_master_option,
          },
        })
      );
      // Close modal and refresh questions
      setShowQuestionModal(false);
      fetchMasterOptions(); // Refetch questions to show updated data
      toast.success("Question updated successfully!");
    } catch (error) {
      console.error("Error updating question", error);
      toast.error("Failed to update the question.");
    }
  };


  // Function to delete a question

  const handleDeleteQuestion = async (question_id) => {
    if (!question_id) {
      toast.error("ID is missing, cannot delete the item.");
      return;
    }

    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        await dispatch(deleteQuestion(question_id)); // Pass the correct ID here
        fetchMasterOptions(); // Refetch questions after deleting
        Swal.fire("Deleted!", "The question has been deleted.", "success");
      } catch (error) {
        console.error("Error deleting the question", error);
        toast.error("Failed to delete the question. Please try again.");
      }
    }
  };



  // Function to handle editing an option
  const handleEditOption = (option, questionId) => {
    setSelectedOption(option); // Set the selected option
    setSelectedQuestionId(questionId); // Set the selected question ID
    setUpdatedOptionData({
      option_text: option.option_text,
      is_correct: option.is_correct,
    });
    setShowOptionModal(true); // Open the option edit modal
  };


  // Function to handle deleting an option
  const handleDeleteOption = (option_id, question_id) => {
    console.log("Option ID:", option_id); // Log option_id
    console.log("Question ID:", question_id); // Log question_id
    Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          await dispatch(deleteOption({ questionId: question_id, optionId: option_id })); // Pass both questionId and optionId
          toast.success("Option deleted successfully!");
          fetchMasterOptions(); // Refetch questions after deletion
        } catch (error) {
          console.error("Error deleting option", error);
          toast.error("Failed to delete the option.");
        }
      }
    });
  };


  // Function to handle submitting the updated option
  const handleSubmitOption = async (e) => {
    e.preventDefault();

    try {
      await dispatch(
        updateOption({
          questionId: selectedQuestionId,
          optionId: selectedOption.option_id, // Pass the option ID to update
          data: {
            option_text: updatedOptionData.option_text, // Updated option text
            is_correct: updatedOptionData.is_correct, // Updated correctness
          },
        })
      );
      setShowOptionModal(false); // Close the modal after submission
      fetchMasterOptions(); // Refetch questions to show updated options
      toast.success("Option updated successfully!");
    } catch (error) {
      console.error("Error updating option", error);
      toast.error("Failed to update the option.");
    }
  };



  return (
    <Container className="mt-1">
      <h2 className="text-center text-success mb-3" style={{ fontSize: "1.5rem" }}>
       Current Affairs / Master Options || Add Ques. <small className="h6"> See live updated here. </small>
      </h2>

      {isLoading ? (
        <div className="text-center mt-5">
          <div className="spinner-border text-success" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      ) : (
        <div className="d-flex flex-wrap">
          {allOptions.map((option) => (
            <Card key={option.BlogSlug} className="shadow position-relative" style={{ width: '100%', marginBottom: '1rem' }}>
              <Card.Body>
                {/* Option Details */}
                <div>
                  <Card.Title>
                    <div className="d-flex justify-content-between">
                      <div>
                      <MathTextRenderer text={option.title} />
                      </div>
                      <div className="d-flex align-items-center">
                        <Link to={`/current_affairs_master_options/${BlogSlug}`}>
                          <Button variant="outline-success" className="action-buttons m-1">
                            <FaMinus size={15} />
                          </Button>
                        </Link>
                        <Button
                          variant="outline-primary"
                          className="action-buttons m-1"
                          onClick={() => handleEdit(option)}
                        >
                          <FaEdit size={15} />
                        </Button>
                        <Button
                          variant="outline-danger"
                          className="action-buttons m-1"
                          onClick={() => handleDelete(option.slug)}
                        >
                          <FaTrashAlt size={15} />
                        </Button>
                      </div>
                    </div>
                  </Card.Title>
                  <Card.Text style={{ marginRight: '0.7rem', textAlign: 'justify' }}>
                    <MathTextRenderer text={option.option_content} />
                  </Card.Text>
                  <Card.Text>
                    <strong>Conditions:</strong> <MathTextRenderer text={option.conditions} />
                  </Card.Text>
                </div>

                {/* Related Questions */}
                {option.related_questions.length > 0 ? (
                  <div className="mt-2">
                    <h5>Related Questions:</h5>
                    {option.related_questions.map((question) => (
                      <div key={question.question_id} className="mt-1">
                        <Card.Title>
                          <span style={{ fontSize: '0.7rem' }}>
                            Subject: {question.subject_name}, Topic: {question.topic_name}, Sub Topic: {question.sub_topic_name}
                          </span>
                        </Card.Title>
                        <Card.Text style={{ marginRight: '0.7rem', textAlign: 'justify' }}>
                          ({question.question_id}) <MathTextRenderer text={question.content} />
                          <Button variant="outline-success" className="action-buttons m-1"
                            onClick={() => handleEditQuestion(question)} // Pass the entire question object here if needed
                          >
                            <FaEdit size={15} />
                          </Button>
                          <Button variant="outline-danger" className="action-buttons m-1 "
                            onClick={() => handleDeleteQuestion(question.question_id)} // Pass question_id along with option_id
                          >
                            <FaTrashAlt size={15} />
                          </Button>
                        </Card.Text>

                        {/* Options under the question */}
                        {question.options && question.options.length > 0 ? (
                          <ol style={{ listStyleType: 'decimal', padding: '0 1.5rem' }}>
                            {question.options.map((option) => (
                              <li key={option.option_id} style={{ marginBottom: '0.5rem' }}>
                                <div
                                  style={{
                                    color: option.is_correct ? '#198754' : '#dc3545',
                                    fontWeight: 'bold',
                                  }}
                                >
                                  <div className="option-text">
                                    <MathTextRenderer text={option.option_text} />
                                  </div>
                                  {/* Option Image - on next line */}
                                  {option.attachments && (
                                    <div className="option-image mt-3">
                                      <img
                                        src={`${import.meta.env.VITE_BASE_URL}/${option.attachments}`}
                                        alt="Option attachment"
                                        className="img-fluid rounded-3 shadow-sm"
                                        style={{
                                          maxWidth: "250px",
                                          height: "auto",
                                          border: "1px solid #e9ecef"
                                        }}
                                      />
                                    </div>
                                  )}
                                </div>
                                <Button
                                  variant="outline-primary"
                                  size="sm"
                                  onClick={() => handleEditOption(option, question.question_id)} // Pass question_id along with option_id
                                  className="ms-2"
                                >
                                  <FaEdit size={12} />
                                </Button>
                                <Button
                                  variant="outline-danger"
                                  size="sm"
                                  onClick={() => handleDeleteOption(option.option_id, question.question_id)} // Pass question_id and option_id
                                  className="ms-2"
                                >
                                  <FaTrashAlt size={12} />
                                </Button>
                              </li>
                            ))}
                          </ol>
                        ) : (
                          <p className="text-info">No options available for this question.</p>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted mt-3">No related questions available for this option.</p>
                )}
              </Card.Body>
            </Card>
          ))}
        </div>
      )}

      {/* Modal for Editing Question*/}
      {showQuestionModal && (
        <Modal show={showQuestionModal} onHide={() => setShowQuestionModal(false)} centered>
          <Modal.Header closeButton>
            <Modal.Title>Edit Question</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form onSubmit={handleSubmitQuestion}>
              <Form.Group controlId="content">
                <RichTextEditor
                  label="Question Content"
                  name="content"
                  value={updatedQuestionData.content}
                  onChange={handleQuestionInputChange}
                  placeholder="Enter question content"
                  rows={3}
                  required
                />
              </Form.Group>

              {/* No options section, just the question content */}

              <Button
                variant="outline-primary"
                type="submit"
                className="mt-3 w-100"
              >
                Save Changes
              </Button>
            </Form>
          </Modal.Body>
          <Toaster />
        </Modal>
      )}

      {/* Modal for Editing Option */}
      {showOptionModal && (
        <Modal show={showOptionModal} onHide={() => setShowOptionModal(false)} centered>
          <Modal.Header closeButton>
            <Modal.Title>Edit Option</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form onSubmit={handleSubmitOption}>
              <Form.Group controlId="optionText">
                <Form.Label>Option Text</Form.Label>
                <Form.Control
                  as="textarea"
                  name="option_text"
                  value={updatedOptionData.option_text}
                  onChange={(e) =>
                    setUpdatedOptionData({ ...updatedOptionData, option_text: e.target.value })
                  }
                  rows={3}
                  required
                />
              </Form.Group>
              <Form.Group controlId="isCorrect">
                <Form.Label>Is Correct</Form.Label>
                <Form.Check
                  type="checkbox"
                  name="is_correct"
                  checked={updatedOptionData.is_correct}
                  onChange={(e) =>
                    setUpdatedOptionData({
                      ...updatedOptionData,
                      is_correct: e.target.checked,
                    })
                  }
                />
              </Form.Group>
              <Button variant="outline-primary" type="submit" className="mt-3 w-100">
                Save Changes
              </Button>
            </Form>
          </Modal.Body>
        </Modal>
      )}


      {/* modal for editing master option title and condition  */}


      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit Option</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleSubmit}>
            <Form.Group controlId="formTitle">
              <RichTextEditor
                label="Title"
                name="title"
                value={updatedData.title}
                onChange={handleInputChange}
                placeholder="Enter title"
                rows={2}
                required
              />
            </Form.Group>
            <Form.Group controlId="formOptionContent" className="mt-3">
              <RichTextEditor
                label="Option Content"
                name="option_content"
                value={updatedData.option_content}
                onChange={handleInputChange}
                placeholder="Enter option content"
                rows={4}
                required
              />
            </Form.Group>
            <Form.Group controlId="formConditions" className="mt-3">
              <RichTextEditor
                label="Conditions"
                name="conditions"
                value={updatedData.conditions}
                onChange={handleInputChange}
                placeholder="Enter conditions"
                rows={2}
                required
              />
            </Form.Group>
            <Button variant="success" type="submit" className="mt-3">
              Save Changes
            </Button>
          </Form>
        </Modal.Body>
      </Modal>
    </Container>
  );
}
