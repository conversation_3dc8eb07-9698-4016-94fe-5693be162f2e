#!/usr/bin/env python3
"""
Test Immediate Membership Expiry Notifications
This script tests all the immediate notification methods we created
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def test_django_signals():
    """Test Django signals by updating a membership"""
    print("🔔 Testing Django Signals (Immediate Notifications)")
    print("=" * 60)
    
    try:
        from membership.models import Membership
        from django.utils import timezone
        
        today = timezone.now().date()
        
        # Find a membership to test with
        membership = Membership.objects.first()
        if not membership:
            print("❌ No memberships found to test with")
            return False
        
        print(f"📋 Testing with membership: {membership.librarian.user.username}")
        print(f"📅 Current expiry date: {membership.expiry_date}")
        
        # Save the original expiry date
        original_expiry = membership.expiry_date
        
        # Test 1: Set expiry to today (should trigger immediate notification)
        print(f"\n🧪 Test 1: Setting expiry to today ({today})")
        membership.expiry_date = today
        membership.save()  # This should trigger the signal
        print("✅ Membership saved - signal should have triggered notification!")
        
        # Test 2: Set expiry to tomorrow
        tomorrow = today + timedelta(days=1)
        print(f"\n🧪 Test 2: Setting expiry to tomorrow ({tomorrow})")
        membership.expiry_date = tomorrow
        membership.save()  # This should trigger the signal
        print("✅ Membership saved - signal should have triggered notification!")
        
        # Restore original expiry date
        print(f"\n🔄 Restoring original expiry date: {original_expiry}")
        membership.expiry_date = original_expiry
        membership.save()
        
        return True
        
    except Exception as e:
        print(f"❌ Django signals test failed: {e}")
        return False


def test_admin_actions():
    """Test admin actions programmatically"""
    print("\n🏛️ Testing Admin Actions")
    print("=" * 40)
    
    try:
        from membership.admin import test_membership_expiry_notification
        from membership.models import Membership
        from django.http import HttpRequest
        from django.contrib.messages.storage.fallback import FallbackStorage
        
        # Create a mock request
        request = HttpRequest()
        request.session = {}
        request._messages = FallbackStorage(request)
        
        # Get memberships to test
        memberships = Membership.objects.all()[:2]  # Test with first 2 memberships
        
        if not memberships:
            print("❌ No memberships found to test with")
            return False
        
        print(f"📋 Testing with {len(memberships)} memberships")
        
        # Call the admin action
        test_membership_expiry_notification(None, request, memberships)
        
        # Check messages
        messages = list(request._messages)
        for message in messages:
            print(f"📨 Admin message: {message}")
        
        print("✅ Admin action test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Admin actions test failed: {e}")
        return False


def test_date_change_detection():
    """Test date change detection system"""
    print("\n📅 Testing Date Change Detection")
    print("=" * 40)
    
    try:
        from librarian.tasks import check_date_change_and_notify
        from django.core.cache import cache
        
        # Clear the cache to simulate first run
        cache.delete('membership_expiry_last_check_date')
        
        print("🧪 Running date change check (first time)")
        result = check_date_change_and_notify()
        
        print(f"📊 Result: {result}")
        
        if result['success']:
            print("✅ Date change detection working!")
            if result.get('date_changed'):
                print(f"📅 Date change detected: {result.get('notifications_sent', 0)} notifications sent")
            else:
                print("ℹ️ No date change (expected on subsequent runs)")
        else:
            print(f"❌ Date change detection failed: {result.get('error')}")
            return False
        
        # Run again to test no date change
        print("\n🧪 Running date change check (second time)")
        result2 = check_date_change_and_notify()
        print(f"📊 Result: {result2}")
        
        return True
        
    except Exception as e:
        print(f"❌ Date change detection test failed: {e}")
        return False


def test_manual_command():
    """Test the manual membership expiry command"""
    print("\n⚡ Testing Manual Command")
    print("=" * 30)
    
    try:
        from django.core.management import call_command
        from io import StringIO
        
        # Capture command output
        out = StringIO()
        call_command('check_membership_expiry', '--verbose', stdout=out)
        
        output = out.getvalue()
        print("📋 Command output:")
        print(output)
        
        if "SUCCESS" in output or "notifications" in output.lower():
            print("✅ Manual command test completed!")
            return True
        else:
            print("⚠️ Manual command completed but no clear success indicator")
            return True
        
    except Exception as e:
        print(f"❌ Manual command test failed: {e}")
        return False


def show_usage_instructions():
    """Show usage instructions for all notification methods"""
    print("\n" + "=" * 80)
    print("📚 USAGE INSTRUCTIONS - HOW TO GET IMMEDIATE NOTIFICATIONS")
    print("=" * 80)
    
    print("\n🔔 METHOD 1: Django Admin (Automatic Signals)")
    print("   1. Go to: http://localhost:8000/admin/membership/membership/")
    print("   2. Click on any membership")
    print("   3. Change the 'Expiry date' field")
    print("   4. Click 'Save'")
    print("   5. 🎉 Notification will be sent immediately!")
    
    print("\n🏛️ METHOD 2: Django Admin Actions")
    print("   1. Go to: http://localhost:8000/admin/membership/membership/")
    print("   2. Select one or more memberships")
    print("   3. Choose action: '🧪 Test Expiry Notifications'")
    print("   4. Click 'Go'")
    print("   5. 🎉 Test notifications will be sent immediately!")
    
    print("\n📅 METHOD 3: Quick Test Actions")
    print("   1. Go to: http://localhost:8000/admin/membership/membership/")
    print("   2. Select memberships")
    print("   3. Choose: '📅 Set Expiry to Today (Test)' or '📅 Set Expiry to Tomorrow (Test)'")
    print("   4. Click 'Go'")
    print("   5. 🎉 Automatic notifications will be triggered!")
    
    print("\n⚡ METHOD 4: Manual Commands")
    print("   • Check all periods: python manage.py check_membership_expiry --verbose")
    print("   • Check specific: python manage.py check_membership_expiry --days 0")
    print("   • Real-time monitor: python manage.py start_membership_monitor")
    
    print("\n🔄 METHOD 5: Real-time Date Change Monitor")
    print("   • Start monitor: python manage.py start_membership_monitor")
    print("   • Runs every minute and detects date changes")
    print("   • Automatically triggers notifications when date changes")
    
    print("\n⏰ METHOD 6: Cron Job (Daily Automatic)")
    print("   • Add to crontab: 0 9 * * * cd /path/to/project && python manage.py check_membership_expiry")
    print("   • Runs daily at 9 AM automatically")
    
    print("\n🎯 RECOMMENDED WORKFLOW:")
    print("   1. 🧪 For testing: Use Django admin actions")
    print("   2. 🔔 For immediate: Django signals (automatic when saving)")
    print("   3. ⏰ For production: Set up cron job for daily checks")
    print("   4. 🔄 For real-time: Use the monitor command")


def main():
    print("🧪 IMMEDIATE MEMBERSHIP EXPIRY NOTIFICATION TESTS")
    print("=" * 80)
    print("Testing all methods for immediate notifications when membership expires")
    
    # Run all tests
    tests = [
        ("Django Signals", test_django_signals),
        ("Admin Actions", test_admin_actions),
        ("Date Change Detection", test_date_change_detection),
        ("Manual Command", test_manual_command),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 80)
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n🎯 Overall: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        print("🎉 ALL TESTS PASSED! Immediate notifications are working!")
    elif passed_count > 0:
        print("⚠️ Some tests passed. Partial functionality available.")
    else:
        print("❌ All tests failed. Please check the setup.")
    
    # Show usage instructions
    show_usage_instructions()

if __name__ == "__main__":
    main()
