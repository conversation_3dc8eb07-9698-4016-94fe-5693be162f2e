/**
 * Utility functions for authentication
 *
 * Note: All token validation functions include a 10-minute safety buffer by default
 * to account for clock synchronization differences between client and server.
 * This means tokens are considered expired 10 minutes before their actual expiration time.
 */

// Default safety buffer in minutes to account for clock differences
export const DEFAULT_SAFETY_BUFFER_MINUTES = 10;

/**
 * Check if a JWT token is expired
 * @param {string} token - The JWT token to check
 * @param {number} safetyBufferMinutes - Safety buffer in minutes to account for clock differences (default: 10)
 * @returns {boolean} - True if token is valid/not expired, false if expired
 */
export const isTokenValid = (token, safetyBufferMinutes = DEFAULT_SAFETY_BUFFER_MINUTES) => {
  if (!token) return false;

  try {
    // Check if it's a JWT token (has 3 parts separated by dots)
    const parts = token.split('.');
    if (parts.length !== 3) {
      return true; // Not a JWT, let server validate
    }

    // Decode the payload to check expiration
    const payload = JSON.parse(atob(parts[1]));
    const currentTime = Date.now() / 1000;

    // Add safety buffer to account for clock differences between client and server
    const safetyBufferSeconds = safetyBufferMinutes * 60;
    const adjustedCurrentTime = currentTime + safetyBufferSeconds;

    // If token has expired (considering safety buffer)
    if (payload.exp && payload.exp < adjustedCurrentTime) {
      return false;
    }

    return true;
  } catch (error) {
    // If token can't be decoded, let server validate
    console.warn('Token validation error:', error);
    return true;
  }
};

/**
 * Get token expiration time
 * @param {string} token - The JWT token
 * @returns {Date|null} - Expiration date or null if not a JWT or no expiration
 */
export const getTokenExpiration = (token) => {
  if (!token) return null;
  
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = JSON.parse(atob(parts[1]));
    if (payload.exp) {
      return new Date(payload.exp * 1000);
    }
    
    return null;
  } catch (error) {
    console.warn('Error getting token expiration:', error);
    return null;
  }
};

/**
 * Get time until token expires
 * @param {string} token - The JWT token
 * @param {number} safetyBufferMinutes - Safety buffer in minutes to account for clock differences (default: 10)
 * @returns {number} - Milliseconds until expiration (considering safety buffer), or -1 if expired/invalid
 */
export const getTimeUntilExpiration = (token, safetyBufferMinutes = DEFAULT_SAFETY_BUFFER_MINUTES) => {
  const expiration = getTokenExpiration(token);
  if (!expiration) return -1;

  const now = new Date();
  const safetyBufferMs = safetyBufferMinutes * 60 * 1000; // Convert to milliseconds
  const adjustedExpiration = expiration.getTime() - safetyBufferMs;
  const timeUntilExpiration = adjustedExpiration - now.getTime();

  return timeUntilExpiration > 0 ? timeUntilExpiration : -1;
};

/**
 * Check if user should be warned about token expiration
 * @param {string} token - The JWT token
 * @param {number} warningThresholdMinutes - Minutes before expiration to show warning (default: 5)
 * @param {number} safetyBufferMinutes - Safety buffer in minutes to account for clock differences (default: 10)
 * @returns {boolean} - True if warning should be shown
 */
export const shouldWarnAboutExpiration = (token, warningThresholdMinutes = 5, safetyBufferMinutes = DEFAULT_SAFETY_BUFFER_MINUTES) => {
  const timeUntilExpiration = getTimeUntilExpiration(token, safetyBufferMinutes);
  if (timeUntilExpiration === -1) return false;

  const warningThreshold = warningThresholdMinutes * 60 * 1000; // Convert to milliseconds
  return timeUntilExpiration <= warningThreshold && timeUntilExpiration > 0;
};

/**
 * Get the effective expiration time considering safety buffer
 * @param {string} token - The JWT token
 * @param {number} safetyBufferMinutes - Safety buffer in minutes (default: 10)
 * @returns {Date|null} - Effective expiration date (actual expiration - safety buffer) or null
 */
export const getEffectiveExpiration = (token, safetyBufferMinutes = DEFAULT_SAFETY_BUFFER_MINUTES) => {
  const actualExpiration = getTokenExpiration(token);
  if (!actualExpiration) return null;

  const safetyBufferMs = safetyBufferMinutes * 60 * 1000;
  return new Date(actualExpiration.getTime() - safetyBufferMs);
};

/**
 * Get human-readable time until token expires (considering safety buffer)
 * @param {string} token - The JWT token
 * @param {number} safetyBufferMinutes - Safety buffer in minutes (default: 10)
 * @returns {string} - Human-readable time until expiration or "Expired"
 */
export const getTimeUntilExpirationString = (token, safetyBufferMinutes = DEFAULT_SAFETY_BUFFER_MINUTES) => {
  const timeUntilExpiration = getTimeUntilExpiration(token, safetyBufferMinutes);

  if (timeUntilExpiration === -1) {
    return 'Expired';
  }

  const minutes = Math.floor(timeUntilExpiration / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days} day${days > 1 ? 's' : ''} ${hours % 24} hour${(hours % 24) !== 1 ? 's' : ''}`;
  } else if (hours > 0) {
    return `${hours} hour${hours > 1 ? 's' : ''} ${minutes % 60} minute${(minutes % 60) !== 1 ? 's' : ''}`;
  } else {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
};
