"""
Views for notification management and API endpoints
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.models import User
from django.shortcuts import render, get_object_or_404
from django.contrib import messages
from django.core.paginator import Paginator
from .models import NotificationHistory, NotificationTemplate, UserNotificationPreference
from .notification_service import notification_service
import json
import logging

logger = logging.getLogger(__name__)


def is_staff_or_librarian(user):
    """Check if user is staff or librarian"""
    return user.is_staff or hasattr(user, 'librarian_param')


@csrf_exempt
@require_http_methods(["POST"])
@login_required
@user_passes_test(is_staff_or_librarian)
def send_notification_api(request):
    """
    API endpoint for sending notifications
    
    POST /notifications/send/
    {
        "event_type": "announcement",
        "recipient_id": 123,
        "context_data": {
            "announcement_title": "Test",
            "announcement_message": "This is a test"
        }
    }
    """
    try:
        data = json.loads(request.body)
        event_type = data.get('event_type')
        recipient_id = data.get('recipient_id')
        context_data = data.get('context_data', {})
        force_send = data.get('force_send', False)
        
        if not event_type or not recipient_id:
            return JsonResponse({
                'success': False,
                'error': 'event_type and recipient_id are required'
            }, status=400)
        
        # Get recipient
        try:
            recipient = User.objects.get(id=recipient_id)
        except User.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Recipient not found'
            }, status=404)
        
        # Send notification
        result = notification_service.send_notification(
            event_type, recipient, force_send=force_send, **context_data
        )
        
        if result:
            return JsonResponse({
                'success': True,
                'notification_id': result.id,
                'status': result.status,
                'message': 'Notification sent successfully'
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'Failed to send notification'
            }, status=500)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        logger.error(f"Error in send_notification_api: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'Internal server error'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
@login_required
@user_passes_test(is_staff_or_librarian)
def send_bulk_notification_api(request):
    """
    API endpoint for sending bulk notifications
    
    POST /notifications/send-bulk/
    {
        "event_type": "announcement",
        "recipient_ids": [123, 456, 789],
        "context_data": {
            "announcement_title": "Test",
            "announcement_message": "This is a test"
        }
    }
    """
    try:
        data = json.loads(request.body)
        event_type = data.get('event_type')
        recipient_ids = data.get('recipient_ids', [])
        context_data = data.get('context_data', {})
        force_send = data.get('force_send', False)
        
        if not event_type or not recipient_ids:
            return JsonResponse({
                'success': False,
                'error': 'event_type and recipient_ids are required'
            }, status=400)
        
        # Get recipients
        recipients = User.objects.filter(id__in=recipient_ids)
        if not recipients.exists():
            return JsonResponse({
                'success': False,
                'error': 'No valid recipients found'
            }, status=404)
        
        # Send notifications
        results = notification_service.send_bulk_notification(
            event_type, list(recipients), context_data, force_send
        )
        
        successful = len([r for r in results if r and r.status != 'failed'])
        
        return JsonResponse({
            'success': True,
            'total_sent': len(results),
            'successful': successful,
            'failed': len(results) - successful,
            'message': f'Sent {successful} out of {len(recipient_ids)} notifications'
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        logger.error(f"Error in send_bulk_notification_api: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'Internal server error'
        }, status=500)


@login_required
def notification_history(request):
    """View for displaying notification history"""
    # Filter notifications based on user role
    if request.user.is_staff:
        # Staff can see all notifications
        notifications = NotificationHistory.objects.all()
    elif hasattr(request.user, 'librarian_param'):
        # Librarians can see notifications for their students
        notifications = NotificationHistory.objects.filter(
            recipient__studentdata__librarian=request.user.librarian_param
        )
    else:
        # Regular users can only see their own notifications
        notifications = NotificationHistory.objects.filter(recipient=request.user)
    
    # Apply filters
    status_filter = request.GET.get('status')
    event_type_filter = request.GET.get('event_type')
    
    if status_filter:
        notifications = notifications.filter(status=status_filter)
    
    if event_type_filter:
        notifications = notifications.filter(template__event_type=event_type_filter)
    
    # Order by most recent
    notifications = notifications.select_related(
        'template', 'recipient'
    ).order_by('-sent_at')
    
    # Pagination
    paginator = Paginator(notifications, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    status_choices = NotificationHistory.STATUS_CHOICES
    event_type_choices = NotificationTemplate.EVENT_TYPE_CHOICES
    
    context = {
        'page_obj': page_obj,
        'status_choices': status_choices,
        'event_type_choices': event_type_choices,
        'current_status': status_filter,
        'current_event_type': event_type_filter,
    }
    
    return render(request, 'librarian/notification_history.html', context)


@login_required
def notification_preferences(request):
    """View for managing notification preferences"""
    preferences, created = UserNotificationPreference.objects.get_or_create(
        user=request.user
    )
    
    if request.method == 'POST':
        # Update preferences
        preferences.email_notifications = request.POST.get('email_notifications') == 'on'
        preferences.push_notifications = request.POST.get('push_notifications') == 'on'
        preferences.sms_notifications = request.POST.get('sms_notifications') == 'on'
        
        quiet_hours_start = request.POST.get('quiet_hours_start')
        quiet_hours_end = request.POST.get('quiet_hours_end')
        
        if quiet_hours_start:
            preferences.quiet_hours_start = quiet_hours_start
        if quiet_hours_end:
            preferences.quiet_hours_end = quiet_hours_end
        
        # Update categories
        selected_categories = request.POST.getlist('categories')
        preferences.categories.set(selected_categories)
        
        preferences.save()
        messages.success(request, 'Notification preferences updated successfully!')
    
    from .models import NotificationCategory
    all_categories = NotificationCategory.objects.filter(is_active=True)
    
    context = {
        'preferences': preferences,
        'all_categories': all_categories,
    }
    
    return render(request, 'librarian/notification_preferences.html', context)


@require_http_methods(["GET"])
def notification_templates_api(request):
    """API endpoint to get available notification templates"""
    templates = NotificationTemplate.objects.filter(is_active=True).select_related('category')
    
    data = []
    for template in templates:
        data.append({
            'event_type': template.event_type,
            'event_type_display': template.get_event_type_display(),
            'category': template.category.name,
            'title_template': template.title_template,
            'body_template': template.body_template,
            'priority': template.priority,
            'action_url': template.action_url,
        })
    
    return JsonResponse({
        'success': True,
        'templates': data
    })


@require_http_methods(["GET"])
@login_required
def notification_status_api(request, notification_id):
    """API endpoint to get notification status"""
    try:
        notification = get_object_or_404(NotificationHistory, id=notification_id)
        
        # Check permissions
        if not (request.user.is_staff or 
                notification.recipient == request.user or
                (hasattr(request.user, 'librarian_param') and 
                 hasattr(notification.recipient, 'studentdata') and
                 notification.recipient.studentdata.librarian == request.user.librarian_param)):
            return JsonResponse({
                'success': False,
                'error': 'Permission denied'
            }, status=403)
        
        return JsonResponse({
            'success': True,
            'notification': {
                'id': notification.id,
                'title': notification.title,
                'body': notification.body,
                'status': notification.status,
                'sent_at': notification.sent_at.isoformat(),
                'delivered_at': notification.delivered_at.isoformat() if notification.delivered_at else None,
                'clicked_at': notification.clicked_at.isoformat() if notification.clicked_at else None,
                'error_message': notification.error_message,
            }
        })
        
    except Exception as e:
        logger.error(f"Error in notification_status_api: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'Internal server error'
        }, status=500)
