#!/usr/bin/env python3
"""
URL Routing Testing Script for Course-Related Django Components
This script performs comprehensive testing of all URL patterns and routing.
"""

import os
import sys
import django
import traceback

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Override ALLOWED_HOSTS for testing
from django.conf import settings
if 'testserver' not in settings.ALLOWED_HOSTS:
    settings.ALLOWED_HOSTS.append('testserver')

# Import after Django setup
from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse, resolve
from rest_framework.test import APIClient
from questions.models import (
    Course, SubCourse, Tier, Paper, Section, Module, Subject, Topic, SubTopic,
    MasterQuestion, MasterOption, Question, Option, PreviousYearQuestion
)
from contributor.models import ContributorProfile
from questions import views


class TestResults:
    """Class to track test results."""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"✅ PASS: {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"❌ FAIL: {test_name} - {error}")
    
    def summary(self):
        total = self.passed + self.failed
        print(f"\n{'='*60}")
        print(f"URL ROUTING TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success Rate: {(self.passed/total*100):.1f}%" if total > 0 else "No tests run")
        
        if self.errors:
            print(f"\n{'='*60}")
            print(f"FAILED TESTS:")
            print(f"{'='*60}")
            for error in self.errors:
                print(f"- {error}")


def test_course_urls():
    """Test Course-related URL patterns."""
    results = TestResults()
    
    try:
        # Test 1: Course list URL
        try:
            url = reverse('course-list')
            if url == '/api/questions/courses/':
                results.add_pass("Course list URL reverse")
            else:
                results.add_fail("Course list URL reverse", f"Expected '/api/questions/courses/', got '{url}'")
        except Exception as e:
            results.add_fail("Course list URL reverse", f"Exception: {str(e)}")
        
        # Test 2: Course detail URL
        try:
            url = reverse('course-detail', kwargs={'slug': 'test-course'})
            if url == '/api/questions/courses/test-course/':
                results.add_pass("Course detail URL reverse")
            else:
                results.add_fail("Course detail URL reverse", f"Expected '/api/questions/courses/test-course/', got '{url}'")
        except Exception as e:
            results.add_fail("Course detail URL reverse", f"Exception: {str(e)}")
        
        # Test 3: Course list URL resolution
        try:
            resolver = resolve('/api/questions/courses/')
            if resolver.view_name == 'course-list':
                results.add_pass("Course list URL resolution")
            else:
                results.add_fail("Course list URL resolution", f"Expected 'course-list', got '{resolver.view_name}'")
        except Exception as e:
            results.add_fail("Course list URL resolution", f"Exception: {str(e)}")
        
        # Test 4: Course detail URL resolution
        try:
            resolver = resolve('/api/questions/courses/test-course/')
            if resolver.view_name == 'course-detail' and resolver.kwargs['slug'] == 'test-course':
                results.add_pass("Course detail URL resolution")
            else:
                results.add_fail("Course detail URL resolution", f"View: {resolver.view_name}, kwargs: {resolver.kwargs}")
        except Exception as e:
            results.add_fail("Course detail URL resolution", f"Exception: {str(e)}")
        
        # Test 5: Courses with subcourses URL
        try:
            url = reverse('courses-with-sub-courses')
            if url == '/api/questions/courses-with-sub-courses/':
                results.add_pass("Courses with subcourses URL reverse")
            else:
                results.add_fail("Courses with subcourses URL reverse", f"Expected '/api/questions/courses-with-sub-courses/', got '{url}'")
        except Exception as e:
            results.add_fail("Courses with subcourses URL reverse", f"Exception: {str(e)}")
        
    except Exception as e:
        results.add_fail("Course URLs tests", f"Exception: {str(e)}")
    
    return results


def test_subject_urls():
    """Test Subject-related URL patterns."""
    results = TestResults()
    
    try:
        # Test 1: Subject list URL
        try:
            url = reverse('subject-list-create')
            if url == '/api/questions/subjects/':
                results.add_pass("Subject list URL reverse")
            else:
                results.add_fail("Subject list URL reverse", f"Expected '/api/questions/subjects/', got '{url}'")
        except Exception as e:
            results.add_fail("Subject list URL reverse", f"Exception: {str(e)}")
        
        # Test 2: Subject detail URL
        try:
            url = reverse('subject-detail', kwargs={'slug': 'mathematics'})
            if url == '/api/questions/subjects/mathematics/':
                results.add_pass("Subject detail URL reverse")
            else:
                results.add_fail("Subject detail URL reverse", f"Expected '/api/questions/subjects/mathematics/', got '{url}'")
        except Exception as e:
            results.add_fail("Subject detail URL reverse", f"Exception: {str(e)}")
        
        # Test 3: Subject list URL resolution
        try:
            resolver = resolve('/api/questions/subjects/')
            if resolver.view_name == 'subject-list-create':
                results.add_pass("Subject list URL resolution")
            else:
                results.add_fail("Subject list URL resolution", f"Expected 'subject-list-create', got '{resolver.view_name}'")
        except Exception as e:
            results.add_fail("Subject list URL resolution", f"Exception: {str(e)}")
        
        # Test 4: Subject detail URL resolution
        try:
            resolver = resolve('/api/questions/subjects/mathematics/')
            if resolver.view_name == 'subject-detail' and resolver.kwargs['slug'] == 'mathematics':
                results.add_pass("Subject detail URL resolution")
            else:
                results.add_fail("Subject detail URL resolution", f"View: {resolver.view_name}, kwargs: {resolver.kwargs}")
        except Exception as e:
            results.add_fail("Subject detail URL resolution", f"Exception: {str(e)}")
        
    except Exception as e:
        results.add_fail("Subject URLs tests", f"Exception: {str(e)}")
    
    return results


def test_topic_urls():
    """Test Topic-related URL patterns."""
    results = TestResults()
    
    try:
        # Test 1: Topic list URL
        try:
            url = reverse('topic-list-create')
            if url == '/api/questions/topics/':
                results.add_pass("Topic list URL reverse")
            else:
                results.add_fail("Topic list URL reverse", f"Expected '/api/questions/topics/', got '{url}'")
        except Exception as e:
            results.add_fail("Topic list URL reverse", f"Exception: {str(e)}")
        
        # Test 2: Topic detail URL
        try:
            url = reverse('topic-detail', kwargs={'slug': 'algebra'})
            if url == '/api/questions/topics/algebra/':
                results.add_pass("Topic detail URL reverse")
            else:
                results.add_fail("Topic detail URL reverse", f"Expected '/api/questions/topics/algebra/', got '{url}'")
        except Exception as e:
            results.add_fail("Topic detail URL reverse", f"Exception: {str(e)}")
        
        # Test 3: SubTopic list URL
        try:
            url = reverse('subtopic-list-create')
            if url == '/api/questions/subtopics/':
                results.add_pass("SubTopic list URL reverse")
            else:
                results.add_fail("SubTopic list URL reverse", f"Expected '/api/questions/subtopics/', got '{url}'")
        except Exception as e:
            results.add_fail("SubTopic list URL reverse", f"Exception: {str(e)}")
        
        # Test 4: SubTopic detail URL
        try:
            url = reverse('subtopic-detail', kwargs={'slug': 'linear-equations'})
            if url == '/api/questions/subtopics/linear-equations/':
                results.add_pass("SubTopic detail URL reverse")
            else:
                results.add_fail("SubTopic detail URL reverse", f"Expected '/api/questions/subtopics/linear-equations/', got '{url}'")
        except Exception as e:
            results.add_fail("SubTopic detail URL reverse", f"Exception: {str(e)}")
        
    except Exception as e:
        results.add_fail("Topic URLs tests", f"Exception: {str(e)}")
    
    return results


def test_tier_paper_urls():
    """Test Tier and Paper-related URL patterns."""
    results = TestResults()
    
    try:
        # Test 1: Tier list URL
        try:
            url = reverse('tier-list')
            if url == '/api/questions/tiers/':
                results.add_pass("Tier list URL reverse")
            else:
                results.add_fail("Tier list URL reverse", f"Expected '/api/questions/tiers/', got '{url}'")
        except Exception as e:
            results.add_fail("Tier list URL reverse", f"Exception: {str(e)}")
        
        # Test 2: Tier detail URL
        try:
            url = reverse('tier-detail', kwargs={'slug': 'tier-1'})
            if url == '/api/questions/tiers/tier-1/':
                results.add_pass("Tier detail URL reverse")
            else:
                results.add_fail("Tier detail URL reverse", f"Expected '/api/questions/tiers/tier-1/', got '{url}'")
        except Exception as e:
            results.add_fail("Tier detail URL reverse", f"Exception: {str(e)}")
        
        # Test 3: Paper list URL
        try:
            url = reverse('paper-list')
            if url == '/api/questions/papers/':
                results.add_pass("Paper list URL reverse")
            else:
                results.add_fail("Paper list URL reverse", f"Expected '/api/questions/papers/', got '{url}'")
        except Exception as e:
            results.add_fail("Paper list URL reverse", f"Exception: {str(e)}")
        
        # Test 4: Paper detail URL
        try:
            url = reverse('paper-detail', kwargs={'slug': 'paper-1'})
            if url == '/api/questions/papers/paper-1/':
                results.add_pass("Paper detail URL reverse")
            else:
                results.add_fail("Paper detail URL reverse", f"Expected '/api/questions/papers/paper-1/', got '{url}'")
        except Exception as e:
            results.add_fail("Paper detail URL reverse", f"Exception: {str(e)}")
        
        # Test 5: Section list URL
        try:
            url = reverse('section-list')
            if url == '/api/questions/sections/':
                results.add_pass("Section list URL reverse")
            else:
                results.add_fail("Section list URL reverse", f"Expected '/api/questions/sections/', got '{url}'")
        except Exception as e:
            results.add_fail("Section list URL reverse", f"Exception: {str(e)}")
        
        # Test 6: Module list URL
        try:
            url = reverse('module-list')
            if url == '/api/questions/modules/':
                results.add_pass("Module list URL reverse")
            else:
                results.add_fail("Module list URL reverse", f"Expected '/api/questions/modules/', got '{url}'")
        except Exception as e:
            results.add_fail("Module list URL reverse", f"Exception: {str(e)}")
        
    except Exception as e:
        results.add_fail("Tier and Paper URLs tests", f"Exception: {str(e)}")
    
    return results


def test_question_urls():
    """Test Question-related URL patterns."""
    results = TestResults()
    
    try:
        # Test 1: Master question list URL
        try:
            url = reverse('master-question-list')
            if url == '/api/questions/master-questions/':
                results.add_pass("Master question list URL reverse")
            else:
                results.add_fail("Master question list URL reverse", f"Expected '/api/questions/master-questions/', got '{url}'")
        except Exception as e:
            results.add_fail("Master question list URL reverse", f"Exception: {str(e)}")
        
        # Test 2: Master question detail URL
        try:
            url = reverse('master-question-detail', kwargs={'slug': 'reading-comprehension'})
            if url == '/api/questions/master-questions/reading-comprehension/':
                results.add_pass("Master question detail URL reverse")
            else:
                results.add_fail("Master question detail URL reverse", f"Expected '/api/questions/master-questions/reading-comprehension/', got '{url}'")
        except Exception as e:
            results.add_fail("Master question detail URL reverse", f"Exception: {str(e)}")
        
        # Test 3: Master option list URL
        try:
            url = reverse('master-option-create')
            if url == '/api/questions/master-options/':
                results.add_pass("Master option list URL reverse")
            else:
                results.add_fail("Master option list URL reverse", f"Expected '/api/questions/master-options/', got '{url}'")
        except Exception as e:
            results.add_fail("Master option list URL reverse", f"Exception: {str(e)}")
        
        # Test 4: Previous year question list URL
        try:
            url = reverse('previous-year-question-list-create')
            if url == '/api/questions/previous-questions/':
                results.add_pass("Previous year question list URL reverse")
            else:
                results.add_fail("Previous year question list URL reverse", f"Expected '/api/questions/previous-questions/', got '{url}'")
        except Exception as e:
            results.add_fail("Previous year question list URL reverse", f"Exception: {str(e)}")
        
        # Test 5: Test series URL
        try:
            url = reverse('test-series-card')
            if url == '/api/questions/test-series-card/':
                results.add_pass("Test series URL reverse")
            else:
                results.add_fail("Test series URL reverse", f"Expected '/api/questions/test-series-card/', got '{url}'")
        except Exception as e:
            results.add_fail("Test series URL reverse", f"Exception: {str(e)}")
        
    except Exception as e:
        results.add_fail("Question URLs tests", f"Exception: {str(e)}")
    
    return results


def test_url_accessibility():
    """Test URL accessibility with actual HTTP requests."""
    results = TestResults()
    
    try:
        client = APIClient()
        
        # Test URLs that should be accessible without authentication
        public_urls = [
            '/api/questions/subjects/',
            '/api/questions/courses/',
            '/api/questions/test-series-card/',
        ]
        
        for url in public_urls:
            try:
                response = client.get(url)
                if response.status_code in [200, 201]:
                    results.add_pass(f"URL accessibility: {url}")
                else:
                    results.add_fail(f"URL accessibility: {url}", f"Status code: {response.status_code}")
            except Exception as e:
                results.add_fail(f"URL accessibility: {url}", f"Exception: {str(e)}")
        
        # Test URLs that require authentication
        import uuid
        unique_username = f'testuser_{uuid.uuid4().hex[:8]}'
        user = User.objects.create_user(username=unique_username, password='test123')
        contributor = ContributorProfile.objects.create(user=user, role='contributor')
        client.force_authenticate(user=user)
        
        auth_urls = [
            '/api/questions/master-questions/',
            '/api/questions/master-options/',
        ]
        
        for url in auth_urls:
            try:
                response = client.get(url)
                if response.status_code in [200, 201]:
                    results.add_pass(f"Authenticated URL accessibility: {url}")
                else:
                    results.add_fail(f"Authenticated URL accessibility: {url}", f"Status code: {response.status_code}")
            except Exception as e:
                results.add_fail(f"Authenticated URL accessibility: {url}", f"Exception: {str(e)}")
        
        # Cleanup
        contributor.delete()
        user.delete()
        
    except Exception as e:
        results.add_fail("URL accessibility tests", f"Exception: {str(e)}")
    
    return results


def main():
    """Run all URL routing tests."""
    print("🚀 Starting Comprehensive URL Routing Tests")
    print("="*60)
    
    all_results = TestResults()
    
    # Run all test functions
    test_functions = [
        test_course_urls,
        test_subject_urls,
        test_topic_urls,
        test_tier_paper_urls,
        test_question_urls,
        test_url_accessibility
    ]
    
    for test_func in test_functions:
        print(f"\n📋 Running {test_func.__name__}...")
        try:
            result = test_func()
            all_results.passed += result.passed
            all_results.failed += result.failed
            all_results.errors.extend(result.errors)
        except Exception as e:
            all_results.add_fail(test_func.__name__, f"Test function failed: {str(e)}")
            print(f"💥 Error in {test_func.__name__}: {str(e)}")
            traceback.print_exc()
    
    # Print final summary
    all_results.summary()


if __name__ == '__main__':
    main()
