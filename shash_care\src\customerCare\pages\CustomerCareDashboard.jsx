import React, { useEffect, useState } from "react";
import { Card, Container, Row, Col, Button } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { getCustomerProfile } from "../../redux/slice/customerCareSlice.js";
import { Link, useNavigate } from "react-router-dom";
import { FaBook, FaQuestionCircle, FaComments, FaGlobeAmericas, FaClipboardList, FaBullhorn, FaBlogger, FaServer, FaSpider, FaAd, FaRegFileAlt , FaBell, FaChartLine, FaTags, FaGift, FaRupeeSign, FaCalendarAlt, FaCheckCircle, FaFilePdf } from 'react-icons/fa';
import { BiSolidBookContent } from "react-icons/bi";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { toast } from "react-hot-toast";
import { FaWalking } from 'react-icons/fa';

const CustomerCareDashboard = () => {
  const [loading, setLoading] = useState(true);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { access } = useSelector((state) => state.customerCare);
  const [customerCareData, setCustomerCareData] = useState({ });

  useEffect(() => {
    if (!access) {
      // toast.error("Please login first");
      navigate("/customer_care_login");
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await dispatch(getCustomerProfile());
        setCustomerCareData(response.payload);
      } catch (error) {
        console.error("Error fetching customer care questions", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData(); 
  }, [dispatch, access, navigate]); 

  const renderCard = (title, link, IconComponent) => (
    <Card className="text-center shadow-lg border-1 w-100 bg-white rounded">
      <Card.Body>
        <Card.Title>
          {loading ? (
            <Skeleton width={50} height={50} circle={true} />
          ) : (
            <IconComponent size={30} className="text-success" />
          )}
        </Card.Title>
        <Card.Title className="fs-5 fw-medium text-black" style={{ fontFamily: "serif" }}>
          {loading ? (
            <Skeleton width={150} height={25} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
          ) : (
            title
          )}
        </Card.Title>
        <Link to={link} style={{ textDecoration: "none" }}>
          <Button
            variant="outline-success"
            className="fw-medium"
            style={{ transition: "0.3s", fontFamily: "serif" }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = "#198754"; // Success background
              e.target.style.color = "white"; // White text
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = "transparent"; // Transparent background
              e.target.style.color = "#198754"; // Success text color
            }}
          >
            {loading ? (
              <Skeleton width={100} height={20} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
            ) : (
              `Go to ${title}`
            )}
          </Button>
        </Link>
      </Card.Body>
    </Card>
  );

  if (loading) {
    return (
      <Container className="my-4">
        <h3 className="text-success mb-4 text-center">Dashboard</h3>
        <Row className="g-2">
          {[...Array(5)].map((_, idx) => (
            <Col xs={12} md={3} key={idx}>
              <Skeleton height={200} width="100%" baseColor="#e6ffe6" highlightColor="#c4f7c4" />
            </Col>
          ))}
        </Row>
      </Container>
    );
  }

  return (
    <Container className="my-4">
      <h3 className="text-success mb-4 text-center">Dashboard</h3>
      <Row className="g-2">
        <Col xs={12} md={3}>
          {renderCard(
            "Normal Questions", "/NormalQuestions",
            FaBook
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Master Questions", "/MasterQuestions",
            FaQuestionCircle
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Master Options", "/MasterOptions",
            FaClipboardList
          )}
        </Col>
        {/* <Col xs={12} md={3}>
          {renderCard(
            "Prev. Year Ques.", "/previous_questions",
            FaComments
          )}
        </Col> */}
        <Col xs={12} md={3}>
          {renderCard(
            "blogs", "/Blogs",
            FaBlogger
          )}
        </Col>
        {/* <Col xs={12} md={3}>
          {renderCard(
            "Current Affairs", "/current_affairs",
            FaGlobeAmericas
          )}
        </Col> */}
        <Col xs={12} md={3}>
          {renderCard(
            "Tickets", "/tickets",
            FaBullhorn
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Packages", "/packages",
            FaServer 
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Signup Content", "/signup-content",
            BiSolidBookContent
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Student Error Logs", "/student-error-logs",
            FaSpider
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Banners", "/banner",
            FaAd 
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Notification Templates", "/notification-templates",
            FaRegFileAlt 
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "FCM Dashboard", "/device-dashboard",
            FaBell 
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Analytics Dashboard", "/analytics-dashboard",
            FaChartLine  
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Coupon Dashboard", "/coupon-dashboard",
            FaTags  
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Gift Card Dashboard", "/gift-card-dashboard",
            FaGift  
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Reward Dashboard", "/reward-dashboard",
            FaRupeeSign
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Events Dashboard", "/events-dashboard",
            FaCalendarAlt
          )}
        </Col>
        {/* <Col xs={12} md={3}>
          {renderCard(
            "Pop Up Dashboard", "/pop-up-dashboard",
            FaBullhorn
          )}
        </Col> */}
        <Col xs={12} md={3}>
          {renderCard(
            "Walk Around Dashboard", "/walk-arounf-dashboard",
            FaWalking
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "Popup Approval", "/popup-approval-dashboard",
            FaCheckCircle
          )}
        </Col>
        <Col xs={12} md={3}>
          {renderCard(
            "SOP Management", "/sop-dashboard",
            FaFilePdf
          )}
        </Col>
      </Row>
    </Container>
  );
};

export default CustomerCareDashboard;
