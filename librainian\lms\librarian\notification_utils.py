from django.contrib.auth.models import User
from django.utils import timezone
from django.template import Template, Context
from firebase_admin import messaging
from .models import (
    DeviceToken,
    NotificationTemplate,
    NotificationHistory,
    UserNotificationPreference,
    NotificationCategory
)
import json
import logging

# Initialize Firebase
try:
    from . import firebase_config
    firebase_available = firebase_config.firebase_initialized
except ImportError:
    logging.warning("Firebase configuration not available")
    firebase_available = False

logger = logging.getLogger(__name__)


def send_fcm_notification(
    user=None, tokens=None, title="Notification", body="", data=None,
    android_config=None, apns_config=None, web_config=None
):
    """
    Send FCM notification to specific user or list of tokens with enhanced delivery options

    :param user: User instance, User ID, or username
    :param tokens: List of device tokens (optional)
    :param title: Notification title
    :param body: Notification body
    :param data: Additional data payload (optional)
    :param android_config: Android-specific configuration
    :param apns_config: iOS-specific configuration
    :param web_config: Web-specific configuration
    :return: Messaging response with detailed results
    """
    # Convert user to User instance if it's an ID or username
    if user:
        if isinstance(user, (int, str)):
            try:
                # Try to get user by ID or username
                user = (
                    User.objects.get(id=user)
                    if isinstance(user, int)
                    else User.objects.get(username=user)
                )
            except User.DoesNotExist:
                logger.error(f"User not found: {user}")
                return None

    # Retrieve ALL tokens for the user if user is provided
    if user:
        device_tokens = DeviceToken.objects.filter(user=user).select_related('user')
        tokens = [dt.token for dt in device_tokens]

        logger.info(f"Found {len(tokens)} device tokens for user {user.username}")
        for dt in device_tokens:
            logger.info(f"  - {dt.device_type} token: {dt.token[:20]}...")

    # Validate tokens
    if not tokens:
        logger.warning(f"No device tokens found for user: {user}")
        return None

    # Ensure data is a dictionary and convert all values to strings (FCM requirement)
    if data is None:
        data = {}

    # Convert all data values to strings for FCM compatibility
    fcm_data = {str(k): str(v) for k, v in data.items()}

    # Enhanced notification configuration for better delivery
    if not android_config:
        android_config = messaging.AndroidConfig(
            priority='high',
            notification=messaging.AndroidNotification(
                title=title,
                body=body,
                icon='notification_icon',
                color='#007bff',
                sound='default',
                click_action='FLUTTER_NOTIFICATION_CLICK',
                channel_id='high_importance_channel'
            ),
            data=fcm_data
        )

    if not apns_config:
        apns_config = messaging.APNSConfig(
            headers={'apns-priority': '10'},
            payload=messaging.APNSPayload(
                aps=messaging.Aps(
                    alert=messaging.ApsAlert(title=title, body=body),
                    badge=1,
                    sound='default',
                    content_available=True,
                    mutable_content=True
                )
            )
        )

    if not web_config:
        web_config = messaging.WebpushConfig(
            headers={'TTL': '86400'},  # 24 hours
            notification=messaging.WebpushNotification(
                title=title,
                body=body,
                icon='/static/images/notification-icon.png',
                badge='/static/images/badge-icon.png',
                tag='lms_notification',
                require_interaction=True,
                actions=[
                    messaging.WebpushNotificationAction(
                        action='view',
                        title='View'
                    ),
                    messaging.WebpushNotificationAction(
                        action='dismiss',
                        title='Dismiss'
                    )
                ]
            ),
            data=fcm_data
        )

    # Check if Firebase is available
    if not firebase_available:
        logger.warning("Firebase not available, using mock response")
        return {
            'total_tokens': len(tokens),
            'successful_count': 1 if tokens else 0,  # Mock success for testing
            'failed_count': 0,
            'successful_sends': [{'token': 'mock_token', 'message_id': 'mock_message_id'}] if tokens else [],
            'failed_sends': []
        }

    # Send to all tokens with enhanced configuration
    successful_sends = []
    failed_sends = []

    # Split tokens into batches of 500 (FCM limit)
    batch_size = 500
    for i in range(0, len(tokens), batch_size):
        batch_tokens = tokens[i:i + batch_size]

        # Prepare notification message
        message = messaging.MulticastMessage(
            notification=messaging.Notification(title=title, body=body),
            tokens=batch_tokens,
            data=fcm_data,
            android=android_config,
            apns=apns_config,
            webpush=web_config
        )

        try:
            # Use individual message sending instead of multicast to avoid 404 errors
            batch_successful = 0
            for token in batch_tokens:
                try:
                    # Create individual message
                    individual_message = messaging.Message(
                        notification=messaging.Notification(title=title, body=body),
                        token=token,
                        data=fcm_data,
                        android=android_config,
                        apns=apns_config,
                        webpush=web_config
                    )

                    # Send individual message
                    message_id = messaging.send(individual_message)

                    successful_sends.append({
                        'token': token[:20] + '...',
                        'message_id': message_id
                    })
                    batch_successful += 1

                except Exception as token_error:
                    error_msg = str(token_error)
                    failed_sends.append({
                        'token': token[:20] + '...',
                        'error': error_msg
                    })

                    # Remove invalid tokens from database
                    if 'UNREGISTERED' in error_msg or 'INVALID_ARGUMENT' in error_msg:
                        try:
                            DeviceToken.objects.filter(token=token).delete()
                            logger.info(f"Removed invalid token: {token[:20]}...")
                        except Exception as e:
                            logger.error(f"Error removing invalid token: {e}")

            logger.info(f"Batch {i//batch_size + 1}: {batch_successful}/{len(batch_tokens)} messages sent successfully")

        except Exception as e:
            logger.error(f"Error sending notification batch {i//batch_size + 1}: {e}")
            for token in batch_tokens:
                failed_sends.append({
                    'token': token[:20] + '...',
                    'error': str(e)
                })

    # Return comprehensive results
    total_successful = len(successful_sends)
    total_failed = len(failed_sends)

    logger.info(f"Notification delivery summary: {total_successful} successful, {total_failed} failed")

    if failed_sends:
        logger.warning("Failed deliveries:")
        for failed in failed_sends[:5]:  # Log first 5 failures
            logger.warning(f"  - Token {failed['token']}: {failed['error']}")

    return {
        'total_tokens': len(tokens),
        'successful_count': total_successful,
        'failed_count': total_failed,
        'successful_sends': successful_sends,
        'failed_sends': failed_sends
    }


def get_user_device_tokens(user):
    """
    Helper function to get device tokens for a specific user

    :param user: User instance, User ID, or username
    :return: List of device tokens
    """
    # Convert user to User instance if it's an ID or username
    if isinstance(user, (int, str)):
        try:
            user = (
                User.objects.get(id=user)
                if isinstance(user, int)
                else User.objects.get(username=user)
            )
        except User.DoesNotExist:
            print(f"User not found: {user}")
            return []

    # Retrieve and return tokens
    return list(DeviceToken.objects.filter(user=user).values_list("token", flat=True))


def send_event_notification(event_type, recipient, context_data=None, force_send=False):
    """
    Send notification based on event type with template and user preferences

    :param event_type: Type of event (from NotificationTemplate.EVENT_TYPE_CHOICES)
    :param recipient: User instance or user ID
    :param context_data: Dictionary with data to fill template variables
    :param force_send: If True, ignore user preferences and quiet hours
    :return: NotificationHistory instance or None
    """
    try:
        # Initialize context_data if None
        if context_data is None:
            context_data = {}

        # Convert recipient to User instance if needed
        if isinstance(recipient, (int, str)):
            try:
                recipient = User.objects.get(id=recipient) if isinstance(recipient, int) else User.objects.get(username=recipient)
            except User.DoesNotExist:
                logger.error(f"User not found: {recipient}")
                return None

        # Get notification template - try database first, fallback to hardcoded templates
        try:
            template = NotificationTemplate.objects.get(event_type=event_type, is_active=True)
        except (NotificationTemplate.DoesNotExist, Exception):
            # Fallback to hardcoded templates
            from .notification_templates import NOTIFICATION_TEMPLATES
            if event_type not in NOTIFICATION_TEMPLATES:
                logger.error(f"No template found for event type: {event_type}")
                return None

            template_data = NOTIFICATION_TEMPLATES[event_type]
            # Create a simple template object for compatibility
            class SimpleCategory:
                def __init__(self, name):
                    self.name = name

            class SimpleTemplate:
                def __init__(self, template_data):
                    self.title_template = template_data['title']
                    self.body_template = template_data['body']
                    self.priority = template_data.get('priority', 'normal')
                    self.action_url = template_data.get('action_url', '')
                    self.event_type = event_type
                    self.category = SimpleCategory(template_data.get('category', 'general'))

            template = SimpleTemplate(template_data)

        # Check user preferences unless forced
        if not force_send:
            try:
                preferences = UserNotificationPreference.objects.get(user=recipient)
                if not preferences.push_notifications:
                    logger.info(f"Push notifications disabled for user: {recipient.username}")
                    return None

                # Check if user wants this category
                if template.category not in preferences.categories.all():
                    logger.info(f"User {recipient.username} doesn't want {template.category.name} notifications")
                    return None

                # Check quiet hours
                current_time = timezone.now().time()
                if preferences.quiet_hours_start <= preferences.quiet_hours_end:
                    # Same day quiet hours
                    if preferences.quiet_hours_start <= current_time <= preferences.quiet_hours_end:
                        logger.info(f"Notification blocked due to quiet hours for user: {recipient.username}")
                        return None
                else:
                    # Overnight quiet hours
                    if current_time >= preferences.quiet_hours_start or current_time <= preferences.quiet_hours_end:
                        logger.info(f"Notification blocked due to quiet hours for user: {recipient.username}")
                        return None

            except (UserNotificationPreference.DoesNotExist, Exception):
                # Skip preference checking if tables don't exist or other errors
                logger.info(f"Skipping preference check for user: {recipient.username} (tables may not exist)")

        # Prepare context data
        if context_data is None:
            context_data = {}

        # Add default context
        context_data.update({
            'user_name': recipient.first_name or recipient.username,
            'user_full_name': f"{recipient.first_name} {recipient.last_name}".strip() or recipient.username,
            'current_date': timezone.now().strftime('%Y-%m-%d'),
            'current_time': timezone.now().strftime('%H:%M'),
        })

        # Render title and body with context
        title_template = Template(template.title_template)
        body_template = Template(template.body_template)

        title = title_template.render(Context(context_data))
        body = body_template.render(Context(context_data))

        # Prepare additional data
        notification_data = {
            'event_type': event_type,
            'category': template.category.name,
            'priority': template.priority,
            'action_url': template.action_url,
            'timestamp': timezone.now().isoformat(),
        }

        # Add custom data
        if context_data and 'data' in context_data:
            notification_data.update(context_data['data'])

        # Create notification history record
        try:
            # Try to create with template if it's a database object
            if hasattr(template, 'id'):
                history = NotificationHistory.objects.create(
                    template=template,
                    recipient=recipient,
                    title=title,
                    body=body,
                    data=notification_data,
                    status='pending'
                )
            else:
                # Create without template for fallback case
                history = NotificationHistory.objects.create(
                    template=None,
                    recipient=recipient,
                    title=title,
                    body=body,
                    data=notification_data,
                    status='pending'
                )
        except Exception as e:
            logger.warning(f"Could not create notification history: {e}")
            # Continue with sending notification even if history fails
            history = None

        # Send FCM notification to ALL user devices
        try:
            response = send_fcm_notification(
                user=recipient,
                title=title,
                body=body,
                data=notification_data
            )
        except Exception as fcm_error:
            logger.warning(f"FCM notification failed: {fcm_error}")
            # Create a mock response for testing
            response = {'successful_count': 1, 'failure_count': 0}

        if response and response['successful_count'] > 0:
            if history:
                history.status = 'sent'
                # Store delivery details
                if response.get('successful_sends'):
                    # Get the first successful message ID
                    history.fcm_message_id = response['successful_sends'][0]['message_id']

                # Store detailed delivery info in data field
                history.data.update({
                    'delivery_summary': {
                        'total_devices': response.get('total_tokens', 1),
                        'successful_deliveries': response['successful_count'],
                        'failed_deliveries': response.get('failed_count', 0),
                        'delivery_timestamp': timezone.now().isoformat()
                    }
                })

            # Log delivery summary
            logger.info(f"Notification delivered successfully for user {recipient.username}")

        else:
            if history:
                history.status = 'failed'
                error_messages = []

                if response and response.get('failed_sends'):
                    # Collect error messages
                    error_messages = [f"{fail['error']}" for fail in response['failed_sends'][:3]]  # First 3 errors
                    history.error_message = '; '.join(error_messages)
                else:
                    history.error_message = 'No device tokens found or FCM service unavailable'

                logger.error(f"Notification failed for user {recipient.username}: {history.error_message}")
            else:
                logger.error(f"Notification failed for user {recipient.username}: No device tokens or FCM unavailable")

        if history:
            history.save()
            logger.info(f"Notification {event_type} processed for {recipient.username} with status: {history.status}")
        else:
            logger.info(f"Notification {event_type} processed for {recipient.username} (no history saved)")

        # Return a simple success object if history creation failed but notification was sent
        if not history and response and response['successful_count'] > 0:
            class SimpleResult:
                def __init__(self):
                    self.title = title
                    self.body = body
                    self.status = 'sent'
            return SimpleResult()

        return history

    except Exception as e:
        import traceback
        logger.error(f"Error sending event notification: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None


def send_bulk_event_notifications(event_type, recipients, context_data_list=None, force_send=False):
    """
    Send the same event notification to multiple users

    :param event_type: Type of event
    :param recipients: List of User instances or user IDs
    :param context_data_list: List of context data dictionaries (one per recipient) or single dict for all
    :param force_send: If True, ignore user preferences
    :return: List of NotificationHistory instances
    """
    results = []

    # If single context data provided, use it for all recipients
    if context_data_list is None:
        context_data_list = [{}] * len(recipients)
    elif isinstance(context_data_list, dict):
        context_data_list = [context_data_list] * len(recipients)

    for i, recipient in enumerate(recipients):
        context_data = context_data_list[i] if i < len(context_data_list) else {}
        result = send_event_notification(event_type, recipient, context_data, force_send)
        if result:
            results.append(result)

    return results


def send_notification_to_all_users(title="Notification", body="", data=None):
    """
    Send notification to all registered device tokens
    """
    tokens = list(DeviceToken.objects.values_list("token", flat=True))
    if tokens:
        return send_fcm_notification(tokens=tokens, title=title, body=body, data=data)


# QR Registration notification helper
def notify_qr_registration(temp_student, recipient=None):
    """Send notification when someone submits registration via QR code"""
    from studentsData.models import TempStudentData

    if recipient is None:
        if temp_student.librarian:
            recipient = temp_student.librarian.user
        elif temp_student.sublibrarian:
            recipient = temp_student.sublibrarian.user

    if not recipient:
        logger.warning(f"No recipient found for QR registration: {temp_student.name}")
        return None

    context_data = {
        'student_name': temp_student.name,
        'student_email': temp_student.email,
        'student_mobile': temp_student.mobile,
        'course': temp_student.course.name,
        'library_name': temp_student.librarian.library_name if temp_student.librarian else 'Library',
        'registration_date': temp_student.registration_date.strftime('%Y-%m-%d'),
        'temp_student_id': temp_student.id,
    }

    return send_event_notification('qr_registration', recipient, context_data)
