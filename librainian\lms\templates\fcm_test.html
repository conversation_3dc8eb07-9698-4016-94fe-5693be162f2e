<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FCM Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .status {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-family: monospace;
        }
        .success { border-left: 5px solid #4CAF50; }
        .error { border-left: 5px solid #f44336; }
        .info { border-left: 5px solid #2196F3; }
        .warning { border-left: 5px solid #ff9800; }
        .countdown-big {
            font-size: 3rem;
            font-weight: bold;
            text-align: center;
            color: #ff9800;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 FCM Notification Test</h1>
        <p>This page tests Firebase Cloud Messaging (FCM) notifications step by step.</p>
        
        <div id="status" class="status info">
            <strong>Status:</strong> Ready to test
        </div>

        <div id="countdown" class="status warning" style="display: none;">
            <div class="countdown-big" id="countdownTimer">5</div>
            <strong>🕐 Background Notification Test</strong>
            <br><small>⚡ MINIMIZE YOUR BROWSER NOW! ⚡</small>
            <br><small>Notification will be sent when countdown reaches 0</small>
            <br><button class="btn" onclick="cancelCountdown()" style="margin-top: 10px; background: rgba(244, 67, 54, 0.3);">❌ Cancel Test</button>
        </div>

        <div class="buttons">
            <button class="btn" onclick="checkSupport()">1. Check Browser Support</button>
            <button class="btn" onclick="requestPermission()">2. Request Permission</button>
            <button class="btn" onclick="initializeFirebase()">3. Initialize Firebase</button>
            <button class="btn" onclick="getToken()">4. Get FCM Token</button>
            <button class="btn" onclick="testNotification()">5. Test Local Notification</button>
            <button class="btn" onclick="saveTokenToServer()">6. Save Token to Server</button>
            <button class="btn" onclick="testServerNotification()">7. Test Server Notification (Instant)</button>
            <button class="btn" onclick="testBackgroundNotification()">8. Test Background Notification (5s delay)</button>
        </div>

        <div id="tokenDisplay" style="display: none;">
            <h3>🎫 FCM Token:</h3>
            <textarea id="tokenText" style="width: 100%; height: 100px; background: rgba(0,0,0,0.3); color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 5px; padding: 10px;"></textarea>
            <button class="btn" onclick="copyToken()">Copy Token</button>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-messaging.js"></script>

    <script>
        let messaging;
        let currentToken;
        let countdownInterval;

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>Status:</strong> ${message}`;
            status.className = `status ${type}`;
            // Status updated
        }

        function checkSupport() {
            updateStatus('Checking browser support...', 'info');
            
            const checks = {
                'Notification API': 'Notification' in window,
                'Service Worker': 'serviceWorker' in navigator,
                'Push Manager': 'PushManager' in window,
                'Secure Context': window.isSecureContext,
                'Protocol': window.location.protocol,
                'Hostname': window.location.hostname
            };

            // Debug info available in alert only

            let allGood = true;
            let message = 'Browser Support Check:\n';
            
            for (const [feature, supported] of Object.entries(checks)) {
                const status = supported ? '✅' : '❌';
                message += `${status} ${feature}: ${supported}\n`;
                if (!supported && feature !== 'Protocol' && feature !== 'Hostname') {
                    allGood = false;
                }
            }

            if (!window.isSecureContext) {
                allGood = false;
                message += '\n❌ CRITICAL: Not a secure context! Use HTTPS or localhost';
            }

            updateStatus(message, allGood ? 'success' : 'error');
            alert(message);
        }

        function requestPermission() {
            updateStatus('Requesting notification permission...', 'info');
            
            if (!('Notification' in window)) {
                updateStatus('❌ Browser does not support notifications', 'error');
                return;
            }

            if (Notification.permission === 'granted') {
                updateStatus('✅ Permission already granted', 'success');
                return;
            }

            if (Notification.permission === 'denied') {
                updateStatus('❌ Permission denied. Reset in browser settings', 'error');
                alert('❌ Notifications are blocked!\n\nTo enable:\n1. Click the lock/info icon in address bar\n2. Set Notifications to "Allow"\n3. Refresh the page');
                return;
            }

            Notification.requestPermission().then(function(permission) {
                if (permission === 'granted') {
                    updateStatus('✅ Permission granted successfully!', 'success');
                    // Test notification
                    new Notification('Test Notification', {
                        body: 'Permission granted! FCM setup can continue.',
                        icon: '/static/favicon.ico'
                    });
                } else if (permission === 'denied') {
                    updateStatus('❌ Permission denied by user', 'error');
                } else {
                    updateStatus('⏳ Permission request dismissed', 'warning');
                }
            }).catch(function(error) {
                updateStatus(`❌ Error requesting permission: ${error.message}`, 'error');
            });
        }

        function initializeFirebase() {
            updateStatus('Initializing Firebase...', 'info');
            
            try {
                const firebaseConfig = {
                    apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
                    authDomain: "librainian-app.firebaseapp.com",
                    projectId: "librainian-app",
                    storageBucket: "librainian-app.firebasestorage.app",
                    messagingSenderId: "623132670328",
                    appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
                    measurementId: "G-XNDKJL6JWH"
                };

                if (!firebase.apps.length) {
                    firebase.initializeApp(firebaseConfig);
                }

                messaging = firebase.messaging();

                // Register service worker for background notifications
                if ('serviceWorker' in navigator) {
                    navigator.serviceWorker.register('/static/firebase-messaging-sw.js')
                        .then((registration) => {
                            console.log('✅ Service Worker registered:', registration);
                            messaging.useServiceWorker(registration);
                        })
                        .catch((error) => {
                            console.error('❌ Service Worker registration failed:', error);
                        });
                }

                // Handle foreground messages
                messaging.onMessage((payload) => {
                    console.log('📨 Foreground message received:', payload);
                    updateStatus('📨 Foreground notification received!', 'success');

                    // Show custom notification for foreground messages
                    if (Notification.permission === 'granted') {
                        const notification = new Notification(payload.notification.title, {
                            body: payload.notification.body,
                            icon: payload.notification.icon || '/static/favicon.ico',
                            badge: '/static/favicon.ico',
                            tag: 'fcm-foreground',
                            data: payload.data
                        });

                        notification.onclick = function() {
                            window.focus();
                            notification.close();
                            // Handle click action based on payload.data
                            if (payload.data && payload.data.url) {
                                window.open(payload.data.url, '_blank');
                            }
                        };
                    }
                });

                updateStatus('✅ Firebase initialized with foreground/background support', 'success');
            } catch (error) {
                updateStatus(`❌ Firebase initialization error: ${error.message}`, 'error');
            }
        }

        function getToken() {
            if (!messaging) {
                updateStatus('❌ Firebase not initialized. Run step 3 first.', 'error');
                return;
            }

            updateStatus('Getting FCM token...', 'info');

            messaging.getToken({ 
                vapidKey: 'BGX2t3YXGQdWxsYD_V3WpvlzCvwS7Ob0b9oGq0Thsg_OLHs22urXWd_CKv4QBdBVJNkZJJHq-QG8zB2p4qPYdrM' 
            }).then((token) => {
                if (token) {
                    currentToken = token;
                    updateStatus('✅ FCM Token generated successfully!', 'success');
                    
                    // Display token
                    document.getElementById('tokenDisplay').style.display = 'block';
                    document.getElementById('tokenText').value = token;
                    
                    console.log('🎫 FCM DEVICE TOKEN:', token);
                    alert(`✅ FCM Token Generated!\n\nLength: ${token.length} characters\n\nCheck console for full token.`);
                } else {
                    updateStatus('❌ No registration token available', 'error');
                }
            }).catch((err) => {
                updateStatus(`❌ Error getting token: ${err.message}`, 'error');
                console.error('Token error:', err);
            });
        }

        function testNotification() {
            if (Notification.permission !== 'granted') {
                updateStatus('❌ Permission not granted. Run step 2 first.', 'error');
                return;
            }

            updateStatus('Sending test notification...', 'info');
            
            try {
                const notification = new Notification('🎉 FCM Test Success!', {
                    body: 'All systems working! FCM is ready to use.',
                    icon: '/static/favicon.ico',
                    badge: '/static/favicon.ico',
                    tag: 'fcm-test',
                    requireInteraction: true
                });

                notification.onclick = function() {
                    window.focus();
                    notification.close();
                };

                updateStatus('✅ Test notification sent successfully!', 'success');
            } catch (error) {
                updateStatus(`❌ Error sending notification: ${error.message}`, 'error');
            }
        }

        function copyToken() {
            const tokenText = document.getElementById('tokenText');
            tokenText.select();
            document.execCommand('copy');

            const btn = event.target;
            const originalText = btn.textContent;
            btn.textContent = '✅ Copied!';
            btn.style.background = 'rgba(76, 175, 80, 0.3)';

            setTimeout(() => {
                btn.textContent = originalText;
                btn.style.background = 'rgba(255, 255, 255, 0.2)';
            }, 2000);
        }

        function saveTokenToServer() {
            if (!currentToken) {
                updateStatus('❌ No token available. Run step 4 first.', 'error');
                return;
            }

            updateStatus('💾 Saving token to server...', 'info');

            // Get CSRF token
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }

            fetch('/librarian/save-device-token/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: `token=${encodeURIComponent(currentToken)}&device_type=web&device_name=FCM Test Browser`
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    updateStatus('✅ Token saved to server successfully!', 'success');
                } else {
                    updateStatus(`❌ Failed to save token: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                updateStatus(`❌ Error saving token: ${error.message}`, 'error');
                console.error('Save token error:', error);
            });
        }

        function testServerNotification() {
            updateStatus('📤 Sending test notification via server...', 'info');

            // Get CSRF token
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }

            const formData = new FormData();
            formData.append('csrfmiddlewaretoken', getCookie('csrftoken'));
            formData.append('title', '🧪 FCM Test from Server');
            formData.append('message', 'This is a test notification sent from the Django server via FCM. Both foreground and background notifications should work!');
            formData.append('send_to_all', 'on'); // Send to all users

            fetch('/librarian/send-push-notification/', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                updateStatus('✅ Server notification sent! Check for notification.', 'success');

                // Instructions for user
                alert(`📤 Server notification sent!\n\n🔍 What to expect:\n\n✅ Foreground (page active): Custom notification popup\n✅ Background (page inactive): System notification\n\nTry minimizing this window and check for notifications!`);
            })
            .catch(error => {
                updateStatus(`❌ Error sending server notification: ${error.message}`, 'error');
                console.error('Server notification error:', error);
            });
        }

        function testBackgroundNotification() {
            updateStatus('⏰ Starting 5-second countdown for background notification test...', 'warning');

            // Show countdown display
            const countdownDiv = document.getElementById('countdown');
            const countdownTimer = document.getElementById('countdownTimer');
            countdownDiv.style.display = 'block';

            let timeLeft = 5;
            countdownTimer.textContent = timeLeft;

            // Update countdown every second
            countdownInterval = setInterval(() => {
                timeLeft--;
                countdownTimer.textContent = timeLeft;

                // Change color as countdown progresses
                if (timeLeft <= 2) {
                    countdownTimer.style.color = '#f44336'; // Red for urgency
                } else if (timeLeft <= 3) {
                    countdownTimer.style.color = '#ff9800'; // Orange
                }

                // Play a subtle beep sound (if available)
                if (timeLeft > 0) {
                    try {
                        // Create a short beep sound
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const oscillator = audioContext.createOscillator();
                        const gainNode = audioContext.createGain();

                        oscillator.connect(gainNode);
                        gainNode.connect(audioContext.destination);

                        oscillator.frequency.value = timeLeft <= 2 ? 800 : 400; // Higher pitch for final seconds
                        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

                        oscillator.start(audioContext.currentTime);
                        oscillator.stop(audioContext.currentTime + 0.1);
                    } catch (e) {
                        // Audio not supported, continue silently
                    }
                }

                if (timeLeft <= 0) {
                    clearInterval(countdownInterval);
                    countdownDiv.style.display = 'none';

                    // Send the notification
                    sendBackgroundNotification();
                }
            }, 1000);

            // Instructions for user
            alert(`🕐 5-Second Countdown Started!\n\n📋 Instructions:\n1. Click OK to close this alert\n2. MINIMIZE this browser window immediately\n3. Wait for the system notification to appear\n4. Click the notification to return to the app\n\n⏰ Countdown: 5 seconds`);
        }

        function sendBackgroundNotification() {
            updateStatus('📤 Sending background notification now...', 'info');

            // Get CSRF token
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }

            const formData = new FormData();
            formData.append('csrfmiddlewaretoken', getCookie('csrftoken'));
            formData.append('title', '🌙 Background Notification Test');
            formData.append('message', 'Success! This background notification was sent after a 5-second delay. Click to return to the app.');
            formData.append('send_to_all', 'on');

            fetch('/librarian/send-push-notification/', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                updateStatus('✅ Background notification sent! Check your system notifications.', 'success');

                // Show success message when user returns
                setTimeout(() => {
                    if (document.visibilityState === 'visible') {
                        alert('🎉 Background notification test completed!\n\nDid you receive the system notification?\n\n✅ If yes: Background notifications are working!\n❌ If no: Check browser/system notification settings.');
                    }
                }, 2000);
            })
            .catch(error => {
                updateStatus(`❌ Error sending background notification: ${error.message}`, 'error');
                console.error('Background notification error:', error);
            });
        }

        function cancelCountdown() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }

            const countdownDiv = document.getElementById('countdown');
            countdownDiv.style.display = 'none';

            updateStatus('❌ Background notification test cancelled', 'error');
        }

        // Auto-check support on page load
        window.addEventListener('load', function() {
            updateStatus('Page loaded. Click "Check Browser Support" to begin.', 'info');
        });
    </script>
</body>
</html>
