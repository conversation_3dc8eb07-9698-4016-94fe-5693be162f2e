import React, { useState, useRef } from 'react';
import {
  View,
  Modal,
  StyleSheet,
  TouchableOpacity,
  Text,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { WebView } from 'react-native-webview';
import Icon from 'react-native-vector-icons/FontAwesome';

const WebViewPayment = ({ 
  visible, 
  onClose, 
  onSuccess, 
  onFailure, 
  paymentData,
  isDarkMode 
}) => {
  const [loading, setLoading] = useState(true);
  const webViewRef = useRef(null);

  // Generate HTML content for Razorpay payment
  const generatePaymentHTML = () => {
    console.log('🌐 Generating payment HTML with data:', paymentData);
    const { razorpay_key, company_name, logo_url, razorpay_order, final_price, currency, subscription_id } = paymentData;
    
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment</title>
        <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
        <style>
            body {
                font-family: Arial, sans-serif;
                background-color: ${isDarkMode ? '#121212' : '#f5f5f5'};
                color: ${isDarkMode ? '#fff' : '#333'};
                padding: 20px;
                margin: 0;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                min-height: 100vh;
            }
            .payment-container {
                background-color: ${isDarkMode ? '#1e1e1e' : '#fff'};
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                text-align: center;
                max-width: 400px;
                width: 100%;
            }
            .logo {
                max-width: 100px;
                height: auto;
                margin-bottom: 20px;
            }
            .amount {
                font-size: 24px;
                font-weight: bold;
                color: #00c853;
                margin: 20px 0;
            }
            .pay-button {
                background-color: #00c853;
                color: white;
                border: none;
                padding: 15px 30px;
                font-size: 16px;
                border-radius: 5px;
                cursor: pointer;
                width: 100%;
                margin-top: 20px;
            }
            .pay-button:hover {
                background-color: #00a843;
            }
            .loading {
                text-align: center;
                padding: 20px;
            }
        </style>
    </head>
    <body>
        <div class="payment-container">
            ${logo_url ? `<img src="${logo_url}" alt="Logo" class="logo">` : ''}
            <h2>${company_name}</h2>
            <div class="amount">₹${(razorpay_order.amount / 100).toFixed(2)}</div>
            <button class="pay-button" onclick="startPayment()">Pay Now</button>
        </div>

        <script>
            function startPayment() {
                console.log('💳 Starting Razorpay payment...');
                const options = {
                    key: "${razorpay_key}",
                    amount: ${razorpay_order.amount},
                    currency: "${currency}",
                    name: "${company_name}",
                    description: "Package Subscription",
                    image: "${logo_url || ''}",
                    order_id: "${razorpay_order.id}",
                    handler: function (response) {
                        // Send success message to React Native
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'PAYMENT_SUCCESS',
                            data: {
                                razorpay_order_id: response.razorpay_order_id,
                                razorpay_payment_id: response.razorpay_payment_id,
                                razorpay_signature: response.razorpay_signature,
                                subscription_id: "${subscription_id}",
                                amount: ${razorpay_order.amount}
                            }
                        }));
                    },
                    prefill: {
                        name: "",
                        email: "",
                        contact: ""
                    },
                    theme: {
                        color: "#00c853"
                    },
                    modal: {
                        ondismiss: function() {
                            // Send cancellation message to React Native
                            window.ReactNativeWebView.postMessage(JSON.stringify({
                                type: 'PAYMENT_CANCELLED',
                                data: { message: 'Payment cancelled by user' }
                            }));
                        }
                    }
                };

                console.log('🔧 Razorpay options:', options);
                const rzp = new Razorpay(options);
                console.log('✅ Razorpay instance created');
                rzp.on('payment.failed', function (response) {
                    // Send failure message to React Native
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'PAYMENT_FAILED',
                        data: {
                            error: response.error.description,
                            code: response.error.code,
                            reason: response.error.reason
                        }
                    }));
                });
                console.log('🚀 Opening Razorpay checkout...');
                rzp.open();
            }

            // Auto-start payment when page loads
            window.onload = function() {
                console.log('📄 Page loaded, starting payment in 1 second...');
                setTimeout(startPayment, 1000);
            };
        </script>
    </body>
    </html>
    `;
  };

  const handleMessage = (event) => {
    try {
      console.log('📨 Received message from WebView:', event.nativeEvent.data);
      const message = JSON.parse(event.nativeEvent.data);
      console.log('📋 Parsed message:', message);

      switch (message.type) {
        case 'PAYMENT_SUCCESS':
          console.log('✅ Payment successful:', message.data);
          onSuccess(message.data);
          break;
        case 'PAYMENT_FAILED':
          console.log('❌ Payment failed:', message.data);
          onFailure(message.data);
          break;
        case 'PAYMENT_CANCELLED':
          console.log('🚫 Payment cancelled');
          onClose();
          break;
        default:
          console.log('❓ Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('❌ Error parsing WebView message:', error);
      onFailure({ error: 'Failed to process payment response' });
    }
  };

  const handleLoadEnd = () => {
    console.log('✅ WebView finished loading');
    setLoading(false);
  };

  const handleError = (syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    console.error('❌ WebView error:', nativeEvent);
    Alert.alert('Error', 'Failed to load payment page. Please check your internet connection.');
    onFailure({ error: 'Failed to load payment page' });
  };

  if (!visible || !paymentData) {
    console.log('🚫 WebViewPayment not rendering - visible:', visible, 'paymentData:', !!paymentData);
    return null;
  }

  console.log('🌐 Rendering WebViewPayment component');

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, isDarkMode && styles.containerDark]}>
        <View style={[styles.header, isDarkMode && styles.headerDark]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Icon name="times" size={24} color={isDarkMode ? "#fff" : "#333"} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, isDarkMode && styles.headerTitleDark]}>
            Payment
          </Text>
          <View style={styles.placeholder} />
        </View>

        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#00c853" />
            <Text style={[styles.loadingText, isDarkMode && styles.loadingTextDark]}>
              Loading payment...
            </Text>
          </View>
        )}

        <WebView
          ref={webViewRef}
          source={{ html: generatePaymentHTML() }}
          onMessage={handleMessage}
          onLoadEnd={handleLoadEnd}
          onError={handleError}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          style={styles.webview}
          userAgent="Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  headerDark: {
    backgroundColor: '#1e1e1e',
    borderBottomColor: '#333',
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerTitleDark: {
    color: '#fff',
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -50 }, { translateY: -50 }],
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  loadingTextDark: {
    color: '#ccc',
  },
  webview: {
    flex: 1,
  },
});

export default WebViewPayment;
