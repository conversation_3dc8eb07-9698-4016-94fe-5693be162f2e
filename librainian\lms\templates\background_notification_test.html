<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Background Notification Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            min-height: 100vh;
            text-align: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: none;
            color: white;
            padding: 20px 40px;
            border-radius: 15px;
            cursor: pointer;
            margin: 15px;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
        }
        .countdown {
            font-size: 4rem;
            font-weight: bold;
            color: #e74c3c;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            animation: pulse 1s infinite;
            margin: 30px 0;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .instructions {
            background: rgba(52, 152, 219, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #3498db;
        }
        .warning {
            background: rgba(230, 126, 34, 0.2);
            border-left: 5px solid #e67e22;
        }
        .success {
            background: rgba(46, 204, 113, 0.2);
            border-left: 5px solid #27ae60;
        }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌙 Background Notification Test</h1>
        <p>This page tests background notifications with a countdown timer.</p>
        
        <div id="step1" class="instructions">
            <h3>📋 Step 1: Setup</h3>
            <p>Click the button below to start the background notification test.</p>
            <button class="btn" onclick="startTest()">🚀 Start Background Test</button>
        </div>

        <div id="step2" class="instructions warning hidden">
            <h3>⚡ Step 2: Minimize Browser</h3>
            <p><strong>MINIMIZE THIS BROWSER WINDOW NOW!</strong></p>
            <p>Notification will be sent in:</p>
            <div class="countdown" id="countdown">10</div>
            <button class="btn" onclick="cancelTest()" style="background: #e74c3c;">❌ Cancel</button>
        </div>

        <div id="step3" class="instructions success hidden">
            <h3>✅ Step 3: Test Complete</h3>
            <p>Background notification has been sent!</p>
            <p>Did you receive a system notification?</p>
            <button class="btn" onclick="resetTest()">🔄 Test Again</button>
        </div>
    </div>

    <script>
        let countdownTimer;
        let timeLeft;

        function startTest() {
            document.getElementById('step1').classList.add('hidden');
            document.getElementById('step2').classList.remove('hidden');
            
            timeLeft = 10;
            updateCountdown();
            
            countdownTimer = setInterval(() => {
                timeLeft--;
                updateCountdown();
                
                if (timeLeft <= 0) {
                    clearInterval(countdownTimer);
                    sendNotification();
                }
            }, 1000);
        }

        function updateCountdown() {
            const countdownEl = document.getElementById('countdown');
            countdownEl.textContent = timeLeft;
            
            // Change color based on time left
            if (timeLeft <= 3) {
                countdownEl.style.color = '#e74c3c';
            } else if (timeLeft <= 5) {
                countdownEl.style.color = '#f39c12';
            } else {
                countdownEl.style.color = '#3498db';
            }
        }

        function cancelTest() {
            if (countdownTimer) {
                clearInterval(countdownTimer);
            }
            resetTest();
        }

        function resetTest() {
            document.getElementById('step1').classList.remove('hidden');
            document.getElementById('step2').classList.add('hidden');
            document.getElementById('step3').classList.add('hidden');
            
            if (countdownTimer) {
                clearInterval(countdownTimer);
            }
        }

        function sendNotification() {
            document.getElementById('step2').classList.add('hidden');
            document.getElementById('step3').classList.remove('hidden');
            
            // Get CSRF token
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }

            const formData = new FormData();
            formData.append('csrfmiddlewaretoken', getCookie('csrftoken'));
            formData.append('title', '🌙 Background Notification Success!');
            formData.append('message', 'Perfect! This background notification was sent after the countdown. Click to return to the test page.');
            formData.append('send_to_all', 'on');

            fetch('/librarian/send-push-notification/', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                // Background notification sent successfully
            })
            .catch(error => {
                // Error sending notification
            });
        }

        // Auto-focus when page becomes visible (user returns)
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                // User returned to page
            }
        });
    </script>
</body>
</html>
