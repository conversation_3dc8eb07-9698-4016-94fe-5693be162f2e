#!/usr/bin/env python3
"""
Test Cron Job Setup for Membership Expiry Notifications
This script tests if the cron job command will work correctly
"""

import os
import sys
import django
import subprocess
from pathlib import Path

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def test_management_command():
    """Test the management command that will be used in cron"""
    print("🧪 Testing Management Command")
    print("=" * 40)
    
    try:
        from django.core.management import call_command
        from io import StringIO
        
        # Test the command
        out = StringIO()
        call_command('check_membership_expiry', '--verbose', stdout=out)
        
        output = out.getvalue()
        print("✅ Command executed successfully!")
        print("\n📋 Command Output:")
        print("-" * 30)
        print(output)
        print("-" * 30)
        
        return True
        
    except Exception as e:
        print(f"❌ Command failed: {e}")
        return False

def test_environment():
    """Test if the environment is set up correctly for cron"""
    print("\n🔧 Testing Environment")
    print("=" * 30)
    
    try:
        # Check Django settings
        from django.conf import settings
        print(f"✅ Django settings loaded: {settings.SETTINGS_MODULE}")
        
        # Check database connection
        from django.db import connection
        connection.ensure_connection()
        print("✅ Database connection working")
        
        # Check if we can import required modules
        from membership.models import Membership
        from librarian.notification_events import notification_events
        print("✅ Required modules can be imported")
        
        # Check if we have memberships to work with
        membership_count = Membership.objects.count()
        print(f"✅ Found {membership_count} memberships in database")
        
        return True
        
    except Exception as e:
        print(f"❌ Environment test failed: {e}")
        return False

def show_cron_command():
    """Show the exact cron command to use"""
    print("\n⏰ CRON JOB COMMAND")
    print("=" * 30)
    
    project_path = Path(__file__).parent.absolute()
    python_path = sys.executable
    
    print("Add this line to your crontab (crontab -e):")
    print()
    print(f"# Membership expiry check - runs daily at 11:11 AM")
    print(f"51 16 * * * cd {project_path} && {python_path} manage.py check_membership_expiry >> /var/log/membership_expiry.log 2>&1")
    print()
    
    print("📋 Manual test command:")
    print(f"cd {project_path} && {python_path} manage.py check_membership_expiry --verbose")
    print()
    
    print("📊 Check logs:")
    print("tail -f /var/log/membership_expiry.log")

def test_notification_system():
    """Test if notifications can be sent"""
    print("\n🔔 Testing Notification System")
    print("=" * 40)
    
    try:
        from librarian.models import DeviceToken
        
        # Check if we have device tokens
        token_count = DeviceToken.objects.filter(is_active=True).count()
        print(f"✅ Found {token_count} active device tokens")
        
        if token_count == 0:
            print("⚠️ No active device tokens found!")
            print("💡 Visit http://localhost:8000/fcm-test/ to register a token")
            return False
        
        # Test sending a notification
        from librarian.notification_utils import send_notification_to_all_users
        
        result = send_notification_to_all_users(
            title="🧪 Cron Job Test",
            body="This is a test notification to verify the cron job setup is working correctly.",
            data={
                "type": "cron_test",
                "timestamp": "2025-07-22",
                "test": True
            }
        )
        
        if result and result.get('successful_count', 0) > 0:
            print("✅ Test notification sent successfully!")
            print(f"📊 Success: {result['successful_count']}, Failed: {result['failed_count']}")
            return True
        else:
            print("❌ Test notification failed")
            return False
            
    except Exception as e:
        print(f"❌ Notification test failed: {e}")
        return False

def main():
    print("🧪 CRON JOB SETUP TEST")
    print("=" * 50)
    print("Testing if the cron job will work correctly")
    print()
    
    # Run tests
    tests = [
        ("Environment", test_environment),
        ("Management Command", test_management_command),
        ("Notification System", test_notification_system),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # Show cron command
    show_cron_command()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n🎯 Overall: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        print("🎉 ALL TESTS PASSED! Cron job is ready to be set up!")
        print("\n📋 NEXT STEPS:")
        print("1. Copy the cron command shown above")
        print("2. Run: crontab -e")
        print("3. Paste the cron command")
        print("4. Save and exit")
        print("5. Verify: crontab -l")
        print("\n⏰ The cron job will run daily at 11:11 AM")
    elif passed_count > 0:
        print("⚠️ Some tests passed. Please fix the failed tests before setting up cron.")
    else:
        print("❌ All tests failed. Please fix the issues before setting up cron.")
    
    print("\n💡 ALTERNATIVE TESTING:")
    print("• Django Admin: Use admin actions for immediate testing")
    print("• Manual Command: Run the management command manually")
    print("• Signals: Automatic when saving membership in admin")

if __name__ == "__main__":
    main()
