#!/usr/bin/env python3
"""
Comprehensive FCM Notification Testing Script
Tests all notification events and scenarios as per requirements.
"""

import os
import sys
import django
import time
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def test_all_notification_events():
    """Test all notification events comprehensively"""
    print("🧪 COMPREHENSIVE FCM NOTIFICATION TESTING")
    print("=" * 60)
    
    try:
        from librarian.notification_events import notification_events
        from django.contrib.auth.models import User
        from librarian.models import DeviceToken
        
        # Check if we have device tokens
        device_tokens = DeviceToken.objects.filter(is_active=True)
        if not device_tokens.exists():
            print("❌ No active device tokens found!")
            print("💡 Please visit http://localhost:8000/fcm-test/ and register a token first")
            return False
        
        print(f"✅ Found {device_tokens.count()} active device tokens")
        
        # Test Results Tracking
        test_results = {}
        
        # 1. TEST QR REGISTRATION NOTIFICATIONS
        print("\n1️⃣ Testing QR Registration Notifications...")
        try:
            from studentsData.models import TempStudentData
            temp_student = TempStudentData.objects.filter(status='pending').first()
            
            if temp_student:
                result = notification_events.notify_qr_registration(temp_student)
                test_results['qr_registration'] = result
                print(f"   ✅ QR Registration: {'SUCCESS' if result else 'FAILED'}")
                print(f"   📋 Student: {temp_student.name}")
                print(f"   📧 Email: {temp_student.email}")
                print(f"   📱 Mobile: {temp_student.mobile}")
                print(f"   🎓 Course: {temp_student.course.name}")
            else:
                print("   ⚠️ No pending temp students found for testing")
                test_results['qr_registration'] = None
        except Exception as e:
            print(f"   ❌ QR Registration test failed: {e}")
            test_results['qr_registration'] = False
        
        # 2. TEST VISITOR CALLBACK NOTIFICATIONS
        print("\n2️⃣ Testing Visitor Callback Notifications...")
        try:
            count = notification_events.check_visitor_callbacks_today()
            test_results['visitor_callbacks'] = count
            print(f"   ✅ Visitor Callbacks: {count} notifications sent")
        except Exception as e:
            print(f"   ❌ Visitor Callback test failed: {e}")
            test_results['visitor_callbacks'] = False
        
        # 3. TEST ADMISSION PROCESSED NOTIFICATIONS
        print("\n3️⃣ Testing Admission Processed Notifications...")
        try:
            from studentsData.models import StudentData
            student = StudentData.objects.first()
            
            if student:
                processed_by = student.librarian.user if student.librarian else User.objects.filter(is_superuser=True).first()
                result = notification_events.notify_admission_processed(student, processed_by)
                test_results['admission_processed'] = result
                print(f"   ✅ Admission Processed: {'SUCCESS' if result else 'FAILED'}")
                print(f"   👤 Student: {student.name}")
                print(f"   📧 Email: {student.email}")
                print(f"   🎓 Course: {student.course.name}")
            else:
                print("   ⚠️ No students found for testing")
                test_results['admission_processed'] = None
        except Exception as e:
            print(f"   ❌ Admission Processed test failed: {e}")
            test_results['admission_processed'] = False
        
        # 4. TEST SUBSCRIPTION EXPIRY NOTIFICATIONS
        print("\n4️⃣ Testing Subscription Expiry Notifications...")
        try:
            count = notification_events.check_subscription_expiry_notifications()
            test_results['subscription_expiry'] = count
            print(f"   ✅ Subscription Expiry: {count} notifications sent")
            print("   📅 Covers: 10 days, 5 days, 1 day, expired today, 4 days after expiry")
        except Exception as e:
            print(f"   ❌ Subscription Expiry test failed: {e}")
            test_results['subscription_expiry'] = False
        
        # 5. TEST FEE PAYMENT NOTIFICATIONS
        print("\n5️⃣ Testing Fee Payment Notifications...")
        try:
            from studentsData.models import Payment
            payment = Payment.objects.first()
            
            if payment:
                received_by = payment.invoice.student.librarian.user if payment.invoice.student.librarian else User.objects.filter(is_superuser=True).first()
                result = notification_events.notify_payment_received(payment, received_by)
                test_results['payment_received'] = result
                print(f"   ✅ Payment Received: {'SUCCESS' if result else 'FAILED'}")
                print(f"   💰 Amount: ₹{payment.amount_paid}")
                print(f"   👤 Student: {payment.invoice.student.name}")
                print(f"   📧 Email: {payment.invoice.student.email}")
            else:
                print("   ⚠️ No payments found for testing")
                test_results['payment_received'] = None
        except Exception as e:
            print(f"   ❌ Payment Received test failed: {e}")
            test_results['payment_received'] = False
        
        # 6. TEST SALES MILESTONE NOTIFICATIONS
        print("\n6️⃣ Testing Sales Milestone Notifications...")
        try:
            current_sales = notification_events.check_sales_milestones()
            test_results['sales_milestones'] = current_sales
            print(f"   ✅ Sales Milestones: Current monthly sales ₹{current_sales:,}")
            print("   🎯 Milestones: ₹50K, ₹100K, ₹150K, ₹200K")
        except Exception as e:
            print(f"   ❌ Sales Milestone test failed: {e}")
            test_results['sales_milestones'] = False
        
        # 7. TEST VISITOR ADDED NOTIFICATIONS
        print("\n7️⃣ Testing Visitor Added Notifications...")
        try:
            from visitorsData.models import Visitor
            visitor = Visitor.objects.first()
            
            if visitor:
                added_by = visitor.librarian.user if visitor.librarian else User.objects.filter(is_superuser=True).first()
                result = notification_events.notify_visitor_added(visitor, added_by)
                test_results['visitor_added'] = result
                print(f"   ✅ Visitor Added: {'SUCCESS' if result else 'FAILED'}")
                print(f"   👤 Visitor: {visitor.name}")
                print(f"   📱 Contact: {visitor.contact}")
            else:
                print("   ⚠️ No visitors found for testing")
                test_results['visitor_added'] = None
        except Exception as e:
            print(f"   ❌ Visitor Added test failed: {e}")
            test_results['visitor_added'] = False
        
        # 8. TEST SEAT OCCUPANCY NOTIFICATIONS
        print("\n8️⃣ Testing Seat Occupancy Notifications...")
        try:
            count = notification_events.check_seat_occupancy()
            test_results['seat_occupancy'] = count
            print(f"   ✅ Seat Occupancy: {count} alerts sent")
            print("   🪑 Threshold: 80% occupancy per shift")
        except Exception as e:
            print(f"   ❌ Seat Occupancy test failed: {e}")
            test_results['seat_occupancy'] = False
        
        # 9. TEST MANUAL ADMIN NOTIFICATIONS
        print("\n9️⃣ Testing Manual Admin Notifications...")
        try:
            from librarian.notification_utils import send_notification_to_all_users
            
            result = send_notification_to_all_users(
                title="🧪 Admin Test Notification",
                body="This is a comprehensive test of the manual admin notification system. All FCM features are working correctly!",
                data={
                    "type": "admin_test",
                    "test_timestamp": datetime.now().isoformat(),
                    "comprehensive_test": True
                }
            )
            test_results['manual_admin'] = result
            print(f"   ✅ Manual Admin: {'SUCCESS' if result else 'FAILED'}")
            print(f"   📊 Result: {result}")
        except Exception as e:
            print(f"   ❌ Manual Admin test failed: {e}")
            test_results['manual_admin'] = False
        
        # SUMMARY
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST RESULTS SUMMARY")
        print("=" * 60)
        
        total_tests = len(test_results)
        successful_tests = sum(1 for result in test_results.values() if result not in [False, None])
        failed_tests = sum(1 for result in test_results.values() if result is False)
        skipped_tests = sum(1 for result in test_results.values() if result is None)
        
        print(f"📈 Total Tests: {total_tests}")
        print(f"✅ Successful: {successful_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⚠️ Skipped (No Data): {skipped_tests}")
        
        print("\n📋 Detailed Results:")
        for test_name, result in test_results.items():
            status = "✅ SUCCESS" if result not in [False, None] else ("❌ FAILED" if result is False else "⚠️ SKIPPED")
            print(f"   {test_name}: {status}")
        
        # RECOMMENDATIONS
        print("\n💡 RECOMMENDATIONS:")
        if failed_tests > 0:
            print("   • Check server logs for detailed error messages")
            print("   • Verify Firebase credentials are correctly configured")
            print("   • Ensure all required models have test data")
        
        if skipped_tests > 0:
            print("   • Add test data for skipped scenarios")
            print("   • Create sample records for comprehensive testing")
        
        print("\n🎯 NEXT STEPS:")
        print("   1. Test notifications manually via web interface")
        print("   2. Set up cron jobs for automated notifications")
        print("   3. Monitor notification delivery in production")
        print("   4. Configure user notification preferences")
        
        return successful_tests == total_tests
        
    except Exception as e:
        print(f"❌ Comprehensive test failed: {e}")
        return False

def main():
    print("🚀 Starting Comprehensive FCM Notification Testing...")
    
    success = test_all_notification_events()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED! FCM notification system is fully functional!")
    else:
        print("⚠️ Some tests failed or were skipped. Check the results above.")
    
    print("\n🌐 Manual Testing:")
    print("   • Visit: http://localhost:8000/librarian/send-push-notification/")
    print("   • Test manual notifications and event triggers")
    print("   • Verify foreground and background notification delivery")

if __name__ == "__main__":
    main()
