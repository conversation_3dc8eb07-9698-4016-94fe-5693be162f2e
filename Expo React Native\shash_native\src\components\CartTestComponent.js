import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { addToCart, removeFromCart, updateQuantity, clearCart } from '../redux/cartSlice';

/**
 * Test component to verify cart functionality
 * This component can be temporarily added to any screen for testing
 * Remove this component before production deployment
 */
const CartTestComponent = () => {
  const dispatch = useDispatch();
  const { items, total, itemCount, error } = useSelector((state) => state.cart);

  const testPackage = {
    id: 'test-package-1',
    name: 'Test Package',
    price: 999,
    quantity: 1,
    type: 'package',
    packageId: 'test-package-1',
    description: 'This is a test package for cart functionality',
    maxQuantity: 5,
  };

  const testProduct = {
    id: 'test-product-1',
    name: 'Test Product',
    price: 499,
    quantity: 1,
    type: 'product',
    description: 'This is a test product for cart functionality',
    maxQuantity: 10,
  };

  const handleAddTestPackage = () => {
    dispatch(addToCart(testPackage));
    Alert.alert('Success', 'Test package added to cart');
  };

  const handleAddTestProduct = () => {
    dispatch(addToCart(testProduct));
    Alert.alert('Success', 'Test product added to cart');
  };

  const handleIncreaseQuantity = (item) => {
    dispatch(updateQuantity({
      id: item.id,
      type: item.type,
      quantity: item.quantity + 1
    }));
  };

  const handleDecreaseQuantity = (item) => {
    if (item.quantity > 1) {
      dispatch(updateQuantity({
        id: item.id,
        type: item.type,
        quantity: item.quantity - 1
      }));
    } else {
      dispatch(removeFromCart({
        id: item.id,
        type: item.type
      }));
    }
  };

  const handleRemoveItem = (item) => {
    dispatch(removeFromCart({
      id: item.id,
      type: item.type
    }));
    Alert.alert('Removed', `${item.name} removed from cart`);
  };

  const handleClearCart = () => {
    Alert.alert(
      'Clear Cart',
      'Are you sure you want to clear all items?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Clear', 
          style: 'destructive',
          onPress: () => {
            dispatch(clearCart());
            Alert.alert('Success', 'Cart cleared');
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🛒 Cart Test Component</Text>
      
      {/* Cart Summary */}
      <View style={styles.summary}>
        <Text style={styles.summaryText}>Items: {itemCount}</Text>
        <Text style={styles.summaryText}>Total: ₹{total}</Text>
        {error && <Text style={styles.errorText}>Error: {error}</Text>}
      </View>

      {/* Test Actions */}
      <View style={styles.actions}>
        <TouchableOpacity style={styles.button} onPress={handleAddTestPackage}>
          <Text style={styles.buttonText}>Add Test Package</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={handleAddTestProduct}>
          <Text style={styles.buttonText}>Add Test Product</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.clearButton]} 
          onPress={handleClearCart}
          disabled={itemCount === 0}
        >
          <Text style={styles.buttonText}>Clear Cart</Text>
        </TouchableOpacity>
      </View>

      {/* Cart Items */}
      {items.length > 0 && (
        <View style={styles.items}>
          <Text style={styles.itemsTitle}>Cart Items:</Text>
          {items.map((item, index) => (
            <View key={`${item.id}-${item.type}`} style={styles.item}>
              <View style={styles.itemInfo}>
                <Text style={styles.itemName}>{item.name}</Text>
                <Text style={styles.itemPrice}>₹{item.price} x {item.quantity}</Text>
              </View>
              
              <View style={styles.itemActions}>
                <TouchableOpacity 
                  style={styles.quantityButton}
                  onPress={() => handleDecreaseQuantity(item)}
                >
                  <Text style={styles.quantityButtonText}>-</Text>
                </TouchableOpacity>
                
                <Text style={styles.quantity}>{item.quantity}</Text>
                
                <TouchableOpacity 
                  style={styles.quantityButton}
                  onPress={() => handleIncreaseQuantity(item)}
                  disabled={item.quantity >= (item.maxQuantity || 99)}
                >
                  <Text style={styles.quantityButtonText}>+</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.removeButton}
                  onPress={() => handleRemoveItem(item)}
                >
                  <Text style={styles.removeButtonText}>Remove</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>
      )}

      <Text style={styles.note}>
        ⚠️ This is a test component. Remove before production.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f0f8ff',
    margin: 10,
    padding: 15,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#007bff',
    borderStyle: 'dashed',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#007bff',
  },
  summary: {
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  summaryText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  errorText: {
    color: '#dc3545',
    fontSize: 14,
    fontStyle: 'italic',
  },
  actions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 10,
  },
  button: {
    backgroundColor: '#007bff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 5,
    flex: 1,
    minWidth: 100,
  },
  clearButton: {
    backgroundColor: '#dc3545',
  },
  buttonText: {
    color: '#fff',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: 12,
  },
  items: {
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  itemsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '600',
  },
  itemPrice: {
    fontSize: 12,
    color: '#666',
  },
  itemActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  quantityButton: {
    backgroundColor: '#28a745',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  quantity: {
    fontSize: 14,
    fontWeight: '600',
    minWidth: 20,
    textAlign: 'center',
  },
  removeButton: {
    backgroundColor: '#dc3545',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  removeButtonText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
  },
  note: {
    fontSize: 12,
    fontStyle: 'italic',
    textAlign: 'center',
    color: '#666',
    marginTop: 5,
  },
});

export default CartTestComponent;
