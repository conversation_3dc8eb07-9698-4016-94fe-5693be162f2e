<!DOCTYPE html>
<html lang="en">
<head>

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5L8WZWCC');</script>
<!-- End Google Tag Manager -->

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="google" content="notranslate">
    <title>Add Sublibrarian | Librainian</title>

    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <!-- Favicon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">

    <style>
        :root {
            /* Modern Color Palette */
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #8b5cf6;
            --secondary: #10b981;
            --accent: #f59e0b;
            --danger: #ef4444;
            --warning: #f59e0b;
            --success: #10b981;
            --info: #06b6d4;

            /* Neutral Colors */
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;

            /* Spacing & Effects */
            --border-radius: 12px;
            --border-radius-lg: 20px;
            --border-radius-xl: 32px;
            --border-radius-sm: 8px;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

            /* Transitions */
            --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            /* Glassmorphism Effects */
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-bg-light: rgba(255, 255, 255, 0.95);
            --glass-bg-medium: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-border-light: rgba(255, 255, 255, 0.3);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            --glass-shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --glass-blur: blur(20px);
            --glass-blur-strong: blur(25px);

            /* Background Gradients */
            --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary) 0%, #059669 100%);
            --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        }

        /* Body and Page Styling */
        body {
            font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--gray-900);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated Background Elements */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 25% 25%, rgba(120, 119, 198, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
            z-index: -2;
            animation: float 8s ease-in-out infinite;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 60% 20%, rgba(120, 119, 198, 0.1) 0%, transparent 40%),
                radial-gradient(circle at 20% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 40%);
            pointer-events: none;
            z-index: -1;
            animation: float 10s ease-in-out infinite reverse;
        }

        /* Back Button */
        .back-button-top {
            position: fixed;
            top: 2rem;
            left: 2rem;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid var(--gray-200);
            border-radius: 50px;
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            text-decoration: none;
            color: var(--gray-700);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
        }

        .back-button-top:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.05);
            color: var(--gray-700);
            text-decoration: none;
        }

        .back-button-top i {
            font-size: 1.25rem;
            transition: all 0.3s ease;
        }

        /* Dark Mode Styles */
        body.dark-mode {
            background:
                radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #1f2937 0%, #111827 100%);
            color: #ffffff;
        }

        body.dark-mode::before {
            background:
                radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.08) 0%, transparent 50%);
        }

        body.dark-mode::after {
            background:
                radial-gradient(circle at 60% 20%, rgba(99, 102, 241, 0.08) 0%, transparent 40%),
                radial-gradient(circle at 20% 60%, rgba(139, 92, 246, 0.05) 0%, transparent 40%);
        }

        body.dark-mode .back-button-top {
            background: rgba(17, 24, 39, 0.9);
            border-color: rgba(255, 255, 255, 0.2);
            color: #ffffff;
        }

        body.dark-mode .back-button-top:hover {
            background: rgba(17, 24, 39, 1);
            color: #ffffff;
        }

        /* Dark mode form styles */
        body.dark-mode .add-sublibrarian-container .card {
            background: rgba(255, 255, 255, 0.05) !important;
            border-color: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(20px) !important;
            -webkit-backdrop-filter: blur(20px) !important;
        }

        body.dark-mode .add-sublibrarian-container .card-header {
            background: rgba(255, 255, 255, 0.1) !important;
            border-bottom-color: rgba(255, 255, 255, 0.1) !important;
            color: #ffffff !important;
        }

        body.dark-mode .add-sublibrarian-container .card-body {
            color: #ffffff !important;
        }

        body.dark-mode .add-sublibrarian-container .form-control {
            background: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: #ffffff !important;
        }

        body.dark-mode .add-sublibrarian-container .form-control:focus {
            background: rgba(255, 255, 255, 0.15) !important;
            border-color: var(--primary) !important;
            color: #ffffff !important;
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25) !important;
        }

        body.dark-mode .add-sublibrarian-container .input-group-text {
            background: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: #ffffff !important;
        }

        body.dark-mode .add-sublibrarian-container .form-label {
            color: #ffffff !important;
        }

        body.dark-mode .add-sublibrarian-container .text-muted {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        body.dark-mode .add-sublibrarian-container .btn-outline-secondary {
            border-color: rgba(255, 255, 255, 0.3) !important;
            color: #ffffff !important;
        }

        body.dark-mode .add-sublibrarian-container .btn-outline-secondary:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            color: #ffffff !important;
        }

        /* Dark mode password toggle styles */
        body.dark-mode .add-sublibrarian-container .password-toggle {
            color: rgba(255, 255, 255, 0.6) !important;
        }

        body.dark-mode .add-sublibrarian-container .password-toggle:hover {
            color: var(--primary) !important;
            background: rgba(99, 102, 241, 0.1) !important;
        }

        /* Dark mode username generation button styles */
        body.dark-mode .add-sublibrarian-container #generateUsername {
            background: rgba(255, 255, 255, 0.05) !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: rgba(255, 255, 255, 0.9) !important;
            backdrop-filter: blur(10px);
        }

        body.dark-mode .add-sublibrarian-container #generateUsername:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.3) !important;
            color: white !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        /* Dark mode form text styles */
        body.dark-mode .add-sublibrarian-container .form-text {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        /* Scoped styles for Add Sublibrarian page only */
        .add-sublibrarian-container {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            font-family: 'Comfortaa', sans-serif;
            line-height: 1.6;
            color: var(--gray-900);
            font-size: 14px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: relative;
            min-height: 100vh;
            padding-top: 6rem;
            padding-bottom: 2rem;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .add-sublibrarian-container .page-container {
            min-height: 100vh;
            padding: 2rem 0;
            position: relative;
        }
        
        .add-sublibrarian-container .card {
            background: rgba(255, 255, 255, 0.15) !important;
            backdrop-filter: blur(25px) !important;
            -webkit-backdrop-filter: blur(25px) !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: var(--border-radius-lg) !important;
            box-shadow: var(--glass-shadow-lg) !important;
            transition: var(--transition-slow) !important;
            position: relative;
            overflow: hidden;
        }

        .add-sublibrarian-container .card:hover {
            transform: translateY(-3px) !important;
            box-shadow:
                0 35px 60px -12px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.15) inset,
                0 2px 4px rgba(255, 255, 255, 0.15) inset !important;
            border-color: rgba(255, 255, 255, 0.4) !important;
        }

        .add-sublibrarian-container .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                transparent 100%);
            pointer-events: none;
            z-index: 1;
        }

        .add-sublibrarian-container .card-header {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            padding: 1.5rem 2rem !important;
            position: relative;
            z-index: 2;
        }

        .add-sublibrarian-container .card-header h4 {
            color: white !important;
            font-weight: 700 !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
            margin: 0 !important;
        }

        .add-sublibrarian-container .card-body {
            position: relative;
            z-index: 2;
            background: transparent !important;
        }

        .add-sublibrarian-container .form-label {
            font-weight: 600 !important;
            margin-bottom: 0.5rem !important;
            color: white !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }

        .add-sublibrarian-container .form-control {
            background: rgba(255, 255, 255, 0.15) !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: var(--border-radius) !important;
            color: white !important;
            padding: 0.875rem 1rem !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            transition: var(--transition) !important;
            font-size: 0.875rem !important;
        }

        .add-sublibrarian-container .form-control:focus {
            background: rgba(255, 255, 255, 0.2) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            color: white !important;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
            outline: none !important;
        }

        .add-sublibrarian-container .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        .add-sublibrarian-container .btn-primary {
            background: var(--gradient-primary) !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: var(--border-radius) !important;
            color: white !important;
            padding: 0.875rem 1.5rem !important;
            font-weight: 600 !important;
            transition: var(--transition) !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
            position: relative;
            overflow: hidden;
        }

        .add-sublibrarian-container .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            color: white !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
        }

        .add-sublibrarian-container .btn-primary:disabled {
            background: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: rgba(255, 255, 255, 0.5) !important;
            transform: none !important;
            box-shadow: none !important;
        }

        .add-sublibrarian-container .btn-outline-secondary {
            background: rgba(255, 255, 255, 0.1) !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: var(--border-radius) !important;
            color: white !important;
            padding: 0.875rem 1.5rem !important;
            font-weight: 600 !important;
            transition: var(--transition) !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }

        .add-sublibrarian-container .btn-outline-secondary:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            color: white !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15) !important;
        }

        .add-sublibrarian-container .alert {
            background: rgba(255, 255, 255, 0.15) !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: var(--border-radius) !important;
            color: white !important;
            padding: 1rem 1.5rem !important;
            margin-bottom: 1.5rem !important;
            backdrop-filter: blur(15px) !important;
            -webkit-backdrop-filter: blur(15px) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
            position: relative;
            overflow: hidden;
        }

        .add-sublibrarian-container .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                transparent 100%);
            pointer-events: none;
            z-index: 1;
        }

        .add-sublibrarian-container .alert > * {
            position: relative;
            z-index: 2;
        }

        .add-sublibrarian-container .alert-success {
            border-left: 4px solid var(--success) !important;
        }

        .add-sublibrarian-container .alert-danger {
            border-left: 4px solid var(--danger) !important;
        }

        .add-sublibrarian-container .alert-warning {
            border-left: 4px solid var(--warning) !important;
        }

        .add-sublibrarian-container .alert-info {
            border-left: 4px solid var(--info) !important;
        }

        .add-sublibrarian-container .breadcrumb {
            background-color: transparent !important;
            padding: 0.5rem 0 !important;
            margin-bottom: 1.5rem !important;
        }

        .add-sublibrarian-container .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.9) !important;
            text-decoration: none !important;
            transition: var(--transition) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }

        .add-sublibrarian-container .breadcrumb-item a:hover {
            color: white !important;
            text-decoration: underline !important;
        }

        .add-sublibrarian-container .breadcrumb-item.active {
            color: rgba(255, 255, 255, 0.8) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }

        .add-sublibrarian-container .valid {
            color: var(--success) !important;
        }

        .add-sublibrarian-container .invalid {
            color: var(--danger) !important;
        }

        .add-sublibrarian-container .input-group-text {
            background: rgba(255, 255, 255, 0.2) !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            border-right: none !important;
            color: white !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            border-radius: var(--border-radius) 0 0 var(--border-radius) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 45px !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }

        .add-sublibrarian-container .input-group .form-control {
            border-left: none !important;
            border-radius: 0 var(--border-radius) var(--border-radius) 0 !important;
            margin-left: 0 !important;
        }

        .add-sublibrarian-container .input-group {
            display: flex !important;
            width: 100% !important;
        }

        .add-sublibrarian-container .input-group > * {
            margin: 0 !important;
        }

        /* Password Group Styling */
        .add-sublibrarian-container .password-group {
            position: relative;
        }

        .add-sublibrarian-container .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: rgba(217, 217, 236, 0.7);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
            z-index: 10;
        }

        .add-sublibrarian-container .password-toggle:hover {
            color: var(--primary);
            background: rgba(99, 102, 241, 0.1);
        }

        .add-sublibrarian-container .password-toggle:focus {
            outline: 2px solid var(--primary);
            outline-offset: 2px;
        }

        .add-sublibrarian-container .password-group .form-control {
            padding-right: 3.5rem;
        }

        /* Username Generation Button */
        .add-sublibrarian-container #generateUsername {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: none;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(10px);
            color: rgba(217, 217, 236, 0.9);
            min-width: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-top-right-radius: var(--border-radius);
            border-bottom-right-radius: var(--border-radius);
        }

        .add-sublibrarian-container #generateUsername:hover {
            background: rgba(255, 255, 255, 0.3);
            color: rgba(217, 217, 236, 1);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
        }

        .add-sublibrarian-container #generateUsername i {
            transition: transform 0.3s ease;
            font-size: 1rem;
        }

        .add-sublibrarian-container #generateUsername:hover i {
            transform: rotate(180deg);
        }

        .add-sublibrarian-container .input-group .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .add-sublibrarian-container .input-group {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            align-items: stretch;
            width: 100%;
        }

        .add-sublibrarian-container .required-field::after {
            content: "*";
            color: var(--danger) !important;
            margin-left: 4px;
        }

        /* Section headings */
        .add-sublibrarian-container h5 {
            color: white !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        /* Description text */
        .add-sublibrarian-container .text-muted {
            color: rgba(255, 255, 255, 0.8) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
        }

        /* Form help text */
        .add-sublibrarian-container .form-text {
            color: rgba(255, 255, 255, 0.7) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
        }

        /* Invalid feedback */
        .add-sublibrarian-container .invalid-feedback {
            color: var(--danger) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }

        /* Button close for alerts */
        .add-sublibrarian-container .btn-close {
            background: rgba(255, 255, 255, 0.2) !important;
            border-radius: 50% !important;
            opacity: 0.8 !important;
        }

        .add-sublibrarian-container .btn-close:hover {
            background: rgba(255, 255, 255, 0.3) !important;
            opacity: 1 !important;
        }

        /* Page Content Spacing */
        .add-sublibrarian-container .container-fluid {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        /* Ensure proper spacing from topbar */
        .add-sublibrarian-container .page-content {
            padding-top: 1rem;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .add-sublibrarian-container .container-fluid {
                padding-left: 0.5rem !important;
                padding-right: 0.5rem !important;
                padding-top: 1rem !important;
                padding-bottom: 1rem !important;
            }

            .add-sublibrarian-container .col-lg-8.col-md-10 {
                padding-left: 0.5rem !important;
                padding-right: 0.5rem !important;
            }

            .add-sublibrarian-container .card {
                margin: 1rem !important;
                border-radius: var(--border-radius) !important;
                max-width: 100% !important;
            }

            .add-sublibrarian-container .card-header {
                padding: 1rem 1.5rem !important;
            }

            .add-sublibrarian-container .card-body {
                padding: 1.5rem !important;
            }

            .add-sublibrarian-container .row.g-3 {
                margin-left: 0 !important;
                margin-right: 0 !important;
            }

            .add-sublibrarian-container .row.g-3 > * {
                padding-left: 0.5rem !important;
                padding-right: 0.5rem !important;
            }
        }

        @media (max-width: 576px) {
            .add-sublibrarian-container .container-fluid {
                padding-left: 0.25rem !important;
                padding-right: 0.25rem !important;
            }

            .add-sublibrarian-container .col-lg-8.col-md-10 {
                padding-left: 0.25rem !important;
                padding-right: 0.25rem !important;
            }

            .add-sublibrarian-container .card-header h4 {
                font-size: 1.1rem !important;
            }

            .add-sublibrarian-container .form-control {
                font-size: 0.875rem !important;
                padding: 0.75rem 0.875rem !important;
            }

            .add-sublibrarian-container .btn {
                padding: 0.75rem 1.25rem !important;
                font-size: 0.875rem !important;
            }

            .add-sublibrarian-container .input-group-text {
                width: 40px !important;
                font-size: 0.875rem !important;
            }

            .back-button-top {
                top: 1rem;
                left: 1rem;
                padding: 0.5rem 0.75rem;
                font-size: 0.9rem;
            }

            .back-button-top i {
                font-size: 1rem;
            }
        }

    </style>
</head>
<body>
    
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5L8WZWCC"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

    <!-- Back Button -->
    <a href="javascript:history.back()" class="back-button-top">
        <i class="fas fa-arrow-left"></i>
        <span>Back</span>
    </a>
<div class="add-sublibrarian-container">
<div class="container-fluid">
    <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="card shadow">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-user-plus me-2"></i>Add Sublibrarian</h4>
                    </div>
                    <div class="card-body p-4">
                        <p class="text-muted mb-4">
                            Add a sublibrarian to help manage your library. They will have access to manage students, handle daily transactions, and assist with library operations.
                        </p>
                        
                        <form method="post" onsubmit="return checkPasswordMatch()" class="needs-validation" novalidate>
                            {% csrf_token %}
                            
                            <!-- Personal Information Section -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-user me-2"></i>Personal Information</h5>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="first_name" class="form-label required-field">First Name</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" id="first_name" name="first_name" class="form-control" required pattern="[A-Za-z ]+" title="Please enter only alphabets">
                                            <div class="invalid-feedback">
                                                Please enter a valid first name (letters only).
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="last_name" class="form-label required-field">Last Name</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" id="last_name" name="last_name" class="form-control" required>
                                            <div class="invalid-feedback">
                                                Please enter a last name.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Account Information Section -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-id-card me-2"></i>Account Information</h5>
                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <label for="username" class="form-label required-field">Username</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-at"></i></span>
                                            <input type="text" id="username" name="username" class="form-control" placeholder="Choose a unique username" required
                                                   pattern="^[a-zA-Z0-9._-]+$"
                                                   title="Username can only contain letters, numbers, periods (.), hyphens (-), and underscores (_)">
                                            <button type="button" id="generateUsername" class="btn btn-outline-primary" title="Generate username suggestion">
                                                <i class="fas fa-refresh"></i>
                                            </button>
                                            <div class="invalid-feedback">
                                                Please choose a username.
                                            </div>
                                        </div>
                                        <small class="form-text text-muted mt-2">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Username can only contain letters, numbers, periods (.), hyphens (-), and underscores (_). Click <i class="fas fa-refresh"></i> for suggestions.
                                        </small>
                                        <div id="username_error" class="text-danger mt-1" style="display: none;"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="email" class="form-label required-field">Email Address</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                            <input type="email" id="email" name="email" class="form-control" required>
                                            <div class="invalid-feedback">
                                                Please enter a valid email address.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="phone" class="form-label required-field">Phone Number</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                            <input type="text" id="phone" name="phone" class="form-control" required>
                                            <div class="invalid-feedback">
                                                Please enter a valid phone number.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <label for="address" class="form-label required-field">Address</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                            <input type="text" id="address" name="address" class="form-control" required>
                                            <div class="invalid-feedback">
                                                Please enter an address.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Password Section -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-lock me-2"></i>Set Password</h5>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="password1" class="form-label required-field">Password</label>
                                        <div class="password-group">
                                            <input type="password" id="password1" name="password1" class="form-control" placeholder="Create a strong password" required>
                                            <button type="button" class="password-toggle password-toggle-1" aria-label="Show password" title="Show password">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <span style="display:block;color:#fff;font-size:0.92em;margin-top:0.25rem;text-shadow:0 1px 2px rgba(0,0,0,0.3);font-weight:500;">
                                            <b>Password must contain:</b>
                                            <ul style="margin:0.25em 0 0 1.2em;padding:0;list-style:disc;color:#fff;">
                                                <li>At least 8 characters</li>
                                                <li>One uppercase letter</li>
                                                <li>One number</li>
                                                <li>One special character</li>
                                            </ul>
                                        </span>
                                        <small id="passwordHelp" class="form-text text-muted">
                                            Password must be at least 8 characters with uppercase, number, and special character.
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="password2" class="form-label required-field">Confirm Password</label>
                                        <div class="password-group">
                                            <input type="password" id="password2" name="password2" class="form-control" placeholder="Confirm your password" required>
                                            <button type="button" class="password-toggle password-toggle-2" aria-label="Show password" title="Show password">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <small id="confirmPasswordHelp" class="form-text text-muted">
                                            Please confirm your password.
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Form Actions -->
                            <div class="d-flex justify-content-center mt-4">
                                <button type="submit" class="btn btn-primary" id="registerButton" disabled>
                                    <i class="fas fa-user-plus me-2"></i>Add Sublibrarian
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
    <!-- Bootstrap 5.3.3 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Initialize dark mode on page load based on existing preference
        function initializeDarkMode() {
            const darkMode = localStorage.getItem('darkMode');
            const body = document.body;

            if (darkMode === 'enabled') {
                body.classList.add('dark-mode');
            } else {
                body.classList.remove('dark-mode');
            }
        }

        // Initialize dark mode when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeDarkMode();
        });
    </script>

    <script>
    // Form validation and password handling
    (function() {
        'use strict';

        // Function to validate phone number
        function validatePhone(input) {
            input.value = input.value.replace(/\D/g, '').slice(0, 10);
        }

        // Function to validate password
        function validatePassword() {
            const password = document.getElementById('password1').value.trim();
            const passwordHelp = document.getElementById('passwordHelp');
            let valid = true;

            if (!/[A-Z]/.test(password)) {
                passwordHelp.textContent = "Password must contain at least one uppercase letter.";
                valid = false;
            } else if (!/\d/.test(password)) {
                passwordHelp.textContent = "Password must contain at least one number.";
                valid = false;
            } else if (!/[!@#$%^&*]/.test(password)) {
                passwordHelp.textContent = "Password must contain at least one special character (!@#$%^&*).";
                valid = false;
            } else if (password.length < 8) {
                passwordHelp.textContent = "Password must be at least 8 characters long.";
                valid = false;
            } else {
                passwordHelp.textContent = "Password is valid.";
                valid = true;
            }

            passwordHelp.classList.toggle('valid', valid);
            passwordHelp.classList.toggle('invalid', !valid);

            validateConfirmPassword();
            checkFormValidity();
        }

        // Function to validate confirm password
        function validateConfirmPassword() {
            const password = document.getElementById('password1').value.trim();
            const confirmPassword = document.getElementById('password2').value.trim();
            const confirmPasswordHelp = document.getElementById('confirmPasswordHelp');

            const isValid = password === confirmPassword && confirmPassword.length > 0;
            confirmPasswordHelp.textContent = isValid ? "Passwords match." : "Passwords do not match.";
            confirmPasswordHelp.classList.toggle('valid', isValid);
            confirmPasswordHelp.classList.toggle('invalid', !isValid);

            checkFormValidity();
        }

        // Function to validate username
        function validateUsername(input) {
            const usernameError = document.getElementById('username_error');
            const value = input.value;

            // Remove invalid characters (allow only letters, numbers, periods, hyphens, underscores)
            const cleanValue = value.replace(/[^a-zA-Z0-9._-]/g, '');

            if (value !== cleanValue) {
                input.value = cleanValue;
                usernameError.textContent = "Only letters, numbers, periods (.), hyphens (-), and underscores (_) are allowed.";
                usernameError.style.display = 'block';
                input.style.borderColor = '#dc3545';
                return false;
            } else {
                usernameError.style.display = 'none';
                input.style.borderColor = '';
                return true;
            }
        }

        // Function to check form validity
        function checkFormValidity() {
            const passwordHelp = document.getElementById('passwordHelp').classList.contains('valid');
            const confirmPasswordHelp = document.getElementById('confirmPasswordHelp').classList.contains('valid');
            const registerButton = document.getElementById('registerButton');

            registerButton.disabled = !(passwordHelp && confirmPasswordHelp);
        }

        // Function to toggle password visibility
        function togglePasswordVisibility(inputId, toggleClass) {
            const passwordField = document.getElementById(inputId);
            const toggleButton = document.querySelector(toggleClass);

            if (!passwordField || !toggleButton) {
                console.error('Password field or toggle button not found:', inputId, toggleClass);
                return;
            }

            const icon = toggleButton.querySelector('i');
            console.log('Toggling password visibility for:', inputId, 'Current type:', passwordField.type);

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleButton.setAttribute('aria-label', 'Hide password');
                toggleButton.setAttribute('title', 'Hide password');
                if (icon) {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                }
            } else {
                passwordField.type = 'password';
                toggleButton.setAttribute('aria-label', 'Show password');
                toggleButton.setAttribute('title', 'Show password');
                if (icon) {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }
        }

        // Function to check password match on form submission
        window.checkPasswordMatch = function() {
            const password = document.getElementById('password1').value.trim();
            const confirmPassword = document.getElementById('password2').value.trim();
            if (password === confirmPassword) {
                return true; // Allow form submission
            } else {
                alert("Passwords do not match.");
                return false; // Prevent form submission
            }
        };

        // Username generation functionality
        function generateUsername() {
            const firstName = document.getElementById('first_name') ? document.getElementById('first_name').value.trim() : '';
            const lastName = document.getElementById('last_name') ? document.getElementById('last_name').value.trim() : '';

            let baseName = '';

            if (!firstName && !lastName) {
                // If no name is provided, generate a random base
                const randomChars = 'abcdefghijklmnopqrstuvwxyz';
                for (let i = 0; i < 3; i++) {
                    baseName += randomChars.charAt(Math.floor(Math.random() * randomChars.length));
                }
            } else {
                // Use name to generate base
                if (firstName) {
                    baseName += firstName.toLowerCase().replace(/[^a-z]/g, '').substring(0, 3);
                }
                if (lastName) {
                    baseName += lastName.toLowerCase().replace(/[^a-z]/g, '').substring(0, 2);
                }

                // Ensure minimum base length
                if (baseName.length < 2) {
                    baseName = 'user';
                }
            }

            // Always add one special character (- or _)
            const separators = ['_', '-'];
            const separator = separators[Math.floor(Math.random() * separators.length)];

            // Always add at least one number (1-2 digits)
            const numbers = Math.floor(Math.random() * 99) + 1;

            // Build username: base + separator + number
            let username = baseName + separator + numbers;

            // Ensure length is between 5-7 characters
            if (username.length > 7) {
                // Trim base name if too long
                const excess = username.length - 7;
                baseName = baseName.substring(0, baseName.length - excess);
                username = baseName + separator + numbers;
            } else if (username.length < 5) {
                // Add more numbers if too short
                const additionalNum = Math.floor(Math.random() * 9);
                username += additionalNum;
            }

            // Final check: ensure we have at least one letter, one separator, and one number
            const hasLetter = /[a-z]/.test(username);
            const hasSeparator = /[_-]/.test(username);
            const hasNumber = /[0-9]/.test(username);

            if (!hasLetter || !hasSeparator || !hasNumber) {
                // Fallback: create a guaranteed valid username
                const fallbackBase = hasLetter ? baseName.substring(0, 2) : 'usr';
                const fallbackSep = hasSeparator ? separator : '_';
                const fallbackNum = hasNumber ? numbers : Math.floor(Math.random() * 99) + 1;
                username = fallbackBase + fallbackSep + fallbackNum;
            }

            return username;
        }

        // Function to hide alerts after 5 seconds
        function setupAlertDismissal() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        }

        // Set up event listeners when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Phone number validation
            const phoneInput = document.getElementById('phone');
            if (phoneInput) {
                phoneInput.addEventListener('input', function() {
                    validatePhone(this);
                });
            }

            // Password validation
            const passwordInput = document.getElementById('password1');
            if (passwordInput) {
                passwordInput.addEventListener('input', validatePassword);
            }

            // Confirm password validation
            const confirmPasswordInput = document.getElementById('password2');
            if (confirmPasswordInput) {
                confirmPasswordInput.addEventListener('input', validateConfirmPassword);
            }

            // Username validation
            const usernameInput = document.getElementById('username');
            if (usernameInput) {
                usernameInput.addEventListener('input', function() {
                    validateUsername(this);
                });
            }

            // Toggle password visibility
            const togglePasswordButton = document.querySelector('.password-toggle-1');
            if (togglePasswordButton) {
                console.log('Password toggle button 1 found, adding event listener');
                togglePasswordButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Password toggle 1 clicked');
                    togglePasswordVisibility('password1', '.password-toggle-1');
                });
            } else {
                console.error('Password toggle button 1 not found');
            }

            const togglePassword2Button = document.querySelector('.password-toggle-2');
            if (togglePassword2Button) {
                console.log('Password toggle button 2 found, adding event listener');
                togglePassword2Button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Password toggle 2 clicked');
                    togglePasswordVisibility('password2', '.password-toggle-2');
                });
            } else {
                console.error('Password toggle button 2 not found');
            }

            // Username generation functionality
            const generateUsernameBtn = document.getElementById('generateUsername');
            // usernameInput already declared above
            const firstNameInput = document.getElementById('first_name');
            const lastNameInput = document.getElementById('last_name');

            if (generateUsernameBtn && usernameInput) {
                generateUsernameBtn.addEventListener('click', function() {
                    const suggestedUsername = generateUsername();
                    usernameInput.value = suggestedUsername;

                    // Add animation effect
                    generateUsernameBtn.style.transform = 'rotate(180deg)';
                    setTimeout(() => {
                        generateUsernameBtn.style.transform = 'rotate(0deg)';
                    }, 300);

                    // Trigger validation if needed
                    usernameInput.dispatchEvent(new Event('input'));
                });
            }

            // Auto-generate username when name fields change
            if (firstNameInput && lastNameInput && usernameInput) {
                function autoSuggestUsername() {
                    if (!usernameInput.value.trim()) {
                        const suggestedUsername = generateUsername();
                        usernameInput.placeholder = `Suggestion: ${suggestedUsername}`;
                    }
                }

                firstNameInput.addEventListener('input', autoSuggestUsername);
                lastNameInput.addEventListener('input', autoSuggestUsername);
            }

            // Set up alert dismissal
            setupAlertDismissal();

            // Bootstrap form validation
            const forms = document.querySelectorAll('.needs-validation');
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        });
    })();
    </script>
</body>
</html>
