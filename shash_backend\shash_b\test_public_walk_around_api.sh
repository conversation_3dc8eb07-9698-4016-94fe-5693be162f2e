#!/bin/bash

# Walk-Around Images API Public Access Testing Script
# This script tests the publicly accessible API endpoints

echo "🌐 Testing Walk-Around Images API - Public Access"
echo "================================================="

BASE_URL="http://localhost:8000"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ $2${NC}"
    else
        echo -e "${RED}✗ $2${NC}"
    fi
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

# Test 1: Test public access without authentication
echo ""
print_info "Test 1: Testing public access (no authentication required)"
response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/api/customrcare/walk-around-images/")
if [ "$response" = "200" ]; then
    print_status 0 "Public access working correctly (HTTP $response)"
else
    print_status 1 "Public access not working (HTTP $response)"
fi

# Test 2: Test statistics endpoint without authentication
echo ""
print_info "Test 2: Testing statistics endpoint without authentication"
STATS_RESPONSE=$(curl -s -X GET "$BASE_URL/api/customrcare/walk-around-images/stats/")
echo "Stats response: $STATS_RESPONSE"

TOTAL_IMAGES=$(echo "$STATS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('total_images', 0))
except:
    print(0)
")

if [ "$TOTAL_IMAGES" -ge 0 ]; then
    print_status 0 "Statistics endpoint accessible (Total images: $TOTAL_IMAGES)"
else
    print_status 1 "Statistics endpoint not accessible"
fi

# Test 3: Test anonymous image creation
echo ""
print_info "Test 3: Testing anonymous image creation"

# Create a simple test image file
echo "Creating test image..."
python3 -c "
from PIL import Image
import io

# Create a simple test image
img = Image.new('RGB', (200, 200), color='blue')
img.save('anonymous_test_image.jpg', 'JPEG')
print('Test image created: anonymous_test_image.jpg')
"

if [ -f "anonymous_test_image.jpg" ]; then
    CREATE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/customrcare/walk-around-images/" \
        -F "image=@anonymous_test_image.jpg" \
        -F "title=Anonymous Test Image" \
        -F "description=This is an anonymous test image" \
        -F "status=active")
    
    echo "Anonymous create response: $CREATE_RESPONSE"
    
    # Extract image ID
    IMAGE_ID=$(echo "$CREATE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('id', ''))
except:
    print('')
")
    
    USER_USERNAME=$(echo "$CREATE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('user_username', ''))
except:
    print('')
")
    
    if [ -n "$IMAGE_ID" ] && [ "$USER_USERNAME" = "Anonymous" ]; then
        print_status 0 "Successfully created anonymous image (ID: $IMAGE_ID, User: $USER_USERNAME)"
    else
        print_status 1 "Failed to create anonymous image"
    fi
else
    print_status 1 "Failed to create test image file"
fi

# Test 4: Test listing all images (should include anonymous ones)
echo ""
print_info "Test 4: Testing image listing (should include all images)"

LIST_RESPONSE=$(curl -s -X GET "$BASE_URL/api/customrcare/walk-around-images/")

IMAGE_COUNT=$(echo "$LIST_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        print(len(data))
    elif 'results' in data:
        print(len(data['results']))
    else:
        print(0)
except:
    print(0)
")

if [ "$IMAGE_COUNT" -gt 0 ]; then
    print_status 0 "Successfully listed $IMAGE_COUNT walk-around images"
else
    print_status 1 "No images found or failed to list images"
fi

# Test 5: Test updating image status without authentication
if [ -n "$IMAGE_ID" ]; then
    echo ""
    print_info "Test 5: Testing status update without authentication"
    
    UPDATE_RESPONSE=$(curl -s -X PATCH "$BASE_URL/api/customrcare/walk-around-images/$IMAGE_ID/status/" \
        -H "Content-Type: application/json" \
        -d '{"status": "inactive"}')
    
    echo "Update response: $UPDATE_RESPONSE"
    
    UPDATED_STATUS=$(echo "$UPDATE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('status', ''))
except:
    print('')
")
    
    if [ "$UPDATED_STATUS" = "inactive" ]; then
        print_status 0 "Successfully updated image status to inactive"
    else
        print_status 1 "Failed to update image status"
    fi
fi

# Test 6: Test deleting image without authentication
if [ -n "$IMAGE_ID" ]; then
    echo ""
    print_info "Test 6: Testing image deletion without authentication"
    
    DELETE_RESPONSE=$(curl -s -X DELETE "$BASE_URL/api/customrcare/walk-around-images/$IMAGE_ID/")
    
    echo "Delete response: $DELETE_RESPONSE"
    
    DELETE_MESSAGE=$(echo "$DELETE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('message', ''))
except:
    print('')
")
    
    if [ -n "$DELETE_MESSAGE" ]; then
        print_status 0 "Successfully deleted image: $DELETE_MESSAGE"
    else
        print_status 1 "Failed to delete image or no message returned"
    fi
fi

# Cleanup
echo ""
print_info "Cleaning up test files..."
rm -f anonymous_test_image.jpg
print_status 0 "Test files cleaned up"

echo ""
echo "================================================="
echo "🎉 Public Walk-Around Images API testing completed!"
echo "================================================="
