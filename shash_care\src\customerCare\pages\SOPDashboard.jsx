import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Form, Modal, <PERSON><PERSON>, <PERSON>, <PERSON>ge } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { 
  fetchSOPs, 
  createSOP, 
  updateSOP, 
  deleteSOP,
  clearError 
} from "../../redux/slice/sopSlice";
import NavigationBar from "../../commonComponents/NavigationBar";
import { toast } from "react-hot-toast";
import { FaFilePdf, FaPlus, FaEdit, FaTrash, FaDownload, FaEye, FaFilter } from "react-icons/fa";
import Swal from "sweetalert2";

const SOPDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { sops, isLoading, error } = useSelector((state) => state.sops);
  const { access } = useSelector((state) => state.customerCare);

  // Local state
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedSOP, setSelectedSOP] = useState(null);
  const [filters, setFilters] = useState({
    access: '',
    search: ''
  });
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    pdf: null,
    access: 'all'
  });

  // Check authentication
  useEffect(() => {
    if (!access) {
      toast.error("Please login first");
      navigate("/customer_care_login");
      return;
    }
    fetchSOPsData();
  }, [access, navigate, dispatch]);

  // Fetch SOPs
  const fetchSOPsData = () => {
    dispatch(fetchSOPs(filters));
  };

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // Apply filters
  const applyFilters = () => {
    fetchSOPsData();
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({ access: '', search: '' });
    dispatch(fetchSOPs({}));
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle file change
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (file.type !== 'application/pdf') {
        toast.error('Only PDF files are allowed');
        e.target.value = '';
        return;
      }
      // Validate file size (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size must be less than 10MB');
        e.target.value = '';
        return;
      }
      setFormData(prev => ({ ...prev, pdf: file }));
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      pdf: null,
      access: 'all'
    });
  };

  // Handle create SOP
  const handleCreateSOP = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Please enter SOP name');
      return;
    }
    
    if (!formData.pdf) {
      toast.error('Please select a PDF file');
      return;
    }

    const formDataToSend = new FormData();
    formDataToSend.append('name', formData.name);
    formDataToSend.append('pdf', formData.pdf);
    formDataToSend.append('access', formData.access);

    try {
      await dispatch(createSOP(formDataToSend)).unwrap();
      toast.success('SOP created successfully!');
      setShowCreateModal(false);
      resetForm();
      fetchSOPsData();
    } catch (error) {
      toast.error('Failed to create SOP');
    }
  };

  // Handle edit SOP
  const handleEditSOP = (sop) => {
    setSelectedSOP(sop);
    setFormData({
      name: sop.name,
      pdf: null,
      access: sop.access
    });
    setShowEditModal(true);
  };

  // Handle update SOP
  const handleUpdateSOP = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Please enter SOP name');
      return;
    }

    const formDataToSend = new FormData();
    formDataToSend.append('name', formData.name);
    formDataToSend.append('access', formData.access);
    
    // Only append PDF if a new file is selected
    if (formData.pdf) {
      formDataToSend.append('pdf', formData.pdf);
    }

    try {
      await dispatch(updateSOP({ id: selectedSOP.id, formData: formDataToSend })).unwrap();
      toast.success('SOP updated successfully!');
      setShowEditModal(false);
      resetForm();
      setSelectedSOP(null);
      fetchSOPsData();
    } catch (error) {
      toast.error('Failed to update SOP');
    }
  };

  // Handle delete SOP
  const handleDeleteSOP = async (id, name) => {
    const result = await Swal.fire({
      title: 'Delete SOP?',
      text: `Are you sure you want to delete "${name}"? This action cannot be undone.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Delete!'
    });

    if (result.isConfirmed) {
      try {
        await dispatch(deleteSOP(id)).unwrap();
        toast.success('SOP deleted successfully!');
        fetchSOPsData();
      } catch (error) {
        toast.error('Failed to delete SOP');
      }
    }
  };

  // Handle view/download PDF
  const handleViewPDF = (pdfUrl, name) => {
    // Open PDF in new tab
    window.open(pdfUrl, '_blank');
  };

  // Get access badge variant
  const getAccessBadge = (access) => {
    switch (access) {
      case 'contributor': return 'primary';
      case 'customer': return 'success';
      case 'all': return 'info';
      default: return 'secondary';
    }
  };

  // Format access text
  const formatAccess = (access) => {
    switch (access) {
      case 'contributor': return 'Contributors';
      case 'customer': return 'Customer Care';
      case 'all': return 'All Users';
      default: return access;
    }
  };

  return (
    <>
      <NavigationBar />
      <Container className="my-4">
        <Row>
          <Col>
            <div className="d-flex justify-content-between align-items-center mb-4">
              <h2 className="text-success mb-0">
                <FaFilePdf className="me-2" />
                SOP Management Dashboard
              </h2>
              <Button variant="success" onClick={() => setShowCreateModal(true)}>
                <FaPlus className="me-2" />
                Add New SOP
              </Button>
            </div>
          </Col>
        </Row>

        {/* Filters */}
        <Card className="mb-4">
          <Card.Header>
            <FaFilter className="me-2" />
            Filters
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Access Level</Form.Label>
                  <Form.Select
                    name="access"
                    value={filters.access}
                    onChange={handleFilterChange}
                  >
                    <option value="">All Access Levels</option>
                    <option value="contributor">Contributors</option>
                    <option value="customer">Customer Care</option>
                    <option value="all">All Users</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Search</Form.Label>
                  <Form.Control
                    type="text"
                    name="search"
                    value={filters.search}
                    onChange={handleFilterChange}
                    placeholder="Search by SOP name..."
                  />
                </Form.Group>
              </Col>
              <Col md={2} className="d-flex align-items-end">
                <div className="d-flex gap-2 mb-3">
                  <Button variant="primary" onClick={applyFilters}>
                    Apply
                  </Button>
                  <Button variant="outline-secondary" onClick={clearFilters}>
                    Clear
                  </Button>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert variant="danger" dismissible onClose={() => dispatch(clearError())}>
            {typeof error === 'string' ? error : 'An error occurred'}
          </Alert>
        )}

        {/* Loading Spinner */}
        {isLoading && (
          <div className="text-center my-4">
            <Spinner animation="border" variant="primary" />
            <p className="mt-2">Loading SOPs...</p>
          </div>
        )}

        {/* SOPs List */}
        <Row>
          {sops.length === 0 && !isLoading ? (
            <Col>
              <Alert variant="info" className="text-center">
                No SOPs found. Click "Add New SOP" to create your first SOP.
              </Alert>
            </Col>
          ) : (
            sops.map((sop) => (
              <Col md={6} lg={4} key={sop.id} className="mb-4">
                <Card className="h-100 shadow-sm">
                  <Card.Header className="d-flex justify-content-between align-items-center">
                    <Badge bg={getAccessBadge(sop.access)}>
                      {formatAccess(sop.access)}
                    </Badge>
                    <small className="text-muted">
                      {new Date(sop.created).toLocaleDateString()}
                    </small>
                  </Card.Header>
                  <Card.Body>
                    <Card.Title className="h6">{sop.name}</Card.Title>
                    <Card.Text className="small text-muted">
                      <strong>Created by:</strong> {sop.created_by_username}<br />
                      <strong>Last updated:</strong> {new Date(sop.last_update).toLocaleDateString()}<br />
                      {sop.updated_by_username && (
                        <>
                          <strong>Updated by:</strong> {sop.updated_by_username}
                        </>
                      )}
                    </Card.Text>
                  </Card.Body>
                  <Card.Footer>
                    <div className="d-flex gap-2 flex-wrap">
                      <Button
                        size="sm"
                        variant="primary"
                        onClick={() => handleViewPDF(sop.pdf, sop.name)}
                      >
                        <FaEye className="me-1" />
                        View PDF
                      </Button>
                      <Button
                        size="sm"
                        variant="outline-primary"
                        onClick={() => handleEditSOP(sop)}
                      >
                        <FaEdit className="me-1" />
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="outline-danger"
                        onClick={() => handleDeleteSOP(sop.id, sop.name)}
                      >
                        <FaTrash className="me-1" />
                        Delete
                      </Button>
                    </div>
                  </Card.Footer>
                </Card>
              </Col>
            ))
          )}
        </Row>

        {/* Create SOP Modal */}
        <Modal show={showCreateModal} onHide={() => setShowCreateModal(false)} size="lg">
          <Modal.Header closeButton>
            <Modal.Title>Create New SOP</Modal.Title>
          </Modal.Header>
          <Form onSubmit={handleCreateSOP}>
            <Modal.Body>
              <Form.Group className="mb-3">
                <Form.Label>SOP Name *</Form.Label>
                <Form.Control
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter SOP name"
                  required
                />
              </Form.Group>
              
              <Form.Group className="mb-3">
                <Form.Label>Access Level *</Form.Label>
                <Form.Select
                  name="access"
                  value={formData.access}
                  onChange={handleInputChange}
                  required
                >
                  <option value="all">All Users</option>
                  <option value="contributor">Contributors Only</option>
                  <option value="customer">Customer Care Only</option>
                </Form.Select>
                <Form.Text className="text-muted">
                  Choose who can access this SOP document
                </Form.Text>
              </Form.Group>
              
              <Form.Group className="mb-3">
                <Form.Label>PDF File *</Form.Label>
                <Form.Control
                  type="file"
                  accept=".pdf"
                  onChange={handleFileChange}
                  required
                />
                <Form.Text className="text-muted">
                  Only PDF files are allowed. Maximum file size: 10MB
                </Form.Text>
              </Form.Group>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={() => setShowCreateModal(false)}>
                Cancel
              </Button>
              <Button type="submit" variant="success">
                Create SOP
              </Button>
            </Modal.Footer>
          </Form>
        </Modal>

        {/* Edit SOP Modal */}
        <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
          <Modal.Header closeButton>
            <Modal.Title>Edit SOP</Modal.Title>
          </Modal.Header>
          <Form onSubmit={handleUpdateSOP}>
            <Modal.Body>
              <Form.Group className="mb-3">
                <Form.Label>SOP Name *</Form.Label>
                <Form.Control
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter SOP name"
                  required
                />
              </Form.Group>
              
              <Form.Group className="mb-3">
                <Form.Label>Access Level *</Form.Label>
                <Form.Select
                  name="access"
                  value={formData.access}
                  onChange={handleInputChange}
                  required
                >
                  <option value="all">All Users</option>
                  <option value="contributor">Contributors Only</option>
                  <option value="customer">Customer Care Only</option>
                </Form.Select>
              </Form.Group>
              
              <Form.Group className="mb-3">
                <Form.Label>PDF File (Optional)</Form.Label>
                <Form.Control
                  type="file"
                  accept=".pdf"
                  onChange={handleFileChange}
                />
                <Form.Text className="text-muted">
                  Leave empty to keep current PDF. Only PDF files are allowed. Maximum file size: 10MB
                </Form.Text>
              </Form.Group>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={() => setShowEditModal(false)}>
                Cancel
              </Button>
              <Button type="submit" variant="success">
                Update SOP
              </Button>
            </Modal.Footer>
          </Form>
        </Modal>
      </Container>
    </>
  );
};

export default SOPDashboard;
