# Generated by Django 4.2.13 on 2025-07-17 11:49

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('librarian', '0002_notificationcategory_notificationlog_and_more'),
        ('studentsData', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_paid', models.IntegerField()),
                ('payment_date', models.DateField(default=django.utils.timezone.now)),
                ('payment_mode', models.CharField(choices=[('Cash', 'Cash'), ('Online', 'Online'), ('UPI', 'UPI'), ('Card', 'Card'), ('Bank Transfer', 'Bank Transfer'), ('Cheque', 'Cheque')], default='Cash', max_length=50)),
                ('next_commitment_date', models.DateField(blank=True, null=True)),
                ('transaction_id', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-payment_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RegistrationFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ease_rating', models.IntegerField(choices=[(0, '0'), (1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5'), (6, '6'), (7, '7'), (8, '8'), (9, '9'), (10, '10')], help_text='0 = Very Difficult, 10 = Extremely Easy')),
                ('time_taken', models.CharField(choices=[('less_than_1', 'Less than 1 minute'), ('1_to_2', '1–2 minutes'), ('3_to_5', '3–5 minutes'), ('more_than_5', 'More than 5 minutes')], help_text='Time taken to complete registration', max_length=20)),
                ('faced_issues', models.BooleanField(default=False, help_text='Whether user faced any issues')),
                ('issue_description', models.TextField(blank=True, help_text='Description of issues faced (if any)', max_length=500, null=True)),
                ('design_rating', models.IntegerField(choices=[(1, '1 star'), (2, '2 stars'), (3, '3 stars'), (4, '4 stars'), (5, '5 stars')], help_text='Visual appeal rating from 1 to 5 stars')),
                ('device_used', models.CharField(choices=[('mobile', 'Mobile'), ('tablet', 'Tablet'), ('laptop_desktop', 'Laptop/Desktop'), ('other', 'Other')], help_text='Device used for registration', max_length=20)),
                ('qr_easy_to_scan', models.BooleanField(default=True, help_text='Whether QR code was easy to scan')),
                ('qr_problem_description', models.TextField(blank=True, help_text='Description of QR code problems (if any)', max_length=300, null=True)),
                ('suggestions', models.TextField(blank=True, help_text='Optional suggestions for improvement', max_length=1000, null=True)),
                ('would_recommend', models.BooleanField(default=True, help_text='Whether user would recommend the system')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address of the user', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='Browser user agent string', null=True)),
                ('submitted_at', models.DateTimeField(auto_now_add=True, help_text='When feedback was submitted')),
            ],
            options={
                'verbose_name': 'Registration Feedback',
                'verbose_name_plural': 'Registration Feedbacks',
                'ordering': ['-submitted_at'],
            },
        ),
        migrations.AddField(
            model_name='invoice',
            name='is_paid_in_full',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='invoice',
            name='payment_status',
            field=models.CharField(choices=[('Unpaid', 'Unpaid'), ('Partially Paid', 'Partially Paid'), ('Paid', 'Paid')], default='Unpaid', max_length=20),
        ),
        migrations.AddField(
            model_name='invoice',
            name='remaining_due',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='invoice',
            name='total_paid',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='studentdata',
            name='identity_number',
            field=models.CharField(blank=True, help_text='Identity document number', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='studentdata',
            name='identity_type',
            field=models.CharField(blank=True, choices=[('pan', 'PAN Card'), ('aadhaar', 'Aadhaar Card'), ('driving_license', 'Driving License'), ('voter_id', 'Voter ID Card')], help_text='Type of identity document', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='tempstudentdata',
            name='identity_number',
            field=models.CharField(blank=True, help_text='Identity document number', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='tempstudentdata',
            name='identity_type',
            field=models.CharField(blank=True, choices=[('pan', 'PAN Card'), ('aadhaar', 'Aadhaar Card'), ('driving_license', 'Driving License'), ('voter_id', 'Voter ID Card')], help_text='Type of identity document', max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='courses',
            name='name',
            field=models.CharField(max_length=225, unique=True),
        ),
        migrations.AlterField(
            model_name='invoice',
            name='mode_pay',
            field=models.CharField(default='Cash', max_length=225),
        ),
        migrations.AlterField(
            model_name='months',
            name='name',
            field=models.CharField(max_length=225, unique=True),
        ),
        migrations.AlterField(
            model_name='registrationfee',
            name='reg_unique_id',
            field=models.CharField(blank=True, max_length=225, unique=True),
        ),
        migrations.AlterField(
            model_name='shift',
            name='name',
            field=models.CharField(max_length=225),
        ),
        migrations.AlterField(
            model_name='shift',
            name='time_range',
            field=models.CharField(max_length=225),
        ),
        migrations.AlterField(
            model_name='states',
            name='name',
            field=models.CharField(max_length=225, unique=True),
        ),
        migrations.AlterField(
            model_name='studentdata',
            name='city',
            field=models.CharField(max_length=225),
        ),
        migrations.AlterField(
            model_name='studentdata',
            name='email',
            field=models.EmailField(max_length=225),
        ),
        migrations.AlterField(
            model_name='studentdata',
            name='f_name',
            field=models.CharField(blank=True, default=None, max_length=225, null=True),
        ),
        migrations.AlterField(
            model_name='studentdata',
            name='gender',
            field=models.CharField(max_length=225),
        ),
        migrations.AlterField(
            model_name='studentdata',
            name='locality',
            field=models.CharField(max_length=225),
        ),
        migrations.AlterField(
            model_name='studentdata',
            name='name',
            field=models.CharField(max_length=225),
        ),
        migrations.AlterField(
            model_name='tempstudentdata',
            name='city',
            field=models.CharField(max_length=225),
        ),
        migrations.AlterField(
            model_name='tempstudentdata',
            name='email',
            field=models.EmailField(max_length=225),
        ),
        migrations.AlterField(
            model_name='tempstudentdata',
            name='f_name',
            field=models.CharField(blank=True, default=None, max_length=225, null=True),
        ),
        migrations.AlterField(
            model_name='tempstudentdata',
            name='gender',
            field=models.CharField(max_length=225),
        ),
        migrations.AlterField(
            model_name='tempstudentdata',
            name='locality',
            field=models.CharField(max_length=225),
        ),
        migrations.AlterField(
            model_name='tempstudentdata',
            name='name',
            field=models.CharField(max_length=225),
        ),
        migrations.AddConstraint(
            model_name='studentdata',
            constraint=models.UniqueConstraint(condition=models.Q(('identity_number__isnull', False), models.Q(('identity_number', ''), _negated=True)), fields=('identity_number',), name='unique_identity_number_studentdata'),
        ),
        migrations.AddConstraint(
            model_name='tempstudentdata',
            constraint=models.UniqueConstraint(condition=models.Q(('identity_number__isnull', False), models.Q(('identity_number', ''), _negated=True)), fields=('identity_number',), name='unique_identity_number_tempstudentdata'),
        ),
        migrations.AddField(
            model_name='registrationfeedback',
            name='librarian',
            field=models.ForeignKey(help_text='Library where registration was done', on_delete=django.db.models.deletion.CASCADE, to='librarian.librarian_param'),
        ),
        migrations.AddField(
            model_name='registrationfeedback',
            name='temp_student',
            field=models.ForeignKey(blank=True, help_text='Associated temporary student record', null=True, on_delete=django.db.models.deletion.CASCADE, to='studentsData.tempstudentdata'),
        ),
        migrations.AddField(
            model_name='payment',
            name='invoice',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='studentsData.invoice'),
        ),
    ]
