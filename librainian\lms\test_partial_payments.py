#!/usr/bin/env python3
"""
Test script for partial payments functionality
Run this script to test the partial payment features
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

from django.contrib.auth.models import User
from studentsData.models import StudentData, Invoice, Payment, Shift, Months
from librarian.models import Librarian_param

def get_existing_data():
    """Get existing data for testing"""
    print("Looking for existing data...")

    # Try to find existing librarian
    librarian = Librarian_param.objects.first()
    if not librarian:
        print("❌ No librarian found. Please create a librarian first.")
        return None, None, None, None

    # Try to find existing shift
    shift = Shift.objects.filter(librarian=librarian).first()
    if not shift:
        print("❌ No shift found. Creating test shift...")
        shift = Shift.objects.create(
            name='Test Morning',
            time_range='9:00 AM - 1:00 PM',
            price=1000,
            librarian=librarian
        )

    # Try to find existing month
    month = Months.objects.first()
    if not month:
        print("❌ No month found. Creating test month...")
        month = Months.objects.create(name='Test January')

    # Try to find existing student
    student = StudentData.objects.filter(librarian=librarian).first()
    if not student:
        print("❌ No student found. Creating test student...")
        student = StudentData.objects.create(
            name='Test Student',
            email='<EMAIL>',
            mobile='1234567890',
            registration_fee=500,
            librarian=librarian
        )

    print(f"✅ Using librarian: {librarian.library_name}")
    print(f"✅ Using shift: {shift.name}")
    print(f"✅ Using month: {month.name}")
    print(f"✅ Using student: {student.name}")

    return librarian, shift, month, student

def test_invoice_creation():
    """Test invoice creation with partial payment fields"""
    print("\n=== Testing Invoice Creation ===")

    librarian, shift, month, student = get_existing_data()
    if not all([librarian, shift, month, student]):
        print("❌ Cannot proceed without required data")
        return None

    # Create invoice
    invoice = Invoice.objects.create(
        student=student,
        due_date=datetime.now().date() + timedelta(days=30),
        total_amount=2000,
        discount_amount=0,
        mode_pay='Cash',
        description='Test invoice for partial payments'
    )
    invoice.shift.add(shift)
    invoice.months.add(month)

    print(f"✅ Created invoice: {invoice.invoice_id}")
    print(f"   Total Amount: ₹{invoice.total_amount}")
    print(f"   Payment Status: {invoice.payment_status}")
    print(f"   Is Paid in Full: {invoice.is_paid_in_full}")
    print(f"   Total Paid: ₹{invoice.total_paid}")
    print(f"   Remaining Due: ₹{invoice.remaining_due}")

    return invoice

def test_partial_payment():
    """Test partial payment functionality"""
    print("\n=== Testing Partial Payment ===")
    
    invoice = test_invoice_creation()
    
    # Create first partial payment
    payment1 = Payment.objects.create(
        invoice=invoice,
        amount_paid=800,
        payment_mode='Cash',
        notes='First partial payment'
    )
    
    # Refresh invoice to see updated values
    invoice.refresh_from_db()
    
    print(f"✅ Created first payment: ₹{payment1.amount_paid}")
    print(f"   Invoice Payment Status: {invoice.payment_status}")
    print(f"   Total Paid: ₹{invoice.total_paid}")
    print(f"   Remaining Due: ₹{invoice.remaining_due}")
    print(f"   Payment Percentage: {invoice.get_payment_percentage()}%")
    
    # Create second partial payment
    payment2 = Payment.objects.create(
        invoice=invoice,
        amount_paid=700,
        payment_mode='Online',
        notes='Second partial payment'
    )
    
    # Refresh invoice again
    invoice.refresh_from_db()
    
    print(f"✅ Created second payment: ₹{payment2.amount_paid}")
    print(f"   Invoice Payment Status: {invoice.payment_status}")
    print(f"   Total Paid: ₹{invoice.total_paid}")
    print(f"   Remaining Due: ₹{invoice.remaining_due}")
    print(f"   Payment Percentage: {invoice.get_payment_percentage()}%")
    print(f"   Is Paid in Full: {invoice.is_paid_in_full}")
    
    return invoice

def test_overpayment_protection():
    """Test overpayment protection"""
    print("\n=== Testing Overpayment Protection ===")

    librarian, shift, month, student = get_existing_data()
    if not all([librarian, shift, month, student]):
        print("❌ Cannot proceed without required data")
        return

    # Create small invoice for overpayment test
    invoice = Invoice.objects.create(
        student=student,
        due_date=datetime.now().date() + timedelta(days=30),
        total_amount=500,
        discount_amount=0,
        mode_pay='Cash',
        description='Small invoice for overpayment test'
    )
    invoice.shift.add(shift)
    invoice.months.add(month)
    
    print(f"Created test invoice: ₹{invoice.total_amount}")
    
    # Try to create payment larger than remaining amount
    try:
        payment = Payment.objects.create(
            invoice=invoice,
            amount_paid=600,  # More than total amount
            payment_mode='Cash',
            notes='Overpayment test'
        )
        
        invoice.refresh_from_db()
        print(f"⚠️  Overpayment allowed: ₹{payment.amount_paid}")
        print(f"   Total Paid: ₹{invoice.total_paid}")
        print(f"   Remaining Due: ₹{invoice.remaining_due}")
        
    except Exception as e:
        print(f"❌ Overpayment blocked: {str(e)}")

def test_payment_history():
    """Test payment history functionality"""
    print("\n=== Testing Payment History ===")
    
    invoice = test_partial_payment()
    
    payments = invoice.payment_set.all().order_by('-payment_date')
    
    print(f"Payment history for invoice {invoice.invoice_id}:")
    for i, payment in enumerate(payments, 1):
        print(f"   {i}. ₹{payment.amount_paid} - {payment.payment_mode} - {payment.payment_date}")
        if payment.notes:
            print(f"      Notes: {payment.notes}")

def run_all_tests():
    """Run all partial payment tests"""
    print("🚀 Starting Partial Payment Tests")
    print("=" * 50)
    
    try:
        test_invoice_creation()
        test_partial_payment()
        test_overpayment_protection()
        test_payment_history()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        print("\nPartial payment functionality is working correctly:")
        print("• Invoice creation with payment tracking ✅")
        print("• Partial payment recording ✅")
        print("• Payment status updates ✅")
        print("• Payment percentage calculation ✅")
        print("• Payment history tracking ✅")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    run_all_tests()
