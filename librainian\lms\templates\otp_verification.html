<!DOCTYPE html>
<html lang="en">
<head>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5L8WZWCC');</script>
<!-- End Google Tag Manager -->

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Basic Meta Tags -->
    <title>OTP Verification - Librainian</title>
    <meta name="description" content="Verify your OTP and set a new password for your Librainian account.">
    <meta name="robots" content="noindex, nofollow">
    <meta name="theme-color" content="#6366f1" id="theme-color-meta">
    <meta name="apple-mobile-web-app-status-bar-style" content="default" id="status-bar-meta">

    <!-- Modern Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

<style>


        /* CSS Variables */
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-white: #ffffff;
            --border-radius: 16px;
            --transition: all 0.3s ease;
            --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.1);
            --shadow-sm: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        /* Global Styles */
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Comfortaa', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
        }

        /* Dark Mode Support */
        body.dark-mode {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        }

        /* Main Container */
        .main-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 2rem 1rem;
            width: 100%;
        }

        /* OTP verification container */
        .otp-container {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: var(--border-radius);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-glass);
            padding: 3rem 2rem;
            max-width: 500px;
            width: 100%;
            text-align: center;
            position: relative;
            overflow: hidden;
            animation: slideUp 0.8s ease-out;
        }

        .otp-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        /* Header section */
        .otp-header {
            margin-bottom: 2.5rem;
            position: relative;
            z-index: 1;
        }

        .otp-header .icon-container {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: var(--shadow-sm);
        }

        .otp-header i {
            font-size: 2.5rem;
            color: var(--text-white);
        }

        .otp-header h1 {
            color: var(--text-white);
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 1.8rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .otp-header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1rem;
            line-height: 1.5;
            margin: 0;
        }

        /* Form styling */
        .form-control {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 0.875rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px);
            color: var(--text-white);
            transition: var(--transition);
            font-size: 0.95rem;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-control:focus {
            border-color: rgba(255, 255, 255, 0.6);
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.2);
            color: var(--text-white);
        }

        .form-label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .input-group-text {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.8);
            border-right: none;
        }

        .input-group .form-control {
            border-left: none;
        }

        .input-group:focus-within .input-group-text {
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.15);
        }

        /* Button styling */
        .btn-primary {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            border: none;
            border-radius: 50px;
            padding: 1rem 2.5rem;
            color: var(--text-white);
            font-weight: 600;
            font-size: 1.1rem;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.4);
            color: var(--text-white);
        }

        .btn-primary:focus {
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
            color: var(--text-white);
        }

        /* Alert styling */
        .alert {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(8px);
        }

        /* Security badge */
        .security-badge {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.75rem;
            margin-top: 2rem;
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.8);
            position: relative;
            z-index: 1;
        }

        /* Animations */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem 0.5rem;
            }

            .otp-container {
                padding: 2rem 1.5rem;
                max-width: 100%;
            }

            .otp-header h1 {
                font-size: 1.5rem;
            }

            .btn-primary {
                padding: 0.875rem 2rem;
                font-size: 1rem;
                width: 100%;
            }
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus indicators */
        .btn-primary:focus-visible,
        .form-control:focus-visible {
            outline: 2px solid var(--text-white);
            outline-offset: 2px;
        }

        /* Dark mode toggle styles */
        .dark-mode-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 50px;
            padding: 0.5rem 1rem;
            color: var(--text-white);
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: var(--transition);
            z-index: 1000;
        }

        .dark-mode-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
        }
</style>
</head>
<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5L8WZWCC"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
 
    <!-- Dark Mode Toggle -->
    <div class="dark-mode-toggle" onclick="toggleDarkMode()">
        <i class="fas fa-moon" id="dark-mode-icon"></i>
    </div>

    <div class="main-container">
        <div class="otp-container">
            <div class="otp-header">
                <div class="icon-container">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1>Verify OTP & Set Password</h1>
                <p>Enter the verification code sent to your email and create a new secure password</p>
            </div>

            {% if messages %}
                <div id="messages">
                    {% for message in messages %}
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="email" value="{{ email }}" />

                <div class="mb-4">
                    <label for="otp" class="form-label">
                        <i class="fas fa-key me-2"></i>
                        Verification Code (OTP):
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-shield-alt"></i></span>
                        <input type="text"
                               id="otp"
                               name="otp"
                               class="form-control"
                               placeholder="Enter 6-digit code"
                               maxlength="6"
                               pattern="[0-9]{6}"
                               required
                               autocomplete="one-time-code" />
                    </div>
                </div>

                <div class="mb-4">
                    <label for="new_password" class="form-label">
                        <i class="fas fa-lock me-2"></i>
                        New Password:
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-key"></i></span>
                        <input type="password"
                               id="new_password"
                               name="new_password"
                               class="form-control"
                               placeholder="Create a strong password"
                               minlength="8"
                               required
                               autocomplete="new-password" />
                    </div>
                    <small class="form-text text-white-50 mt-2">
                        <i class="fas fa-info-circle me-1"></i>
                        Password must be at least 8 characters long
                    </small>
                </div>

                <div class="mb-4">
                    <label for="confirm_password" class="form-label">
                        <i class="fas fa-check-double me-2"></i>
                        Confirm Password:
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-check"></i></span>
                        <input type="password"
                               id="confirm_password"
                               name="confirm_password"
                               class="form-control"
                               placeholder="Confirm your password"
                               minlength="8"
                               required
                               autocomplete="new-password" />
                    </div>
                </div>

                <div class="d-flex justify-content-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-check-circle me-2"></i>
                        Verify & Set Password
                    </button>
                </div>
            </form>

            <div class="security-badge">
                <i class="fas fa-shield-alt me-2"></i>
                Your data is protected with enterprise-grade security
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced OTP verification functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-hide messages after 5 seconds
            setTimeout(function() {
                var messages = document.getElementById('messages');
                if (messages) {
                    messages.style.opacity = '0';
                    messages.style.transition = 'opacity 0.5s ease-out';
                    setTimeout(() => {
                        messages.style.display = 'none';
                    }, 500);
                }
            }, 5000);

            // Password validation
            const newPassword = document.getElementById('new_password');
            const confirmPassword = document.getElementById('confirm_password');
            const submitBtn = document.querySelector('button[type="submit"]');

            function validatePasswords() {
                const password = newPassword.value;
                const confirm = confirmPassword.value;

                if (password.length >= 8 && password === confirm) {
                    confirmPassword.style.borderColor = 'rgba(16, 185, 129, 0.6)';
                    return true;
                } else if (confirm.length > 0) {
                    confirmPassword.style.borderColor = 'rgba(239, 68, 68, 0.6)';
                    return false;
                }
                return false;
            }

            newPassword.addEventListener('input', validatePasswords);
            confirmPassword.addEventListener('input', validatePasswords);

            // OTP input formatting
            const otpInput = document.getElementById('otp');
            otpInput.addEventListener('input', function(e) {
                // Only allow numbers
                this.value = this.value.replace(/[^0-9]/g, '');

                // Limit to 6 digits
                if (this.value.length > 6) {
                    this.value = this.value.slice(0, 6);
                }
            });

            // Form submission enhancement
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                const otp = otpInput.value;
                const password = newPassword.value;
                const confirm = confirmPassword.value;

                if (otp.length !== 6) {
                    e.preventDefault();
                    showError('Please enter a valid 6-digit OTP');
                    otpInput.focus();
                    return;
                }

                if (password.length < 8) {
                    e.preventDefault();
                    showError('Password must be at least 8 characters long');
                    newPassword.focus();
                    return;
                }

                if (password !== confirm) {
                    e.preventDefault();
                    showError('Passwords do not match');
                    confirmPassword.focus();
                    return;
                }

                // Show loading state
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Verifying...';
                submitBtn.disabled = true;
            });

            function showError(message) {
                // Remove existing error messages
                const existingError = document.querySelector('.error-message');
                if (existingError) {
                    existingError.remove();
                }

                // Create new error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger error-message';
                errorDiv.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i>${message}`;
                errorDiv.style.background = 'rgba(239, 68, 68, 0.1)';
                errorDiv.style.border = '1px solid rgba(239, 68, 68, 0.2)';
                errorDiv.style.color = '#ef4444';

                form.insertBefore(errorDiv, form.firstChild);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.remove();
                    }
                }, 5000);
            }

            // Track page view
            if (typeof gtag === 'function') {
                gtag('event', 'otp_verification_view', {
                    'event_category': 'Security',
                    'event_label': 'OTP Verification Page'
                });
            }
        });

        // Dark mode functionality
        function toggleDarkMode() {
            const body = document.body;
            const icon = document.getElementById('dark-mode-icon');

            body.classList.toggle('dark-mode');

            if (body.classList.contains('dark-mode')) {
                icon.className = 'fas fa-sun';
                localStorage.setItem('darkMode', 'enabled');
            } else {
                icon.className = 'fas fa-moon';
                localStorage.setItem('darkMode', 'disabled');
            }
        }

        // Initialize dark mode from localStorage
        document.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('darkMode') === 'enabled') {
                document.body.classList.add('dark-mode');
                document.getElementById('dark-mode-icon').className = 'fas fa-sun';
            }
        });
    </script>
</body>
</html>
