import { Platform } from 'react-native';

/**
 * Calculate dynamic tab bar height based on device type and safe areas
 * @param {Object} insets - Safe area insets from useSafeAreaInsets()
 * @returns {number} - Calculated tab bar height
 */
export const calculateTabBarHeight = (insets) => {
  const baseHeight = 60; // Base height for tab content (icon + label)
  const paddingTop = 8;
  const minBottomPadding = 20; // Minimum bottom padding
  
  // Use safe area bottom inset, but ensure minimum padding
  const bottomPadding = Math.max(insets.bottom, minBottomPadding);
  
  // Detect gesture navigation vs physical buttons
  const hasGestureNavigation = insets.bottom > 0;
  
  // Add extra height based on navigation type
  // Gesture navigation devices need less extra space since they have built-in safe areas
  // Physical button devices need more space to avoid collision with buttons
  const extraHeight = hasGestureNavigation ? 10 : 25;
  
  return baseHeight + paddingTop + bottomPadding + extraHeight;
};

/**
 * Get dynamic padding values for tab bar
 * @param {Object} insets - Safe area insets from useSafeAreaInsets()
 * @returns {Object} - Padding values { paddingTop, paddingBottom }
 */
export const getTabBarPadding = (insets) => {
  return {
    paddingTop: 8,
    paddingBottom: Math.max(insets.bottom, 20),
  };
};

/**
 * Check if device uses gesture navigation
 * @param {Object} insets - Safe area insets from useSafeAreaInsets()
 * @returns {boolean} - True if device uses gesture navigation
 */
export const hasGestureNavigation = (insets) => {
  return insets.bottom > 0;
};

/**
 * Get content margin bottom to prevent overlap with tab bar
 * @param {Object} insets - Safe area insets from useSafeAreaInsets()
 * @returns {number} - Margin bottom value
 */
export const getContentMarginBottom = (insets) => {
  return calculateTabBarHeight(insets);
};
