#!/usr/bin/env python3
"""
Test script for contacts functionality
"""

import os
import sys
import django
import json

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from contacts.models import UserContact, Contact, ContactRelationship, UserContactStats
from contacts.serializers import BulkContactUploadSerializer
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from django.urls import reverse

def test_models():
    """Test contact models functionality"""
    print("Testing Contact Models...")
    
    # Create test users
    user1 = User.objects.create_user(
        username='testuser1',
        email='<EMAIL>',
        password='testpass123'
    )
    user2 = User.objects.create_user(
        username='testuser2',
        email='<EMAIL>',
        password='testpass123'
    )
    
    # Create student profiles
    student1 = Student.objects.create(user=user1, phone='9876543210')
    student2 = Student.objects.create(user=user2, phone='9876543211')
    
    # Test UserContact creation
    user_contact = UserContact.objects.create(
        user=user1,
        name='Test Contact',
        contact_number='9876543212'
    )
    print(f"✓ UserContact created: {user_contact}")
    
    # Test Contact creation with registered user detection
    contact = Contact.objects.create(
        name='Test User 2',
        contact_number='9876543211'  # This should match student2's phone
    )
    print(f"✓ Contact created: {contact}")
    print(f"✓ Is registered user: {contact.is_registered_user}")
    print(f"✓ Registered user: {contact.registered_user}")
    
    # Test ContactRelationship creation
    relationship = ContactRelationship.objects.create(
        user=user1,
        contact=contact,
        relationship_type='contact'
    )
    print(f"✓ ContactRelationship created: {relationship}")
    
    # Test UserContactStats
    stats = UserContactStats.objects.create(user=user1)
    stats.update_stats()
    print(f"✓ UserContactStats created and updated: {stats}")
    
    print("✓ All model tests passed!\n")
    return user1, user2, student1, student2

def test_serializers(user1):
    """Test contact serializers"""
    print("Testing Contact Serializers...")
    
    # Test BulkContactUploadSerializer
    data = {
        'contacts': [
            {'name': 'Contact 1', 'contact': '9876543213'},
            {'name': 'Contact 2', 'contact': '9876543214'},
            {'name': 'Contact 3', 'contact': '9876543215'}
        ]
    }
    
    # Create a mock request object
    class MockRequest:
        def __init__(self, user):
            self.user = user
    
    mock_request = MockRequest(user1)
    
    serializer = BulkContactUploadSerializer(
        data=data, 
        context={'request': mock_request}
    )
    
    if serializer.is_valid():
        result = serializer.save()
        print(f"✓ Bulk upload successful: {result['contacts_uploaded']} contacts uploaded")
        print(f"✓ Relationships created: {result['relationships_created']}")
    else:
        print(f"✗ Serializer validation failed: {serializer.errors}")
        return False
    
    print("✓ All serializer tests passed!\n")
    return True

def test_api_endpoints():
    """Test API endpoints"""
    print("Testing API Endpoints...")
    
    # Create test user
    user = User.objects.create_user(
        username='apiuser',
        email='<EMAIL>',
        password='testpass123'
    )
    student = Student.objects.create(user=user, phone='9876543220')
    
    # Set up API client with authentication
    client = APIClient()
    refresh = RefreshToken.for_user(user)
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    # Test bulk upload endpoint
    upload_data = {
        'contacts': [
            {'name': 'API Contact 1', 'contact': '9876543221'},
            {'name': 'API Contact 2', 'contact': '9876543222'}
        ]
    }
    
    try:
        response = client.post('/api/contacts/upload/', upload_data, format='json')
        print(f"✓ Bulk upload API response status: {response.status_code}")
        if response.status_code == 201:
            print(f"✓ Upload successful: {response.data}")
        else:
            print(f"✗ Upload failed: {response.data}")
    except Exception as e:
        print(f"✗ API test failed: {str(e)}")
        return False
    
    # Test get user contacts endpoint
    try:
        response = client.get('/api/contacts/my-contacts/')
        print(f"✓ Get contacts API response status: {response.status_code}")
        if response.status_code == 200:
            print(f"✓ Contacts retrieved: {len(response.data.get('data', []))} contacts")
        else:
            print(f"✗ Get contacts failed: {response.data}")
    except Exception as e:
        print(f"✗ Get contacts API test failed: {str(e)}")
        return False
    
    # Test get relationships endpoint
    try:
        response = client.get('/api/contacts/relationships/')
        print(f"✓ Get relationships API response status: {response.status_code}")
        if response.status_code == 200:
            print(f"✓ Relationships retrieved: {len(response.data.get('data', []))} relationships")
        else:
            print(f"✗ Get relationships failed: {response.data}")
    except Exception as e:
        print(f"✗ Get relationships API test failed: {str(e)}")
        return False
    
    # Test stats endpoint
    try:
        response = client.get('/api/contacts/stats/')
        print(f"✓ Get stats API response status: {response.status_code}")
        if response.status_code == 200:
            print(f"✓ Stats retrieved: {response.data}")
        else:
            print(f"✗ Get stats failed: {response.data}")
    except Exception as e:
        print(f"✗ Get stats API test failed: {str(e)}")
        return False
    
    print("✓ All API tests passed!\n")
    return True

def main():
    """Main test function"""
    print("=" * 60)
    print("CONTACTS FUNCTIONALITY TEST")
    print("=" * 60)
    
    try:
        # Test models
        user1, user2, student1, student2 = test_models()
        
        # Test serializers
        if not test_serializers(user1):
            print("✗ Serializer tests failed!")
            return
        
        # Test API endpoints
        if not test_api_endpoints():
            print("✗ API tests failed!")
            return
        
        print("=" * 60)
        print("✓ ALL TESTS PASSED SUCCESSFULLY!")
        print("=" * 60)
        
        # Print summary
        print("\nSUMMARY:")
        print(f"Total UserContacts: {UserContact.objects.count()}")
        print(f"Total Contacts: {Contact.objects.count()}")
        print(f"Total Relationships: {ContactRelationship.objects.count()}")
        print(f"Total Stats Records: {UserContactStats.objects.count()}")
        
    except Exception as e:
        print(f"✗ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
