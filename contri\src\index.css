/* Header Title Styles */

  /* Meta Description Styles */
  .blog-meta-description {
    font-size: 1.2rem;
  }

  
  /* General Info Box Styles */
.info-box {
    font-size: 1.2rem;
    line-height: 1.5;
    font-weight: bold;
    background-color: #efefef;
    color: #3a3a3a;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 4.5rem;
    padding: 0.5rem 1rem;
    margin-bottom: 1rem;
  }
  
  

  /* Content Column Styles */
.content-column {
    font-size: 1.1rem;
    line-height: 1.3;
    text-align: justify;
  }
  
  .d-none.d-md-block {
    column-count: 1;
    column-gap: 30px;
  }
  
 

  /* Footer Image Styles */
.footer-image {
    width: 400px; /* Fixed width for the image */
    height: 300px;
    object-fit: cover;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .footer-image-caption {
    font-size: 0.9rem;
  }
  
  .share-button {
    text-decoration: none;
  }
  
  .share-button .mr-2 {
    margin-right: 8px;
  }

  /* Social Share Buttons */
.social-share-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
  }
  
  .social-share-text {
    margin-top: 5px;
    color: black;
  }
  
  /* Facebook */
  .facebook {
    color: #3b5998;
  }
  
  /* Twitter */
  .twitter {
    color: #1da1f2;
  }
  
  /* LinkedIn */
  .linkedin {
    color: #0077b5;
  }
  
  /* WhatsApp */
  .whatsapp {
    color: #25d366;
  }

  
  .fancy-button {
    position: relative;
    padding: 12px 30px;
    font-size: 1.2rem;
    font-weight: bold;
    color: white;
    background: linear-gradient(90deg, #1dc85c, #20a4bc);
    border: none;
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    overflow: hidden;
    transition: all 0.4s ease;
  }
  
  .fancy-button:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    width: 150%;
    height: 400%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
    border-radius: 50%;
    transition: all 0.5s ease;
    z-index: 0;
  }
  
  .fancy-button:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(229, 46, 113, 0.4), 0 0 10px rgba(255, 138, 0, 0.5);
  }
  
  .fancy-button:hover:before {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  
  .fancy-button:active {
    transform: translateY(2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  }
  

a {
  color: #198754;
}

.accordion-button {
  background-color: #e6ffe6 !important;
}

ul, li {
  margin: 0;
  padding: 0;
  text-decoration: none;
  list-style-type: none;
}

.text-truncate-4 {
  display: -webkit-box;
  -webkit-line-clamp: 3; /* Limits to 4 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 5.6em; /* Adjust based on font size */
  line-height: 1.4em; /* Adjust for readability */
}

/* tbody, td, tfoot, th, thead, tr {
  border-color: none !important;
  border-style: none !important;
  border-width: 0;
} 

tbody {
  border-radius: 0.5rem !important;
} */

  .min-height-85vh {
    min-height: 85vh;
  }
  
  @media only screen and (min-width: 767px) {
    .search-bar {
      max-width: 15rem;
    } 
  }

  @media only screen and (max-width: 767px) {
    .search-bar {
      margin-bottom: 0.7rem;
    } 
  }