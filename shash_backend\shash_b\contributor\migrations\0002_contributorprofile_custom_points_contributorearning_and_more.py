# Generated by Django 5.1.1 on 2025-07-23 09:36

import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contributor', '0001_initial'),
        ('customrcare', '0002_examgrouplevel_walkaroundimage_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='contributorprofile',
            name='custom_points',
            field=models.ForeignKey(blank=True, help_text='Custom point configuration for this contributor. If not set, uses default points.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_contributors', to='contributor.contributorpoints'),
        ),
        migrations.CreateModel(
            name='ContributorEarning',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('normal_questions_count', models.PositiveIntegerField(default=0, help_text='Number of normal questions created')),
                ('master_questions_count', models.PositiveIntegerField(default=0, help_text='Number of master questions created')),
                ('master_options_count', models.PositiveIntegerField(default=0, help_text='Number of master options created')),
                ('blogs_count', models.PositiveIntegerField(default=0, help_text='Number of blogs written')),
                ('previous_questions_count', models.PositiveIntegerField(default=0, help_text='Number of previous questions answered')),
                ('normal_questions_points', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Points earned from normal questions', max_digits=10)),
                ('master_questions_points', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Points earned from master questions', max_digits=10)),
                ('master_options_points', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Points earned from master options', max_digits=10)),
                ('blogs_points', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Points earned from blogs', max_digits=10)),
                ('previous_questions_points', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Points earned from previous questions', max_digits=10)),
                ('total_points', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total points earned', max_digits=10)),
                ('total_earnings', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total earnings in currency (if applicable)', max_digits=10)),
                ('period_type', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly'), ('lifetime', 'Lifetime')], default='monthly', help_text='The period this earning record covers', max_length=20)),
                ('period_start', models.DateTimeField(help_text='Start of the earning period')),
                ('period_end', models.DateTimeField(help_text='End of the earning period')),
                ('is_paid', models.BooleanField(default=False, help_text='Whether this earning has been paid out')),
                ('paid_at', models.DateTimeField(blank=True, help_text='When this earning was paid out', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('contributor', models.ForeignKey(help_text='The contributor who earned these points', on_delete=django.db.models.deletion.CASCADE, related_name='earnings', to='contributor.contributorprofile')),
                ('points_config_used', models.ForeignKey(blank=True, help_text='The points configuration used for this calculation', null=True, on_delete=django.db.models.deletion.SET_NULL, to='contributor.contributorpoints')),
            ],
            options={
                'verbose_name': 'Contributor Earning',
                'verbose_name_plural': 'Contributor Earnings',
                'ordering': ['-period_start', '-created_at'],
                'indexes': [models.Index(fields=['contributor', 'period_type'], name='contributor_contrib_4d44d0_idx'), models.Index(fields=['period_start', 'period_end'], name='contributor_period__89f279_idx'), models.Index(fields=['is_paid'], name='contributor_is_paid_d86fcc_idx')],
                'unique_together': {('contributor', 'period_type', 'period_start', 'period_end')},
            },
        ),
        migrations.CreateModel(
            name='PopupBanner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Banner title', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Banner description', null=True)),
                ('content_type', models.CharField(choices=[('text_only', 'Text Only'), ('image_only', 'Image Only'), ('text_image', 'Text + Image'), ('image_text', 'Image + Text'), ('text_link', 'Text + Link'), ('link_anchor', 'Link + Anchor Tag')], help_text='Type of content in the banner', max_length=20)),
                ('image', models.ImageField(blank=True, help_text='Banner image (required for image-based content types)', null=True, upload_to='popup_banners/images/')),
                ('text_content', models.TextField(blank=True, help_text='Text content for the banner', null=True)),
                ('link_url', models.URLField(blank=True, help_text='URL for link-based content types', null=True)),
                ('link_text', models.CharField(blank=True, help_text='Display text for the link', max_length=255, null=True)),
                ('anchor_tag', models.CharField(blank=True, help_text='HTML anchor tag attributes', max_length=255, null=True)),
                ('display_duration', models.PositiveIntegerField(default=5000, help_text='Duration to display banner in milliseconds')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', help_text='Banner display priority', max_length=10)),
                ('is_active', models.BooleanField(default=False, help_text='Whether the banner is currently active')),
                ('approval_status', models.CharField(choices=[('pending', 'Pending'), ('approved_by_care', 'Approved by Customer Care'), ('approved_by_admin', 'Approved by Admin'), ('rejected_by_care', 'Rejected by Customer Care'), ('rejected_by_admin', 'Rejected by Admin'), ('active', 'Active'), ('inactive', 'Inactive')], default='pending', help_text='Current approval status', max_length=20)),
                ('rejection_reason', models.TextField(blank=True, help_text='Reason for rejection', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('activated_at', models.DateTimeField(blank=True, null=True)),
                ('slug', models.SlugField(blank=True, max_length=255, unique=True)),
                ('approved_by_admin', models.ForeignKey(blank=True, help_text='Admin user who gave final approval', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='admin_approved_popup_banners', to=settings.AUTH_USER_MODEL)),
                ('approved_by_care', models.ForeignKey(blank=True, help_text='Customer care user who approved the banner', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_popup_banners', to='customrcare.customrcareprofile')),
                ('created_by', models.ForeignKey(help_text='Contributor who created the banner', on_delete=django.db.models.deletion.CASCADE, related_name='created_popup_banners', to=settings.AUTH_USER_MODEL)),
                ('rejected_by', models.ForeignKey(blank=True, help_text='User who rejected the banner', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rejected_popup_banners', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Popup Banner',
                'verbose_name_plural': 'Popup Banners',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['approval_status'], name='contributor_approva_eb9209_idx'), models.Index(fields=['is_active'], name='contributor_is_acti_02bc6f_idx'), models.Index(fields=['created_by'], name='contributor_created_0bc5a5_idx'), models.Index(fields=['content_type'], name='contributor_content_0de90a_idx')],
            },
        ),
    ]
