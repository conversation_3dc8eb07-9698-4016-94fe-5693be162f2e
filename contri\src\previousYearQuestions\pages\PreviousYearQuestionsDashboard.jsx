import React, { useState, useEffect } from "react";
import ReactPaginate from "react-paginate";
import { Card, ButtonGroup, Button, Row, Col, Form, Container, Modal, Pagination, Dropdown, DropdownButton } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { getAllPreviousYearQuestions, getPreviousYearQuestion, updatePreviousYearQuestion, deletePreviousYearQuestion } from "../../redux/slice/previousYearQuestionSlice";
import { FaEdit, FaTrashAlt } from "react-icons/fa";
import Swal from 'sweetalert2';
import toast from 'react-hot-toast';
import NavigationBar from "../../commonComponents/NavigationBar";
import { FaQuestionCircle, FaCalendar, FaTasks, FaBookOpen, FaChalkboardTeacher, FaCheckCircle } from 'react-icons/fa';
const AllPrevQuestion = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [questionData, setQuestionData] = useState({ year: "", month: "", exams: "", count: "", status: "", note: "" });
  const [previousYearQuestions, setPreviousYearQuestions] = useState([]);
  const [filteredQuestions, setFilteredQuestions] = useState([]);
  const [filter, setFilter] = useState("all"); // Current filter (all, pending, approved, rejected)
  const [itemsPerPage, setItemsPerPage] = useState(5); // State for items per page

  const dispatch = useDispatch();

  // Fetch questions from API
  const fetchPrevQuestions = async () => {
    try {
      const response = await dispatch(getAllPreviousYearQuestions());
      if (response?.payload) {
        setPreviousYearQuestions(response.payload);  // Set the fetched questions to state
        setFilteredQuestions(response.payload); // Initially, display all questions
      } else {
        toast.error('Error fetching questions');
      }
    } catch (error) {
      toast.error('Error fetching questions');
    }
  };

  useEffect(() => {
    fetchPrevQuestions();
  }, [dispatch]); // Fetch questions on component mount

  // Handle edit question
  const handleEditQuestion = (slug) => {
    dispatch(getPreviousYearQuestion(slug)).then((action) => {
      if (action.type === 'previousYearQuestion/getPreviousYearQuestion/fulfilled') {
        const question = action.payload;
        setQuestionData({ year: question.year, month: question.month, exams: question.exams, count: question.count, status: question.status, note: question.note });
        setSelectedQuestion(question);
        setShowEditModal(true);
      }
    });
  };

  // Handle delete question
  const handleDeleteQuestion = async (slug) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!"
    });

    if (result.isConfirmed) {
      try {
        await dispatch(deletePreviousYearQuestion(slug));
        fetchPrevQuestions();  // Re-fetch the updated list of questions
        toast.success("Question deleted successfully!");
      } catch (error) {
        console.error("Error deleting question", error);
        toast.error("Failed to delete the question. Please try again.");
      }
    }
  };

  // Handle update question
  const handleUpdateQuestion = () => {
    const updatedData = {
      question: selectedQuestion.id,
      year: questionData.year,
      month: questionData.month,
      exams: questionData.exams,
      count: questionData.count,
      status: questionData.status,
      note: questionData.note,
    };

    dispatch(updatePreviousYearQuestion({ slug: selectedQuestion.slug, updatedData }))
      .then(() => {
        setShowEditModal(false);
        fetchPrevQuestions();  // Re-fetch the updated list of questions
        toast.success("Question updated successfully!");
      })
      .catch((error) => {
        console.error("Error updating question", error);
        toast.error("Failed to update the question. Please try again.");
      });
  };

  // Handle filter button clicks
  const handleFilterClick = (newFilter) => {
    setFilter(newFilter);
    let filteredData = previousYearQuestions;

    if (newFilter === "pending") {
      filteredData = previousYearQuestions.filter((q) => q.question_details?.approval_status === "pending");
    } else if (newFilter === "approved") {
      filteredData = previousYearQuestions.filter((q) => q.question_details?.approval_status === "approved");
    } else if (newFilter === "rejected") {
      filteredData = previousYearQuestions.filter((q) => q.question_details?.approval_status === "rejected");
    }

    setFilteredQuestions(filteredData);
    setCurrentPage(0); // Reset pagination to first page
  };

  const handleSearch = (searchQuery) => {
    setSearchQuery(searchQuery);

    if (searchQuery === "") {
      // If search bar is cleared, show all questions
      setFilteredQuestions(previousYearQuestions);
    } else {
      // Filter based on search query
      const filteredData = previousYearQuestions.filter((question) =>
        question?.question_details.content?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredQuestions(filteredData);
    }

    setCurrentPage(0); // Reset pagination to first page
  };

  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(0); // Reset pagination to the first page
  };

  // Pagination logic
  const handlePageChange = ({ selected }) => {
    setCurrentPage(selected);
  };

  const indexOfLastQuestion = (currentPage + 1) * itemsPerPage;
  const indexOfFirstQuestion = indexOfLastQuestion - itemsPerPage;
  const currentQuestions = filteredQuestions.slice(indexOfFirstQuestion, indexOfLastQuestion);

  return (
    <>
      <NavigationBar />
      <Container style={{ height: "90vh" }}>
        <h2 className="text-center text-success mt-3 mb-2" style={{ fontSize: "1.9rem" }}>
          Dashboard / Previous Year Questions, <small className="h4"> See updated here. </small>
        </h2>

        {/* Search Bar */}
        <Row className="mb-4 justify-content-center">
          <Col xs={12} sm={8} md={6} lg={5} className="d-flex justify-content-between">
            <Form.Control
              type="text"
              placeholder="Search questions..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
            />
            <DropdownButton
              variant="success"
              title={`${itemsPerPage} per page`}
              className="ml-2"
              onSelect={handleItemsPerPageChange}
            >
              <Dropdown.Item eventKey={5}>5 per page</Dropdown.Item>
              <Dropdown.Item eventKey={25}>25 per page</Dropdown.Item>
              <Dropdown.Item eventKey={50}>50 per page</Dropdown.Item>
              <Dropdown.Item eventKey={100}>100 per page</Dropdown.Item>
              <Dropdown.Item eventKey={filteredQuestions.length}>All</Dropdown.Item>
            </DropdownButton>
          </Col>
        </Row>

        {/* Button to filter pending, approved, or rejected */}
        <ButtonGroup className="mb-3">
          <Button
            variant={filter === "all" ? "primary" : "outline-primary"}
            onClick={() => handleFilterClick("all")}
          >
            All
          </Button>
          <Button
            variant={filter === "pending" ? "primary" : "outline-primary"}
            onClick={() => handleFilterClick("pending")}
          >
            Pending
          </Button>
          <Button
            variant={filter === "approved" ? "primary" : "outline-primary"}
            onClick={() => handleFilterClick("approved")}
          >
            Approved
          </Button>
          <Button
            variant={filter === "rejected" ? "primary" : "outline-primary"}
            onClick={() => handleFilterClick("rejected")}
          >
            Rejected
          </Button>
        </ButtonGroup>

        {/* Previous Year Questions Cards */}
        <Row>
          {currentQuestions.map((question) => {
            // Determine background color and text color based on approval status
            let cardStyle = {};
            switch (question.question_details.approval_status) {
              case "approved":
                cardStyle = { backgroundColor: "#e6ffee", color: "#155724" }; // Greenish background
                break;
              case "pending":
                cardStyle = { backgroundColor: "#ffffff", color: "#856404" }; // Yellowish background
                break;
              case "rejected":
                cardStyle = { backgroundColor: "#ffe6e6", color: "#721c24" }; // Reddish background
                break;
              default:
                cardStyle = { backgroundColor: "#ffffff", color: "#000" }; // Default grayish background
            }

            return (
              <Col key={question.slug} xs={12} sm={6} md={6} lg={4}>
                <Card className="shadow-lg rounded-3 mb-4" style={cardStyle}>
                  <Card.Body style={{ height: "auto", wslugth: "auto" }}>
                    {/* Question Title and Metadata (Year, Month, Course, Exam) */}
                    <Card.Title className="text-success" style={{ fontSize: "1.2rem" }}>
                      <FaQuestionCircle
                        className="text-success"
                        style={{ marginRight: "5px" }}
                      />
                      {question.question_details.content}
                    </Card.Title>

                    <Row>
                      <Col xs={6}>
                        <Card.Text>
                          <strong>
                            <FaCalendar
                              className="text-success"
                              style={{ marginRight: "5px" }}
                            />
                            Year:
                          </strong>{" "}
                          {question.question_details.previous_year_questions[0]?.year}{" "}
                          <br />
                          <strong>
                            <FaCalendar
                              className="text-success"
                              style={{ marginRight: "5px" }}
                            />
                            Month:
                          </strong>{" "}
                          {question.question_details.previous_year_questions[0]?.month}{" "}
                          <br />
                          <strong>
                            <FaCheckCircle
                              className="text-success"
                              style={{ marginRight: "5px" }}
                            />
                            Note:
                          </strong>{" "}
                          {question.note} <br />
                          <strong>
                            <FaTasks
                              className="text-success"
                              style={{ marginRight: "5px" }}
                            />
                            Exam:
                          </strong>{" "}
                          {question.question_details.previous_year_questions[0]?.exams
                            ?.name || "N/A"}{" "}
                          <br />
                        </Card.Text>
                      </Col>
                      <Col xs={6}>
                        <Card.Text>
                          <strong>
                            <FaChalkboardTeacher
                              className="text-success"
                              style={{ marginRight: "5px" }}
                            />
                            Courses:
                          </strong>{" "}
                          {question.question_details.course.join(", ")} <br />
                          <strong>
                            <FaBookOpen
                              className="text-success"
                              style={{ marginRight: "5px" }}
                            />
                            Subcourses:
                          </strong>{" "}
                          {question.question_details.subcourse.join(", ")} <br />
                          <strong>
                            <FaTasks
                              className="text-success"
                              style={{ marginRight: "5px" }}
                            />
                            Difficulty:
                          </strong>{" "}
                          {question.question_details.difficulty} <br />
                          <strong>
                            <FaBookOpen
                              className="text-success"
                              style={{ marginRight: "5px" }}
                            />
                            Course Name:
                          </strong>{" "}
                          {question.question_details.previous_year_questions[0]?.course
                            ?.name || "N/A"}{" "}
                          <br />
                        </Card.Text>
                      </Col>
                    </Row>

                    {/* Options */}
                    <div style={{ marginTop: "1rem" }}>
                      <p>
                        {" "}
                        <strong>
                          <FaQuestionCircle
                            className="text-success"
                            style={{ marginRight: "5px" }}
                          />
                          Question:
                        </strong>{" "}
                        {question.question_details.content}{" "}
                      </p>
                      <strong>
                        <FaTasks
                          className="text-success"
                          style={{ marginRight: "5px" }}
                        />
                        Options:
                      </strong>
                      <ul style={{ paddingLeft: "2rem" }}>
                        {question.question_details.options.map((option) => (
                          <li
                            key={option.option_slug}
                            style={{
                              color: option.is_correct ? "green" : "red",
                              fontWeight: option.is_correct ? "bold" : "normal",
                            }}
                          >
                            {option.option_text} {option.is_correct && "(Correct)"}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Edit/Delete Buttons */}
                    <div
                      className="d-flex justify-content-evenly align-items-end"
                      style={{ marginTop: "1rem" }}
                    >
                      <Button
                        variant="outline-success"
                        className="mr-1"
                        onClick={() => handleEditQuestion(question.slug)}
                      >
                        <FaEdit /> Edit
                      </Button>
                      <Button
                        variant="outline-danger"
                        onClick={() => handleDeleteQuestion(question.slug)}
                      >
                        <FaTrashAlt /> Delete
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            );
          })}
        </Row>

            {/* Edit Previous Year Question Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit Question</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group>
              <Form.Label>Year</Form.Label>
              <Form.Control
                type="text"
                value={questionData.year}
                onChange={(e) => setQuestionData({ ...questionData, year: e.target.value })}
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Month</Form.Label>
              <Form.Control
                type="text"
                value={questionData.month}
                onChange={(e) => setQuestionData({ ...questionData, month: e.target.value })}
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Exams</Form.Label>
              <Form.Control
                type="text"
                value={questionData.exams}
                onChange={(e) => setQuestionData({ ...questionData, exams: e.target.value })}
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Count</Form.Label>
              <Form.Control
                type="number"
                value={questionData.count}
                onChange={(e) => setQuestionData({ ...questionData, count: e.target.value })}
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Status</Form.Label>
              <Form.Control
                type="text"
                value={questionData.status}
                onChange={(e) => setQuestionData({ ...questionData, status: e.target.value })}
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Note</Form.Label>
              <Form.Control
                type="text"
                value={questionData.note}
                onChange={(e) => setQuestionData({ ...questionData, note: e.target.value })}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="success"
            onClick={handleUpdateQuestion}
          >
            Update Question
          </Button>
        </Modal.Footer>
      </Modal>


       {/* Pagination */}
       <Row className="mt-4 justify-content-center">
        <Col xs={12} className="text-center">
          <ReactPaginate
            previousLabel={"Previous"}
            nextLabel={"Next"}
            pageCount={Math.ceil(filteredQuestions.length / itemsPerPage)}
            onPageChange={handlePageChange}
            containerClassName={"pagination justify-content-center"}
            pageClassName={"page-item"}
            previousClassName={"page-item"}
            nextClassName={"page-item"}
            disabledClassName={"disabled"}
            activeClassName={"active"}
            pageLinkClassName={"page-link shadow-sm border-0 rounded-pill"}
            previousLinkClassName={"page-link shadow-sm border-0 rounded-pill"}
            nextLinkClassName={"page-link shadow-sm border-0 rounded-pill"}
          />
        </Col>
      </Row>
      
      </Container>
    </>
  );
}

export default AllPrevQuestion;
