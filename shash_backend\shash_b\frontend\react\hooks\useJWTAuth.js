/**
 * React JWT Authentication Hook
 * Provides automatic token refresh, storage management, and API request handling
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import axios from 'axios';

// Token storage keys
const ACCESS_TOKEN_KEY = 'jwt_access_token';
const REFRESH_TOKEN_KEY = 'jwt_refresh_token';
const TOKEN_EXPIRY_KEY = 'jwt_token_expiry';
const USER_DATA_KEY = 'user_data';

// API endpoints for different user types
const API_ENDPOINTS = {
  students: {
    login: '/api/students/login/',
    refresh: '/api/students/token/refresh/',
    logout: '/api/students/logout/',
  },
  contributor: {
    login: '/api/contributor/login/',
    refresh: '/api/contributor/token/refresh/',
    logout: '/api/contributor/logout/',
  },
  customrcare: {
    login: '/api/customrcare/login/',
    refresh: '/api/customrcare/token/refresh/',
    logout: '/api/customrcare/logout/',
  },
};

export const useJWTAuth = (userType = 'students') => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Refs to prevent multiple simultaneous refresh attempts
  const refreshPromiseRef = useRef(null);
  const axiosInstanceRef = useRef(null);

  // Get API endpoints for the specified user type
  const endpoints = API_ENDPOINTS[userType] || API_ENDPOINTS.students;

  // Token management functions
  const getStoredTokens = useCallback(() => {
    try {
      const accessToken = localStorage.getItem(ACCESS_TOKEN_KEY);
      const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
      const expiry = localStorage.getItem(TOKEN_EXPIRY_KEY);
      
      return {
        accessToken,
        refreshToken,
        expiry: expiry ? parseInt(expiry) : null,
      };
    } catch (error) {
      console.error('Error reading tokens from storage:', error);
      return { accessToken: null, refreshToken: null, expiry: null };
    }
  }, []);

  const storeTokens = useCallback((tokenData) => {
    try {
      localStorage.setItem(ACCESS_TOKEN_KEY, tokenData.access);
      localStorage.setItem(REFRESH_TOKEN_KEY, tokenData.refresh);
      
      // Calculate expiry time (current time + expires_in seconds - 60 seconds buffer)
      const expiryTime = Date.now() + (tokenData.expires_in - 60) * 1000;
      localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());
      
      return true;
    } catch (error) {
      console.error('Error storing tokens:', error);
      return false;
    }
  }, []);

  const clearTokens = useCallback(() => {
    try {
      localStorage.removeItem(ACCESS_TOKEN_KEY);
      localStorage.removeItem(REFRESH_TOKEN_KEY);
      localStorage.removeItem(TOKEN_EXPIRY_KEY);
      localStorage.removeItem(USER_DATA_KEY);
    } catch (error) {
      console.error('Error clearing tokens:', error);
    }
  }, []);

  const isTokenExpired = useCallback(() => {
    const { expiry } = getStoredTokens();
    if (!expiry) return true;
    
    return Date.now() >= expiry;
  }, [getStoredTokens]);

  // Token refresh function
  const refreshToken = useCallback(async () => {
    // Prevent multiple simultaneous refresh attempts
    if (refreshPromiseRef.current) {
      return refreshPromiseRef.current;
    }

    const { refreshToken: storedRefreshToken } = getStoredTokens();
    
    if (!storedRefreshToken) {
      throw new Error('No refresh token available');
    }

    refreshPromiseRef.current = (async () => {
      try {
        const response = await axios.post(endpoints.refresh, {
          refresh: storedRefreshToken,
        });

        const tokenData = response.data;
        
        // Store new tokens
        if (storeTokens(tokenData)) {
          console.log('Tokens refreshed successfully');
          return tokenData;
        } else {
          throw new Error('Failed to store new tokens');
        }
      } catch (error) {
        console.error('Token refresh failed:', error);
        
        // Clear invalid tokens
        clearTokens();
        setIsAuthenticated(false);
        setUser(null);
        
        throw error;
      } finally {
        refreshPromiseRef.current = null;
      }
    })();

    return refreshPromiseRef.current;
  }, [endpoints.refresh, getStoredTokens, storeTokens, clearTokens]);

  // Create axios instance with interceptors
  const createAxiosInstance = useCallback(() => {
    const instance = axios.create({
      baseURL: process.env.REACT_APP_API_BASE_URL || '',
      timeout: 10000,
    });

    // Request interceptor to add auth header
    instance.interceptors.request.use(
      async (config) => {
        const { accessToken } = getStoredTokens();
        
        if (accessToken) {
          // Check if token is expired and refresh if needed
          if (isTokenExpired()) {
            try {
              const newTokenData = await refreshToken();
              config.headers.Authorization = `Bearer ${newTokenData.access}`;
            } catch (error) {
              console.error('Failed to refresh token in request interceptor:', error);
              // Let the request proceed without auth header
            }
          } else {
            config.headers.Authorization = `Bearer ${accessToken}`;
          }
        }
        
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle token expiration
    instance.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;
        
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          const errorCode = error.response.data?.code;
          
          if (errorCode === 'TOKEN_EXPIRED' || errorCode === 'INVALID_TOKEN') {
            try {
              const newTokenData = await refreshToken();
              originalRequest.headers.Authorization = `Bearer ${newTokenData.access}`;
              return instance(originalRequest);
            } catch (refreshError) {
              console.error('Token refresh failed in response interceptor:', refreshError);
              // Redirect to login or handle as needed
              return Promise.reject(refreshError);
            }
          }
        }
        
        return Promise.reject(error);
      }
    );

    return instance;
  }, [getStoredTokens, isTokenExpired, refreshToken]);

  // Initialize axios instance
  useEffect(() => {
    axiosInstanceRef.current = createAxiosInstance();
  }, [createAxiosInstance]);

  // Login function
  const login = useCallback(async (credentials) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.post(endpoints.login, credentials);
      const data = response.data;
      
      // Extract token data based on user type
      let tokenData;
      let userData;
      
      if (userType === 'students') {
        tokenData = data.JWT_Token;
        userData = data.student;
      } else if (userType === 'contributor') {
        tokenData = {
          access: data.access,
          refresh: data.refresh,
          expires_in: 900, // 15 minutes
        };
        userData = data.profile;
      } else if (userType === 'customrcare') {
        tokenData = {
          access: data.access,
          refresh: data.refresh,
          expires_in: 900, // 15 minutes
        };
        userData = data.user;
      }
      
      // Store tokens and user data
      if (storeTokens(tokenData)) {
        localStorage.setItem(USER_DATA_KEY, JSON.stringify(userData));
        setIsAuthenticated(true);
        setUser(userData);
        return { success: true, data };
      } else {
        throw new Error('Failed to store authentication data');
      }
    } catch (error) {
      console.error('Login failed:', error);
      setError(error.response?.data?.message || 'Login failed');
      return { success: false, error: error.response?.data || error.message };
    } finally {
      setLoading(false);
    }
  }, [endpoints.login, userType, storeTokens]);

  // Logout function
  const logout = useCallback(async () => {
    try {
      const { refreshToken: storedRefreshToken } = getStoredTokens();
      
      if (storedRefreshToken && axiosInstanceRef.current) {
        // Attempt to logout on server
        try {
          await axiosInstanceRef.current.post(endpoints.logout, {
            refresh: storedRefreshToken,
          });
        } catch (error) {
          console.warn('Server logout failed, proceeding with local logout:', error);
        }
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local data
      clearTokens();
      setIsAuthenticated(false);
      setUser(null);
      setError(null);
    }
  }, [endpoints.logout, getStoredTokens, clearTokens]);

  // Check authentication status on mount
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const { accessToken, refreshToken: storedRefreshToken } = getStoredTokens();
        
        if (!accessToken || !storedRefreshToken) {
          setIsAuthenticated(false);
          setLoading(false);
          return;
        }
        
        // Try to get user data from storage
        const storedUserData = localStorage.getItem(USER_DATA_KEY);
        if (storedUserData) {
          setUser(JSON.parse(storedUserData));
        }
        
        // Check if token needs refresh
        if (isTokenExpired()) {
          try {
            await refreshToken();
          } catch (error) {
            console.error('Initial token refresh failed:', error);
            setIsAuthenticated(false);
            setLoading(false);
            return;
          }
        }
        
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Auth status check failed:', error);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuthStatus();
  }, [getStoredTokens, isTokenExpired, refreshToken]);

  return {
    // State
    isAuthenticated,
    user,
    loading,
    error,
    
    // Functions
    login,
    logout,
    refreshToken,
    
    // Axios instance with automatic token handling
    apiClient: axiosInstanceRef.current,
    
    // Utility functions
    getStoredTokens,
    isTokenExpired,
  };
};
