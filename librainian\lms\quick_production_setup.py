#!/usr/bin/env python3
"""
Quick Production Setup for Membership Expiry Notification System
This script fixes the most critical production issues
"""

import os
import secrets
import string
from pathlib import Path

def generate_secret_key():
    """Generate a secure Django SECRET_KEY"""
    chars = string.ascii_letters + string.digits + '!@#$%^&*(-_=+)'
    return ''.join(secrets.choice(chars) for _ in range(50))

def create_production_settings():
    """Create production settings file"""
    print("🔧 CREATING PRODUCTION SETTINGS")
    print("=" * 40)
    
    secret_key = generate_secret_key()
    
    production_settings = f'''"""
Production settings for Library Management System
"""

from .settings import *

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '{secret_key}'

# Production allowed hosts
ALLOWED_HOSTS = [
    'librainian.com',
    'www.librainian.com',
    '**************',  # Your server IP
    'localhost',  # For local testing
    '127.0.0.1',  # For local testing
]

# Security settings
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'

# Session security
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# Database for production (PostgreSQL recommended)
# Uncomment and configure when ready to switch from SQLite
# DATABASES = {{
#     'default': {{
#         'ENGINE': 'django.db.backends.postgresql',
#         'NAME': os.environ.get('DB_NAME', 'library_db'),
#         'USER': os.environ.get('DB_USER', 'library_user'),
#         'PASSWORD': os.environ.get('DB_PASSWORD', ''),
#         'HOST': os.environ.get('DB_HOST', 'localhost'),
#         'PORT': os.environ.get('DB_PORT', '5432'),
#         'OPTIONS': {{
#             'MAX_CONNS': 20,
#             'CONN_MAX_AGE': 600,
#         }}
#     }}
# }}

# Caching (Redis recommended for production)
# CACHES = {{
#     'default': {{
#         'BACKEND': 'django_redis.cache.RedisCache',
#         'LOCATION': 'redis://127.0.0.1:6379/1',
#         'OPTIONS': {{
#             'CLIENT_CLASS': 'django_redis.client.DefaultClient',
#         }}
#     }}
# }}

# Logging configuration
LOGGING = {{
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {{
        'verbose': {{
            'format': '{{levelname}} {{asctime}} {{module}} {{process:d}} {{thread:d}} {{message}}',
            'style': '{{',
        }},
        'simple': {{
            'format': '{{levelname}} {{message}}',
            'style': '{{',
        }},
    }},
    'handlers': {{
        'file': {{
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/django.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        }},
        'notification_file': {{
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/notifications.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        }},
        'console': {{
            'level': 'ERROR',
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        }},
    }},
    'loggers': {{
        'django': {{
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        }},
        'librarian.notification_events': {{
            'handlers': ['notification_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        }},
    }},
}}

# Email configuration for error reporting
ADMINS = [
    ('Admin', '<EMAIL>'),
]

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', '<EMAIL>')

# Static files (CSS, JavaScript, Images)
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# Media files
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Notification settings
NOTIFICATION_RATE_LIMIT = {{
    'per_user_per_hour': 10,
    'per_event_per_day': 100,
}}
'''

    settings_path = Path('Library/production_settings.py')
    with open(settings_path, 'w') as f:
        f.write(production_settings)
    
    print(f"✅ Created: {settings_path}")
    return settings_path

def create_environment_file():
    """Create .env file for environment variables"""
    print("\n🌍 CREATING ENVIRONMENT FILE")
    print("=" * 40)
    
    env_content = '''# Production Environment Variables
# Copy this file to .env and fill in the values

# Database Configuration (when switching from SQLite)
DB_NAME=library_db
DB_USER=library_user
DB_PASSWORD=your_secure_password_here
DB_HOST=localhost
DB_PORT=5432

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password_here
DEFAULT_FROM_EMAIL=<EMAIL>

# Firebase Configuration
FIREBASE_SERVICE_ACCOUNT_PATH=firebase-service-account.json

# Security
DJANGO_SETTINGS_MODULE=Library.production_settings

# Optional: Sentry for error monitoring
# SENTRY_DSN=your_sentry_dsn_here
'''
    
    env_path = Path('.env.example')
    with open(env_path, 'w') as f:
        f.write(env_content)
    
    print(f"✅ Created: {env_path}")
    print("💡 Copy this to .env and fill in your values")
    return env_path

def create_requirements_txt():
    """Create requirements.txt for production"""
    print("\n📦 CREATING REQUIREMENTS.TXT")
    print("=" * 40)
    
    requirements = '''# Core Django
Django>=4.2.0,<5.0.0
django-cors-headers>=4.0.0

# Database
psycopg2-binary>=2.9.0  # PostgreSQL adapter

# Caching
django-redis>=5.2.0
redis>=4.5.0

# Notifications
firebase-admin>=6.0.0
requests>=2.28.0

# Task Queue (optional but recommended)
celery>=5.2.0
celery[redis]>=5.2.0

# Production Server
gunicorn>=20.1.0

# Environment Variables
python-decouple>=3.6

# Monitoring (optional)
sentry-sdk[django]>=1.15.0

# Utilities
pytz>=2022.7
Pillow>=9.4.0

# Development/Testing (remove in production)
# django-debug-toolbar>=3.2.0
'''
    
    req_path = Path('requirements.txt')
    with open(req_path, 'w') as f:
        f.write(requirements)
    
    print(f"✅ Created: {req_path}")
    return req_path

def create_gunicorn_config():
    """Create Gunicorn configuration for production"""
    print("\n🚀 CREATING GUNICORN CONFIG")
    print("=" * 40)
    
    gunicorn_config = '''# Gunicorn configuration file
import multiprocessing

# Server socket
bind = "127.0.0.1:8000"
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 50

# Logging
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
loglevel = "info"

# Process naming
proc_name = "library_management_system"

# Server mechanics
daemon = False
pidfile = "logs/gunicorn.pid"
user = None
group = None
tmp_upload_dir = None

# SSL (uncomment when SSL is configured)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"
'''
    
    config_path = Path('gunicorn.conf.py')
    with open(config_path, 'w') as f:
        f.write(gunicorn_config)
    
    print(f"✅ Created: {config_path}")
    return config_path

def create_nginx_config():
    """Create Nginx configuration template"""
    print("\n🌐 CREATING NGINX CONFIG TEMPLATE")
    print("=" * 45)
    
    nginx_config = '''# Nginx configuration for Library Management System
# Save this as /etc/nginx/sites-available/librainian.com

server {
    listen 80;
    server_name librainian.com www.librainian.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name librainian.com www.librainian.com;
    
    # SSL Configuration (update paths to your certificates)
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL Security
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload";
    
    # Document root
    root /path/to/your/project;
    
    # Static files
    location /static/ {
        alias /path/to/your/project/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Media files
    location /media/ {
        alias /path/to/your/project/media/;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # Django application
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Security: Block access to sensitive files
    location ~ /\\.ht {
        deny all;
    }
    
    location ~ /\\.env {
        deny all;
    }
}
'''
    
    nginx_path = Path('nginx_librainian.conf')
    with open(nginx_path, 'w') as f:
        f.write(nginx_config)
    
    print(f"✅ Created: {nginx_path}")
    print("💡 Copy this to /etc/nginx/sites-available/ on your server")
    return nginx_path

def create_logs_directory():
    """Create logs directory"""
    print("\n📝 CREATING LOGS DIRECTORY")
    print("=" * 35)
    
    logs_dir = Path('logs')
    logs_dir.mkdir(exist_ok=True)
    
    # Create empty log files
    (logs_dir / 'django.log').touch()
    (logs_dir / 'notifications.log').touch()
    (logs_dir / 'gunicorn_access.log').touch()
    (logs_dir / 'gunicorn_error.log').touch()
    
    print(f"✅ Created: {logs_dir}")
    return logs_dir

def main():
    print("🚀 QUICK PRODUCTION SETUP")
    print("=" * 40)
    print("Setting up critical production configurations...")
    print()
    
    try:
        # Create all production files
        create_production_settings()
        create_environment_file()
        create_requirements_txt()
        create_gunicorn_config()
        create_nginx_config()
        create_logs_directory()
        
        print("\n" + "=" * 60)
        print("✅ PRODUCTION SETUP COMPLETE!")
        print("=" * 60)
        
        print("\n📋 NEXT STEPS:")
        print("1. 🔧 Copy .env.example to .env and fill in values")
        print("2. 📦 Install production packages: pip install -r requirements.txt")
        print("3. 🗄️ Set up PostgreSQL database")
        print("4. 🔄 Run migrations: python manage.py migrate")
        print("5. 📁 Collect static files: python manage.py collectstatic")
        print("6. 🚀 Start with: gunicorn -c gunicorn.conf.py Library.wsgi:application")
        print("7. 🌐 Configure Nginx with the provided config")
        print("8. 🔒 Set up SSL certificate")
        
        print("\n⚠️ IMPORTANT:")
        print("• Test in staging environment first")
        print("• Set DJANGO_SETTINGS_MODULE=Library.production_settings")
        print("• Never commit .env file to version control")
        print("• Set up database backups")
        print("• Configure monitoring (Sentry recommended)")
        
        return 0
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
