import React, { useContext } from 'react';
import { TouchableOpacity } from 'react-native';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { ThemeContext } from '../context/ThemeContext';
import { useSelector, useDispatch } from 'react-redux';
import * as Icons from "@expo/vector-icons";
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { DrawerActions } from '@react-navigation/native';
import HeaderMenu from '../components/HeaderMenu';
import AboutUsScreen from '../screens/drawer/AboutUsScreen';
import PrivacyPolicyScreen from '../screens/drawer/PrivacyPolicyScreen';
import TabNavigator from './TabNavigator';
import ProfileScreen from '../screens/auth/ProfileScreen';
import PackagesScreen from '../screens/PackagesScreen';
import RewardScreen from '../screens/RewardScreen';
import FAQScreen from '../screens/FAQScreen';
import RaiseQueryScreen from '../screens/RaiseQueryScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import SignupScreen from '../screens/auth/SignupScreen';
import TermsAndConditionsScreen from '../screens/drawer/TermsAndConditionsScreen';
import { logout } from '../redux/authSlice';
import ContactUSScreen from '../screens/drawer/ContactUSScreen';
import CartScreen from '../screens/CartScreen';
import ProgressScreen from '../screens/ProgressScreen';
import EventsScreen from '../screens/EventsScreen';

const Drawer = createDrawerNavigator();
const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

// Helper function to create a screen component with bottom navigation
const createScreenComponent = (ScreenComponent, screenName) => {
  const ScreenStack = () => {
    const { isDarkMode, toggleTheme } = useContext(ThemeContext);
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: true,
          presentation: 'card',
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen
          name={screenName}
          component={ScreenComponent}
          options={({ navigation, route }) => ({
            headerTitle: screenName,
            headerTitleStyle: {
              fontWeight: 'bold',
              color: isDarkMode ? '#fff' : '#000',
            },
            headerStyle: {
              backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
            },
            headerTintColor: isDarkMode ? '#fff' : '#000',
            headerLeft: () =>
              screenName !== 'Home' && screenName !== 'HomeTab' ? (
                <TouchableOpacity
                  onPress={() => navigation.goBack()}
                  style={{ marginLeft: 0, padding: 4 }} // Remove extra margin
                >
                  <Icons.Ionicons name="arrow-back" size={24} color={isDarkMode ? '#fff' : '#000'} />
                </TouchableOpacity>
              ) : null,
            headerRight: () => (
              <HeaderMenu onToggleDarkMode={toggleTheme} isDarkMode={isDarkMode} />
            ),
            headerRightContainerStyle: {
              paddingRight: 10,
            },
          })}
        />
      </Stack.Navigator>
    );
  };

  // Then wrap it in a tab navigator
  const WrappedComponent = () => {
    const { isDarkMode } = useContext(ThemeContext);
    return (
      <Tab.Navigator
        screenOptions={{
          headerShown: false,
          tabBarStyle: {
            backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
            borderTopColor: isDarkMode ? '#333' : '#e0e0e0',
          },
          tabBarActiveTintColor: isDarkMode ? '#fff' : '#000',
          tabBarInactiveTintColor: 'gray',
        }}
      >
        <Tab.Screen 
          name={`${screenName}Stack`}
          component={ScreenStack}
          options={{
            title: screenName,
            tabBarStyle: { display: 'none' }
          }}
        />
      </Tab.Navigator>
    );
  };
  
  return WrappedComponent;
};

const DrawerNavigator = () => {
  const { isDarkMode } = useContext(ThemeContext);
  const { isAuthenticated } = useSelector((state) => state.auth);
  const dispatch = useDispatch();

  const handleLogout = () => {
    dispatch(logout());
  };

  return (
    <Drawer.Navigator 
      screenOptions={({ route }) => ({
        headerShown: false,
        drawerStyle: {
          backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
        },
        drawerLabelStyle: {
          color: isDarkMode ? '#fff' : '#000',
        },
        drawerActiveTintColor: isDarkMode ? '#fff' : '#000',
        drawerInactiveTintColor: isDarkMode ? '#fff' : '#000',
        overlayColor: 'transparent',
        unmountOnBlur: true,
        detachInactiveScreens: true
      })}    ><Drawer.Screen 
        name="MainTabs" 
        component={TabNavigator} 
        options={{
          headerShown: false,
          drawerLabel: 'Home',
          drawerIcon: ({ size, color }) => (
            <Icons.Ionicons name="home-outline" size={size} color={color} />
          )
        }}
        listeners={({ navigation }) => ({
          drawerItemPress: () => {
            navigation.navigate('MainTabs', {
              screen: 'HomeTab',
              params: { screen: 'HomeScreen' }
            });
          }
        })}
      />{isAuthenticated ? (
        <React.Fragment>
        {/* profile option is commented out */}
        {/* <Drawer.Screen 
            name="Profile" 
            component={createScreenComponent(ProfileScreen, 'Profile')} 
            options={{
              drawerLabel: 'Profile',
              drawerIcon: ({ size, color }) => (
                <Icons.Ionicons name="person-outline" size={size} color={color} />
              )
            }}
          /> */}
          <Drawer.Screen
            name="Packages"
            component={createScreenComponent(PackagesScreen, 'Packages')}
            options={{
              drawerLabel: 'Packages',
              drawerIcon: ({ size, color }) => (
                <Icons.Ionicons name="cube-outline" size={size} color={color} />
              )
            }}
          />
          <Drawer.Screen
            name="Events"
            component={createScreenComponent(EventsScreen, 'Events')}
            options={{
              drawerLabel: 'Events & Activities',
              drawerIcon: ({ size, color }) => (
                <Icons.Ionicons name="calendar-outline" size={size} color={color} />
              )
            }}
          />
          <Drawer.Screen
            name="Cart"
            component={createScreenComponent(CartScreen, 'Shopping Cart')}
            options={{
              drawerLabel: 'Shopping Cart',
              drawerIcon: ({ size, color }) => (
                <Icons.Ionicons name="cart-outline" size={size} color={color} />
              )
            }}
          />
          <Drawer.Screen
            name="Progress"
            component={createScreenComponent(ProgressScreen, 'Progress & Friends')}
            options={{
              drawerLabel: 'Progress & Friends',
              drawerIcon: ({ size, color }) => (
                <MaterialCommunityIcons name="chart-line" size={size} color={color} />
              )
            }}
          />
          <Drawer.Screen
            name="Rewards"
            component={createScreenComponent(RewardScreen, 'Refer & Earn')}
            options={{
              drawerLabel: 'Refer & Earn',
              drawerIcon: ({ size, color }) => (
                <Icons.Ionicons name="gift-outline" size={size} color={color} />
              )
            }}
          /><Drawer.Screen
            name="RaiseQuery" 
            component={createScreenComponent(RaiseQueryScreen, 'Raise Query')} 
            options={{
              drawerLabel: 'Raise Query',
              drawerIcon: ({ size, color }) => (
                <Icons.Ionicons name="help-circle-outline" size={size} color={color} />
              )
            }}
          /><Drawer.Screen
            name="FAQScreen"
            component={createScreenComponent(FAQScreen, 'FAQs')}
            options={{
              drawerLabel: 'FAQs',
              drawerIcon: ({ size, color }) => (
                <Icons.Ionicons name="information-circle-outline" size={size} color={color} />
              )
            }}
          />
<Drawer.Screen
            name="Logout" 
            component={TabNavigator}
            listeners={{
              drawerItemPress: () => {
                handleLogout();
                return false;
              },
            }}
            options={{
              drawerLabel: 'Logout',
              drawerIcon: ({ size, color }) => (
                <Icons.Ionicons name="log-out-outline" size={size} color={color} />
              )
            }}
          /></React.Fragment>
      ) : (
        <React.Fragment><Drawer.Screen
            name="Login" 
            component={createScreenComponent(LoginScreen, 'Login')} 
            options={{
              drawerLabel: 'Login',
              drawerIcon: ({ size, color }) => (
                <Icons.Ionicons name="log-in-outline" size={size} color={color} />
              )
            }}
          /><Drawer.Screen 
            name="Signup" 
            component={createScreenComponent(SignupScreen, 'Sign Up')} 
            options={{
              drawerLabel: 'Sign Up',
              drawerIcon: ({ size, color }) => (                
                <Icons.Ionicons name="person-add-outline" size={size} color={color} />
              )
            }}
          /></React.Fragment>
      )}<Drawer.Screen 
        name="AboutUs" 
        component={createScreenComponent(AboutUsScreen, 'About Us')} 
        options={{
          drawerLabel: 'About Us',
          drawerIcon: ({ size, color }) => (
            <Icons.Ionicons name="information-outline" size={size} color={color} />
          )
        }}
      /><Drawer.Screen 
        name="PrivacyPolicy" 
        component={createScreenComponent(PrivacyPolicyScreen, 'Privacy Policy')} 
        options={{
          drawerLabel: 'Privacy Policy',
          drawerIcon: ({ size, color }) => (
            <Icons.Ionicons name="shield-outline" size={size} color={color} />
          )
        }}
      /><Drawer.Screen 
        name="ContactUs" 
        component={createScreenComponent(ContactUSScreen, 'Contact Us')} 
        options={{
          drawerLabel: 'Contact Us',
          drawerIcon: ({ size, color }) => (
            <Icons.Ionicons name="mail-outline" size={size} color={color} />
          )
        }}
      /><Drawer.Screen 
        name="Terms" 
        component={createScreenComponent(TermsAndConditionsScreen, 'Terms & Conditions')} 
        options={{
          drawerLabel: 'Terms & Conditions',
          drawerIcon: ({ size, color }) => (
            <Icons.Ionicons name="document-text-outline" size={size} color={color} />
          )
        }}
      /></Drawer.Navigator>
  );
};

export default DrawerNavigator;
