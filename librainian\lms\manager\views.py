from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.models import Group
from django.contrib.auth import logout
from django.db import IntegrityError
from .models import Manager_param
from Library.user_auth import *
from libraryCommander.models import *
from django.contrib.auth.decorators import login_required
from django.core.mail import send_mail
import threading
from django.contrib.auth.models import AnonymousUser
from django.http import JsonResponse
from membership.models import *
from django.utils import timezone
from datetime import timedelta


def manager_signup(request, *args, **kwargs):
    if request.method == "POST":
        try:
            library_commander = request.POST.get("library_commander")
            phone = request.POST.get("phone")
            address = request.POST.get("address")

            library_commander = LibraryCommander_param.objects.get(id=library_commander)

            manager = create_user_and_login(request)

            if manager:
                Manager_param.objects.create(
                    user=manager,
                    library_commander=library_commander,
                    manager_phone_num=phone,
                    manager_address=address,
                )

                group = Group.objects.get(name="manager")
                manager.groups.add(group)

                # Wlcome mail send
                # send_welcome_email(manager.email, manager.first_name, manager.last_name)

                return redirect("/manager/login/")
            else:
                messages.error(request, "Failed to create user. Please try again.")
        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")

    library_commander = LibraryCommander_param.objects.all()

    return render(
        request,
        "signup.html",
        {"role": "manager", "librarycommander": library_commander},
    )


def manager_login(request, *args, **kwargs):
    if request.method == "POST":
        try:
            manager = authenticate_and_login(request, "manager")
            if manager:
                return redirect("/manager/dashboard/")
            else:
                messages.error(
                    request, "Invalid credentials or you do not have manager access."
                )
        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")

    return render(request, "login.html", {"role": "manager"})


@login_required(login_url="/manager/login/")
def manager_logout(request, *args, **kwargs):
    try:
        logout(request)
    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")

    return redirect("/")


@login_required(login_url="/manager/login/")
def manager_profile(request, *args, **kwargs):
    manager_profile = Manager_param.objects.get(user=request.user)

    return render(
        request,
        "user_profile.html",
        {"lib": manager_profile, "role": "manager"},
    )


@login_required(login_url="/manager/login/")
def edit_manager_profile(request):
    try:
        # Fetch the logged-in user and their manager profile
        manager = request.user
        manager_profile = Manager_param.objects.get(user=manager)

        if request.method == "POST":
            # Get data from the form for the User model
            first_name = request.POST.get("first_name")
            last_name = request.POST.get("last_name")
            email = request.POST.get("email")

            # Get data for the Manager_param model
            phone = request.POST.get("phone")
            address = request.POST.get("address")

            try:
                # Update the User model fields
                manager.first_name = first_name
                manager.last_name = last_name
                manager.email = email
                manager.save()

                # Update the Manager_param model fields
                manager_profile.manager_phone_num = phone
                manager_profile.manager_address = address
                manager_profile.save()

                messages.success(request, "Profile updated successfully.")
                return redirect("/manager/profile/")  # Redirect to profile page

            except IntegrityError as e:
                error_message = str(e).lower()
                if "unique constraint" in error_message or "duplicate" in error_message:
                    if "email" in error_message:
                        messages.error(request, "A user with this email already exists.")
                    else:
                        messages.error(request, "A user with this information already exists.")
                else:
                    messages.error(request, "Database error occurred. Please try again.")

                # Return to the form with current data
                return render(
                    request,
                    "edit_profile.html",
                    {"profile": manager_profile, "manager": manager, "role": "manager"},
                )

        # Prepopulate the form with current data
        return render(
            request,
            "edit_profile.html",
            {"profile": manager_profile, "manager": manager, "role": "manager"},
        )
    except Manager_param.DoesNotExist:
        messages.error(request, "Manager profile not found.")
        return redirect("/manager/login/")  # Redirect to login if no profile is found
    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")
        return redirect("/manager/edit-profile/")


@login_required(login_url="/manager/login/")
def librarian_approval(request, slug, *args, **kwargs):
    user = request.user
    manager = Manager_param.objects.get(user=user)

    if manager.is_manager and user.is_authenticated:
        if request.method == "POST":
            librarian = get_object_or_404(Librarian_param, slug=slug)

            # Update the is_librarian field based on approval
            approval_action = request.POST.get("approval_action")
            if approval_action == "approve":
                librarian.is_librarian = True

                librarian.save()

                # Sending the approval email using threading
                thread = threading.Thread(target=send_approval_email, args=(librarian,))
                thread.start()
                messages.success(request, "Library give access successfully")
                return redirect("/manager/table/")

            elif approval_action == "reject":
                librarian.is_librarian = False
                librarian.save()

                # Sending the rejection email using threading
                thread = threading.Thread(
                    target=send_rejection_email, args=(librarian,)
                )
                thread.start()
                messages.error(request, "Library give access rejected successfully")
                return redirect("/manager/table/")

        librarian_approval_data = librarian = get_object_or_404(
            Librarian_param, slug=slug
        )
        return render(
            request,
            "librarian_approval.html",
            {
                "librarian": librarian_approval_data,
                "role": "manager",
            },
        )
    else:
        return render(request, "unauthorized.html", {"role": "manager"})


def send_approval_email(librarian):
    try:
        from django.conf import settings
        subject = "Library Approval Status"
        from_email = settings.EMAIL_HOST_USER or settings.DEFAULT_FROM_EMAIL
        to_email = librarian.user.email

        html_content = render_to_string(
            "templates/Welcome_mail_lib.html",
            {
                "librarian": librarian,
            },
        )
        text_content = strip_tags(html_content)

        msg = EmailMultiAlternatives(subject, text_content, from_email, [to_email])
        msg.attach_alternative(html_content, "text/html")
        msg.send()

    except Exception as e:
        print(f"Error sending approval email: {str(e)}")


def send_rejection_email(librarian):
    try:
        from django.conf import settings
        subject = "Library Approval Status"
        message = "We regret to inform you that your application for librarian has been rejected."
        from_email = settings.EMAIL_HOST_USER or settings.DEFAULT_FROM_EMAIL
        to_email = librarian.user.email

        send_mail(subject, message, from_email, [to_email])

    except Exception as e:
        print(f"Error sending rejection email: {str(e)}")


@login_required(login_url="/manager/login/")
def manager_dashboard(request):
    user = request.user

    if isinstance(user, AnonymousUser):
        return render(request, "unauthorized.html", {"role": "manager"})

    try:
        manager = Manager_param.objects.get(user=user)
    except Manager_param.DoesNotExist:
        return render(request, "unauthorized.html", {"role": "manager"})

    if manager.is_manager:
        return render(request, "dashboard.html", {"role": "manager"})
    else:
        return render(request, "unauthorized.html", {"role": "manager"})


@login_required(login_url="/manager/login/")
def manager_analytics_data(request):
    user = request.user

    if isinstance(user, AnonymousUser):
        return render(request, "unauthorized.html", {"role": "manager"})

    try:
        manager = Manager_param.objects.get(user=user)
    except Manager_param.DoesNotExist:
        return render(request, "unauthorized.html", {"role": "manager"})

    if manager.is_manager:
        return render(request, "analytics.html", {"role": "manager"})
    else:
        return render(request, "unauthorized.html", {"role": "manager"})


@login_required(login_url="/manager/login/")
def library_data(request, *args, **kwargs):
    user = request.user
    manager = Manager_param.objects.get(user=user)

    if manager.is_manager and user.is_authenticated:
        library_data = Librarian_param.objects.filter(manager=manager)

        return render(
            request, "table.html", {"library_data": library_data, "role": "manager"}
        )

    else:
        # messages.error(request, "You are not authorized to view this page.")
        return render(request, "unauthorized.html", {"role": "manager"})


def approve_library(request, slug):
    library = get_object_or_404(Librarian_param, slug=slug)
    library.is_librarian = True
    library.save()
    messages.success(request, "Library give access successfully")
    return redirect(
        "/manager/table/"
    )  # Replace with your actual library list view name


def reject_library(request, slug):
    library = get_object_or_404(Librarian_param, slug=slug)
    library.is_librarian = False
    library.save()
    messages.error(request, "Library give access rejected successfully")
    return redirect("/manager/table/")


@login_required(login_url="/manager/login/")
def help_page(request):
    return render(request, "help.html", {"role": "manager"})


@login_required(login_url="/manager/login/")
def feedback_page(request):
    return render(request, "feedback.html", {"role": "manager"})


def page_not_found_view(request, exception=None):
    return render(request, "404.html", {"role": "manager"})
