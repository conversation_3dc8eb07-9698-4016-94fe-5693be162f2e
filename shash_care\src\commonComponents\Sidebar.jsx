import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Nav } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
import { useSelector } from "react-redux";
import { 
  FaBars, 
  FaTachometerAlt,
  FaBook, 
  FaQuestionCircle, 
  FaClipboardList, 
  FaBlogger,
  FaBullhorn, 
  FaServer, 
  FaSpider, 
  FaAd, 
  FaRegFileAlt, 
  FaBell, 
  FaChartLine, 
  FaTags, 
  FaGift, 
  FaRupeeSign, 
  FaCalendarAlt,
  FaWalking,
  FaCheckCircle,
  FaFilePdf
} from "react-icons/fa";
import { BiSolidBookContent } from "react-icons/bi";

const Sidebar = () => {
  const [show, setShow] = useState(false);
  const accessTokenCustomer = useSelector((state) => state.customerCare?.access);

  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  // Navigation items from CustomerCareDashboard
  const navigationItems = [
    {
      name: "Dashboard",
      path: "/customer_care_dashboard",
      icon: FaTachometerAlt,
      variant: "primary"
    },
    {
      name: "Normal Questions",
      path: "/NormalQuestions",
      icon: FaBook,
      variant: "success"
    },
    {
      name: "Master Questions",
      path: "/MasterQuestions",
      icon: FaQuestionCircle,
      variant: "info"
    },
    {
      name: "Master Options",
      path: "/MasterOptions",
      icon: FaClipboardList,
      variant: "warning"
    },
    {
      name: "Blogs",
      path: "/Blogs",
      icon: FaBlogger,
      variant: "secondary"
    },
    {
      name: "Tickets",
      path: "/tickets",
      icon: FaBullhorn,
      variant: "danger"
    },
    {
      name: "Packages",
      path: "/packages",
      icon: FaServer,
      variant: "dark"
    },
    {
      name: "Signup Content",
      path: "/signup-content",
      icon: BiSolidBookContent,
      variant: "primary"
    },
    {
      name: "Student Error Logs",
      path: "/student-error-logs",
      icon: FaSpider,
      variant: "warning"
    },
    {
      name: "Banners",
      path: "/banner",
      icon: FaAd,
      variant: "info"
    },
    {
      name: "Notification Templates",
      path: "/notification-templates",
      icon: FaRegFileAlt,
      variant: "secondary"
    },
    {
      name: "FCM Dashboard",
      path: "/device-dashboard",
      icon: FaBell,
      variant: "success"
    },
    {
      name: "Analytics Dashboard",
      path: "/analytics-dashboard",
      icon: FaChartLine,
      variant: "primary"
    },
    {
      name: "Coupon Dashboard",
      path: "/coupon-dashboard",
      icon: FaTags,
      variant: "warning"
    },
    {
      name: "Gift Card Dashboard",
      path: "/gift-card-dashboard",
      icon: FaGift,
      variant: "danger"
    },
    {
      name: "Reward Dashboard",
      path: "/reward-dashboard",
      icon: FaRupeeSign,
      variant: "success"
    },
    {
      name: "Events Dashboard",
      path: "/events-dashboard",
      icon: FaCalendarAlt,
      variant: "info"
    },
    {
      name: "Pop Up Dashboard",
      path: "/pop-up-dashboard",
      icon: FaBullhorn,
      variant: "secondary"
    },
    {
      name: "Walk Around Dashboard",
      path: "/walk-arounf-dashboard",
      icon: FaWalking,
      variant: "dark"
    },
    {
      name: "Popup Approval",
      path: "/popup-approval-dashboard",
      icon: FaCheckCircle,
      variant: "success"
    },
    {
      name: "SOP Management",
      path: "/sop-dashboard",
      icon: FaFilePdf,
      variant: "danger"
    }
  ];

  // Only render if user is logged in
  if (!accessTokenCustomer) {
    return null;
  }

  return (
    <>
      {/* Toggle Button - Fixed position with high z-index */}
      <Button
        variant="primary"
        onClick={handleShow}
        style={{
          position: "fixed",
          top: "7px",
          left: "10px",
          zIndex: 1060,
          borderRadius: "50%",
          width: "50px",
          height: "50px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          boxShadow: "0 4px 8px rgba(0,0,0,0.2)"
        }}
        title="Open Navigation Menu"
      >
        <FaBars size={18} />
      </Button>

      {/* Sidebar Offcanvas */}
      <Offcanvas 
        show={show} 
        onHide={handleClose} 
        placement="start"
        style={{ zIndex: 1070 }}
        className="sidebar-offcanvas"
      >
        <Offcanvas.Header closeButton className="bg-primary text-white">
          <Offcanvas.Title>
            <FaTachometerAlt className="me-2" />
            Navigation Menu
          </Offcanvas.Title>
        </Offcanvas.Header>
        
        <Offcanvas.Body className="p-0">
          <Nav className="flex-column">
            {navigationItems.map((item, index) => {
              const IconComponent = item.icon;
              return (
                <Nav.Link
                  key={index}
                  as={Link}
                  to={item.path}
                  onClick={handleClose}
                  className={`d-flex align-items-center p-3 border-bottom text-decoration-none sidebar-nav-link`}
                  style={{
                    transition: "all 0.2s ease",
                    color: "#333"
                  }}
                >
                  <IconComponent 
                    size={18} 
                    className="me-3"
                    style={{ color: `var(--bs-${item.variant})` }}
                  />
                  <span className="fw-medium">{item.name}</span>
                </Nav.Link>
              );
            })}
          </Nav>
        </Offcanvas.Body>
      </Offcanvas>

      {/* Custom CSS for hover effects */}
      <style jsx>{`
        .sidebar-nav-link:hover {
          background-color: #f8f9fa !important;
          transform: translateX(5px);
        }
        
        .sidebar-offcanvas {
          width: 280px !important;
        }
        
        @media (max-width: 768px) {
          .sidebar-offcanvas {
            width: 250px !important;
          }
        }
      `}</style>
    </>
  );
};

export default Sidebar;
