# 🔧 CORRECTED Postman API Testing Guide - Working Endpoints

## ⚠️ **IMPORTANT: Updated URLs**

The streamlined contact system uses **NEW URLs**. The old URLs (`/upload/`, `/relationships/`) no longer exist.

## 🚀 **Base URL**
```
http://localhost:8000
```

## 🔐 **Step 1: Get Authentication Token**

**Method:** `POST`  
**URL:** `http://localhost:8000/api/students/login/`  
**Headers:**
```json
{
  "Content-Type": "application/json"
}
```
**Body (raw JSON):**
```json
{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "student": {...},
  "JWT_Token": {
    "refresh": "...",
    "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**Copy the `access` token for all subsequent requests.**

---

## 📱 **Step 2: Contact Sync (Main Feature)**

**Method:** `POST`  
**URL:** `http://localhost:8000/api/contacts/sync/`  ⚠️ **NEW URL**  
**Headers:**
```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```

**Body (raw JSON):**
```json
{
  "contacts": [
    {
      "name": "John Doe",
      "contact": "**********"
    },
    {
      "name": "Jane Smith", 
      "contact": "+91-*********1"
    },
    {
      "name": "Bob Johnson",
      "contact": "91 9876 543 212"
    },
    {
      "name": "Alice Brown",
      "contact": "*********3"
    }
  ]
}
```

**✅ Expected Success Response (201):**
```json
{
  "success": true,
  "message": "Successfully synced 4 contacts",
  "data": {
    "created": 4,
    "updated": 0,
    "matched": 2,
    "total_processed": 4,
    "errors": []
  }
}
```

---

## 📋 **Step 3: Get My Contacts**

**Method:** `GET`  
**URL:** `http://localhost:8000/api/contacts/my-contacts/`  
**Headers:**
```json
{
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```

**✅ Expected Response (200):**
```json
{
  "count": 4,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "John Doe",
      "contact_number": "**********",
      "is_matched": true,
      "related_user_info": {
        "username": "johndoe",
        "first_name": "John",
        "last_name": "Doe",
        "student_id": "STU001"
      },
      "synced_at": "2025-06-25T20:18:03.892311+05:30",
      "updated_at": "2025-06-25T20:18:03.892331+05:30"
    }
  ]
}
```

---

## 👥 **Step 4: Who's on the App? (Matched Contacts)**

**Method:** `GET`  
**URL:** `http://localhost:8000/api/contacts/matched/`  ⚠️ **NEW URL**  
**Headers:**
```json
{
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```

**✅ Expected Response (200):**
```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "John Doe",
      "contact_number": "**********",
      "is_matched": true,
      "related_user_info": {
        "username": "johndoe",
        "first_name": "John",
        "last_name": "Doe",
        "student_id": "STU001"
      },
      "synced_at": "2025-06-25T20:18:03.892311+05:30",
      "updated_at": "2025-06-25T20:18:03.892331+05:30"
    }
  ],
  "message": "Found 2 contacts who are registered users"
}
```

---

## 📊 **Step 5: Contact Statistics**

**Method:** `GET`  
**URL:** `http://localhost:8000/api/contacts/stats/`  
**Headers:**
```json
{
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```

**✅ Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "total_synced": 4,
    "total_matched": 2,
    "total_unmatched": 2,
    "last_sync": "2025-06-25T14:48:03.899167Z",
    "match_rate": 50.0
  }
}
```

---

## 🔍 **Step 6: Search Contacts**

**Method:** `GET`  
**URL:** `http://localhost:8000/api/contacts/search/`  
**Headers:**
```json
{
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```

**Query Parameters (at least one required):**
- `?query=John` - Search by name or number
- `?contact_number=**********` - Exact contact number
- `?is_matched=true` - Filter by match status

**Example URLs:**
- `http://localhost:8000/api/contacts/search/?query=John`
- `http://localhost:8000/api/contacts/search/?contact_number=**********`
- `http://localhost:8000/api/contacts/search/?is_matched=true`

---

## 🛠️ **Admin Endpoints (Admin Only)**

### **View All Contacts**
**Method:** `GET`  
**URL:** `http://localhost:8000/api/contacts/admin/contacts/`  
**Headers:**
```json
{
  "Authorization": "Bearer ADMIN_JWT_ACCESS_TOKEN_HERE"
}
```

---

## ❌ **Common Errors & Solutions**

### **1. 404 Not Found**
**Problem:** Using old URLs  
**Solution:** Use the new URLs:
- ❌ OLD: `/api/contacts/upload/`
- ✅ NEW: `/api/contacts/sync/`

- ❌ OLD: `/api/contacts/relationships/`
- ✅ NEW: `/api/contacts/matched/`

### **2. 401 Unauthorized**
**Problem:** Missing or invalid JWT token  
**Solution:** 
1. Login first to get token
2. Add `Authorization: Bearer YOUR_TOKEN` header

### **3. 403 Forbidden**
**Problem:** Wrong user role (customer care/contributor trying to access)  
**Solution:** Use student account for testing

### **4. 400 Bad Request**
**Problem:** Invalid contact data  
**Solution:** Ensure phone numbers are 10 digits, names are provided

---

## 🧪 **Quick Test Sequence**

### **1. Login**
```bash
curl -X POST http://localhost:8000/api/students/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'
```

### **2. Sync Contacts**
```bash
curl -X POST http://localhost:8000/api/contacts/sync/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"contacts": [{"name": "Test Contact", "contact": "**********"}]}'
```

### **3. Check Results**
```bash
curl -X GET http://localhost:8000/api/contacts/my-contacts/ \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## 📱 **Android App Integration**

### **Endpoint for Android**
```
POST http://your-domain.com/api/contacts/sync/
```

### **Request Format**
```json
{
  "contacts": [
    {"name": "Contact Name", "contact": "**********"}
  ]
}
```

### **Headers Required**
```
Content-Type: application/json
Authorization: Bearer JWT_TOKEN
```

---

## ✅ **Verification Checklist**

- [ ] Server is running (`python manage.py runserver`)
- [ ] Using correct URL: `/api/contacts/sync/` (not `/upload/`)
- [ ] JWT token is valid and not expired
- [ ] User is a student (not customer care/contributor)
- [ ] Content-Type header is set to `application/json`
- [ ] Contact numbers are valid (10 digits)

---

## 🎯 **Working Example**

Here's a complete working example that was tested successfully:

```bash
# 1. Get token (replace with your credentials)
TOKEN=$(curl -s -X POST http://localhost:8000/api/students/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}' \
  | jq -r '.JWT_Token.access')

# 2. Sync contacts
curl -X POST http://localhost:8000/api/contacts/sync/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"contacts": [{"name": "John Doe", "contact": "**********"}]}'

# 3. Check results
curl -X GET http://localhost:8000/api/contacts/my-contacts/ \
  -H "Authorization: Bearer $TOKEN"
```

**This guide contains the correct, tested, and working endpoints!** 🚀
