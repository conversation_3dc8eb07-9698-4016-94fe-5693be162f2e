#!/usr/bin/env python3
"""
Setup Windows Task Scheduler for Membership Expiry Notifications
This script creates a Windows scheduled task that runs daily at 4:51 PM
"""

import os
import sys
import subprocess
from pathlib import Path

def create_batch_file():
    """Create a batch file to run the Django command"""
    project_path = Path(__file__).parent.absolute()
    python_path = sys.executable
    
    batch_content = f"""@echo off
REM Membership Expiry Notification Task
REM Runs daily at 4:51 PM

REM Set environment variables
set DJANGO_SETTINGS_MODULE=Library.settings

REM Change to project directory
cd /d "{project_path}"

REM Run the membership expiry check
"{python_path}" manage.py check_membership_expiry >> membership_expiry.log 2>&1

REM Log the execution
echo Membership expiry check completed at %date% %time% >> membership_expiry.log
"""
    
    batch_path = project_path / "membership_expiry_task.bat"
    
    with open(batch_path, 'w') as f:
        f.write(batch_content)
    
    return batch_path

def create_task_scheduler_command():
    """Create the schtasks command to set up the scheduled task"""
    project_path = Path(__file__).parent.absolute()
    batch_path = create_batch_file()
    
    # Create the schtasks command
    task_name = "MembershipExpiryNotifications"
    
    schtasks_command = [
        "schtasks",
        "/create",
        "/tn", task_name,
        "/tr", str(batch_path),
        "/sc", "daily",
        "/st", "16:51",  # 4:51 PM
        "/f"  # Force create (overwrite if exists)
    ]
    
    return schtasks_command, task_name, batch_path

def setup_task_scheduler():
    """Set up the Windows Task Scheduler"""
    print("🪟 WINDOWS TASK SCHEDULER SETUP")
    print("=" * 50)
    
    try:
        schtasks_command, task_name, batch_path = create_task_scheduler_command()
        
        print(f"📁 Project path: {Path(__file__).parent.absolute()}")
        print(f"📄 Batch file created: {batch_path}")
        print(f"📅 Task name: {task_name}")
        print(f"⏰ Schedule: Daily at 4:51 PM")
        print()
        
        print("🚀 Creating scheduled task...")
        result = subprocess.run(schtasks_command, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Task created successfully!")
            print(f"📊 Output: {result.stdout}")
        else:
            print("❌ Failed to create task")
            print(f"📊 Error: {result.stderr}")
            return False
        
        # Verify the task was created
        print("\n🔍 Verifying task creation...")
        verify_command = ["schtasks", "/query", "/tn", task_name]
        verify_result = subprocess.run(verify_command, capture_output=True, text=True)
        
        if verify_result.returncode == 0:
            print("✅ Task verified successfully!")
            print("📋 Task details:")
            print(verify_result.stdout)
        else:
            print("⚠️ Could not verify task creation")
        
        return True
        
    except FileNotFoundError:
        print("❌ schtasks command not found")
        print("💡 Make sure you're running on Windows with administrative privileges")
        return False
    except Exception as e:
        print(f"❌ Error setting up task scheduler: {e}")
        return False

def show_manual_setup_instructions():
    """Show manual setup instructions for Task Scheduler"""
    project_path = Path(__file__).parent.absolute()
    python_path = sys.executable
    batch_path = project_path / "membership_expiry_task.bat"
    
    print("\n📋 MANUAL TASK SCHEDULER SETUP")
    print("=" * 50)
    print("If automatic setup failed, follow these steps:")
    print()
    
    print("1. 🪟 Open Task Scheduler:")
    print("   - Press Win + R")
    print("   - Type: taskschd.msc")
    print("   - Press Enter")
    print()
    
    print("2. 📝 Create Basic Task:")
    print("   - Click 'Create Basic Task' in the right panel")
    print("   - Name: Membership Expiry Notifications")
    print("   - Description: Daily check for membership expiry notifications")
    print("   - Click Next")
    print()
    
    print("3. ⏰ Set Trigger:")
    print("   - Select 'Daily'")
    print("   - Click Next")
    print("   - Start date: Today")
    print("   - Start time: 4:51 PM")
    print("   - Recur every: 1 days")
    print("   - Click Next")
    print()
    
    print("4. 🎯 Set Action:")
    print("   - Select 'Start a program'")
    print("   - Click Next")
    print(f"   - Program/script: {batch_path}")
    print(f"   - Start in: {project_path}")
    print("   - Click Next")
    print()
    
    print("5. ✅ Finish:")
    print("   - Review settings")
    print("   - Check 'Open the Properties dialog...'")
    print("   - Click Finish")
    print()
    
    print("6. 🔧 Advanced Settings (in Properties dialog):")
    print("   - General tab: Check 'Run whether user is logged on or not'")
    print("   - Settings tab: Check 'Run task as soon as possible after a scheduled start is missed'")
    print("   - Click OK")
    print()

def test_batch_file():
    """Test the batch file to make sure it works"""
    print("\n🧪 TESTING BATCH FILE")
    print("=" * 30)
    
    try:
        batch_path = create_batch_file()
        print(f"📄 Testing batch file: {batch_path}")
        
        # Run the batch file
        result = subprocess.run([str(batch_path)], capture_output=True, text=True, shell=True)
        
        print(f"📊 Return code: {result.returncode}")
        
        if result.stdout:
            print("📤 Output:")
            print(result.stdout)
        
        if result.stderr:
            print("📥 Errors:")
            print(result.stderr)
        
        # Check if log file was created
        log_path = batch_path.parent / "membership_expiry.log"
        if log_path.exists():
            print(f"✅ Log file created: {log_path}")
            
            # Show last few lines of log
            with open(log_path, 'r') as f:
                lines = f.readlines()
                if lines:
                    print("📋 Last log entries:")
                    for line in lines[-5:]:
                        print(f"   {line.strip()}")
        else:
            print("⚠️ No log file created")
        
        if result.returncode == 0:
            print("✅ Batch file test successful!")
            return True
        else:
            print("❌ Batch file test failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing batch file: {e}")
        return False

def show_monitoring_instructions():
    """Show how to monitor the scheduled task"""
    print("\n📊 MONITORING INSTRUCTIONS")
    print("=" * 40)
    
    print("1. 🔍 Check Task Status:")
    print("   - Open Task Scheduler")
    print("   - Find 'MembershipExpiryNotifications' task")
    print("   - Check 'Last Run Result' column")
    print("   - 0x0 means success")
    print()
    
    print("2. 📋 View Task History:")
    print("   - Right-click the task")
    print("   - Select 'Properties'")
    print("   - Go to 'History' tab")
    print("   - Check for execution events")
    print()
    
    print("3. 📄 Check Log Files:")
    project_path = Path(__file__).parent.absolute()
    log_path = project_path / "membership_expiry.log"
    print(f"   - Log file: {log_path}")
    print("   - Open in notepad to see execution logs")
    print()
    
    print("4. 🧪 Test Manually:")
    print("   - Right-click the task")
    print("   - Select 'Run'")
    print("   - Check if notifications appear")
    print()
    
    print("5. 🔧 Troubleshooting:")
    print("   - Make sure Django server is NOT required to be running")
    print("   - Check Windows Event Viewer for errors")
    print("   - Verify FCM tokens are registered")
    print("   - Test the batch file manually first")

def main():
    print("🪟 WINDOWS TASK SCHEDULER SETUP FOR MEMBERSHIP EXPIRY")
    print("=" * 70)
    print("Setting up daily notifications at 4:51 PM using Windows Task Scheduler")
    print()
    
    # Test batch file first
    batch_test = test_batch_file()
    
    if not batch_test:
        print("❌ Batch file test failed. Please fix issues before setting up scheduler.")
        return 1
    
    # Try automatic setup
    print("\n🚀 Attempting automatic setup...")
    auto_setup = setup_task_scheduler()
    
    if auto_setup:
        print("\n🎉 SUCCESS! Task Scheduler setup complete!")
        print("📅 Your task will run daily at 4:51 PM")
        print("🔔 Notifications will be sent for membership expiry")
    else:
        print("\n⚠️ Automatic setup failed. Please use manual setup.")
    
    # Show manual instructions regardless
    show_manual_setup_instructions()
    show_monitoring_instructions()
    
    print("\n" + "=" * 70)
    print("✅ SETUP COMPLETE!")
    print("=" * 70)
    print("Your Windows system is now configured to check membership expiry daily.")
    print("The task will run at 4:51 PM and send push notifications as needed.")
    print()
    print("🧪 IMMEDIATE TESTING:")
    print("• Use Django admin actions for immediate testing")
    print("• Run the batch file manually to test")
    print("• Use Task Scheduler 'Run' option to test")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
