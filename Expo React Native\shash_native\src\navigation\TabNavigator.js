import React, { useContext } from 'react';
import { Image, View, Text, Platform, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { calculateTabBarHeight, getTabBarPadding } from '../utils/tabBarUtils';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import * as Icons from '@expo/vector-icons';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { DrawerActions, getFocusedRouteNameFromRoute } from '@react-navigation/native';
import RaiseQueryScreen from '../screens/RaiseQueryScreen';
import { ThemeContext } from '../context/ThemeContext';
import HomeScreen from '../screens/HomeScreen';
import SavedTestSeriesScreen from '../screens/SavedTestSeriesScreen';
import ProfileScreen from '../screens/auth/ProfileScreen';
import TestSeriesDetailsScreen from '../screens/TestSeriesDetailsScreen';
import ChatBotScreen from '../screens/ChatBotScreen';
import HeaderMenu from '../components/HeaderMenu';
import FAQScreen from '../screens/FAQScreen';
import PackagesScreen from '../screens/PackagesScreen';
import CheckoutScreen from '../screens/Checkout';
import RewardsScreen from '../screens/RewardScreen';
import AboutUsScreen from '../screens/drawer/AboutUsScreen';
import PrivacyPolicyScreen from '../screens/drawer/PrivacyPolicyScreen';
import CartScreen from '../screens/CartScreen';
import PaymentScreen from '../screens/PaymentScreen';
import PaymentSuccessScreen from '../screens/PaymentSuccessScreen';
import PaymentFailureScreen from '../screens/PaymentFailureScreen';
import OrderHistoryScreen from '../screens/OrderHistoryScreen';
import OrderDetailsScreen from '../screens/OrderDetailsScreen';
import ProgressScreen from '../screens/ProgressScreen';

const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();

// We already have these screens imported above

// Shared header options creator
const createCommonHeaderOptions = (isDarkMode, toggleTheme) => ({
  headerTitleStyle: {
    fontWeight: 'bold',
    color: isDarkMode ? '#fff' : '#000',
  },
  headerStyle: {
    backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
  },
  headerTintColor: isDarkMode ? '#fff' : '#000',
  headerRight: () => (
    <HeaderMenu onToggleDarkMode={toggleTheme} isDarkMode={isDarkMode} />
  ),
  headerRightContainerStyle: {
    paddingRight: 10,
  },
});

// Common function to determine if tab bar should be hidden
const getTabBarVisibility = (route) => {
  const hideTabBarScreens = [
    'TestSeriesDetails',
    'RaiseQuery',
    'FAQScreen',
    'PackagesScreen',
    'Profile',
    'AboutUs',
    'PrivacyPolicy',
    'PaymentSuccessScreen',
    'PaymentFailureScreen',
    'OrderHistoryScreen',
    'OrderDetailsScreen',
    'ProgressScreen'
  ];
  
  const routeName = getFocusedRouteNameFromRoute(route);
  if (!routeName) return true;
  
  // Always show tab bar for main tab screens
  const mainScreens = ['HomeScreen', 'SavedTestSeries', 'AcharyaBot'];
  if (mainScreens.includes(routeName)) return true;
  
  return !hideTabBarScreens.includes(routeName);
};

function withCustomBackButton(options, isDarkMode) {
  return ({ navigation, route }) => ({
    ...options,
    headerLeft: () =>
      route.name !== 'HomeScreen' ? (
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{ marginLeft: 0, padding: 4 }} // Remove extra margin for all
        >
          <Icons.Ionicons name="arrow-back" size={24} color={isDarkMode ? "#fff" : "#000"} />
        </TouchableOpacity>
      ) : null,
  });
}

function HomeStack() {
  const { isDarkMode, toggleTheme } = useContext(ThemeContext);
  const commonHeaderOptions = createCommonHeaderOptions(isDarkMode, toggleTheme);
  return (
    <Stack.Navigator initialRouteName="HomeScreen">
      <Stack.Screen
        name="HomeScreen"
        component={HomeScreen}
        options={({ navigation }) => ({
          headerTitle: () => (
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Image
                source={require('../assets/placeholder.png')}
                style={{ width: 170, height: 50 }}
                resizeMode="contain"
              />
            </View>
          ),
          ...commonHeaderOptions,
          headerLeft: () => null,
          headerRight: () => (
            <HeaderMenu onToggleDarkMode={toggleTheme} isDarkMode={isDarkMode} />
          ),
          headerRightContainerStyle: {
            paddingRight: 10,
          },
        })}
      />
      <Stack.Screen
        name="RaiseQuery"
        component={RaiseQueryScreen}
        options={withCustomBackButton({
          title: 'Raise a Query',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="Profile"
        component={ProfileScreen}
        options={withCustomBackButton({
          title: 'My Profile',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="TestSeriesDetails"
        component={TestSeriesDetailsScreen}
        options={withCustomBackButton({
          title: 'Test Series Details',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="FAQScreen"
        component={FAQScreen}
        options={withCustomBackButton({
          title: 'FAQs',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PackagesScreen"
        component={PackagesScreen}
        options={withCustomBackButton({
          title: 'Packages',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="CheckoutScreen"
        component={CheckoutScreen}
        options={withCustomBackButton({
          title: 'Checkout',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="CartScreen"
        component={CartScreen}
        options={withCustomBackButton({
          title: 'Shopping Cart',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PaymentScreen"
        component={PaymentScreen}
        options={withCustomBackButton({
          title: 'Payment',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PaymentSuccessScreen"
        component={PaymentSuccessScreen}
        options={withCustomBackButton({
          title: 'Payment Successful',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PaymentFailureScreen"
        component={PaymentFailureScreen}
        options={withCustomBackButton({
          title: 'Payment Failed',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="OrderHistoryScreen"
        component={OrderHistoryScreen}
        options={withCustomBackButton({
          title: 'Order History',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="OrderDetailsScreen"
        component={OrderDetailsScreen}
        options={withCustomBackButton({
          title: 'Order Details',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="ProgressScreen"
        component={ProgressScreen}
        options={withCustomBackButton({
          title: 'Your Progress',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
    </Stack.Navigator>
  );
}

function SavedStack() {
  const { isDarkMode, toggleTheme } = useContext(ThemeContext);
  const commonHeaderOptions = createCommonHeaderOptions(isDarkMode, toggleTheme);
  return (
    <Stack.Navigator initialRouteName="SavedTestSeries">
      <Stack.Screen
        name="SavedTestSeries"
        component={SavedTestSeriesScreen}
        options={withCustomBackButton({
          title: 'Saved Test Series',
          ...commonHeaderOptions
        }, isDarkMode)}
      />
      <Stack.Screen
        name="TestSeriesDetails"
        component={TestSeriesDetailsScreen}
        options={withCustomBackButton({
          title: 'Test Series Details',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="RaiseQuery"
        component={RaiseQueryScreen}
        options={withCustomBackButton({
          title: 'Raise a Query',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="FAQScreen"
        component={FAQScreen}
        options={withCustomBackButton({
          title: 'FAQs',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PackagesScreen"
        component={PackagesScreen}
        options={withCustomBackButton({
          title: 'Packages',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="CheckoutScreen"
        component={CheckoutScreen}
        options={withCustomBackButton({
          title: 'Checkout',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="CartScreen"
        component={CartScreen}
        options={withCustomBackButton({
          title: 'Shopping Cart',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PaymentScreen"
        component={PaymentScreen}
        options={withCustomBackButton({
          title: 'Payment',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PaymentSuccessScreen"
        component={PaymentSuccessScreen}
        options={withCustomBackButton({
          title: 'Payment Successful',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PaymentFailureScreen"
        component={PaymentFailureScreen}
        options={withCustomBackButton({
          title: 'Payment Failed',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="OrderHistoryScreen"
        component={OrderHistoryScreen}
        options={withCustomBackButton({
          title: 'Order History',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="OrderDetailsScreen"
        component={OrderDetailsScreen}
        options={withCustomBackButton({
          title: 'Order Details',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
    </Stack.Navigator>
  );
}

function ChatBotStack() {
  const { isDarkMode, toggleTheme } = useContext(ThemeContext);
  const commonHeaderOptions = createCommonHeaderOptions(isDarkMode, toggleTheme);
  return (
    <Stack.Navigator initialRouteName="AcharyaBot">
      <Stack.Screen
        name="AcharyaBot"
        component={ChatBotScreen}
        options={withCustomBackButton({
          headerTitle: () => (
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <MaterialCommunityIcons
                name="robot"
                size={24}
                color={isDarkMode ? '#fff' : '#000'}
                style={{ marginRight: 8 }}
              />
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: isDarkMode ? '#fff' : '#000',
                }}
              >
                Acharya AI Chat Bot
              </Text>
            </View>
          ),
          headerTitleAlign: 'left',
          headerStyle: {
            backgroundColor: isDarkMode ? '#1a1a1a' : '#198754',
          },
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="RaiseQuery"
        component={RaiseQueryScreen}
        options={withCustomBackButton({
          title: 'Raise a Query',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="FAQScreen"
        component={FAQScreen}
        options={withCustomBackButton({
          title: 'FAQs',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PackagesScreen"
        component={PackagesScreen}
        options={withCustomBackButton({
          title: 'Packages',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="CheckoutScreen"
        component={CheckoutScreen}
        options={withCustomBackButton({
          title: 'Checkout',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="CartScreen"
        component={CartScreen}
        options={withCustomBackButton({
          title: 'Shopping Cart',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PaymentScreen"
        component={PaymentScreen}
        options={withCustomBackButton({
          title: 'Payment',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PaymentSuccessScreen"
        component={PaymentSuccessScreen}
        options={withCustomBackButton({
          title: 'Payment Successful',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PaymentFailureScreen"
        component={PaymentFailureScreen}
        options={withCustomBackButton({
          title: 'Payment Failed',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="OrderHistoryScreen"
        component={OrderHistoryScreen}
        options={withCustomBackButton({
          title: 'Order History',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
      <Stack.Screen
        name="OrderDetailsScreen"
        component={OrderDetailsScreen}
        options={withCustomBackButton({
          title: 'Order Details',
          ...commonHeaderOptions,
        }, isDarkMode)}
      />
    </Stack.Navigator>
  );
}

function RewardsStack() {
  const { isDarkMode, toggleTheme } = useContext(ThemeContext);
  const commonHeaderOptions = createCommonHeaderOptions(isDarkMode, toggleTheme);
  return (
    <Stack.Navigator initialRouteName="RewardsScreen">
      <Stack.Screen
        name="RewardsScreen"
        component={RewardsScreen}
        options={withCustomBackButton({
          title: 'Refer & Earn',
          ...commonHeaderOptions
        }, isDarkMode)}
      />
      <Stack.Screen
        name="RaiseQuery"
        component={RaiseQueryScreen}
        options={withCustomBackButton({
          title: 'Raise Query',
          ...commonHeaderOptions
        }, isDarkMode)}
      />
      <Stack.Screen
        name="FAQScreen"
        component={FAQScreen}
        options={withCustomBackButton({
          title: 'FAQs',
          ...commonHeaderOptions
        }, isDarkMode)}
      />
      <Stack.Screen
        name="AboutUs"
        component={AboutUsScreen}
        options={withCustomBackButton({
          title: 'About Us',
          ...commonHeaderOptions
        }, isDarkMode)}
      />
      <Stack.Screen
        name="PrivacyPolicy"
        component={PrivacyPolicyScreen}
        options={withCustomBackButton({
          title: 'Privacy Policy',
          ...commonHeaderOptions
        }, isDarkMode)}
      />
    </Stack.Navigator>
  );
}

function MoreStack() {
  const { isDarkMode, toggleTheme } = useContext(ThemeContext);
  const commonHeaderOptions = createCommonHeaderOptions(isDarkMode, toggleTheme);
  
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="MoreScreen"
        component={AboutUsScreen} // Using AboutUsScreen as placeholder
        options={withCustomBackButton({
          title: 'More',
          ...commonHeaderOptions
        }, isDarkMode)}
      />
    </Stack.Navigator>
  );
}

const TabNavigator = () => {
  const { isDarkMode } = useContext(ThemeContext);
  const insets = useSafeAreaInsets();

  const tabBarHeight = calculateTabBarHeight(insets);
  const tabBarPadding = getTabBarPadding(insets);

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        unmountOnBlur: true,
        tabBarHideOnKeyboard: true,
        tabBarStyle: {
          height: tabBarHeight,
          backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
          borderTopColor: isDarkMode ? '#333' : '#e0e0e0',
          borderTopWidth: 1,
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: -2,
          },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 8,
          display: getTabBarVisibility(route) ? 'flex' : 'none',
          paddingTop: tabBarPadding.paddingTop,
          paddingBottom: tabBarPadding.paddingBottom,
          paddingHorizontal: 10,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
          marginBottom: 5,
        },
        tabBarActiveTintColor: '#198754',
        tabBarInactiveTintColor: isDarkMode ? '#666' : '#999',
        tabBarIcon: ({ focused, color, size }) => {
          let iconName = 'home';
          
          if (route.name === 'HomeTab') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'SavedTab') {
            iconName = focused ? 'bookmark' : 'bookmark-outline';
          } else if (route.name === 'ChatbotTab') {
            return (
              <MaterialCommunityIcons
                name="robot"
                size={size}
                color={focused ? '#198754' : color}
              />
            );
          } else if (route.name === 'MoreTab') {
            iconName = focused ? 'menu' : 'menu-outline';
          }
          
          return (
            <Icons.Ionicons
              name={iconName}
              size={size}
              color={focused ? '#198754' : color}
            />
          );
        }
      })}
    >
      <Tab.Screen 
        name="HomeTab"
        component={HomeStack}
        options={{
          title: 'Home',
          tabBarLabel: 'Home'
        }}
      />
      <Tab.Screen 
        name="SavedTab"
        component={SavedStack}
        options={{
          title: 'Saved',
          tabBarLabel: 'Saved'
        }}
      />
      <Tab.Screen 
        name="ChatbotTab"
        component={ChatBotStack}
        options={{
          title: 'Acharya AI',
          tabBarLabel: 'Acharya AI'
        }}
      />
      <Tab.Screen 
        name="MoreTab"
        component={MoreStack}
        options={{
          title: 'More',
          tabBarLabel: 'More'
        }}
        listeners={({ navigation }) => ({
          tabPress: (e) => {
            // Prevent default navigation
            e.preventDefault();
            // Open drawer
            navigation.dispatch(DrawerActions.openDrawer());
          },
        })}
      />
    </Tab.Navigator>
  );
};

export default TabNavigator;
