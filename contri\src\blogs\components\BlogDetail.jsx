import React, { useEffect, useState, Suspense, useMemo } from "react";
import {
  Container,
  Row,
  Col,
  Image,
  Button,
  Modal,
  Breadcrumb,
} from "react-bootstrap";
import { useDispatch } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import { getBlog } from "../../redux/slice/blogSlice";
import toast from "react-hot-toast";
import parse from "html-react-parser";
// import LoadingScreen from "../../commonComponents/LoadingScreen";
import Skeleton from "react-loading-skeleton"; // Import Skeleton
import "react-loading-skeleton/dist/skeleton.css"; // Import skeleton styles
import logo from "../../assets/logo.png";

// Import the HelmetAsync component from react-helmet-async
import { Helmet } from "react-helmet-async";
import NavigationBar from "../../commonComponents/NavigationBar";
import {
  FaFacebook,
  FaLinkedin,
  FaS<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "react-icons/fa";

const BlogDetail = () => {
  const { slug } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [blog, setBlog] = useState(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const baseUrl = import.meta.env.VITE_BASE_URL;
  const hostedDomainUrl = import.meta.env.VITE_HOSTED_DOMAIN;

  const defaultImage = "../../../../assets/default.jpeg";

  // Fetching the blog post using the provsluged slug
  useEffect(() => {
    const fetchBlog = async () => {
      try {
        const response = await dispatch(getBlog(slug));
        if (response?.payload) {
          setBlog(response.payload);
        } else {
          toast.error("Error fetching blog");
        }
      } catch (error) {
        toast.error("Error fetching blog");
      }
    };
    fetchBlog();
  }, [dispatch, slug]);

  // Formatting dates using memoization to avoslug recalculating on each render
  const formattedPublishedDate = useMemo(
    () => blog?.published_date ? new Date(blog?.published_date).toLocaleDateString() : null,
    [blog?.published_date]
  );

  const formattedUpdatedDate = useMemo(
    () =>
      blog?.updated_date ? new Date(blog.updated_date).toLocaleString() : null,
    [blog?.updated_date]
  );

  // // Show loading screen if blog data is not yet available
  // if (!blog) {
  //   return <LoadingScreen />;
  // }

  // Function to process links in the blog content
  const processLinks = (content) => {
    return content
      .replace(/<a href="(http[s]?:\/\/(?:www\.)?[^"]+)"/g, (match, url) => {
        return `<a href="${url}" target="_blank" rel="noopener noreferrer"`;
      })
      .replace(/<a href="\/([^"]+)"/g, (match, path) => {
        return `<a href="/${path}" as={Link}`;
      });
  };

  // Function to handle social media sharing
  const handleShare = (platform) => {
    const shareUrl = `${hostedDomainUrl}/blog/${slug}`;

    let shareLink = "";

    switch (platform) {
      case "facebook":
        shareLink = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
          shareUrl
        )}`;
        break;
      case "twitter":
        shareLink = `https://twitter.com/intent/tweet?url=${encodeURIComponent(
          shareUrl
        )}&text=${encodeURIComponent(blog?.title)}`;
        break;
      case "linkedin":
        shareLink = `https://www.linkedin.com/shareArticle?mini=true&url=${encodeURIComponent(
          shareUrl
        )}&title=${encodeURIComponent(
          blog?.title
        )}&summary=${encodeURIComponent(blog?.meta_description)}`;
        break;
      case "whatsapp":
        shareLink = `https://wa.me/?text=${encodeURIComponent(
          `${blog?.title} ${shareUrl}`
        )}`;
        break;
      default:
        break;
    }

    window.open(shareLink, "_blank");
  };

  // for truncateText

  const truncateText = (text) => {
    if (!text) return "";
    const words = text.split(" ");
    return words.length > 3 ? `${words.slice(0, 3).join(" ")}...` : text;
  };

  return (
    <>
      <NavigationBar />
      <Container
        as="article"
        aria-labelledby="blog-title"
        className="mt-5 bg-white px-3"
        style={{ fontFamily: "'Times New Roman', Times, serif" }}
      >
        {/* Helmet Component to manage meta tags */}
        <Helmet>
          <title>{blog?.meta_title}</title>
          <meta name="description" content={blog?.meta_description} />
          <meta name="keywords" content={blog?.meta_keywords} />
          <link rel="canonical" href={blog?.canonical_url} />

          {/* Open Graph Meta Tags */}
          <meta property="og:title" content={blog?.open_graph?.["og:title"]} />
          <meta
            property="og:description"
            content={blog?.open_graph?.["og:description"]}
          />
          <meta property="og:image" content={blog?.open_graph?.["og:image"]} />
          <meta property="og:url" content={blog?.canonical_url} />
          <meta property="og:type" content="website" />

          {/* Twitter Card Meta Tags */}
          <meta
            name="twitter:title"
            content={blog?.twitter_cards?.["twitter:title"]}
          />
          <meta
            name="twitter:description"
            content={blog?.twitter_cards?.["twitter:description"]}
          />
          <meta
            name="twitter:image"
            content={blog?.twitter_cards?.["twitter:image"]}
          />

          <meta name="twitter:card" content="summary_large_image" />

          {/* Breadcrumb Schema */}
          <script type="application/ld+json">
            {JSON.stringify(blog?.breadcrumb_schema)}
          </script>

          {/* Article Schema */}
          <script type="application/ld+json">
            {JSON.stringify(blog?.article_schema)}
          </script>
        </Helmet>

        <section style={{ minHeight: "85vh" }}>
          {/* Breadcrumb */}
          <Row className="justify-content-center">
            <Col xs={12} md={8}>
              <Breadcrumb>
                {blog?.breadcrumb_schema?.itemListElement?.map(
                  (item, index) => (
                    <Breadcrumb.Item
                      key={index}
                      onClick={() => {
                        const url = item.item.startsWith("http")
                          ? item.item
                          : `https://${item.item}`;
                        window.location.href = url;
                      }}
                      active={
                        index ===
                        blog?.breadcrumb_schema.itemListElement.length - 1
                      }
                      style={{ cursor: "pointer" }}
                    >
                      {/* Truncated text for small screens */}
                      <span className="d-sm-none text-truncate">
                        {truncateText(item.name)}
                      </span>

                      {/* Full text for medium and larger screens */}
                      <span className="d-none d-sm-inline">{item.name}</span>
                    </Breadcrumb.Item>
                  )
                )}
              </Breadcrumb>
            </Col>
          </Row>

          <header>
            <Row className="justify-content-center mb-4">
              <Col xs={12} md={8}>
                <div className="d-flex justify-content-between border-bottom py-4 mb-4 ">
                {
                  formattedPublishedDate ? (
                    <a
                    href="https://shashtrarth.com/"
                    className="text-decoration-none text-dark"
                  >
                    shashtrarth.com
                  </a>
                  ) : (
                    <Skeleton width={100} />
                  )
                }
                  {formattedPublishedDate ? (
                    <span>Published On: {formattedPublishedDate}</span>
                  ) : (
                    <Skeleton width={100} />
                  )}
                </div>
                <h1
                  slug="blog-title"
                  className="display-4 text-center fw-bold mt-3 mb-3 letter-spacing-1"
                >
                  {blog?.title || <Skeleton wslugth={300} />}
                </h1>
                <p className="mt-2 mb-2 text-muted blog-meta-description">
                  <em>{blog?.introduction || <Skeleton wslugth={250} />}</em>
                </p>
              </Col>
            </Row>
          </header>

          <div>
            <Row className="d-flex justify-content-center align-items-center mb-2">
              <Col xs={12} md={4}>
                <div className="info-box updated-on">
                  {formattedUpdatedDate ? (
                    <span>Updated On: {formattedUpdatedDate}</span>
                  ) : (
                    <Skeleton width={100} />
                  )}
                </div>
              </Col>
              <Col xs={12} md={4}>
                <div className="info-box author-info">
                  {blog?.author_first_name ? (
                    <span>
                      Author: {blog?.author_first_name} {blog?.author_last_name}
                    </span>
                  ) : (
                    <Skeleton width={200} />
                  )}
                </div>
              </Col>
            </Row>
          </div>

          <div>
            <Row className="justify-content-center align-items-center mb-4">
              <Col xs={12} md={8}>
                <Row className="justify-content-between">
                  <Col xs={6} md={4}>
                    <div className="updated-on">
                    {formattedUpdatedDate ? (
                      <span>Total Views: 100k +</span>
                  ) : (
                    <Skeleton width={100} />
                  )}
                      
                    </div>
                  </Col>
                  <Col xs={6} md={4} className="d-flex justify-content-end">
                    <Button
                      variant="link"
                      onClick={() => setShowShareModal(true)}
                      className="share-button text-end"
                    >
                      {formattedUpdatedDate ? (
                        <span><FaShareAlt size={16} className="mr-2" />
                      Share this Blog</span>
                  ) : (
                    <Skeleton width={100} />
                  )}
                    </Button>
                  </Col>
                </Row>
              </Col>
            </Row>
          </div>
        </section>

        <section>
          <Row className="justify-content-center">
            <Col xs={12} md={8} className="text-center">
              <Suspense fallback={<Skeleton count={1} />}>
                <Image
                  src={`${baseUrl}${blog?.image ? blog.image : defaultImage}`}
                  alt={blog?.image_caption || "Blog Image"}
                  fluid
                  rounded
                  className="footer-image"
                  loading="lazy"
                  aria-label="Blog Image"
                />
              </Suspense>
              <p className="text-muted mt-2 footer-image-caption">
                {blog?.image_caption || <Skeleton width={250} />}
              </p>
            </Col>
            <Col xs={12} md={8}>
              <Suspense fallback={<Skeleton count={5} />}>
                <div className="content-column d-none d-md-block">
                  {(blog?.content && parse(processLinks(blog?.content))) || (
                    <Skeleton count={5} />
                  )}
                </div>
                <div className="content-column d-block d-md-none">
                  {(blog?.content && parse(processLinks(blog?.content))) || (
                    <Skeleton count={5} />
                  )}
                </div>
              </Suspense>
            </Col>
          </Row>
        </section>

        <footer>
          <Row className="justify-content-center">
            <Col xs={12} md={8} className="text-center">
              <div className="mt-4 d-flex justify-content-between align-items-center">
                <div>
                  <a href={blog?.internal_link}>
                    <Button variant="link">
                      <img
                        src={logo}
                        alt="Logo"
                        width="180"
                        height="auto"
                        className="d-inline-block align-top"
                      />
                    </Button>
                  </a>
                </div>
                <div>
                  <a href="/blogs">
                    <Button variant="link text-success">
                      See other Articles
                    </Button>
                  </a>
                </div>
              </div>
            </Col>
          </Row>
        </footer>
      </Container>
      {/* Share Modal */}
      <Modal
        show={showShareModal}
        onHide={() => setShowShareModal(false)}
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>Share Blog</Modal.Title>
        </Modal.Header>
        <Modal.Body className="d-flex justify-content-center">
          {/* Facebook */}
          <Button
            variant="link"
            onClick={() => handleShare("facebook")}
            className="social-share-btn facebook"
          >
            <FaFacebook size={24} />
            <span className="social-share-text">Facebook</span>
          </Button>

          {/* Twitter */}
          <Button
            variant="link"
            onClick={() => handleShare("twitter")}
            className="social-share-btn twitter"
          >
            <FaTwitter size={24} />
            <span className="social-share-text">Twitter</span>
          </Button>

          {/* LinkedIn */}
          <Button
            variant="link"
            onClick={() => handleShare("linkedin")}
            className="social-share-btn linkedin"
          >
            <FaLinkedin size={24} />
            <span className="social-share-text">LinkedIn</span>
          </Button>

          {/* WhatsApp */}
          <Button
            variant="link"
            onClick={() => handleShare("whatsapp")}
            className="social-share-btn whatsapp"
          >
            <FaWhatsapp size={24} />
            <span className="social-share-text">WhatsApp</span>
          </Button>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default BlogDetail;
