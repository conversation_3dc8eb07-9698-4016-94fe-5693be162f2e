import React, { useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import { ThemeContext } from '../context/ThemeContext';

const { width } = Dimensions.get('window');

const PaymentFailureScreen = ({ route, navigation }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const { 
    error = 'Payment failed', 
    canRetry = true,
    orderData = null 
  } = route.params || {};

  const handleRetryPayment = () => {
    // Go back to payment screen
    navigation.goBack();
  };

  const handleGoHome = () => {
    navigation.navigate('HomeScreen');
  };

  const handleContactSupport = () => {
    navigation.navigate('RaiseQuery');
  };

  const handleContinueShopping = () => {
    navigation.navigate('PackagesScreen');
  };

  const getErrorIcon = () => {
    return 'times-circle';
  };

  const getErrorMessage = () => {
    switch (error) {
      case 'Payment verification failed':
        return 'Your payment was processed but we couldn\'t verify it. Please contact support with your payment details.';
      case 'Payment cancelled':
        return 'You cancelled the payment. You can try again or continue shopping.';
      case 'Network error':
        return 'There was a network issue. Please check your connection and try again.';
      case 'Insufficient funds':
        return 'Your payment method has insufficient funds. Please try a different payment method.';
      default:
        return 'Something went wrong with your payment. Please try again or contact support if the issue persists.';
    }
  };

  const getErrorTitle = () => {
    switch (error) {
      case 'Payment verification failed':
        return 'Payment Verification Failed';
      case 'Payment cancelled':
        return 'Payment Cancelled';
      case 'Network error':
        return 'Connection Error';
      case 'Insufficient funds':
        return 'Insufficient Funds';
      default:
        return 'Payment Failed';
    }
  };

  return (
    <ScrollView style={[
      styles.container,
      isDarkMode && styles.containerDark
    ]}>
      <View style={styles.content}>
        {/* Error Icon */}
        <View style={[
          styles.errorIcon,
          isDarkMode && styles.errorIconDark
        ]}>
          <Icon
            name={getErrorIcon()}
            size={60}
            color="#fff"
          />
        </View>

        {/* Error Message */}
        <Text style={[
          styles.errorTitle,
          isDarkMode && styles.errorTitleDark
        ]}>
          {getErrorTitle()}
        </Text>

        <Text style={[
          styles.errorSubtitle,
          isDarkMode && styles.errorSubtitleDark
        ]}>
          {getErrorMessage()}
        </Text>

        {/* Error Details Card */}
        <View style={[
          styles.errorCard,
          isDarkMode && styles.errorCardDark
        ]}>
          <View style={styles.errorHeader}>
            <Text style={[
              styles.errorCardTitle,
              isDarkMode && styles.textDark
            ]}>
              What happened?
            </Text>
          </View>

          <View style={styles.errorInfo}>
            <View style={styles.errorRow}>
              <Icon
                name="exclamation-triangle"
                size={16}
                color="#dc3545"
                style={styles.errorRowIcon}
              />
              <Text style={[
                styles.errorText,
                isDarkMode && styles.errorTextDark
              ]}>
                {error}
              </Text>
            </View>

            <View style={styles.errorRow}>
              <Icon
                name="clock-o"
                size={16}
                color={isDarkMode ? '#999' : '#666'}
                style={styles.errorRowIcon}
              />
              <Text style={[
                styles.errorTime,
                isDarkMode && styles.errorTimeDark
              ]}>
                {new Date().toLocaleString('en-IN')}
              </Text>
            </View>
          </View>
        </View>

        {/* Troubleshooting Tips */}
        <View style={[
          styles.tipsCard,
          isDarkMode && styles.tipsCardDark
        ]}>
          <Text style={[
            styles.tipsTitle,
            isDarkMode && styles.textDark
          ]}>
            Troubleshooting Tips
          </Text>

          <View style={styles.tipsList}>
            <View style={styles.tipItem}>
              <Icon
                name="wifi"
                size={16}
                color={isDarkMode ? '#4CAF50' : '#198754'}
                style={styles.tipIcon}
              />
              <Text style={[
                styles.tipText,
                isDarkMode && styles.tipTextDark
              ]}>
                Check your internet connection
              </Text>
            </View>

            <View style={styles.tipItem}>
              <Icon
                name="credit-card"
                size={16}
                color={isDarkMode ? '#4CAF50' : '#198754'}
                style={styles.tipIcon}
              />
              <Text style={[
                styles.tipText,
                isDarkMode && styles.tipTextDark
              ]}>
                Verify your payment method details
              </Text>
            </View>

            <View style={styles.tipItem}>
              <Icon
                name="bank"
                size={16}
                color={isDarkMode ? '#4CAF50' : '#198754'}
                style={styles.tipIcon}
              />
              <Text style={[
                styles.tipText,
                isDarkMode && styles.tipTextDark
              ]}>
                Ensure sufficient balance in your account
              </Text>
            </View>

            <View style={styles.tipItem}>
              <Icon
                name="shield"
                size={16}
                color={isDarkMode ? '#4CAF50' : '#198754'}
                style={styles.tipIcon}
              />
              <Text style={[
                styles.tipText,
                isDarkMode && styles.tipTextDark
              ]}>
                Check if your bank allows online transactions
              </Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {canRetry && (
            <TouchableOpacity
              style={[
                styles.primaryButton,
                isDarkMode && styles.primaryButtonDark
              ]}
              onPress={handleRetryPayment}
              activeOpacity={0.8}
            >
              <Icon name="refresh" size={16} color="#fff" style={styles.buttonIcon} />
              <Text style={styles.primaryButtonText}>Try Again</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.secondaryButton,
              isDarkMode && styles.secondaryButtonDark
            ]}
            onPress={handleContactSupport}
            activeOpacity={0.8}
          >
            <Icon 
              name="support" 
              size={16} 
              color={isDarkMode ? '#4CAF50' : '#198754'} 
              style={styles.buttonIcon} 
            />
            <Text style={[
              styles.secondaryButtonText,
              isDarkMode && styles.secondaryButtonTextDark
            ]}>
              Contact Support
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tertiaryButton,
              isDarkMode && styles.tertiaryButtonDark
            ]}
            onPress={handleContinueShopping}
            activeOpacity={0.8}
          >
            <Icon 
              name="shopping-bag" 
              size={16} 
              color={isDarkMode ? '#999' : '#666'} 
              style={styles.buttonIcon} 
            />
            <Text style={[
              styles.tertiaryButtonText,
              isDarkMode && styles.tertiaryButtonTextDark
            ]}>
              Continue Shopping
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tertiaryButton,
              isDarkMode && styles.tertiaryButtonDark
            ]}
            onPress={handleGoHome}
            activeOpacity={0.8}
          >
            <Icon 
              name="home" 
              size={16} 
              color={isDarkMode ? '#999' : '#666'} 
              style={styles.buttonIcon} 
            />
            <Text style={[
              styles.tertiaryButtonText,
              isDarkMode && styles.tertiaryButtonTextDark
            ]}>
              Go to Home
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  content: {
    padding: 20,
    alignItems: 'center',
  },
  errorIcon: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#dc3545',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  errorIconDark: {
    backgroundColor: '#dc3545',
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#dc3545',
    textAlign: 'center',
    marginBottom: 8,
  },
  errorTitleDark: {
    color: '#ff6b6b',
  },
  errorSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    paddingHorizontal: 20,
    lineHeight: 24,
  },
  errorSubtitleDark: {
    color: '#999',
  },
  errorCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: width - 40,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  errorCardDark: {
    backgroundColor: '#1e1e1e',
  },
  errorHeader: {
    marginBottom: 16,
  },
  errorCardTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
  },
  errorInfo: {
    gap: 12,
  },
  errorRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  errorRowIcon: {
    marginRight: 12,
    width: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#dc3545',
    fontWeight: '600',
    flex: 1,
  },
  errorTextDark: {
    color: '#ff6b6b',
  },
  errorTime: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  errorTimeDark: {
    color: '#999',
  },
  textDark: {
    color: '#fff',
  },
  tipsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: width - 40,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipsCardDark: {
    backgroundColor: '#1e1e1e',
  },
  tipsTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 16,
  },
  tipsList: {
    gap: 12,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tipIcon: {
    marginRight: 12,
    width: 16,
  },
  tipText: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  tipTextDark: {
    color: '#999',
  },
  actionButtons: {
    width: width - 40,
    gap: 12,
  },
  primaryButton: {
    backgroundColor: '#198754',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    borderRadius: 8,
  },
  primaryButtonDark: {
    backgroundColor: '#4CAF50',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#198754',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 14,
    borderRadius: 8,
  },
  secondaryButtonDark: {
    borderColor: '#4CAF50',
  },
  secondaryButtonText: {
    color: '#198754',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonTextDark: {
    color: '#4CAF50',
  },
  tertiaryButton: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
  },
  tertiaryButtonDark: {
    backgroundColor: 'transparent',
  },
  tertiaryButtonText: {
    color: '#666',
    fontSize: 16,
  },
  tertiaryButtonTextDark: {
    color: '#999',
  },
  buttonIcon: {
    marginRight: 8,
  },
});

export default PaymentFailureScreen;
