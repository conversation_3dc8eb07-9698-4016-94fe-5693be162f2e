import React, { useState, useEffect, useRef } from "react";
import { Card, Form, Button, Row, Col, Alert } from "react-bootstrap";
import JoditEditor from "jodit-react";

const BlogForm = ({ initialValues, onSubmit, loading }) => {
  const editorRef = useRef(null);

  const [formData, setFormData] = useState(initialValues);
  const [preview, setPreview] = useState(null);
  const [errors, setErrors] = useState({});
  const [formSubmitted, setFormSubmitted] = useState(false);

  useEffect(() => {
    setFormData(initialValues);
  }, [initialValues]);

  useEffect(() => {
    if (formData.image && typeof formData.image === "string") {
      setPreview(`${import.meta.env.VITE_BASE_URL}/${formData.image}`);
    } else if (formData.image && formData.image instanceof File) {
      const objectUrl = URL.createObjectURL(formData.image);
      setPreview(objectUrl);
      return () => URL.revokeObjectURL(objectUrl);
    }
  }, [formData.image]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleJoditChange = (content) => {
    setFormData((prevData) => ({
      ...prevData,
      content,
    }));
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    const file = files[0];
    setFormData((prevData) => ({
      ...prevData,
      [name]: file,
    }));

    // Validate file type and size immediately
    let fileError = "";
    const validTypes = ["image/jpg", "image/jpeg", "image/png"];
    if (file) {
      if (!validTypes.includes(file.type)) {
        fileError = "Only JPG, JPEG, and PNG files are allowed.";
      } else if (file.size > 150 * 1024) { // 150 KB max size
        fileError = "File size must not exceed 150 KB.";
      }
    }

    setErrors((prevErrors) => ({
      ...prevErrors,
      image: fileError,
    }));
  };

  const validateForm = () => {
    let formErrors = {};

    // Validate required fields
    if (!formData.title) formErrors.title = "Title is required";
    if (!formData.meta_title) formErrors.meta_title = "Meta Title is required";
    if (!formData.meta_description) formErrors.meta_description = "Meta Description is required";
    if (!formData.short_content) formErrors.short_content = "Short Content is required";
    if (!formData.content) formErrors.content = "Content is required";
    if (!formData.introduction) formErrors.introduction = "Introduction is required";

    // Validate URL formats
    if (formData.internal_link && !/^https?:\/\/[^\s/$.?#].[^\s]*$/.test(formData.internal_link)) {
      formErrors.internal_link = "Invalid URL format for Internal Link";
    }
    if (formData.external_link && !/^https?:\/\/[^\s/$.?#].[^\s]*$/.test(formData.external_link)) {
      formErrors.external_link = "Invalid URL format for External Link";
    }

    setErrors(formErrors);
    return Object.keys(formErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setFormSubmitted(true);

    if (!validateForm()) return;

    const formDataToSubmit = {
      ...formData,
      ...(formData.image instanceof File && { image: formData.image }), // Only add image if it's a File
    };

    onSubmit(formDataToSubmit);
    setPreview(null);
    setFormSubmitted(false);
  };

  const editorConfig = {
    readonly: false,
    placeholder: "Write your content here...",
    toolbarSticky: false,
    buttons: [
      "bold",
      "italic",
      "underline",
      "strikethrough",
      "align",
      "outdent",
      "indent",
      "font",
      "fontsize",
      "paragraph",
      "image",
      "link",
      "undo",
      "redo",
    ],
    height: 400,
  };

  return (
    <Card className="shadow-lg mt-3 p-4 text-start">
      <Form onSubmit={handleSubmit} encType="multipart/form-data">
        {formSubmitted && Object.keys(errors).length > 0 && (
          <Alert variant="danger">
            Please fill in all required fields.
          </Alert>
        )}
        <Row className="mb-3">
          <Col md={12}>
            <Form.Group>
              <Form.Label>
                Title <span style={{ color: "red" }}>*</span>
              </Form.Label>
              <Form.Control
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                isInvalid={!!errors.title}
                required
              />
              {errors.title && <Form.Control.Feedback type="invalid">{errors.title}</Form.Control.Feedback>}
            </Form.Group>
          </Col>
        </Row>
        <Row className="mb-3">
          <Col md={6}>
            <Form.Group>
              <Form.Label>
                Meta Title <span style={{ color: "red" }}>*</span>
              </Form.Label>
              <Form.Control
                type="text"
                name="meta_title"
                value={formData.meta_title}
                onChange={handleChange}
                isInvalid={!!errors.meta_title}
                required
              />
              {errors.meta_title && <Form.Control.Feedback type="invalid">{errors.meta_title}</Form.Control.Feedback>}
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group>
              <Form.Label>Meta Keywords <span style={{ color: "red" }}>*</span></Form.Label>
              <Form.Control
                type="text"
                name="meta_keywords"
                value={formData.meta_keywords}
                onChange={handleChange}
                placeholder="Comma-separated keywords"
                required
              />
            </Form.Group>
          </Col>
        </Row>
        <Row className="mb-3">
          <Col md={6}>
            <Form.Group>
              <Form.Label>
                Meta Description <span style={{ color: "red" }}>*</span>
              </Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="meta_description"
                value={formData.meta_description}
                onChange={handleChange}
                isInvalid={!!errors.meta_description}
                required
              />
              {errors.meta_description && <Form.Control.Feedback type="invalid">{errors.meta_description}</Form.Control.Feedback>}
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group>
              <Form.Label>
                Short Content <span style={{ color: "red" }}>*</span>
              </Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="short_content"
                value={formData.short_content}
                onChange={handleChange}
                placeholder="60 words"
                isInvalid={!!errors.short_content}
                required
              />
              {errors.short_content && <Form.Control.Feedback type="invalid">{errors.short_content}</Form.Control.Feedback>}
            </Form.Group>
          </Col>
        </Row>
        <Form.Group className="mb-3">
          <Form.Label>Content</Form.Label>
          <JoditEditor
            ref={editorRef}
            value={formData.content}
            config={editorConfig}
            tabIndex={1}
            onBlur={(newContent) => handleJoditChange(newContent)}
            required
          />
          {errors.content && <div style={{ color: "red" }}>{errors.content}</div>}
        </Form.Group>
        <Row className="mb-3 mt-5">
          <Col md={6}>
            <Form.Group>
              <Form.Label>Image <span style={{ color: "red" }}>*</span></Form.Label>
              <Form.Control
                type="file"
                name="image"
                onChange={handleFileChange}
                isInvalid={!!errors.image}
                required
              />
              {errors.image && <Form.Control.Feedback type="invalid">{errors.image}</Form.Control.Feedback>}
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group>
              <Form.Label>Image Caption <span style={{ color: "red" }}>*</span></Form.Label>
              <Form.Control
                type="text"
                name="image_caption"
                value={formData.image_caption}
                onChange={handleChange}
                required
              />
            </Form.Group>
          </Col>
        </Row>
        {preview && (
          <div className="mb-3 text-center">
            <img
              src={preview}
              alt="Preview"
              style={{ maxWidth: "100%", maxHeight: "300px" }}
            />
          </div>
        )}
        <Form.Group className="mb-3">
          <Form.Label>
            Introduction <span style={{ color: "red" }}>*</span>
          </Form.Label>
          <Form.Control
            as="textarea"
            rows={4}
            name="introduction"
            value={formData.introduction}
            onChange={handleChange}
            required
            isInvalid={!!errors.introduction}
          />
          {errors.introduction && <Form.Control.Feedback type="invalid">{errors.introduction}</Form.Control.Feedback>}
        </Form.Group>
        <Button
          variant="outline-primary"
          type="submit"
          className="w-100"
          disabled={loading}
        >
          {loading ? "Submitting..." : initialValues.id ? "Update Blog" : "Create Blog"}
        </Button>
      </Form>
    </Card>
  );
};

export default BlogForm;
