import os
import random
import string
from tokenize import TokenError
import uuid
from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken

from contributor import serializers
from .serializers import (
    LoginSerializer,
    StudentSerializer,
    CreateReferralLinkSerializer,
    RegisterWithReferralSerializer,
    WithdrawalRequestSerializer,
    AdminWithdrawalUpdateSerializer,
    SignupContentSerializer
)
from .models import Referral, Student, WithdrawalRequest, SignupContent
from .permissions import IsStudentUser
from rest_framework.exceptions import ValidationError, NotFound, PermissionDenied
from django.db import IntegrityError

from django.conf import settings
from django.core.cache import cache
from django.core.mail import send_mail
from django.utils.crypto import get_random_string
import threading
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth.models import User

import threading

import qrcode
from io import BytesIO
from django.core.files.base import ContentFile
from utils.notifications import send_sms_bulk
from contributor.throttles import LoginRateThrottle, RegisterRateThrottle
from rest_framework.decorators import api_view, throttle_classes

DEFAULT_FROM_EMAIL = settings.DEFAULT_FROM_EMAIL

from django.template.loader import render_to_string

@throttle_classes([RegisterRateThrottle])
class RegisterView(APIView):
    def post(self, request, *args, **kwargs):
        serializer = StudentSerializer(data=request.data)
        if serializer.is_valid():
            user_data = serializer.validated_data.get("user")
            if user_data:
                email = user_data["email"]
                otp = get_random_string(length=6, allowed_chars="0123456789")

                # Store OTP and email in session
                request.session["otp"] = otp
                request.session["email"] = email
                request.session["student_data"] = request.data

                # Send OTP via email
                subject = "OTP for Student Signup"
                message = f"Your OTP is {otp}"
                from_email = settings.DEFAULT_FROM_EMAIL
                recipient_list = [email]
                context = {"student_name": request.data['user']['username'], "otp": otp, "librarian": None}
                html_message = render_to_string("email_otp.html", context)
                email_thread = threading.Thread(
                    target=send_mail,
                    args=(subject, message, from_email, recipient_list, html_message),
                    kwargs={"html_message": html_message},
                )

                sms_msg = f"Dear student, Welcome to Shashtrarth, {otp} is your OTP for registration. PINAK VENTURE"
                template_id = 171853

                sms_thread = threading.Thread(
                    target=send_sms_bulk,
                    args=(request.data['phone'], sms_msg, template_id),
                )

                email_thread.start()
                sms_thread.start()


                return Response(
                    {"message": "OTP has been sent to your email for verification."},
                    status=status.HTTP_200_OK,
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class VerifyOTPView(APIView):
    def post(self, request):
        otp = request.data.get("otp")
        email_user = request.data.get("email_user")
        email = request.session.get("email")

        # Validate required fields
        if not otp or not email_user:
            return Response(
                {"error": "OTP and email are required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Fetch OTP and email from session
        session_otp = request.session.get("otp")
        if otp != session_otp:
            return Response(
                {"error": "Invalid OTP. Please try again."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if email_user != email:
            return Response(
                {"error": "Mismatch Email. Please try again."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Create the student if OTP matches
        registration_data = request.session.get("student_data")
        
        ref = registration_data.get("referral_code",0)
            
        if ref:
            ref_ser = RegisterWithReferralSerializer(data=registration_data)
            if ref_ser.is_valid():
                student= ref_ser.save()
                request.session.flush()
                std = Student.objects.all().last()
                return Response(
                {"message": "Student created successfully.", "student_id": std.id},
                status=status.HTTP_201_CREATED,
            )
            return Response(ref_ser.errors, status=status.HTTP_400_BAD_REQUEST)
        else:
            serializer = StudentSerializer(data=registration_data)
            if serializer.is_valid():
                student = serializer.save()
                request.session.flush()
                return Response(
                {"message": "Student created successfully.", "student_id": student.id},
                status=status.HTTP_201_CREATED,
            )
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            # Clear session data after successful registration
            
        return Response({"msg": "something went wrong"}, status=status.HTTP_400_BAD_REQUEST)


class ResendOTPView(APIView):
    def post(self, request, *args, **kwargs):
        email = request.session.get("email")

        if not email:
            return Response(
                {"error": "No email found in session. Please register again."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Generate a new OTP and store it in the session
        otp = get_random_string(length=6, allowed_chars="0123456789")
        request.session["otp"] = otp

        # Send the new OTP via email
        subject = "Your Resend OTP for Verification"
        message = f"Your new OTP is {otp}"
        from_email = settings.DEFAULT_FROM_EMAIL
        recipient_list = [email]

        email_thread = threading.Thread(
            target=send_mail, args=(subject, message, from_email, recipient_list)
        )
        email_thread.start()

        return Response(
            {"message": "A new OTP has been sent to your email."},
            status=status.HTTP_200_OK,
        )

from paper_engine.models import PracticeRecord
from customrcare.models import Ticket
from customrcare.serializers import TicketSerializer
from datetime import timedelta
from questions.models import Tier, Paper, Option, Course, SubCourse
from collections import defaultdict

def format_duration(duration):
    if duration:
        total_seconds = int(duration.total_seconds())  # Convert timedelta to seconds
        hours, remainder = divmod(total_seconds, 3600)  # Get hours
        minutes, seconds = divmod(remainder, 60)  # Get minutes and seconds
        return f"{hours:02}:{minutes:02}:{seconds:02}"  # Format as HH:MM:SS
    return "00:00:00" 

def profile_paper_card(student):
    papers = Paper.objects.filter(student=student).order_by('-updated_date')

    # Initialize the result list
    result = []

    # Dictionary to store subcourse data
    subcourse_dict = defaultdict(lambda: {
        "course": "",
        "course_slug": "",
        "language": "English & Hindi",
        "subcourse_slug": "",
        "sub_course_name": "",
        "paid": False,
        "paper_details": []
    })

    # Iterate through each paper and gather the related data
    for paper in papers:
        paper_data = {
            "paper_id": paper.paper_id,
            "duration": format_duration(paper.duration),
            "total_marks": paper.max_marks,
            "slug": paper.slug,
        }

        # Get the related tier
        tier = paper.tier
        if tier:
            # Get the related subcourse
            subcourse = tier.subcourse
            if subcourse:
                subcourse_data = subcourse_dict[subcourse.subcourse_id]
                subcourse_data["course"] = subcourse.course.name
                subcourse_data["course_slug"] = subcourse.course.slug
                subcourse_data["subcourse_slug"] = subcourse.slug
                subcourse_data["sub_course_name"] = subcourse.name
                subcourse_data["paid"] = True  # Set paid status as needed
                subcourse_data["paper_details"].append(paper_data)

    # Convert the subcourse_dict to a list
    result = list(subcourse_dict.values())

    return result
from django.utils import timezone
from packages_and_subscriptions.models import Subscription
class StudentDetailView(APIView):
    permission_classes = [IsStudentUser]

    def get_object(self, pk):
        # Helper method to retrieve a student by primary key (ID)
        return get_object_or_404(Student, pk=pk)

    # Retrieve a student by ID
    def get(self, request, pk, *args, **kwargs):
        
        student = self.get_object(pk)
        serializer = StudentSerializer(student)

        card_data = profile_paper_card(student)
      
        daily_practice = PracticeRecord.get_daily_practice(request.user)
        weekly_practice = PracticeRecord.get_weekly_practice(request.user)
        monthly_practice = PracticeRecord.get_monthly_practice(request.user)
        continuous_practice = PracticeRecord.get_continuous_practice(request.user)

         #  Fetch valid subscriptions using the new property
        valid_subscriptions = student.active_subscriptions

        subscription_data = []
        for sub in valid_subscriptions:
            sub_data = {
                "package_name": sub.package.name,
                "package_type": sub.package.package_type,
                "start_date": sub.start_date,
                "end_date": sub.end_date,
                "final_price": sub.final_price,
                "coupon_used": sub.coupon_used,
                "gift_card_used": sub.gift_card_used,
                "referror": sub.referror,
                "is_valid": sub.is_valid_subscription(),
            }
            subscription_data.append(sub_data)

         # Update student's subscription_type field using the new method
        student.update_subscription_type_field()


        ticket_objects  = Ticket.objects.filter(student=student)
        ticket_data = []
        if ticket_objects.exists():
            for ticket in ticket_objects:
                ticket_data.append(TicketSerializer(ticket).data)

        data = {
            "student": serializer.data,       
            "tickets": ticket_data,     
            "daily_practice": daily_practice,
            "weekly_practice": weekly_practice,
            "monthly_practice": monthly_practice,
            "continuous_practice": continuous_practice,
            "card_data": card_data,
            "subscriptions": subscription_data  # 
            
        }
      
        return Response(data, status=status.HTTP_200_OK)

    # Update a student by ID
    def patch(self, request, pk, *args, **kwargs):
        user_data = request.data['user']
        new_username = user_data.get("username")
        instance = User.objects.filter(username = new_username)
       
        if User.objects.filter(username=new_username).exists():
            user_data.pop("username", None)
        
        student = self.get_object(pk)
        serializer = StudentSerializer(student, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"profile": serializer.data, "message": "Student updated successfully"},
                status=status.HTTP_200_OK,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    # Delete a student by ID
    def delete(self, request, pk, *args, **kwargs):
        student = self.get_object(pk)
        student.delete()
        return Response(
            {"message": "Student deleted successfully"},
            status=status.HTTP_204_NO_CONTENT,
        )

from log_admin.models import UserActivity
from authentication.jwt_utils import generate_tokens_for_user

class LoginView(APIView):
    def post(self, request, *args, **kwargs):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data["user"]
            student = serializer.validated_data["student"]
            student_serializer = StudentSerializer(student)

            # Generate tokens using enhanced utility
            token_data = generate_tokens_for_user(user)
            UserActivity.objects.create(user=user, action="Student Login", metadata={"path": request.path})

            return Response(
                {
                    "student": student_serializer.data,
                    "JWT_Token": {
                        "refresh": token_data["refresh"],
                        "access": token_data["access"],
                        "token_type": token_data["token_type"],
                        "expires_in": token_data["expires_in"],
                        "refresh_expires_in": token_data["refresh_expires_in"],
                    },
                }
            )
        return Response(serializer.errors, status=status.HTTP_401_UNAUTHORIZED)


class LogoutView(APIView):
    permission_classes = [IsStudentUser]

    def post(self, request, *args, **kwargs):
        refresh_token = request.data.get("refresh")
        if not refresh_token:
            return Response(
                {"error": "Refresh token is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            token = RefreshToken(refresh_token)
            token.blacklist()  # This method will handle the blacklisting of the token
            return Response(
                {"message": "Logged out successfully"},
                status=status.HTTP_204_NO_CONTENT,
            )
        except TokenError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# Import the enhanced token refresh view
from authentication.jwt_utils import EnhancedTokenRefreshView

class TokenRefreshView(EnhancedTokenRefreshView):
    """
    Enhanced token refresh view for students app.
    Inherits from EnhancedTokenRefreshView for consistent behavior.
    """
    pass


class StudentListView(APIView):
    def get(self, request):
        students = Student.objects.select_related("user").all().order_by("-created_at")
        serializer = StudentSerializer(students, many=True)
        return Response(serializer.data)


class CreateReferralLinkView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = CreateReferralLinkSerializer(
            data=request.data, context={"request": request}
        )
        if serializer.is_valid():
            referral = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SignupRequestView(APIView):

    def post(self, request, reffer_code):
        serializer = StudentSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data["user"]["email"]
            otp = random.randint(100000, 999999)
            print(otp)

            # Store OTP in cache for 5 minutes
            cache.set(email, otp, timeout=300)

            # Send the OTP via email
            subject = "Your OTP for Verification"
            message = f"Your OTP is {otp}"
            from_email = settings.DEFAULT_FROM_EMAIL
            recipient_list = [email]

            # Send email asynchronously
            email_thread = threading.Thread(
                target=send_mail, args=(subject, message, from_email, recipient_list)
            )
            email_thread.start()

            # Store validated data in session
            request.session["signup_data"] = serializer.validated_data

            return Response({"message": "OTP sent to email"}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class VerifyOTPAndRegisterView(APIView):
    def post(self, request):
        signup_data = request.session.get("signup_data")
        otp = request.data.get("otp")
        email = signup_data["user"]["email"]

        # Verify OTP
        stored_otp = cache.get(email)
        if not stored_otp:
            return Response(
                {"error": "OTP has expired or does not exist."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if str(stored_otp) != str(otp):
            return Response(
                {"error": "Invalid OTP."}, status=status.HTTP_400_BAD_REQUEST
            )

        if not signup_data:
            return Response(
                {
                    "error": "No signup data found. Please start the signup process again."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Use RegisterWithReferralSerializer for validation and creation
        serializer = RegisterWithReferralSerializer(data=signup_data)
        if serializer.is_valid():
            result = (
                serializer.save()
            )  # Call create() method in RegisterWithReferralSerializer
            points_earned = result.get("points_earned", 0)
            return Response(
                {
                    "message": "Student registered successfully",
                    "points_earned": points_earned,
                },
                status=status.HTTP_201_CREATED,
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class WithdrawalRequestView(APIView):
    def post(self, request):
        serializer = WithdrawalRequestSerializer(
            data=request.data, context={"request": request}
        )

        if serializer.is_valid():
            withdrawal_request = serializer.save()  # Serializer handles everything
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, transaction_id=None):
        if transaction_id:
            try:
                withdrawal_request = WithdrawalRequest.objects.get(
                    transaction_id=transaction_id
                )
                serializer = WithdrawalRequestSerializer(withdrawal_request)
                return Response(serializer.data, status=status.HTTP_200_OK)
            except WithdrawalRequest.DoesNotExist:
                return Response(
                    {"detail": "Withdrawal request not found."},
                    status=status.HTTP_404_NOT_FOUND,
                )
        else:
            user = request.user
            if user.is_staff:
                withdrawal_requests = (
                    WithdrawalRequest.objects.all()
                )  # Admin sees all requests
            else:
                withdrawal_requests = WithdrawalRequest.objects.filter(
                    student=user.student_profile
                )  # Student sees their own requests

            serializer = WithdrawalRequestSerializer(withdrawal_requests, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)


class AdminWithdrawalRequestUpdateView(APIView):
    permission_classes = [IsAuthenticated]

    def patch(self, request, transaction_id):
        try:
            withdrawal_request = WithdrawalRequest.objects.get(
                transaction_id=transaction_id
            )
        except WithdrawalRequest.DoesNotExist:
            raise NotFound("Withdrawal request not found.")

        if (
            not request.user.is_staff
        ):  # Assuming only staff can update withdrawal status
            raise PermissionDenied("You do not have permission to update this request.")

        serializer = AdminWithdrawalUpdateSerializer(
            withdrawal_request, data=request.data, partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class WithdrawalRequestListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Check if the user is an admin (optional, if you want to restrict this to admins only)
        if not request.user.is_staff:
            return Response(
                {"detail": "Unauthorized"}, status=status.HTTP_401_UNAUTHORIZED
            )

        withdrawal_requests = WithdrawalRequest.objects.all()
        serializer = WithdrawalRequestSerializer(withdrawal_requests, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class ReferralQRCodeView(APIView):
    def get(self, request, *args, **kwargs):
        user = request.user

        try:
            # Fetch the student profile for the logged-in user
            student = get_object_or_404(Student, user=user)

            # Check if the student has been referred by someone
            referral_entry = Referral.objects.filter(referrer=student).last()

            if referral_entry:
                # Fetch the referral code from the Referral table (referrer's code)
                referral_code = referral_entry.referral_code
            else:
                # If no referrer exists, generate a unique referral code for the student

                referral_code = self.generate_unique_referral_code()

                # Create a new Referral entry for the student
                Referral.objects.create(referrer=student, referral_code=referral_code)

            # Ensure the student has a QR code; if not, generate one
            if student.qr_code:
                if os.path.isfile(student.qr_code.path):
                    os.remove(student.qr_code.path)
                student.qr_code.delete(save=False)
            base_url = os.getenv("SASH_STUDENT_DOMAIN") 
            referral_url = f"{base_url}/students/signup-request/{referral_code}"
            
            qr_img = qrcode.make(referral_url)

            buffer = BytesIO()
            qr_img.save(buffer, format="PNG")
            buffer.seek(0)
            student.qr_code.save(
                f"{referral_code}.png",
                ContentFile(buffer.read()),
                save=True,
            )

            # Return the referral details
            return Response(
                {
                    "name": student.user.username,
                    "referral_code": referral_code,
                    "qr_code_url": student.qr_code.url,
                },
                status=status.HTTP_200_OK,
            )

        except Student.DoesNotExist:
            return Response(
                {"detail": "Student not found for the logged-in user"},
                status=status.HTTP_404_NOT_FOUND,
            )

    def generate_unique_referral_code(self):
        """Generates a unique referral code."""
        while True:
            code = "".join(random.choices(string.ascii_uppercase + string.digits, k=8))
            if not Referral.objects.filter(referral_code=code).exists():
                return code
            
from rest_framework_simplejwt.tokens import RefreshToken
class LoginwithGoogle(APIView):
    def post(self, request, *args, **kwargs):
        try:
            email = request.data.get("email")
            first_name = request.data.get("first_name")
            last_name = request.data.get("last_name", "")  # Make last_name optional
            image_file = request.data.get('image')

            # Only require email and first_name
            if not email or not first_name:
                return Response(
                    {"error": "Email and first name are required."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate email format
            from django.core.validators import validate_email
            from django.core.exceptions import ValidationError
            try:
                validate_email(email)
            except ValidationError:
                return Response(
                    {"error": "Invalid email format."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Check if user already exists with this email
            try:
                # Handle multiple users with same email (data integrity issue)
                users_with_email = User.objects.filter(email=email)

                if users_with_email.count() == 0:
                    # No user exists, will create new one
                    raise User.DoesNotExist()
                elif users_with_email.count() == 1:
                    # Single user exists, use it
                    user = users_with_email.first()
                else:
                    # Multiple users with same email - data integrity issue
                    # Use the most recent one with a student profile

                    # Try to find user with student profile first
                    user_with_student = None
                    for u in users_with_email:
                        if hasattr(u, 'student_profile'):
                            user_with_student = u
                            break

                    if user_with_student:
                        user = user_with_student
                    else:
                        # Use the most recently created user
                        user = users_with_email.order_by('-date_joined').first()

                # Update user info if needed (merge account)
                if not user.first_name:
                    user.first_name = first_name
                if not user.last_name and last_name:
                    user.last_name = last_name
                user.save()

                created = False  # User already existed

            except User.DoesNotExist:
                # Create new user
                # Generate username from email if not provided
                username = email.split('@')[0]
                # Ensure username is unique
                counter = 1
                original_username = username
                while User.objects.filter(username=username).exists():
                    username = f"{original_username}{counter}"
                    counter += 1

                user = User.objects.create_user(
                    username=username,
                    email=email,
                    first_name=first_name,
                    last_name=last_name or "",
                    password=None  # No password for Google users initially
                )
                created = True  # New user was created

            # Get or create student profile - but only for new users or existing student users
            try:
                student = Student.objects.get(user=user)
                # Update image if provided
                if image_file:
                    student.image_url = image_file
                    student.save()
            except Student.DoesNotExist:
                # For existing users without student profile, check if they have other profiles
                if not created:  # User already existed
                    # Check if user has other profiles (care or contributor)
                    has_care_profile = hasattr(user, 'customrcare_profile')
                    has_contributor_profile = hasattr(user, 'contributor_profile')

                    if has_care_profile or has_contributor_profile:
                        return Response(
                            {"error": "Access denied. This account is not authorized for student access."},
                            status=status.HTTP_403_FORBIDDEN,
                        )

                # Create student profile only for new users or users without other profiles
                student = Student.objects.create(
                    user=user,
                    image_url=image_file or "",
                    phone=f"google_{user.id}_{email.split('@')[0]}"  # Temporary phone
                )
                # Note: Wallet creation removed temporarily due to database schema issues

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            access = refresh.access_token

            # Serialize the student data
            serializer = StudentSerializer(student)

            return Response(
                {
                    "student": serializer.data,
                    "JWT_Token": {
                        "refresh": str(refresh),
                        "access": str(access),
                    },
                    "is_google_user": not user.has_usable_password(),
                    "message": "Login successful"
                },
                status=status.HTTP_200_OK,
            )

        except ValidationError as e:
            # Handle Django validation errors
            return Response(
                {"error": "Invalid data provided."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        except IntegrityError as e:
            # Handle database integrity errors (duplicate keys, etc.)
            return Response(
                {"error": "Account creation failed due to data conflict. Please try again."},
                status=status.HTTP_409_CONFLICT,
            )

        except Exception as e:
            # Handle any other unexpected errors
            return Response(
                {"error": "An unexpected error occurred. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

class SignupContentView(APIView):
    def get(self, request):
        content = SignupContent.objects.all()[::-1]  # Fetch the latest content
        if not content:
            return Response({"message": "No content available"}, status=status.HTTP_404_NOT_FOUND)

        serializer = SignupContentSerializer(content, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    def post(self, request):
        content = SignupContent()# Fetch the latest content
        
        content.heading = request.data['heading']
        content.subtext_1 = request.data['subtext_1']
        content.subtext_2 = request.data['subtext_2']
        content.urls = request.data['urls']
        content.save()
        serializer = SignupContentSerializer(content)
        return Response(serializer.data, status=status.HTTP_200_OK)


class SignupContentDetailView(APIView):
    def delete(self, request, pk=None):
        try:
            content = SignupContent.objects.get(pk=pk)  # Fetch the latest content
            content.delete()
            return Response({"msg":"content delete successfully"}, status=status.HTTP_200_OK)
        except SignupContent.DoesNotExist:
            return Response({"msg":"content is not found"}, status=status.HTTP_404_NOT_FOUND)

    def put(self, request, pk=None):
        try:
            content = SignupContent.objects.get(pk=pk)  # Fetch the latest content
            content.heading = request.data.get('heading', content.heading)
            content.subtext_1 = request.data.get('subtext_1', content.subtext_1)
            content.subtext_2 = request.data.get('subtext_2', content.subtext_2)
            content.urls = request.data.get('urls', content.urls)
            content.save()
            serializer = SignupContentSerializer(content)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except SignupContent.DoesNotExist:
            return Response({"msg":"content is not found"}, status=status.HTTP_404_NOT_FOUND)
        
from .models import ScratchCard
from .serializers import ScratchCardSerializer, ScratchCardRevealSerializer
class ScratchCardListView(APIView):

    def get(self, request):
        student = Student.objects.get(user=request.user)
        
        cards = ScratchCard.objects.filter(referrer=student).order_by('-created_at')
        serializer = ScratchCardSerializer(cards, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

from wallet_and_transaction.models import Wallet, Transaction
from notifications.models import FCMDevice
from django.conf import settings
from django.core.mail import send_mail
from django.db import transaction

class ScratchCardRevealView(APIView):
    @transaction.atomic
    def post(self, request):
        serializer = ScratchCardRevealSerializer(data=request.data)
        if serializer.is_valid():
            card = serializer.save()
            
            # Update the wallet
            wallet, _ = Wallet.objects.get_or_create(user=card.referrer.user)
            wallet.balance += card.amount
            wallet.save()

            # Create a transaction
            Transaction.objects.create(
                wallet=wallet,
                amount=card.amount,
                is_credit=True,
                note="Scratch card reward"
            )

            # Send email
            send_mail(
                subject="Scratch Card Revealed 🎉",
                message=f"Congrats! You've won {card.amount} coins from a scratch card.",
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[card.referrer.user.email],
                fail_silently=True
            )

            # Send notification
            try:
                from firebase_admin import messaging
                fcm_devices = FCMDevice.objects.filter(user=card.referrer.user, is_active=True)
                for device in fcm_devices:
                    message = messaging.Message(
                        notification=messaging.Notification(
                            title="🎉 Scratch Card Revealed!",
                            body=f"You won {card.amount} points! Check your wallet.",
                        ),
                        token=device.registration_token,
                        data={"type": "scratch_card_reveal", "amount": str(card.amount)},
                    )
                    messaging.send(message)
                    
            except Exception as e:
                print("FCM notification error:", e)

            card.delete()
            return Response({"message": "Scratch card revealed!", "amount": card.amount})
            
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)




