import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Define the async thunk for tracking page views
export const trackPageView = createAsyncThunk(
  "pageView/track",
  async (pageViews, { rejectWithValue }) => {
    try {
      const response = await axios.post( `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_VIEWS_TRACKER}`, pageViews);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error tracking page view");
    }
  }
);

const trackPageViewSlice = createSlice({
  name: "pageView",
  initialState: {
    loading: false,
    error: null,
    success: false,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(trackPageView.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(trackPageView.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(trackPageView.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default trackPageViewSlice.reducer;
