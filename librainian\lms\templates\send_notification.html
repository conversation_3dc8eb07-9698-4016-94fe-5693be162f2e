{% extends "base.html" %}

{% block title %}Send Notification - Librainian{% endblock %}

{% block extra_css %}
<!-- Firebase SDK v8.x.x -->
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-messaging.js"></script>

    <style>
        /* Template-specific styles - CSS variables inherited from base.html */

        /* Glass Design Components */
        .glass-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
            position: relative;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .glass-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            text-align: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .glass-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            transform: rotate(-45deg);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) rotate(-45deg); }
            50% { transform: translateX(100%) rotate(-45deg); }
        }

        .glass-header h4 {
            margin: 0 0 1rem 0;
            font-weight: 700;
            font-size: 1.75rem;
            position: relative;
            z-index: 1;
        }

        .notification-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .notification-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-2px);
        }

        .glass-body {
            padding: 2.5rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            font-size: 0.95rem;
        }

        .form-label i {
            color: var(--primary);
            font-size: 1rem;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 0.875rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
            background: rgba(255, 255, 255, 0.95);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        .form-check {
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-check-input {
            margin-right: 0.75rem;
        }

        .form-check-label {
            color: var(--text-primary);
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        /* Button Styles */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            color: white;
        }

        /* Messages */
        .messages-container {
            margin-top: 2rem;
        }

        .alert {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .glass-container {
                padding: 1rem 0.5rem;
            }

            .glass-card {
                border-radius: 20px;
            }

            .glass-body {
                padding: 2rem 1.5rem;
            }

            .glass-header {
                padding: 1.5rem;
            }

            .glass-header h4 {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .glass-body {
                padding: 1.5rem 1rem;
            }

            .form-control, .form-select {
                padding: 0.75rem;
            }
        }

        /* Disable zoom on mobile */
        @media (max-width: 768px) {
            body {
                touch-action: pan-x pan-y;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="send-notification-content fade-in">
    <div class="glass-container">
        <div class="glass-card">
            <div class="glass-header">
                <h4><i class="fas fa-bell me-2"></i>Send Notification</h4>
                <button id="notificationBtn" class="notification-btn">
                    <i class="fas fa-bell me-2"></i>Enable Notifications
                </button>

                <!-- Debug Section -->
                <div class="mt-3">
                    <button id="debugBtn" class="notification-btn" style="background: rgba(255, 193, 7, 0.3); font-size: 0.875rem;">
                        <i class="fas fa-bug me-2"></i>Debug Permission Status
                    </button>
                </div>

                <!-- FCM Token Display Section -->
                <div id="tokenDisplay" class="mt-3" style="display: none;">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-key me-2"></i>FCM Device Token Generated</h6>
                        <small id="tokenText" class="text-break"></small>
                        <button id="copyTokenBtn" class="btn btn-sm btn-outline-light mt-2">
                            <i class="fas fa-copy me-1"></i>Copy Token
                        </button>
                    </div>
                </div>
            </div>
            <div class="glass-body">
                <form method="post" action="" class="notification-form">
                    {% csrf_token %}

                    <div class="form-group mb-4">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-2"></i>
                            Notification Title
                        </label>
                        <input type="text" class="form-control" id="title" name="title" placeholder="Enter notification title" required>
                    </div>

                    <div class="form-group mb-4">
                        <label for="body" class="form-label">
                            <i class="fas fa-align-left me-2"></i>
                            Message Body
                        </label>
                        <textarea class="form-control" id="body" name="body" rows="4" placeholder="Enter notification message" required></textarea>
                    </div>

                    <div class="form-group mb-4">
                        <label for="user" class="form-label">
                            <i class="fas fa-user me-2"></i>
                            Select User
                        </label>
                        <select class="form-select" id="user" name="user">
                            <option value="">-- Select User --</option>
                            {% for user in users %}
                                <option value="{{ user.id }}">{{ user.username }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Notification Type Selection -->
                    <div class="form-group mb-4">
                        <label for="notification_type" class="form-label">
                            <i class="fas fa-cog me-2"></i>
                            Notification Type
                        </label>
                        <select class="form-select" id="notification_type" name="notification_type" onchange="toggleNotificationOptions()">
                            <option value="manual">Manual Notification</option>
                            <option value="test_events">Test Event Notifications</option>
                        </select>
                    </div>

                    <!-- Manual Notification Options -->
                    <div id="manual_options">
                        <div class="form-group mb-4">
                            <label for="priority" class="form-label">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Priority Level
                            </label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="normal">Normal</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                            </select>
                        </div>

                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="sendToAll" name="send_to_all">
                            <label class="form-check-label" for="sendToAll">
                                <i class="fas fa-users me-2"></i>
                                Send to all users
                            </label>
                        </div>
                    </div>

                    <!-- Test Event Options -->
                    <div id="test_options" style="display: none;">
                        <div class="form-group mb-4">
                            <label for="event_type" class="form-label">
                                <i class="fas fa-flask me-2"></i>
                                Test Event Type
                            </label>
                            <select class="form-select" id="event_type" name="event_type">
                                <option value="">-- Select Test Event --</option>
                                <option value="test_qr_registration">🔍 Test QR Registration Notification</option>
                                <option value="test_seat_occupancy">🪑 Test Seat Occupancy Alert</option>
                                <option value="test_subscription_expiry">📅 Test Subscription Expiry Notifications</option>
                                <option value="test_visitor_callbacks">📞 Test Visitor Callback Notifications</option>
                                <option value="test_sales_milestones">💰 Test Sales Milestone Check</option>
                            </select>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Test Mode:</strong> This will trigger actual notifications based on current data in your system.
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>
                            <span id="submit_text">Send Notification</span>
                        </button>
                    </div>
                </form>

                <!-- Enhanced Admin Panel -->
                <div class="mt-4 pt-4 border-top">
                    <h5><i class="fas fa-tools me-2"></i>Admin Tools</h5>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="card bg-transparent border-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-2x mb-2 text-primary"></i>
                                    <h6>Notification Stats</h6>
                                    <p class="small">View delivery statistics</p>
                                    <button class="btn btn-outline-primary btn-sm" onclick="showNotificationStats()">
                                        View Stats
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card bg-transparent border-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-history fa-2x mb-2 text-success"></i>
                                    <h6>Notification History</h6>
                                    <p class="small">View sent notifications</p>
                                    <button class="btn btn-outline-success btn-sm" onclick="showNotificationHistory()">
                                        View History
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                    </div>
                {% if messages %}
                <div class="messages-container">
                    {% for message in messages %}
                        <div class="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Bootstrap 5.3.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Firebase Configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
            authDomain: "librainian-app.firebaseapp.com",
            projectId: "librainian-app",
            storageBucket: "librainian-app.firebasestorage.app",
            messagingSenderId: "623132670328",
            appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
            measurementId: "G-XNDKJL6JWH"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const messaging = firebase.messaging();

        // Register service worker for background notifications
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/static/firebase-messaging-sw.js')
                .then((registration) => {
                    console.log('✅ Service Worker registered for background notifications:', registration);
                    messaging.useServiceWorker(registration);
                })
                .catch((error) => {
                    console.error('❌ Service Worker registration failed:', error);
                });
        }

        // Handle foreground messages (when page is active)
        messaging.onMessage((payload) => {

            // Show custom notification for foreground messages
            if (Notification.permission === 'granted' && payload.notification) {
                const notification = new Notification(payload.notification.title, {
                    body: payload.notification.body,
                    icon: payload.notification.icon || '/static/favicon.ico',
                    badge: '/static/favicon.ico',
                    tag: 'fcm-foreground',
                    data: payload.data,
                    requireInteraction: true
                });

                notification.onclick = function() {
                    window.focus();
                    notification.close();

                    // Handle click action based on payload data
                    if (payload.data && payload.data.url) {
                        window.open(payload.data.url, '_blank');
                    }
                };

                // Auto close after 10 seconds
                setTimeout(() => {
                    notification.close();
                }, 10000);
            }
        });

        // Modern notification handling
        document.addEventListener('DOMContentLoaded', function() {
            const notificationBtn = document.getElementById('notificationBtn');

            // Check notification support and current permission

            // Check if notifications are already enabled
            if (Notification.permission === 'granted') {
                notificationBtn.innerHTML = '<i class="fas fa-check me-2"></i>Notifications Enabled';
                notificationBtn.disabled = true;
                notificationBtn.style.background = 'rgba(34, 197, 94, 0.3)';
                registerDeviceToken();
            } else if (Notification.permission === 'denied') {
                notificationBtn.innerHTML = '<i class="fas fa-times me-2"></i>Permission Denied';
                notificationBtn.disabled = true;
                notificationBtn.style.background = 'rgba(239, 68, 68, 0.3)';
                alert('❌ Notifications are blocked!\n\nTo enable:\n1. Click the lock/info icon in address bar\n2. Set Notifications to "Allow"\n3. Refresh the page');
            } else {
                notificationBtn.addEventListener('click', requestAndShowNotification);
            }

            if (!('Notification' in window)) {
                notificationBtn.innerHTML = '<i class="fas fa-times me-2"></i>Not Supported';
                notificationBtn.disabled = true;
                alert('❌ Your browser does not support push notifications.\n\nPlease use Chrome, Firefox, Safari, or Edge.');
            }

            // Add debug button functionality
            const debugBtn = document.getElementById('debugBtn');
            debugBtn.addEventListener('click', function() {
                showDebugInfo();
            });

            // Form validation
            const form = document.querySelector('.notification-form');
            const sendToAllCheckbox = document.getElementById('sendToAll');
            const userSelect = document.getElementById('user');

            sendToAllCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    userSelect.disabled = true;
                    userSelect.value = '';
                } else {
                    userSelect.disabled = false;
                }
            });

            form.addEventListener('submit', function(e) {
                if (!sendToAllCheckbox.checked && !userSelect.value) {
                    e.preventDefault();
                    alert('Please select a user or choose to send to all users.');
                }
            });
        });

        function requestAndShowNotification() {
            const notificationBtn = document.getElementById('notificationBtn');

            // Starting notification permission request

            // Update button to show processing
            notificationBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Requesting Permission...';
            notificationBtn.disabled = true;

            // Check if we can request permission
            if (Notification.permission === 'denied') {
                notificationBtn.innerHTML = '<i class="fas fa-times me-2"></i>Permission Denied';
                notificationBtn.style.background = 'rgba(239, 68, 68, 0.3)';
                alert('❌ Notifications are blocked!\n\nTo enable:\n1. Click the lock/info icon in address bar\n2. Set Notifications to "Allow"\n3. Refresh the page');
                return;
            }

            // Multiple approaches to request permission

            // Method 1: Modern Promise-based approach
            if (Notification.requestPermission && typeof Notification.requestPermission === 'function') {
                try {
                    const permissionPromise = Notification.requestPermission();

                    if (permissionPromise && typeof permissionPromise.then === 'function') {
                        // Promise-based (modern browsers)
                        console.log('📱 Using Promise-based permission request');
                        permissionPromise.then(function(permission) {
                            handlePermissionResult(permission, notificationBtn);
                        }).catch(function(error) {
                            console.error('❌ Promise-based request failed:', error);
                            tryLegacyPermissionRequest(notificationBtn);
                        });
                    } else {
                        // Callback-based (older browsers)
                        console.log('📱 Using callback-based permission request');
                        const permission = permissionPromise; // Some browsers return permission directly
                        handlePermissionResult(permission, notificationBtn);
                    }
                } catch (error) {
                    console.error('❌ Modern request failed:', error);
                    tryLegacyPermissionRequest(notificationBtn);
                }
            } else {
                console.log('📱 Modern requestPermission not available, trying legacy');
                tryLegacyPermissionRequest(notificationBtn);
            }
        }

        // Handle permission result
        function handlePermissionResult(permission, notificationBtn) {
            console.log('📊 Permission result:', permission);

            if (permission === 'granted') {
                // Permission granted
                notificationBtn.innerHTML = '<i class="fas fa-check me-2"></i>Notifications Enabled';
                notificationBtn.style.background = 'rgba(34, 197, 94, 0.3)';
                showNotification();
                registerDeviceToken();
            } else if (permission === 'denied') {
                // Permission denied by user
                notificationBtn.innerHTML = '<i class="fas fa-times me-2"></i>Permission Denied';
                notificationBtn.style.background = 'rgba(239, 68, 68, 0.3)';
                alert('❌ Notification permission denied!\n\nTo enable later:\n1. Click the lock/info icon in address bar\n2. Set Notifications to "Allow"\n3. Refresh the page');
            } else {
                // Permission default (dismissed or pending)
                notificationBtn.innerHTML = '<i class="fas fa-bell me-2"></i>Enable Notifications';
                notificationBtn.disabled = false;
                alert('⏳ Permission request was dismissed.\n\nClick the button again to retry.');
            }
        }

        // Legacy permission request for older browsers
        function tryLegacyPermissionRequest(notificationBtn) {
            try {
                // For very old browsers that might use callback
                Notification.requestPermission(function(permission) {
                    handlePermissionResult(permission, notificationBtn);
                });
            } catch (error) {
                notificationBtn.innerHTML = '<i class="fas fa-times me-2"></i>Request Failed';
                notificationBtn.style.background = 'rgba(239, 68, 68, 0.3)';
                alert(`❌ Failed to request notification permission.\n\nError: ${error.message}\n\nTry refreshing the page or using a different browser.`);
            }
        }

        function showNotification() {
            const options = {
                body: 'Welcome to Librainian! Your CRM tool for libraries and study centers.',
                icon: '/static/img/librainian-logo-black-transparent.png',
                badge: '/static/img/librainian-logo-black-transparent.png'
            };

            new Notification('Librainian Notification', options);
        }

        function registerDeviceToken() {
            messaging.getToken({ vapidKey: 'BGX2t3YXGQdWxsYD_V3WpvlzCvwS7Ob0b9oGq0Thsg_OLHs22urXWd_CKv4QBdBVJNkZJJHq-QG8zB2p4qPYdrM' }).then((currentToken) => {
                if (currentToken) {
                    console.log('🎫 FCM DEVICE TOKEN:', currentToken);

                    // Display token in UI
                    displayTokenInUI(currentToken);

                    // Create a visual alert in the browser
                    alert(`✅ FCM Token Generated!\n\nToken: ${currentToken.substring(0, 50)}...\n\nCheck browser console for full token or see below.`);

                    const deviceType = 'web';
                    saveDeviceToken(currentToken, deviceType);
                } else {
                    alert('❌ Failed to generate FCM token. Please check browser permissions.');
                }
            }).catch((err) => {
                alert(`❌ FCM Token Error: ${err.message}`);
            });
        }

        function saveDeviceToken(token, deviceType) {
            const deviceInfo = {
                name: getBrowserInfo(),
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                timestamp: new Date().toISOString()
            };

            fetch('/librarian/save-device-token/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: `token=${encodeURIComponent(token)}&device_type=${encodeURIComponent(deviceType)}&device_name=${encodeURIComponent(deviceInfo.name)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Show success message to user
                    const successMsg = `✅ FCM Token Saved Successfully!\n\nDevice: ${deviceInfo.name}\nTotal Devices: ${data.device_info?.total_devices || 1}`;
                    alert(successMsg);
                } else {
                    alert(`❌ Failed to save FCM token: ${data.message}`);
                }
            })
            .catch(error => {
                alert(`❌ Network error while saving token: ${error.message}`);
            });
        }

        // Helper function to get browser info
        function getBrowserInfo() {
            const ua = navigator.userAgent;
            let browser = 'Unknown Browser';

            if (ua.includes('Chrome') && !ua.includes('Edg')) {
                browser = 'Chrome';
            } else if (ua.includes('Firefox')) {
                browser = 'Firefox';
            } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
                browser = 'Safari';
            } else if (ua.includes('Edg')) {
                browser = 'Edge';
            } else if (ua.includes('Opera')) {
                browser = 'Opera';
            }

            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua);
            return `${browser}${isMobile ? ' Mobile' : ' Desktop'}`;
        }

        // Function to display token in UI
        function displayTokenInUI(token) {
            const tokenDisplay = document.getElementById('tokenDisplay');
            const tokenText = document.getElementById('tokenText');
            const copyBtn = document.getElementById('copyTokenBtn');

            tokenText.textContent = token;
            tokenDisplay.style.display = 'block';

            // Add copy functionality
            copyBtn.addEventListener('click', function() {
                navigator.clipboard.writeText(token).then(function() {
                    copyBtn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
                    copyBtn.classList.remove('btn-outline-light');
                    copyBtn.classList.add('btn-success');

                    setTimeout(function() {
                        copyBtn.innerHTML = '<i class="fas fa-copy me-1"></i>Copy Token';
                        copyBtn.classList.remove('btn-success');
                        copyBtn.classList.add('btn-outline-light');
                    }, 2000);
                }).catch(function(err) {
                    console.error('Failed to copy token: ', err);
                    alert('Failed to copy token to clipboard');
                });
            });
        }

        // Enhanced Admin Functions
        function toggleNotificationOptions() {
            const notificationType = document.getElementById('notification_type').value;
            const manualOptions = document.getElementById('manual_options');
            const testOptions = document.getElementById('test_options');
            const submitText = document.getElementById('submit_text');
            const titleField = document.getElementById('title');
            const bodyField = document.getElementById('body');

            if (notificationType === 'test_events') {
                manualOptions.style.display = 'none';
                testOptions.style.display = 'block';
                submitText.textContent = 'Run Test Event';
                titleField.required = false;
                bodyField.required = false;
                titleField.value = 'Test Event Notification';
                bodyField.value = 'This is a test notification triggered by admin.';
            } else {
                manualOptions.style.display = 'block';
                testOptions.style.display = 'none';
                submitText.textContent = 'Send Notification';
                titleField.required = true;
                bodyField.required = true;
                titleField.value = '';
                bodyField.value = '';
            }
        }

        function showNotificationStats() {
            // Create a modal or redirect to stats page
            alert('Notification Statistics:\n\n' +
                  '📊 Total Notifications Sent: Loading...\n' +
                  '✅ Successful Deliveries: Loading...\n' +
                  '❌ Failed Deliveries: Loading...\n' +
                  '📱 Active Device Tokens: Loading...\n\n' +
                  'Full statistics page coming soon!');
        }

        function showNotificationHistory() {
            // Create a modal or redirect to history page
            alert('Notification History:\n\n' +
                  '📋 Recent Notifications:\n' +
                  '• QR Registration notifications\n' +
                  '• Payment received notifications\n' +
                  '• Visitor callback reminders\n' +
                  '• Subscription expiry alerts\n\n' +
                  'Full history page coming soon!');
        }

        // Debug function to show detailed permission info
        function showDebugInfo() {
            console.log('\n🔍 === DETAILED DEBUG INFO ===');

            const debugInfo = {
                'Notification Support': 'Notification' in window,
                'Current Permission': Notification.permission,
                'Protocol': window.location.protocol,
                'Hostname': window.location.hostname,
                'Is Secure Context': window.isSecureContext,
                'User Agent': navigator.userAgent,
                'Browser': getBrowserInfo(),
                'Service Worker Support': 'serviceWorker' in navigator,
                'Push Manager Support': 'PushManager' in window,
                'Firebase Available': typeof firebase !== 'undefined'
            };

            console.table(debugInfo);

            // Create detailed alert message
            let message = '🔍 NOTIFICATION DEBUG INFO:\n\n';
            for (const [key, value] of Object.entries(debugInfo)) {
                message += `${key}: ${value}\n`;
            }

            // Add troubleshooting tips
            message += '\n💡 TROUBLESHOOTING TIPS:\n';

            if (!window.isSecureContext) {
                message += '❌ Not secure context! Use HTTPS or localhost\n';
            }

            if (Notification.permission === 'denied') {
                message += '❌ Permission denied. Reset in browser settings\n';
            }

            if (!('Notification' in window)) {
                message += '❌ Browser doesn\'t support notifications\n';
            }

            alert(message);

            // Try to manually request permission if possible
            if (Notification.permission === 'default') {
                console.log('🔄 Attempting manual permission request...');
                if (confirm('🔔 Try to request notification permission now?')) {
                    requestAndShowNotification();
                }
            }
        }

        // Function to get CSRF token from cookies
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    </script>

    <script>
        // Register the service worker
        if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/firebase-messaging-sw.js')
            .then(function(registration) {
            console.log('Service Worker registered with scope:', registration.scope);
            })
            .catch(function(error) {
            console.log('Service Worker registration failed:', error);
            });
        }

    </script>
</div>
</div>
{% endblock %}
