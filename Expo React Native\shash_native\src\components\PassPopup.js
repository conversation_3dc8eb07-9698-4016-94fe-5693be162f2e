import React, { useEffect, useState } from 'react';
import { Modal, View, StyleSheet, Image, TouchableOpacity, Text, Linking } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { setHasSeenWelcomePopup, getPopUps, setCurrentPopup, clearCurrentPopup } from '../redux/popupSlice';

export const PassPopup = ({ visible, onClose }) => {
  const dispatch = useDispatch();
  const { popups, currentPopup } = useSelector((state) => state.popup);
  const [showPopup, setShowPopup] = useState(false);
  const [displayTimer, setDisplayTimer] = useState(null);

  // Filter eligible popups based on criteria
  const getEligiblePopups = () => {
    return popups.filter(popup =>
      popup.is_active === true &&
      (popup.approval_status === 'approved_by_care' || popup.approval_status === 'approved_by_admin')
    );
  };

  // Select random popup from eligible ones
  const selectRandomPopup = () => {
    const eligiblePopups = getEligiblePopups();
    if (eligiblePopups.length > 0) {
      const randomIndex = Math.floor(Math.random() * eligiblePopups.length);
      return eligiblePopups[randomIndex];
    }
    return null;
  };

  // Fetch popups and set up display logic
  useEffect(() => {
    if (visible && popups.length === 0) {
      dispatch(getPopUps());
    }
  }, [visible, dispatch, popups.length]);

  // Handle popup display timing
  useEffect(() => {
    if (visible && popups.length > 0 && !currentPopup) {
      const selectedPopup = selectRandomPopup();
      if (selectedPopup) {
        dispatch(setCurrentPopup(selectedPopup));

        // Set delay before showing popup
        const delayTimer = setTimeout(() => {
          setShowPopup(true);

          // Set display duration timer
          const durationTimer = setTimeout(() => {
            handleClose();
          }, selectedPopup.display_duration);

          setDisplayTimer(durationTimer);
        }, selectedPopup.delay_ms);

        return () => {
          clearTimeout(delayTimer);
          if (displayTimer) {
            clearTimeout(displayTimer);
          }
        };
      }
    }
  }, [visible, popups, currentPopup, dispatch]);

  const handleClose = () => {
    setShowPopup(false);
    dispatch(clearCurrentPopup());
    dispatch(setHasSeenWelcomePopup(true));
    if (displayTimer) {
      clearTimeout(displayTimer);
      setDisplayTimer(null);
    }
    onClose();
  };

  const handleLinkPress = () => {
    if (currentPopup?.link_url) {
      Linking.openURL(currentPopup.link_url);
    }
  };

  // Don't render if no popup is selected or not ready to show
  if (!currentPopup || !showPopup) {
    return null;
  }

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible && showPopup}
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.popupContainer}>
          {/* Title - Always shown at top */}
          <Text style={styles.titleText}>{currentPopup.title}</Text>

          {/* Content based on content_type */}
          {currentPopup.content_type === 'text_only' && (
            <Text style={styles.contentText}>{currentPopup.text_content}</Text>
          )}

          {currentPopup.content_type === 'image_only' && currentPopup.image && (
            <View style={styles.imageWrapper}>
              <Image
                source={{ uri: currentPopup.image }}
                style={styles.image}
                resizeMode="cover"
              />
            </View>
          )}

          {currentPopup.content_type === 'text_image' && (
            <>
              <Text style={styles.contentText}>{currentPopup.text_content}</Text>
              {currentPopup.image && (
                <View style={styles.imageWrapper}>
                  <Image
                    source={{ uri: currentPopup.image }}
                    style={styles.image}
                    resizeMode="cover"
                  />
                </View>
              )}
            </>
          )}

          {currentPopup.content_type === 'text_link' && (
            <Text style={styles.contentText}>{currentPopup.text_content}</Text>
          )}

          {currentPopup.content_type === 'link_anchor' && (
            <Text style={styles.contentText}>{currentPopup.description}</Text>
          )}

          {/* Action buttons - Always shown at bottom */}
          <View style={styles.buttonContainer}>
            {(currentPopup.content_type === 'text_link' || currentPopup.content_type === 'link_anchor') && (
              <TouchableOpacity onPress={handleLinkPress} style={styles.primaryButton}>
                <Text style={styles.primaryButtonText}>
                  {currentPopup.link_text || currentPopup.anchor_tag || 'Continue'}
                </Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity onPress={handleClose} style={styles.cancelButton}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  popupContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 32,
    maxWidth: 400,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.3,
    shadowRadius: 15,
    elevation: 20,
    alignItems: 'center',
  },
  titleText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
    lineHeight: 28,
  },
  contentText: {
    fontSize: 15,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  imageWrapper: {
    marginBottom: 24,
    width: '100%',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    maxHeight: 200,
    borderRadius: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 8,
    justifyContent: 'center',
    marginTop: 24,
    width: '100%',
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    minWidth: 80,
    alignItems: 'center',
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    minWidth: 80,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '500',
  },
});
