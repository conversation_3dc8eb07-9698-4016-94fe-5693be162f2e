import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor;
  return accessToken;
};

// Create Course Thunk
export const createCourse = createAsyncThunk(
  'course/createCourse',
  async (courseData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const formData = new FormData();

      Object.keys(courseData).forEach((key) => {
        if (key === 'attachments' && courseData.attachments instanceof File) {
          formData.append(key, courseData.attachments);
        } else if (key !== 'attachments') {
          formData.append(key, courseData[key]);
        }
      });

      await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CREATE_COURSE}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return; // No need to store the course data here, just perform the action
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error creating course');
    }
  }
);

// Get Courses Thunk
export const getCourses = createAsyncThunk(
  'course/getCourses',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_COURSES}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data; // Handle the courses data at the component level
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching courses');
    }
  }
);

// Get Single Course Thunk
export const getCourse = createAsyncThunk(
  'course/getCourse',
  async (courseSlug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_COURSE}${courseSlug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data; // Handle course data at the component level
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching course');
    }
  }
);

// Update Course Thunk
export const updateCourse = createAsyncThunk(
  'course/updateCourse',
  async ({ slug, updatedData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const formData = new FormData();

      Object.keys(updatedData).forEach((key) => {
        if (key === 'attachments' && updatedData.attachments instanceof File) {
          formData.append(key, updatedData.attachments);
        } else if (key !== 'attachments') {
          formData.append(key, updatedData[key]);
        }
      });

      await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_UPDATE_COURSE}${slug}/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return; // Handle the update without saving data to Redux
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error updating course');
    }
  }
);

// Delete Course Thunk
export const deleteCourse = createAsyncThunk(
  'course/deleteCourse',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_DELETE_COURSE}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return { slug }; // We only need to update the state with the course's slug
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error deleting course');
    }
  }
);

const courseSlice = createSlice({
  name: 'course',
  initialState: {
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(createCourse.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createCourse.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null; // No need to store the created course data
      })
      .addCase(createCourse.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(getCourses.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCourses.fulfilled, (state, action) => {
        state.isLoading = false;
        state.error = null; // Handle courses data at the component level
      })
      .addCase(getCourses.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(getCourse.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCourse.fulfilled, (state, action) => {
        state.isLoading = false;
        state.error = null; // Handle course data at the component level
      })
      .addCase(getCourse.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(updateCourse.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateCourse.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null; // Handle update without storing data
      })
      .addCase(updateCourse.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(deleteCourse.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteCourse.fulfilled, (state, action) => {
        state.isLoading = false;
        state.error = null; // Handle deletion without storing data
      })
      .addCase(deleteCourse.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export default courseSlice.reducer;
