import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useParams, Link } from "react-router-dom";
import { Container, Row, Col, Card, Button, Form, Pagination, Modal, Dropdown, DropdownButton } from "react-bootstrap";
import { deleteSection, updateSection } from "../../redux/slice/sectionSlice";
import { getPaper } from "../../redux/slice/paperSlice";
import ViewModal from "../../commonComponents/ViewModal";

import { BsPencilSquare, BsTrash } from "react-icons/bs";
import toast, { Toaster } from "react-hot-toast";
import Swal from "sweetalert2";

const AllSections = ({ sectionAdded }) => {
  const dispatch = useDispatch();
  const { paperSlug } = useParams();

  const [tierName, setTierName] = useState("");
  const [sections, setSections] = useState([]);
  const [paperName, setpaperName] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [sectionsPerPage, setSectionsPerPage] = useState(6);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedSection, setSelectedSection] = useState(null);
  const [editData, setEditData] = useState({ name: "", max_marks: "", number_of_questions: "" }); // Edit data structure

  // Re-fetch sections when sectionAdded changes or on component mount
  useEffect(() => {
    const fetchSections = async () => {
      try {
        const response = await dispatch(getPaper(paperSlug)).unwrap();
        setTierName(response.tier_name)
        setSections(response.sections);
        setpaperName(response.name)
      } catch (error) {
        toast.error("Failed to fetch sections. Please try again.");
      }
    };

    fetchSections();
  }, [dispatch, sectionAdded]);

  const filteredSections = sections.filter((section) =>
    (section.name && section.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (section.description && section.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const indexOfLastSection = currentPage * sectionsPerPage;
  const indexOfFirstSection = indexOfLastSection - sectionsPerPage;
  const currentSections = filteredSections.slice(indexOfFirstSection, indexOfLastSection);

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleSectionsPerPageChange = (value) => {
    setSectionsPerPage(value);
    setCurrentPage(1); // Reset to the first page
  };

  const handleDeleteSection = async (sectionId) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        await dispatch(deleteSection(sectionId)).unwrap();
        setSections(sections.filter((section) => section.slug !== sectionId));
        toast.success("Section deleted successfully!");
      } catch (error) {
        toast.error("Failed to delete the section. Please try again.");
      }
    }
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // for view modal

  const handleViewSection = (section) => {
    setSelectedSection(section);
    setShowViewModal(true);
  };

  // Handle Edit Modal Open
  const openEditModal = (section) => {
    setSelectedSection(section);
    setEditData({
      name: section.name,
      max_marks: section.max_marks,
      number_of_questions: section.number_of_questions
    });
    setShowEditModal(true);
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setSelectedSection(null);
    setEditData({ name: "", max_marks: "", number_of_questions: "" });
  };

  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditData((prevData) => ({ ...prevData, [name]: value }));
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    try {
      await dispatch(updateSection({ slug: selectedSection.slug, data: editData })).unwrap();
      toast.success("Section updated successfully!");
      setShowEditModal(false);
      setSections(sections.map((section) => (section.slug === selectedSection.slug ? { ...section, ...editData } : section)));
    } catch (error) {
      toast.error("Failed to update the section. Please try again.");
    }
  };

  return (
    <Container>
      <Row className="justify-content-center">
        <Col>
          <h5 className="text-center text-success mt-2 h5"> Sub Course / {tierName} / {paperName} / Sections</h5>

          {/* Search Bar */}
          <Form.Group className="mb-4 d-flex align-items-center">
            <Form.Control
              type="text"
              placeholder="Search sections..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="me-2"
            />
            <DropdownButton
              id="dropdown-sections-per-page"
              title={`${sectionsPerPage} per page`}
              variant="success"
            >
              {[5, 25, 50, 100, "All"].map((option) => (
                <Dropdown.Item
                  key={option}
                  onClick={() => handleSectionsPerPageChange(option === "All" ? sections.length : option)}
                  active={sectionsPerPage === (option === "All" ? sections.length : option)}
                >
                  {option} per page
                </Dropdown.Item>
              ))}
            </DropdownButton>
          </Form.Group>

          {/* Display Sections */}
          <Row>
            {currentSections.length > 0 ? (
              currentSections.map((section) => (
                <Col key={section.slug} xs={12} sm={6} md={6} lg={6}>
                  <Card className="mb-4 shadow-sm rounded-3" >
                    <Card.Body>
                      <Card.Title className="text-success text-truncate w-100">{section.name}</Card.Title>
                      <Card.Text className="text-truncate w-100">{section.description}</Card.Text>
                      <Card.Text className="mt-2">{`Max Marks: ${section.max_marks}, Questions: ${section.number_of_questions}`}</Card.Text>
                      <div className="d-flex flex-wrap justify-content-center mt-2">
                        <Link to={`/add_module/${section.slug}`}>
                          <Button variant="outline-primary" className="m-1 fs-6">
                            Module
                          </Button>
                        </Link>

                        <Button variant="outline-info" className="m-1 fs-6" onClick={() => handleViewSection(section)}>
                          View
                        </Button>

                        <Button
                          variant="outline-success" className="m-1 fs-6"
                          onClick={() => openEditModal(section)}
                        >
                          <BsPencilSquare />
                        </Button>

                        <Button
                          variant="outline-danger" className="m-1 fs-6"
                          onClick={() => handleDeleteSection(section.slug)}
                        >
                          <BsTrash />
                        </Button>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))
            ) : (
              <div className="text-center mt-5">
                <div className="spinner-border text-success" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              </div>
            )}
          </Row>

          {/* Pagination */}
          <div className="d-flex justify-content-center mt-4">
            <Pagination>
              {Array.from({ length: Math.ceil(filteredSections.length / sectionsPerPage) }).map(
                (_, index) => (
                  <Pagination.Item
                    key={index + 1}
                    active={index + 1 === currentPage}
                    onClick={() => handlePageChange(index + 1)}
                  >
                    {index + 1}
                  </Pagination.Item>
                )
              )}
            </Pagination>
          </div>
        </Col>
      </Row>

      {/* Edit Modal */}
      <Modal show={showEditModal} onHide={handleCloseEditModal} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit Section</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleEditSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Section Name</Form.Label>
              <Form.Control
                type="text"
                name="name"
                value={editData.name}
                onChange={handleEditChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Max Marks</Form.Label>
              <Form.Control
                type="number"
                name="max_marks"
                value={editData.max_marks}
                onChange={handleEditChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Number of Questions</Form.Label>
              <Form.Control
                type="number"
                name="number_of_questions"
                value={editData.number_of_questions}
                onChange={handleEditChange}
                required
              />
            </Form.Group>

            <Button variant="outline-primary w-100" type="submit">
              Save Changes
            </Button>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary " onClick={handleCloseEditModal}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      <ViewModal show={showViewModal} onHide={() => setShowViewModal(false)} content={selectedSection} />

      <Toaster />
    </Container>
  );
};

export default AllSections;
