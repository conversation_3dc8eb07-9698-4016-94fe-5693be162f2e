#!/usr/bin/env python3
"""
Comprehensive test script for the logging system
"""

import os
import sys
import django
import json
import time
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.management import call_command
from django.db import connection
from rest_framework.test import APIClient
from rest_framework import status

from log_admin.models import (
    LogConfig, PerformanceLog, ErrorLog, UserActivity,
    APIAccessLog, DatabaseQueryLog, AuthenticationLog,
    SecurityIncident, SystemHealthLog
)
from log_admin.utils import LoggingUtils, LogAnalytics, LogCleanup
from log_admin.middleware import LoggingMiddleware, ActivityLoggingMiddleware, SecurityMiddleware


class TestResults:
    """Class to track test results"""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"✅ PASS: {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"❌ FAIL: {test_name} - {error}")
    
    def print_summary(self):
        total = self.passed + self.failed
        print(f"\n{'='*60}")
        print(f"TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success Rate: {(self.passed/total*100):.1f}%" if total > 0 else "No tests run")
        
        if self.errors:
            print(f"\nFAILED TESTS:")
            for error in self.errors:
                print(f"  - {error}")


def test_log_models():
    """Test all log models can be created and saved"""
    results = TestResults()

    try:
        # Create test user with unique username
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        user = User.objects.create_user(
            username=f'testuser_{unique_id}',
            email=f'test_{unique_id}@example.com',
            password='testpass123'
        )
        
        # Test LogConfig
        config = LogConfig.objects.create(
            level='INFO',
            enable_performance_logging=True,
            enable_error_logging=True,
            max_log_entries=1000,
            log_retention_days=30
        )
        results.add_pass("LogConfig creation")
        
        # Test PerformanceLog
        perf_log = PerformanceLog.objects.create(
            path='/test/path/',
            method='GET',
            duration=0.5,
            user=user,
            ip_address='127.0.0.1',
            status_code=200
        )
        results.add_pass("PerformanceLog creation")
        
        # Test ErrorLog
        error_log = ErrorLog.objects.create(
            view_name='test_view',
            error_type='VALIDATION',
            severity='LOW',
            error_message='Test error',
            stack_trace='Test stack trace',
            user=user,
            ip_address='127.0.0.1'
        )
        results.add_pass("ErrorLog creation")
        
        # Test UserActivity
        activity = UserActivity.objects.create(
            user=user,
            activity_type='LOGIN',
            action='User logged in',
            ip_address='127.0.0.1',
            success=True
        )
        results.add_pass("UserActivity creation")
        
        # Test APIAccessLog
        api_log = APIAccessLog.objects.create(
            endpoint='/api/test/',
            method='GET',
            user=user,
            ip_address='127.0.0.1',
            response_status=200,
            response_time=0.3
        )
        results.add_pass("APIAccessLog creation")
        
        # Test DatabaseQueryLog
        db_log = DatabaseQueryLog.objects.create(
            query_type='SELECT',
            table_name='test_table',
            query_hash='abc123',
            execution_time=0.1,
            user=user
        )
        results.add_pass("DatabaseQueryLog creation")
        
        # Test AuthenticationLog
        auth_log = AuthenticationLog.objects.create(
            user=user,
            event_type='LOGIN_SUCCESS',
            ip_address='127.0.0.1',
            success=True
        )
        results.add_pass("AuthenticationLog creation")
        
        # Test SecurityIncident
        security_incident = SecurityIncident.objects.create(
            incident_type='BRUTE_FORCE',
            severity='MEDIUM',
            description='Test security incident',
            ip_address='127.0.0.1'
        )
        results.add_pass("SecurityIncident creation")
        
        # Test SystemHealthLog
        health_log = SystemHealthLog.objects.create(
            metric_type='CPU_USAGE',
            value=75.5,
            unit='%',
            status='WARNING'
        )
        results.add_pass("SystemHealthLog creation")
        
    except Exception as e:
        results.add_fail("Model creation tests", str(e))
    
    return results


def test_logging_utils():
    """Test logging utility functions"""
    results = TestResults()

    try:
        # Create test user with unique username
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        user = User.objects.create_user(
            username=f'testuser2_{unique_id}',
            email=f'test2_{unique_id}@example.com',
            password='testpass123'
        )
        
        # Test manual user activity logging
        LoggingUtils.log_user_activity(
            user=user,
            activity_type='API_CALL',
            action='Test API call',
            description='Testing manual logging',
            metadata={'test': True}
        )
        
        # Verify activity was logged
        activity = UserActivity.objects.filter(user=user, action='Test API call').first()
        if activity:
            results.add_pass("Manual user activity logging")
        else:
            results.add_fail("Manual user activity logging", "Activity not found")
        
        # Test manual authentication logging
        LoggingUtils.log_authentication_event(
            event_type='LOGIN_SUCCESS',
            user=user,
            success=True,
            additional_data={'test': True}
        )
        
        # Verify auth log was created
        auth_log = AuthenticationLog.objects.filter(user=user, event_type='LOGIN_SUCCESS').first()
        if auth_log:
            results.add_pass("Manual authentication logging")
        else:
            results.add_fail("Manual authentication logging", "Auth log not found")
        
        # Test manual error logging
        LoggingUtils.log_error_manual(
            error_type='BUSINESS_LOGIC',
            error_message='Test error message',
            view_name='test_view',
            severity='MEDIUM',
            user=user
        )
        
        # Verify error log was created
        error_log = ErrorLog.objects.filter(user=user, error_message='Test error message').first()
        if error_log:
            results.add_pass("Manual error logging")
        else:
            results.add_fail("Manual error logging", "Error log not found")
        
    except Exception as e:
        results.add_fail("Logging utils tests", str(e))
    
    return results


def test_analytics():
    """Test analytics functionality"""
    results = TestResults()
    
    try:
        # Test error summary
        error_summary = LogAnalytics.get_error_summary(days=7)
        if isinstance(error_summary, dict) and 'total_errors' in error_summary:
            results.add_pass("Error summary analytics")
        else:
            results.add_fail("Error summary analytics", "Invalid response format")
        
        # Test performance summary
        perf_summary = LogAnalytics.get_performance_summary(days=7)
        if isinstance(perf_summary, dict) and 'total_requests' in perf_summary:
            results.add_pass("Performance summary analytics")
        else:
            results.add_fail("Performance summary analytics", "Invalid response format")
        
        # Test user activity summary
        activity_summary = LogAnalytics.get_user_activity_summary(days=7)
        if isinstance(activity_summary, dict) and 'total_activities' in activity_summary:
            results.add_pass("User activity summary analytics")
        else:
            results.add_fail("User activity summary analytics", "Invalid response format")
        
        # Test security summary
        security_summary = LogAnalytics.get_security_summary(days=7)
        if isinstance(security_summary, dict) and 'total_incidents' in security_summary:
            results.add_pass("Security summary analytics")
        else:
            results.add_fail("Security summary analytics", "Invalid response format")
        
    except Exception as e:
        results.add_fail("Analytics tests", str(e))
    
    return results


def test_api_endpoints():
    """Test API endpoints"""
    results = TestResults()
    
    try:
        # Create admin user with unique username
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        admin_user = User.objects.create_superuser(
            username=f'admin_{unique_id}',
            email=f'admin_{unique_id}@example.com',
            password='adminpass123'
        )
        
        client = APIClient()
        client.force_authenticate(user=admin_user)
        
        # Test health check endpoint
        response = client.get('/api/log-admin/health/')
        if response.status_code == 200:
            results.add_pass("Health check endpoint")
        else:
            results.add_fail("Health check endpoint", f"Status: {response.status_code}")
        
        # Test performance logs endpoint
        response = client.get('/api/log-admin/performance/')
        if response.status_code == 200:
            results.add_pass("Performance logs endpoint")
        else:
            results.add_fail("Performance logs endpoint", f"Status: {response.status_code}")
        
        # Test error logs endpoint
        response = client.get('/api/log-admin/errors/')
        if response.status_code == 200:
            results.add_pass("Error logs endpoint")
        else:
            results.add_fail("Error logs endpoint", f"Status: {response.status_code}")
        
        # Test analytics endpoint
        response = client.get('/api/log-admin/analytics/')
        if response.status_code == 200:
            results.add_pass("Analytics endpoint")
        else:
            results.add_fail("Analytics endpoint", f"Status: {response.status_code}")
        
        # Test dashboard endpoint
        response = client.get('/api/log-admin/dashboard/')
        if response.status_code == 200:
            results.add_pass("Dashboard endpoint")
        else:
            results.add_fail("Dashboard endpoint", f"Status: {response.status_code}")
        
        # Test config endpoint
        response = client.get('/api/log-admin/config/')
        if response.status_code == 200:
            results.add_pass("Config endpoint")
        else:
            results.add_fail("Config endpoint", f"Status: {response.status_code}")
        
    except Exception as e:
        results.add_fail("API endpoint tests", str(e))
    
    return results


def test_management_commands():
    """Test management commands"""
    results = TestResults()
    
    try:
        # Test cleanup command (dry run)
        call_command('cleanup_logs', '--dry-run', verbosity=0)
        results.add_pass("Cleanup logs command (dry run)")
        
        # Test system health monitoring command
        call_command('monitor_system_health', verbosity=0)
        results.add_pass("System health monitoring command")
        
        # Test report generation command
        call_command('generate_log_report', '--days', '1', verbosity=0)
        results.add_pass("Generate log report command")
        
    except Exception as e:
        results.add_fail("Management commands tests", str(e))
    
    return results


def main():
    """Run all tests"""
    print("🚀 Starting Comprehensive Logging System Tests")
    print("="*60)
    
    all_results = TestResults()
    
    # Run all test functions
    test_functions = [
        test_log_models,
        test_logging_utils,
        test_analytics,
        test_api_endpoints,
        test_management_commands,
    ]
    
    for test_func in test_functions:
        print(f"\n📋 Running {test_func.__name__}...")
        try:
            result = test_func()
            all_results.passed += result.passed
            all_results.failed += result.failed
            all_results.errors.extend(result.errors)
        except Exception as e:
            all_results.add_fail(test_func.__name__, f"Test function failed: {str(e)}")
            print(f"💥 Error in {test_func.__name__}: {str(e)}")
    
    # Print final summary
    all_results.print_summary()
    
    # Return exit code
    return 0 if all_results.failed == 0 else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
