#!/bin/bash

# Fix Contacts App Configuration Script
# Run this on the production server

echo "🚀 Starting Contacts App Fix..."

# Navigate to project directory
cd /one/shash_b

# Activate virtual environment
source /one/myenv/bin/activate

echo "📋 Current INSTALLED_APPS status:"
grep -A 25 "INSTALLED_APPS" shashtrarth/settings.py

echo ""
echo "🔧 Adding contacts to INSTALLED_APPS..."

# Create a backup of settings.py
cp shashtrarth/settings.py shashtrarth/settings.py.backup.$(date +%Y%m%d_%H%M%S)

# Add contacts to INSTALLED_APPS after log_admin
python3 << 'EOF'
import re

# Read the current settings file
with open('shashtrarth/settings.py', 'r') as f:
    content = f.read()

# Check if contacts is already in INSTALLED_APPS
if '"contacts"' in content:
    print("✅ contacts already in INSTALLED_APPS")
else:
    # Add contacts after log_admin
    content = re.sub(
        r'("log_admin",?\s*\n)',
        r'\1    "contacts",\n',
        content
    )
    
    # Write back to file
    with open('shashtrarth/settings.py', 'w') as f:
        f.write(content)
    
    print("✅ Added contacts to INSTALLED_APPS")
EOF

echo ""
echo "📋 Updated INSTALLED_APPS:"
grep -A 25 "INSTALLED_APPS" shashtrarth/settings.py

echo ""
echo "🔍 Running Django check..."
python manage.py check

if [ $? -eq 0 ]; then
    echo "✅ Django check passed!"
    
    echo ""
    echo "📦 Creating migrations for contacts app..."
    python manage.py makemigrations contacts
    
    echo ""
    echo "🗃️ Running all migrations..."
    python manage.py migrate
    
    echo ""
    echo "🔄 Restarting services..."
    supervisorctl reread
    supervisorctl update
    supervisorctl restart all
    
    echo ""
    echo "🌐 Restarting nginx..."
    service nginx restart
    
    echo ""
    echo "✅ All done! Contacts app should now be working."
    echo ""
    echo "🧪 Testing the setup..."
    python manage.py check --deploy
    
else
    echo "❌ Django check failed. Please review the errors above."
    echo "Restoring backup..."
    cp shashtrarth/settings.py.backup.* shashtrarth/settings.py
fi

echo ""
echo "🏁 Script completed!"
