import React, { useState, useEffect } from "react";
import { <PERSON>, But<PERSON>, Row, Col, Form, Container, Pagination } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { getTestPatterns, deleteTestPattern } from "../../redux/slice/paperEngineSlice";
import { FaPlusCircle } from "react-icons/fa";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import ViewTestPatternModal from "./ViewTestPatternModal" // Import the ViewTestPatternModel component

const AllTestPatterns = ({ patternAdded, showButtons = true }) => {
  const navigate = useNavigate();
  const accessToken = useSelector((state) => state.contributor.accessToken);

  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedPattern, setSelectedPattern] = useState(null);
  const [showViewModal, setShowViewModal] = useState(false);

  const patternsPerPage = 1;

  const dispatch = useDispatch();
  const { testPatterns, isLoading, error } = useSelector((state) => state.paperEngine);

  useEffect(() => {
    if (!accessToken) {
      toast.error("Please log in as a contributor!");
      const timer = setTimeout(() => {
        navigate("/contributor_login");
      }, 2000);
      return () => clearTimeout(timer);
    }

    dispatch(getTestPatterns());
    setLoading(false);
  }, [accessToken, navigate, dispatch, patternAdded]);

  const handleSearch = () => {
    setCurrentPage(0);
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const filteredPatterns = testPatterns.filter((pattern) =>
    pattern.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const indexOfLastPattern = (currentPage + 1) * patternsPerPage;
  const indexOfFirstPattern = indexOfLastPattern - patternsPerPage;
  const currentPatterns = filteredPatterns.slice(indexOfFirstPattern, indexOfLastPattern);

  if (loading || !accessToken) {
    return null;
  }

  const totalPages = Math.ceil(filteredPatterns.length / patternsPerPage);

  const handleView = (patternId) => {
    const pattern = testPatterns.find((p) => p.pattern_id === patternId);
    if (pattern) {
      setSelectedPattern(pattern);
      setShowViewModal(true);
    }
  };

  const handleDelete = (patternId) => {
    const pattern = testPatterns.find((p) => p.pattern_id === patternId);
    setSelectedPattern(pattern);
    dispatch(deleteTestPattern(pattern.pattern_id))
      .then(() => {
        toast.success("Pattern deleted successfully.");
        dispatch(getTestPatterns());
      })
      .catch((error) => {
        toast.error(error.message || "Failed to delete pattern.");
      });
  };

  return (
    <Container>
      <h2 className="text-center text-success" style={{ fontSize: "1.9rem", margin: "1rem" }}>
        See live updated here.
      </h2>

      <Row className="mb-4 justify-content-center">
        <Col xs={12} sm={8} md={6} lg={5} className="d-flex justify-content-between align-items-center">
          <Form.Control
            type="text"
            placeholder="Search patterns..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-100"
          />
          <Link to="/view_test_patterns">
            <Button onClick={handleSearch} variant="success" style={{ fontSize: "0.9rem", width: "5rem", marginLeft: "0.2rem" }}>
              View All
            </Button>
          </Link>
        </Col>
      </Row>

      <Row xs={1} md={1} lg={1} className="g-4">
        {currentPatterns?.map((pattern) => (
          <Col key={pattern?.pattern_id} className="d-flex justify-content-center">
            <Card
              className="shadow-lg rounded-3"
              style={{
                width: "95%",
                maxWidth: "600px",
                transform: "scale(0.95)",
                transition: "transform 0.2s ease-in-out",
              }}
              onMouseEnter={(e) => (e.currentTarget.style.transform = "scale(1)")}
              onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(0.95)")}
            >
              <Card.Header className="bg-primary text-white text-center">
                <h5>{pattern.name}</h5>
                <small>Pattern ID: {pattern?.pattern_id}</small>
              </Card.Header>
              <Card.Body>
                <div className="row mb-3">
                  <div className="col-6">
                    {/* <p><strong>Random Topic:</strong> {pattern?.random_topic ? "Yes" : "No"}</p> */}
                    <p><strong>Positive Mark:</strong> {pattern?.positive_mark}</p>
                    <p><strong>Negative Mark:</strong> {pattern?.negative_marking}</p>
                  </div>
                  <div className="col-6">
                    <p><strong>Version:</strong> {pattern?.version}</p>
                    <p><strong>Updated At:</strong> {new Date(pattern?.updated_at).toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: '2-digit' })} {new Date(pattern.updated_at).toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit', hour12: true })}</p>
                    {/* <p><strong>Subjects:</strong> {pattern?.sections?.flatMap(section => section?.subject_ids).join(", ")}</p> */}
                    {/* <p><strong>Contributor ID:</strong> {pattern.contributor}</p> */}
                  </div>
                </div>

                {/* <div className="d-flex flex wrap mb-3 p-2"> */}
                <div className="d-flex gap-1 justify-content-evenly flex-wrap mb-3">
                  {pattern?.sections.map((section, index) => (
                    <div
                      key={index}
                      className="col-12 col-md-5"
                      style={{
                        border: "1px solid #ddd",
                        borderRadius: "8px",
                        padding: "10px",
                        marginBottom: "10px",
                        background: "#f9f9f9",
                      }}
                    >
                      <h6 className="text-success">{section?.section_name}</h6>
                      <p>
                        <strong>Time Limit:</strong> {section?.time_limit} min
                      </p>
                      <p>
                        <strong>Questions:</strong> {section?.number_of_questions}
                      </p>
                      <p><strong>Subjects:</strong>{section.subject_names}</p>
                    </div>
                  ))}
                </div>

                {showButtons && (
                  <div className="d-flex justify-content-center">
                    <div className="text-start">
                      <Button variant="outline-primary" className="me-2" onClick={() => handleView(pattern?.pattern_id)}>
                        View
                      </Button>
                      <Link to={`/edit_test_pattern/${pattern?.pattern_id}`}>
                        <Button variant="outline-success" className="me-2">
                          Edit
                        </Button>
                      </Link>
                      <Button variant="danger" onClick={() => handleDelete(pattern?.pattern_id)}>
                        Delete
                      </Button>
                    </div>
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      <Row className="mt-4">
        <Col className="d-flex justify-content-center">
          <Pagination>
            {[...Array(totalPages).keys()].map((pageNumber) => (
              <Pagination.Item
                key={pageNumber}
                active={pageNumber === currentPage}
                onClick={() => handlePageChange(pageNumber)}
              >
                {pageNumber + 1}
              </Pagination.Item>
            ))}
          </Pagination>
        </Col>
      </Row>

      <Link to="/create_test_pattern">
        <Button variant="outline-success" className="position-fixed bottom-0 end-0 m-4 p-3 rounded-circle shadow-lg">
          <FaPlusCircle style={{ fontSize: "2rem" }} />
        </Button>
      </Link>

      {/* View Modal */}
      <ViewTestPatternModal
        show={showViewModal}
        onHide={() => setShowViewModal(false)}
        pattern={selectedPattern}
      />
    </Container>
  );
};

export default AllTestPatterns;
