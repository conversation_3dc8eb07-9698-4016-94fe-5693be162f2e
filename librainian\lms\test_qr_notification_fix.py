#!/usr/bin/env python3
"""
Test QR Registration Notification Fix
This script tests if the template variables are properly replaced.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def test_qr_notification_template():
    """Test QR notification with proper template variable replacement"""
    print("🧪 Testing QR Registration Notification Template Fix")
    print("=" * 60)
    
    try:
        from librarian.notification_service import notification_service
        from studentsData.models import TempStudentData
        from librarian.models import DeviceToken
        
        # Check if we have device tokens
        device_tokens = DeviceToken.objects.filter(is_active=True)
        if not device_tokens.exists():
            print("❌ No active device tokens found!")
            print("💡 Please visit http://localhost:8000/fcm-test/ and register a token first")
            return False
        
        print(f"✅ Found {device_tokens.count()} active device tokens")
        
        # Find a temp student for testing
        temp_student = TempStudentData.objects.filter(status='pending').first()
        
        if not temp_student:
            print("❌ No pending temp students found for testing")
            print("💡 Please register a new student via QR code first")
            return False
        
        print(f"📋 Testing with student: {temp_student.name}")
        print(f"📧 Email: {temp_student.email}")
        print(f"📱 Mobile: {temp_student.mobile}")
        print(f"🎓 Course: {temp_student.course.name if temp_student.course else 'Not specified'}")
        print(f"🏙️ City: {temp_student.city}")
        
        # Test the notification service
        print("\n🔔 Sending QR registration notification...")
        result = notification_service.qr_registration_submitted(temp_student)
        
        if result:
            print("✅ QR registration notification sent successfully!")
            print(f"📊 Notification ID: {result.pk if hasattr(result, 'pk') else 'N/A'}")
            print(f"📝 Title: {result.title if hasattr(result, 'title') else 'N/A'}")
            print(f"📄 Body: {result.body if hasattr(result, 'body') else 'N/A'}")
            
            # Check if template variables were replaced
            if hasattr(result, 'body') and '{' not in result.body and '}' not in result.body:
                print("✅ Template variables properly replaced!")
            else:
                print("❌ Template variables not replaced properly")
                if hasattr(result, 'body'):
                    print(f"Body content: {result.body}")
            
            return True
        else:
            print("❌ QR registration notification failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_rendering_directly():
    """Test template rendering directly"""
    print("\n🔧 Testing Template Rendering Directly")
    print("=" * 40)
    
    try:
        from django.template import Template, Context
        from librarian.notification_templates import NOTIFICATION_TEMPLATES
        
        # Get QR registration template
        template_data = NOTIFICATION_TEMPLATES['qr_registration']
        
        print(f"📝 Template Title: {template_data['title']}")
        print(f"📄 Template Body: {template_data['body']}")
        
        # Test data
        context_data = {
            'student_name': 'John Doe',
            'student_email': '<EMAIL>',
            'student_mobile': '9876543210',
            'course': 'Computer Science',
            'student_city': 'Mumbai',
            'registration_date': '2024-01-15'
        }
        
        # Render templates
        title_template = Template(template_data['title'])
        body_template = Template(template_data['body'])
        
        rendered_title = title_template.render(Context(context_data))
        rendered_body = body_template.render(Context(context_data))
        
        print(f"\n✅ Rendered Title: {rendered_title}")
        print(f"✅ Rendered Body: {rendered_body}")
        
        # Check if variables were replaced
        if '{{' not in rendered_body and '}}' not in rendered_body:
            print("✅ Template variables properly replaced in direct test!")
            return True
        else:
            print("❌ Template variables not replaced in direct test")
            return False
            
    except Exception as e:
        print(f"❌ Direct template test failed: {e}")
        return False

def main():
    print("🚀 Starting QR Notification Template Fix Test...")
    
    # Test direct template rendering first
    direct_test = test_template_rendering_directly()
    
    # Test full notification flow
    full_test = test_qr_notification_template()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    print(f"🔧 Direct Template Test: {'✅ PASSED' if direct_test else '❌ FAILED'}")
    print(f"🔔 Full Notification Test: {'✅ PASSED' if full_test else '❌ FAILED'}")
    
    if direct_test and full_test:
        print("\n🎉 SUCCESS: QR notification template fix is working!")
        print("💡 Now register a new student to see the fixed notification")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")
    
    print("\n🌐 Next Steps:")
    print("   1. Register a new student via QR code")
    print("   2. Check the notification popup for proper data")
    print("   3. Verify all student information is displayed correctly")

if __name__ == "__main__":
    main()
