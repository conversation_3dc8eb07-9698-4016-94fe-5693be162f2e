import React, { useState, useContext, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
  Animated,
} from 'react-native';
import * as Contacts from 'expo-contacts';
import Icon from 'react-native-vector-icons/FontAwesome';
import { ThemeContext } from '../context/ThemeContext';
import PracticeStatsComponent from '../components/PracticeStatsComponent';
import BottomTabBar from '../components/BottomTabBar';
import { useDispatch } from 'react-redux';
import { syncContacts } from '../redux/contactSlice';

const { width } = Dimensions.get('window');

// Dummy data
const userProgress = {
  currentLevel: 'ADVANCED', // Updated to match 76.5 score
  progressPercent: 76,
  nextLevel: 'EXPERT',
  neededPercent: 14, // Need 90 - 76.5 = 13.5% more to reach Expert
  studyHoursThisWeek: 12,
  averagePerDay: 1.7,
  currentScore: 76.5 // User's current performance score
};

// User's practice stats
const userPracticeStats = {
  daily_practice: 15,
  weekly_practice: 8,
  monthly_practice: 3,
  continuous_practice: 12
};

// Friends progress with comparative analysis
const friendsProgress = [
  {
    name: 'Anjali Sharma',
    currentLevel: 'ADVANCED',
    examType: 'Banking',
    progressPercent: 85,
    userComparison: 'You need 9% more practice to reach Anjali\'s level',
    friendStatus: 'Anjali is ahead of you by 9%'
  },
  {
    name: 'Ravi Mehta',
    currentLevel: 'INTERMEDIATE',
    examType: 'SSC',
    progressPercent: 68,
    userComparison: 'You are ahead of Ravi by 8%',
    friendStatus: 'Ravi needs 8% more to reach your level'
  },
  {
    name: 'Sneha Verma',
    currentLevel: 'BEGINNER',
    examType: 'UPSC',
    progressPercent: 45,
    userComparison: 'You are ahead of Sneha by 31%',
    friendStatus: 'Sneha needs 31% more to reach your level'
  }
];

const ProgressScreen = ({ isEmbedded = false }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const [showFriends, setShowFriends] = useState(false);
  const [contactNumbers, setContactNumbers] = useState([]);
  const dispatch = useDispatch();

  // Progress levels configuration (BMI-style meter)
  // Color progression: Grey (lowest) → Yellow → Orange → Green (highest)
  const levels = [
    {
      name: 'EXPERT',
      color: '#28a745', // Green (highest level)
      range: '90 - 100',
      minScore: 90,
      maxScore: 100,
      description: 'Ready for competitive exams'
    },
    {
      name: 'ADVANCED',
      color: '#fd7e14', // Orange
      range: '75 - 89.9',
      minScore: 75,
      maxScore: 89.9,
      description: 'Strong preparation level'
    },
    {
      name: 'INTERMEDIATE',
      color: '#ffc107', // Yellow
      range: '60 - 74.9',
      minScore: 60,
      maxScore: 74.9,
      description: 'Good foundation built'
    },
    {
      name: 'BEGINNER',
      color: '#ffd700', // Lighter Yellow (distinct from intermediate)
      range: '40 - 59.9',
      minScore: 40,
      maxScore: 59.9,
      description: 'Learning in progress'
    },
    {
      name: 'STARTER',
      color: '#0099ff', // Gray (lowest level)
      range: '0 - 39.9',
      minScore: 0,
      maxScore: 39.9,
      description: 'Just getting started'
    },
  ];

  // Clean phone number function
  const cleanPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return '';
    return phoneNumber.replace(/[\s\-\(\)\+]/g, '');
  };

  // Request contacts and extract phone numbers
  const requestContactsAccess = async () => {
    try {
      const { status } = await Contacts.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Denied',
          'We need access to your contacts to show your friends\' progress.',
          [{ text: 'OK' }]
        );
        return;
      }
      const { data } = await Contacts.getContactsAsync({
        fields: [Contacts.Fields.PhoneNumbers, Contacts.Fields.Name],
      });
      if (data.length === 0) {
        Alert.alert(
          'No Contacts Found',
          'No contacts were found on your device.',
          [{ text: 'OK' }]
        );
        return;
      }
      // Extract and clean phone numbers with names
      const phoneNumbers = [];
      const contactsWithNumbers = [];
      const contactsPayload = [];
      data.forEach(contact => {
        if (contact.phoneNumbers && contact.phoneNumbers.length > 0) {
          contact.phoneNumbers.forEach(phone => {
            const cleanedNumber = cleanPhoneNumber(phone.number);
            if (cleanedNumber) {
              phoneNumbers.push(cleanedNumber);
              contactsWithNumbers.push({
                name: contact.name || 'Unknown',
                phoneNumber: cleanedNumber,
                originalNumber: phone.number
              });
              contactsPayload.push({
                name: contact.name || 'Unknown',
                contact: phone.number
              });
            }
          });
        }
      });
      // Log actual contact names and numbers
      console.log('📱 Actual Contact Names and Numbers:');
      contactsWithNumbers.forEach((contact, index) => {
        console.log(`${index + 1}. Name: ${contact.name}, Number: ${contact.phoneNumber}`);
      });
      console.log('📱 Total Cleaned Phone Numbers:', phoneNumbers.length);
      setContactNumbers(phoneNumbers);
      setShowFriends(true);
      // Dispatch syncContacts with contactsPayload
      if (contactsPayload.length > 0) {
        dispatch(syncContacts({ contacts: contactsPayload }));
      }
    } catch (error) {
      console.error('Error accessing contacts:', error);
      Alert.alert(
        'Error',
        'Failed to access contacts. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  // Animation refs - separate native and non-native animations
  const meterFillAnimation = useRef(new Animated.Value(0)).current; // For height (non-native)
  const pointerPositionAnimation = useRef(new Animated.Value(0)).current; // For position (non-native)
  const pulseAnimation = useRef(new Animated.Value(1)).current; // For scale (native)

  // Get current level based on score
  const getCurrentLevel = () => {
    return levels.find(level =>
      userProgress.currentScore >= level.minScore &&
      userProgress.currentScore <= level.maxScore
    ) || levels[levels.length - 1];
  };



  const currentLevel = getCurrentLevel();

  // Calculate meter fill percentage (0-1)
  const getMeterFillPercentage = () => {
    const totalRange = 100; // 0-100 score range
    return userProgress.currentScore / totalRange;
  };

  // Start meter animation
  const startMeterAnimation = () => {

    // Reset animations
    meterFillAnimation.setValue(0);
    pointerPositionAnimation.setValue(0);
    pulseAnimation.setValue(1);

    // Animation sequence: 100% → 100% → Actual Value
    Animated.sequence([
      // First: Animate to 100% (1st time)
      Animated.parallel([
        Animated.timing(meterFillAnimation, {
          toValue: 1, // 100%
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(pointerPositionAnimation, {
          toValue: 100, // 100%
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
      // Reset to 0
      Animated.parallel([
        Animated.timing(meterFillAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(pointerPositionAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]),
      // Second: Animate to 100% (2nd time)
      Animated.parallel([
        Animated.timing(meterFillAnimation, {
          toValue: 1, // 100%
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(pointerPositionAnimation, {
          toValue: 100, // 100%
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
      // Reset to 0
      Animated.parallel([
        Animated.timing(meterFillAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(pointerPositionAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]),
      // Third: Animate to actual value
      Animated.parallel([
        Animated.timing(meterFillAnimation, {
          toValue: getMeterFillPercentage(),
          duration: 1500,
          useNativeDriver: false,
        }),
        Animated.timing(pointerPositionAnimation, {
          toValue: userProgress.currentScore,
          duration: 1500,
          useNativeDriver: true,
        }),
      ]),
      // Finally: Pulse animation when complete
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnimation, {
            toValue: 1.2,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnimation, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
        ]),
        { iterations: 3 }
      ),
    ]).start();
  };

  // Start animation on component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      startMeterAnimation();
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  // Render BMI-style meter
  const renderProgressMeter = () => (
    <TouchableOpacity
      style={styles.meterContainer}
      onPress={startMeterAnimation}
      activeOpacity={0.8}
    >
      <Text style={[styles.sectionTitle, isDarkMode && styles.textDark]}>
        Your Performance Level
      </Text>

      <View style={styles.meterWrapper}>
        {/* Meter Background */}
        <View style={[styles.meterBackground, isDarkMode && styles.meterBackgroundDark]}>

          {/* Level Sections */}
          {levels.map((level, index) => {
            const sectionHeight = (level.maxScore - level.minScore) / 100 * 300; // 300 is total meter height
            return (
              <View
                key={level.name}
                style={[
                  styles.meterSection,
                  {
                    height: sectionHeight,
                    backgroundColor: level.color,
                  }
                ]}
              >
                <View style={styles.levelLabels}>
                  <Text style={styles.levelName}>{level.name}</Text>
                  <Text style={styles.levelRange}>{level.range}</Text>
                </View>
              </View>
            );
          })}

          {/* Animated Fill */}
          <Animated.View
            style={[
              styles.meterFill,
              {
                height: meterFillAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 300], // 300 is total meter height
                  extrapolate: 'clamp',
                }),
              }
            ]}
          />
        </View>

        {/* Current Level Pointer - Outside the meter */}
        <Animated.View
          style={[
            styles.pointerContainer,
            {
              transform: [
                {
                  translateY: pointerPositionAnimation.interpolate({
                    inputRange: [0, 100],
                    outputRange: [150 - 10, -150 - 10], // Move from bottom to top, 10px above actual position
                    extrapolate: 'clamp',
                  })
                },
                { scale: pulseAnimation }
              ],
            }
          ]}
        >
          <View style={[styles.pointer, { borderLeftColor: currentLevel.color }]} />
          <View style={[styles.pointerDot, { backgroundColor: currentLevel.color }]} />
        </Animated.View>

        {/* Score Display */}
        <View style={styles.scoreDisplay}>
          <Animated.Text
            style={[
              styles.currentScore,
              { color: currentLevel.color },
              { transform: [{ scale: pulseAnimation }] }
            ]}
          >
            {userProgress.currentScore}
          </Animated.Text>
          <Text style={[
            styles.scoreLabel,
            { color: currentLevel.color },
            isDarkMode && styles.textDark
          ]}>
            PERFORMANCE SCORE
          </Text>
        </View>
      </View>

      {/* Current Level Info */}
      <View style={[styles.currentLevelInfo, isDarkMode && styles.currentLevelInfoDark]}>
        <View style={[styles.levelIndicator, { backgroundColor: currentLevel.color }]} />
        <View style={styles.levelDetails}>
          <Text style={[styles.currentLevelName, { color: currentLevel.color }]}>
            {currentLevel.name}
          </Text>
          <Text style={[styles.currentLevelDesc, isDarkMode && styles.textDark]}>
            {currentLevel.description}
          </Text>
          <Text style={[styles.nextLevelText, isDarkMode && styles.textDark]}>
            {userProgress.nextLevel && `${userProgress.neededPercent}% more to reach ${userProgress.nextLevel}`}
          </Text>
        </View>
      </View>


    </TouchableOpacity>
  );

  // Render study stats
  const renderStudyStats = () => (
    <View style={[styles.statsContainer, isDarkMode && styles.statsContainerDark]}>
      <Text style={[styles.sectionTitle, isDarkMode && styles.textDark]}>
        Study Statistics
      </Text>

      <View style={styles.statsRow}>
        <View style={styles.statItem}>
          <Icon name="clock-o" size={24} color="#4CAF50" />
          <Text style={[styles.statValue, isDarkMode && styles.textDark]}>
            {userProgress.studyHoursThisWeek} hrs
          </Text>
          <Text style={[styles.statLabel, isDarkMode && styles.textDark]}>
            This Week
          </Text>
        </View>

        <View style={styles.statItem}>
          <Icon name="calendar" size={24} color="#2196F3" />
          <Text style={[styles.statValue, isDarkMode && styles.textDark]}>
            {userProgress.averagePerDay} hrs
          </Text>
          <Text style={[styles.statLabel, isDarkMode && styles.textDark]}>
            Daily Average
          </Text>
        </View>
      </View>
    </View>
  );

  // Render practice stats
  const renderPracticeStats = () => (
    <View style={styles.practiceContainer}>
      <PracticeStatsComponent
        dailyPractice={userPracticeStats.daily_practice}
        weeklyPractice={userPracticeStats.weekly_practice}
        monthlyPractice={userPracticeStats.monthly_practice}
        continuousPractice={userPracticeStats.continuous_practice}
        title="Practice Statistics"
        showTitle={true}
      />
    </View>
  );

  // Render friends section
  const renderFriendsSection = () => (
    <View style={styles.friendsContainer}>
      <Text style={[styles.sectionTitle, isDarkMode && styles.textDark]}>
        Friends Progress
      </Text>
      
      {!showFriends ? (
        <TouchableOpacity
          style={[styles.friendsButton, isDarkMode && styles.friendsButtonDark]}
          onPress={requestContactsAccess}
        >
          <Icon name="users" size={20} color="#fff" style={styles.buttonIcon} />
          <Text style={styles.friendsButtonText}>
            See the performance of your friends
          </Text>
        </TouchableOpacity>
      ) : (
        <View style={styles.friendsList}>
          <Text style={[styles.contactsInfo, isDarkMode && styles.textDark]}>
            Found {contactNumbers.length} contacts
          </Text>
          
          {friendsProgress.map((friend, index) => (
            <View key={index} style={[styles.friendCard, isDarkMode && styles.friendCardDark]}>
              <View style={styles.friendHeader}>
                <Text style={[styles.friendName, isDarkMode && styles.textDark]}>
                  {friend.name}
                </Text>
                <View style={[styles.levelBadge, { backgroundColor: getLevelColor(friend.currentLevel) }]}>
                  <Text style={styles.levelBadgeText}>{friend.currentLevel}</Text>
                </View>
              </View>

              <Text style={[styles.examType, isDarkMode && styles.textDark]}>
                {friend.examType} Preparation • {friend.progressPercent}% Complete
              </Text>

              {/* User comparison line */}
              <Text style={[styles.userComparison, isDarkMode && styles.textDark]}>
                📊 {friend.userComparison}
              </Text>

              {/* Friend status line */}
              <Text style={[styles.friendStatus, isDarkMode && styles.textDark]}>
                👤 {friend.friendStatus}
              </Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  // Get level color helper
  const getLevelColor = (levelName) => {
    const level = levels.find(l => l.name === levelName);
    return level ? level.color : '#9E9E9E';
  };

  return (
    <BottomTabBar>
      <ScrollView 
        style={[styles.container, isDarkMode && styles.containerDark]}
        showsVerticalScrollIndicator={false}
      >
        {!isEmbedded && (
          <Text style={[styles.screenTitle, isDarkMode && styles.textDark]}>
            Your Progress
          </Text>
        )}
        
        {renderProgressMeter()}
        {renderStudyStats()}
        {renderPracticeStats()}
        {renderFriendsSection()}
      </ScrollView>
    </BottomTabBar>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  screenTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 20,
    color: '#333',
  },
  textDark: {
    color: '#fff',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
    color: '#333',
  },

  // Meter Styles
  meterContainer: {
    padding: 20,
    alignItems: 'center',
  },
  meterWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  meterBackground: {
    width: 60,
    height: 300,
    backgroundColor: '#f0f0f0',
    borderRadius: 30,
    overflow: 'hidden',
    position: 'relative',
    marginRight: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  meterBackgroundDark: {
    backgroundColor: '#2a2a2a',
  },
  meterSection: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.2)',
  },
  levelLabels: {
    alignItems: 'center',
  },
  levelName: {
    color: '#fff',
    fontSize: 8,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  levelRange: {
    color: '#fff',
    fontSize: 6,
    textAlign: 'center',
    marginTop: 2,
  },
  meterFill: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  pointerContainer: {
    position: 'absolute',
    left: -35, // 20px gap from left side of meter
    top: '50%', // Center vertically, then use translateY for animation
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 10,
  },
  pointer: {
    width: 0,
    height: 0,
    borderTopWidth: 8,
    borderBottomWidth: 8,
    borderLeftWidth: 15,
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
    borderLeftColor: '#ffc107', // Will be overridden by dynamic color
  },
  pointerDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#ffc107', // Will be overridden by dynamic color
    marginLeft: 2,
    borderWidth: 2,
    borderColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  scoreDisplay: {
    alignItems: 'center',
    marginLeft: 20,
  },
  currentScore: {
    fontSize: 36,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  scoreLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
    letterSpacing: 1,
  },
  currentLevelInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 12,
    marginTop: 20,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  currentLevelInfoDark: {
    backgroundColor: '#1e1e1e',
  },
  levelIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  levelDetails: {
    flex: 1,
  },
  currentLevelName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  currentLevelDesc: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  nextLevelText: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
  },


  // Stats Styles
  statsContainer: {
    margin: 20,
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsContainerDark: {
    backgroundColor: '#1e1e1e',
  },

  // Practice Stats Styles
  practiceContainer: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
    color: '#333',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },

  // Friends Styles
  friendsContainer: {
    padding: 20,
  },
  friendsButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  friendsButtonDark: {
    backgroundColor: '#388E3C',
  },
  buttonIcon: {
    marginRight: 10,
  },
  friendsButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  contactsInfo: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    textAlign: 'center',
  },
  friendsList: {
    marginTop: 10,
  },
  friendCard: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  friendCardDark: {
    backgroundColor: '#1e1e1e',
  },
  friendHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  friendName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  levelBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  levelBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  examType: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  userComparison: {
    fontSize: 13,
    color: '#2196F3',
    marginBottom: 4,
    fontWeight: '500',
  },
  friendStatus: {
    fontSize: 13,
    color: '#666',
    fontStyle: 'italic',
  },
});

export default ProgressScreen;
