import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get auth token
const getAuthToken = (getState) => {
  const { access } = getState().customerCare;
  return access;
};

// Base API URL for popup banners
const API_URL = `${import.meta.env.VITE_BASE_URL}api/customrcare/popup-banners/`;

const initialState = {
  isLoading: false,
  error: null,
  banners: [],
  totalCount: 0,
};

// Fetch all popup banners for approval
export const fetchPopupBanners = createAsyncThunk(
  "popupApproval/fetchAll",
  async (filters = {}, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const queryParams = new URLSearchParams();
      
      // Add filters to query params
      if (filters.approval_status) queryParams.append('approval_status', filters.approval_status);
      if (filters.content_type) queryParams.append('content_type', filters.content_type);
      if (filters.created_by) queryParams.append('created_by', filters.created_by);
      if (filters.search) queryParams.append('search', filters.search);
      
      const url = queryParams.toString() ? `${API_URL}?${queryParams}` : API_URL;
      
      const response = await axios.get(url, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching popup banners");
    }
  }
);

// Approve popup banner
export const approvePopupBanner = createAsyncThunk(
  "popupApproval/approve",
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.patch(
        `${API_URL}${id}/`,
        { approval_status: "approved_by_care" },
        {
          headers: { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error approving banner");
    }
  }
);

// Reject popup banner
export const rejectPopupBanner = createAsyncThunk(
  "popupApproval/reject",
  async ({ id, rejection_reason }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.patch(
        `${API_URL}${id}/`,
        { 
          approval_status: "rejected_by_care",
          rejection_reason: rejection_reason || "Content does not meet guidelines"
        },
        {
          headers: { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error rejecting banner");
    }
  }
);

// Activate/Deactivate popup banner
export const togglePopupBanner = createAsyncThunk(
  "popupApproval/toggle",
  async ({ id, is_active }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.patch(
        `${API_URL}${id}/`,
        { is_active },
        {
          headers: { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error toggling banner status");
    }
  }
);

const popupApprovalSlice = createSlice({
  name: "popupApproval",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearBanners: (state) => {
      state.banners = [];
      state.totalCount = 0;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch banners
      .addCase(fetchPopupBanners.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPopupBanners.fulfilled, (state, action) => {
        state.isLoading = false;
        state.banners = Array.isArray(action.payload) ? action.payload : action.payload.results || [];
        state.totalCount = action.payload.count || state.banners.length;
      })
      .addCase(fetchPopupBanners.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Approve banner
      .addCase(approvePopupBanner.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(approvePopupBanner.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.banners.findIndex(banner => banner.id === action.payload.id);
        if (index !== -1) {
          state.banners[index] = action.payload;
        }
      })
      .addCase(approvePopupBanner.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Reject banner
      .addCase(rejectPopupBanner.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(rejectPopupBanner.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.banners.findIndex(banner => banner.id === action.payload.id);
        if (index !== -1) {
          state.banners[index] = action.payload;
        }
      })
      .addCase(rejectPopupBanner.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Toggle banner
      .addCase(togglePopupBanner.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(togglePopupBanner.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.banners.findIndex(banner => banner.id === action.payload.id);
        if (index !== -1) {
          state.banners[index] = action.payload;
        }
      })
      .addCase(togglePopupBanner.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, clearBanners } = popupApprovalSlice.actions;
export default popupApprovalSlice.reducer;
