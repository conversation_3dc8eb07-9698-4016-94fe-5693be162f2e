import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { forgotPassword, verifyForgotPasswordOtp } from '../Redux/studentSlice';  // Import the async thunks
import { Button, Form, Container, Row, Col, Card, Alert, Spinner } from 'react-bootstrap';
import { useNavigate, Link } from 'react-router-dom';

function ForgotPasswordPage() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { forgotPasswordOtpLoading, forgotPasswordSuccess, error } = useSelector((state) => state.student);

  // States for handling email and OTP/password form
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showOtpForm, setShowOtpForm] = useState(false); // To toggle between email and OTP form
  const [showError, setShowError] = useState(false);

  // Handle email form submission
  const handleEmailSubmit = (e) => {
    e.preventDefault();
    if (!email) {
      setShowError(true);
      return;
    }
    
    // Dispatch the forgotPassword thunk to send OTP to the entered email
    dispatch(forgotPassword(email))
      .unwrap()
      .then(() => {
        setShowOtpForm(true); // Show OTP verification form if OTP request is successful
      })
      .catch((err) => {
        setShowError(true); // Show error if the OTP request fails
      });
  };

  // Handle OTP form submission
  const handleOtpSubmit = (e) => {
    e.preventDefault();
    setShowError(false);

    // Validation
    if (!otp || !newPassword || !confirmPassword) {
      setShowError(true);
      return;
    }

    if (newPassword !== confirmPassword) {
      setShowError(true);
      return;
    }

    // Dispatch the async thunk to verify OTP and reset the password
    dispatch(verifyForgotPasswordOtp({ email, otp, new_password: newPassword, confirm_password: confirmPassword }))
      .unwrap()
      .then(() => {
        alert("Your password has been reset successfully!");
        navigate('/login'); // Redirect to login page
      })
      .catch(() => {
        setShowError(true); // Show error if the OTP verification fails
      });
  };

  return (
    <Container fluid className="d-flex justify-content-center align-items-center min-vh-100 bg-light">
      <Row className="w-100">
        <Col md={6} lg={4} className="mx-auto">
          <Card className="p-4 shadow-lg">
            <Card.Body>
              {!showOtpForm ? (
                // Step 1: Email form (for requesting OTP)
                <>
                  <h2 className="text-center mb-4">Forgot Password</h2>
                  <Form onSubmit={handleEmailSubmit}>
                    <Form.Group controlId="email" className="mb-3">
                      <Form.Label>Email Address</Form.Label>
                      <Form.Control
                        type="email"
                        placeholder="Enter your email address"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                      />
                    </Form.Group>

                    {showError && (
                      <Alert variant="danger" className="mb-3">
                        Please enter a valid email address.
                      </Alert>
                    )}

                    <Button type="submit" variant="primary" className="w-100">
                      Send OTP
                    </Button>
                    <div className="mt-3 text-center">
                    <Link to="/register"><small> Don't have a account ? Register here. </small></Link>
                  </div>
                  </Form>
                </>
              ) : (
                // Step 2: OTP and password reset form
                <>
                  <h2 className="text-center mb-4">Verify OTP & Reset Password</h2>
                  <Form onSubmit={handleOtpSubmit}>
                    <Form.Group controlId="otp" className="mb-3">
                      <Form.Label>OTP</Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Enter OTP"
                        value={otp}
                        onChange={(e) => setOtp(e.target.value)}
                        required
                      />
                    </Form.Group>

                    <Form.Group controlId="newPassword" className="mb-3">
                      <Form.Label>New Password</Form.Label>
                      <Form.Control
                        type="password"
                        placeholder="Enter new password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        required
                      />
                    </Form.Group>

                    <Form.Group controlId="confirmPassword" className="mb-3">
                      <Form.Label>Confirm New Password</Form.Label>
                      <Form.Control
                        type="password"
                        placeholder="Confirm new password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        required
                      />
                    </Form.Group>

                    {showError && (
                      <Alert variant="danger" className="mb-3">
                        Please make sure all fields are filled in correctly and the passwords match.
                      </Alert>
                    )}

                    {error && !forgotPasswordSuccess && (
                      <Alert variant="danger" className="mb-3">
                        {error}
                      </Alert>
                    )}

                    <Button
                      type="submit"
                      variant="primary"
                      className="w-100"
                      disabled={forgotPasswordOtpLoading}
                    >
                      {forgotPasswordOtpLoading ? <Spinner animation="border" size="sm" /> : 'Verify OTP & Reset Password'}
                    </Button>
                  </Form>
                </>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
}

export default ForgotPasswordPage;
