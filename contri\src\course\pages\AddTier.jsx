import React, { useState, useEffect } from "react"; 
import { But<PERSON>, Form, Container, Row, Col, Spinner } from "react-bootstrap"; 
import { useDispatch, useSelector } from "react-redux"; 
import { createTier } from "../../redux/slice/tierSlice"; 
import { getTestPatterns } from "../../redux/slice/paperEngineSlice"; // Redux action to fetch test patterns 
import { Link, useNavigate, useParams } from "react-router-dom"; 
import NavigationBar from "../../commonComponents/NavigationBar"; 
import { toast } from "react-hot-toast"; 
import AllTiers from "../components/AllTiers";

const AddTier = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { subcourseSlug } = useParams(); // Get subcourseSlug from URL parameters
  const accessToken = useSelector((state) => state.contributor.accessToken);
  const { testPatterns, status: testPatternsStatus } = useSelector(
    (state) => state.paperEngine
  ); // Access test patterns from Redux store

  const [tierName, setTierName] = useState("");
  const [tierDescription, setTierDescription] = useState("");
  const [testPatternValue, setTestPatternValue] = useState(""); // State to hold selected test pattern ID
  const [tierAdded, setTierAdded] = useState(false); // State to track tier addition
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!accessToken) {
      toast.error("Please log in as a contributor!");
      const timer = setTimeout(() => {
        navigate("/contributor_login");
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [accessToken, navigate]);

  useEffect(() => {
    if (testPatternsStatus === "idle") {
      dispatch(getTestPatterns()); // Fetch test patterns when the component mounts
    }
  }, [dispatch, testPatternsStatus]);

  if (!accessToken) {
    return (
      <Container
        className="d-flex justify-content-center align-items-center text-success"
        style={{ height: "100vh" }}
      >
        <Spinner animation="border" />
      </Container>
    );
  }

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);
  
    const tierData = {
      subcourse: subcourseSlug,
      name: tierName,
      description: tierDescription,
      ...(testPatternValue && testPatternValue !== "none" && { test_pattern: testPatternValue }),
    };
  
    dispatch(createTier({ data: tierData }))
      .unwrap()
      .then(() => {
        setTierName("");
        setTierDescription("");
        setTestPatternValue(""); // Reset test pattern selection
        setTierAdded(true); // Set tierAdded to true
        toast.success("Tier added successfully!");
        setTimeout(() => setTierAdded(false), 2000); // Reset tierAdded after delay
      })
      .catch((error) => {
        console.error("Error creating tier:", error);
        toast.error("Failed to create tier. Please try again.");
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  return (
    <>
      <NavigationBar />
      <Container>
        <Row>
          <Col xs={12} md={4} lg={3}>
            <Form
              onSubmit={handleSubmit}
              className="border mt-5 p-4 rounded shadow-lg"
            >
              <Form.Group controlId="tierName" className="mb-3">
                <Form.Label>Tier Name</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter tier name"
                  value={tierName}
                  onChange={(e) => setTierName(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="tierDescription" className="mb-3">
                <Form.Label>Tier Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={4}
                  placeholder="Enter tier description"
                  value={tierDescription}
                  onChange={(e) => setTierDescription(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="testPattern" className="mb-3">
                <Form.Label>Test Pattern</Form.Label>
                {testPatternsStatus === "loading" ? (
                  <Spinner animation="border" variant="primary" />
                ) : (
                  <Form.Control
                    as="select"
                    value={testPatternValue}
                    onChange={(e) => setTestPatternValue(e.target.value)}
                  >
                    <option value="" disabled>
                      Select a Test Pattern
                    </option>
                    <option value="none">None</option> {/* Explicit "None" option */}
                    {testPatterns.map((pattern) => (
                      <option key={pattern.pattern_id} value={pattern.pattern_id}>
                        {pattern.name} (Version: {pattern.version})
                      </option>
                    ))}
                  </Form.Control>
                )}
              </Form.Group>

              <Button
                variant="success"
                type="submit"
                className="w-100"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Add Tier"}
              </Button>
            </Form>

            <div className="d-flex justify-content-center align-items-center">
              <Link to="/contributor_dashboard">
                <Button variant="secondary" className="mt-5 text-center">
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </Col>

          <Col xs={12} md={8} lg={9} className="border-left">
            <AllTiers tierAdded={tierAdded} />
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default AddTier;
