import React from 'react';
import { <PERSON>, Row, Col } from 'react-bootstrap';
import SmallCard from './SmallCard'; // Import SmallCard to map it

const BigCard = ({ questionsSummary, isLoading }) => {
  return (
    <Card className="mb-4 px-3 shadow">
      <Card.Body>
        <h1 className="text-center m-4" style={{ fontSize: '1.9rem', color: '#146c43' }}>QUESTIONS SUMMARY</h1>
        <Row>
          {/* Display each category's summary in a small card */}
          <Col lg={4} md={6} sm={12}>
            <SmallCard title="Questions" data={questionsSummary?.questions?.total} isLoading={isLoading}/>
          </Col>
          <Col lg={4} md={6} sm={12}>
            <SmallCard title="Master Questions" data={questionsSummary?.master_questions?.total} isLoading={isLoading}/>
          </Col>
          <Col lg={4} md={6} sm={12}>
            <SmallCard title="Master Options" data={questionsSummary?.master_options?.total} isLoading={isLoading}/>
          </Col>
          <Col lg={4} md={6} sm={12}>
            <SmallCard title="Blogs" data={questionsSummary?.blogs?.total} isLoading={isLoading}/>
          </Col>
          <Col lg={4} md={6} sm={12}>
            <SmallCard title="Previous Questions" data={questionsSummary?.previous_questions?.total} isLoading={isLoading}/>
          </Col>
        </Row>
      </Card.Body>
    </Card>
  );
};

export default BigCard;
