from django.contrib.auth import authenticate, login
from django.contrib.auth.models import User
from django.contrib import messages

from django.core.mail import send_mail
from django.shortcuts import render, redirect
from django.utils.crypto import get_random_string
from django.views.decorators.csrf import csrf_exempt

import os
import re
from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from email.mime.image import MIMEImage

from django.contrib.auth.models import Group
from libraryCommander.models import *
from manager.models import *
from librarian.models import *
from subLibrarian.models import *
import threading
from wallet_and_transactions.models import *
from studentsData.models import *
from utils.notifications import send_email_async

def create_user_and_login(request):
    first_name = request.POST.get("first_name")
    last_name = request.POST.get("last_name")
    username = request.POST.get("username")
    email = request.POST.get("email")
    password1 = request.POST.get("password1")
    password2 = request.POST.get("password2")

    if not username or not email or not password1 or not password2:
        messages.error(request, "All fields are required.")
        return None

    # Validate username format - only allow letters, numbers, periods, hyphens, underscores
    if not re.match(r'^[a-zA-Z0-9._-]+$', username):
        messages.error(request, "Username can only contain letters, numbers, periods (.), hyphens (-), and underscores (_).")
        return None

    if password1 != password2:
        messages.error(request, "Passwords do not match.")
        return None

    if User.objects.filter(username=username).exists():
        messages.error(request, "Username already taken.")
        return None

    if User.objects.filter(email=email).exists():
        messages.error(request, "Email already taken.")
        return None

    user = User.objects.create_user(
        first_name=first_name,
        last_name=last_name,
        username=username,
        email=email,
        password=password1,
    )
    # login(request, user)
    return user


def authenticate_and_login(request, role):
    username = request.POST.get("username")
    password = request.POST.get("password")

    if not username or not password:
        messages.error(request, "Both username and password are required.")
        return None

    if not User.objects.filter(username=username).exists():
        messages.error(request, "Username is not found")
        return None

    user = authenticate(request, username=username, password=password)
    
    if user is not None:
        print(user, role)
        if user.groups.filter(name=role).exists():
            
            login(request, user)
            return user
    else:
        messages.error(request, "Invalid username or password.")
        return None


# Dictionary to store OTPs, can be replaced with a database or cache solution
otp_storage = {}


@csrf_exempt
def password_reset_request(request):
    if request.method == "POST":
        email = request.POST.get("email")
        if email:
            try:
                user = User.objects.get(email=email)
                otp = get_random_string(length=6, allowed_chars="0123456789")
                otp_storage[email] = otp

                # Prepare email content
                subject = "OTP Verification"
                from_email = settings.EMAIL_HOST_USER
                to_email = user.email
                html_content = render_to_string(
                    "forgetpassword_mail.html", {"user": user, "otp": otp}
                )

                # Send email asynchronously using threading
                email_thread = threading.Thread(
                    target=send_email_async,
                    args=(subject, html_content, from_email, to_email),
                )
                email_thread.start()

                messages.success(request, "OTP sent to your email.")
                return redirect(f"/otp-verification/?email={email}")
            except User.DoesNotExist:
                messages.error(request, "Email not found.")
        else:
            messages.error(request, "Email is required.")
    return render(request, "password_reset_request.html")


@csrf_exempt
def otp_verification(request):
    email = request.GET.get("email")
    if request.method == "POST":
        otp = request.POST.get("otp")
        new_password = request.POST.get("new_password")
        confirm_password = request.POST.get("confirm_password")
        if otp_storage.get(email) == otp:
            if new_password == confirm_password:
                user = User.objects.get(email=email)
                user.set_password(new_password)
                user.save()
                messages.success(request, "Password reset successful.")
                return redirect("/librarian/login/")
            else:
                messages.error(request, "Passwords do not match.")
        else:
            messages.error(request, "Invalid OTP.")
    return render(request, "otp_verification.html", {"email": email})


def user_wallet(user, note):
    try:
        # Fetch the user's wallet
        user_wallet = Wallet.objects.get(user=user)

        # Ensure that the balance will not go negative
        if user_wallet.balance >= 1:
            # Deduct 1 from the balance
            user_wallet.balance -= 1
            user_wallet.save()

            # Create a transaction record
            Transaction.objects.create(
                wallet=user_wallet, amount=1, is_debit=True, note=note
            )

            return {"status": "success", "message": "Transaction successful"}
        else:
            # If balance is insufficient, return a failure response
            return {"status": "failure", "message": "Insufficient balance"}

    except Wallet.DoesNotExist:
        # Handle the case where the wallet does not exist
        return {"status": "failure", "message": "Wallet does not exist"}

    except Exception as e:
        # Handle any other exceptions
        return {"status": "error", "message": f"An error occurred: {str(e)}"}
