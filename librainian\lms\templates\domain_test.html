<!DOCTYPE html>
<html>
<head>
    <title>Firebase Domain Test</title>
</head>
<body>
    <h1>Firebase Domain Whitelist Test</h1>
    <div id="status">Testing...</div>
    <div id="details"></div>

    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-messaging.js"></script>

    <script>
        const firebaseConfig = {
            apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
            authDomain: "librainian-app.firebaseapp.com",
            projectId: "librainian-app",
            storageBucket: "librainian-app.firebasestorage.app",
            messagingSenderId: "623132670328",
            appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
            measurementId: "G-XNDKJL6JWH"
        };

        const vapidKey = "BFm8KEWYXyt703OsjQ4338IbyV72W3m6nndMoZhzRV9SlSj0UHMv4INixoql0AJLWh6LJKC1CrP3r_M8YqsGrAY";

        function log(message) {
            document.getElementById('details').innerHTML += '<p>' + message + '</p>';
            console.log(message);
        }

        try {
            log('🌐 Current domain: ' + window.location.origin);
            log('🔧 Initializing Firebase...');
            
            const app = firebase.initializeApp(firebaseConfig);
            const messaging = firebase.messaging();
            
            log('✅ Firebase initialized successfully');
            log('📱 Project ID: ' + app.options.projectId);
            log('🔑 Auth Domain: ' + app.options.authDomain);
            
            // Test if we can get a token (this will fail if domain is not whitelisted)
            log('🎫 Testing token generation...');
            
            // First check if service worker can be registered
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/firebase-messaging-sw.js')
                    .then(function(registration) {
                        log('✅ Service Worker registered: ' + registration.scope);
                        messaging.useServiceWorker(registration);
                        
                        // Now try to get token
                        return messaging.getToken({ vapidKey: vapidKey });
                    })
                    .then((token) => {
                        if (token) {
                            log('✅ FCM Token generated successfully!');
                            log('🎫 Token: ' + token.substring(0, 50) + '...');
                            document.getElementById('status').innerHTML = '✅ Domain is whitelisted and working!';
                            document.getElementById('status').style.color = 'green';
                        } else {
                            log('❌ No token received - domain may not be whitelisted');
                            document.getElementById('status').innerHTML = '❌ Domain not whitelisted or other issue';
                            document.getElementById('status').style.color = 'red';
                        }
                    })
                    .catch((error) => {
                        log('❌ Token generation failed: ' + error.message);
                        
                        if (error.message.includes('auth/unauthorized-domain')) {
                            log('🚨 DOMAIN NOT WHITELISTED! Add ' + window.location.origin + ' to Firebase Auth settings');
                            document.getElementById('status').innerHTML = '🚨 Domain not whitelisted in Firebase!';
                        } else {
                            document.getElementById('status').innerHTML = '❌ Other error: ' + error.message;
                        }
                        document.getElementById('status').style.color = 'red';
                    });
            } else {
                log('❌ Service Worker not supported');
                document.getElementById('status').innerHTML = '❌ Service Worker not supported';
                document.getElementById('status').style.color = 'red';
            }
            
        } catch (error) {
            log('❌ Firebase initialization failed: ' + error.message);
            document.getElementById('status').innerHTML = '❌ Firebase initialization failed';
            document.getElementById('status').style.color = 'red';
        }
    </script>
</body>
</html>
