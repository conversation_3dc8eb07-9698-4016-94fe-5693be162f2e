<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>e <PERSON>minder - {{ library_name|default:'Librainian' }}</title>
    <script type="application/ld+json">
        {
        "@context": "http://schema.org",
        "@type": "EmailMessage",
        "potentialAction": {
            "@type": "ViewAction",
            "target": "https://librainian.com/student/payment/{{ student.unique_id }}",
            "name": "Pay Now"
        },
        "description": "Reminder to pay your library fee by {{ due_date|date:'F d, Y' }}."
        }
    </script>
    <style>
        :root {
            --primary: #3b82f6;
            --danger: #ef4444;
            --warning: #f59e0b;
            --success: #22c55e;
            --neutral: #6b7280;
            --bg-light: #f9fafb;
            --bg-white: #ffffff;
            --text-main: #1f2937;
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--bg-light);
            color: var(--text-main);
            padding: 30px;
            margin: 0;
            max-width: 640px;
            margin: auto;
        }

        .card {
            background-color: var(--bg-white);
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 30px;
        }

        h1, h2, h3 {
            margin: 0 0 10px;
        }

        .badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 999px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .badge-due_today { background-color: rgba(34,197,94,0.1); color: var(--success); }
        .badge-overdue { background-color: rgba(239,68,68,0.1); color: var(--danger); }
        .badge-long_overdue { background-color: rgba(75,85,99,0.1); color: var(--neutral); }
        .badge-default { background-color: rgba(59,130,246,0.1); color: var(--primary); }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        td {
            padding: 8px 0;
        }

        .footer {
            text-align: center;
            font-size: 12px;
            color: #9ca3af;
            margin-top: 40px;
        }

        .action {
            background-color: var(--bg-white);
            text-align: center;
            padding: 20px;
            border-radius: 12px;
            border: 2px dashed var(--primary);
        }

        .contact {
            font-size: 14px;
            line-height: 1.6;
            margin-top: 20px;
        }

        /* Make amount due font bigger and more prominent */
        td strong {
            font-size: 18px;
        }

        /* Specifically target the amount due row */
        tr:has(td:contains("Amount Due")) td:last-child strong,
        td:contains("₹") strong {
            font-size: 24px;
            color: var(--danger);
            background-color: rgba(239, 68, 68, 0.1);
            padding: 6px 12px;
            border-radius: 8px;
            border: 2px solid rgba(239, 68, 68, 0.2);
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="card">
        <div style="text-align:center;">
            <h1>
                {% if status == 'due_today' %}⏰ Fee Due Today{% elif status == 'overdue' %}⚠️ Overdue Notice{% elif status == 'long_overdue' %}🚨 Final Reminder{% else %}💳 Fee Reminder{% endif %}
            </h1>
            <span class="badge badge-{{ status|default:'default' }}">
                {% if status == 'due_today' %}Due Today{% elif status == 'overdue' %}Overdue{% elif status == 'long_overdue' %}Final Notice{% else %}Reminder{% endif %}
            </span>
            <p><strong>{{ library_name|default:'Librainian' }}</strong></p>
        </div>

        <h2 style="margin-top: 20px;">Hello {{ student.name }},</h2>

        {% if status == 'due_today' %}
        <p>Your library fee is <strong>due today</strong> ({{ due_date|date:"F d, Y" }}). Please ensure your payment is made today to avoid any service disruption.</p>
        {% elif status == 'overdue' %}
        <p>Your payment was due on <strong>{{ due_date|date:"F d, Y" }}</strong> and is now <strong>{{ days_overdue }} day{{ days_overdue|pluralize }} overdue</strong>. Kindly pay immediately to avoid suspension.</p>
        {% elif status == 'long_overdue' %}
        <p>This is a <strong>final reminder</strong>. Your payment due on <strong>{{ due_date|date:"F d, Y" }}</strong> is now <strong>{{ days_overdue }} days overdue</strong>. Please contact us without delay.</p>
        {% else %}
        <p>This is a friendly reminder: your fee is due on <strong>{{ due_date|date:"F d, Y" }}</strong>. ({{ days_until_due }} day{{ days_until_due|pluralize }} left). Kindly plan your payment.</p>
        {% endif %}

        <div class="action">
            {% if status in 'due_today overdue long_overdue' %}
                <h3 style="color: var(--danger);">Immediate Action Needed</h3>
                <p>Visit the library or contact us now to complete your payment.</p>
            {% else %}
                <h3 style="color: var(--primary);">Please Pay on Time</h3>
                <p>Ensure your dues are cleared before the due date to avoid interruptions.</p>
            {% endif %}
        </div>

        <div class="card">
            <h3>📋 Account Summary</h3>
            <table>
                <tr><td><strong>Student ID:</strong></td><td>{{ student.unique_id }}</td></tr>
                <tr><td><strong>Name:</strong></td><td>{{ student.name }}</td></tr>
                <tr><td><strong>Course:</strong></td><td>{{ student.course }}</td></tr>
                {% if due_date %}<tr><td><strong>Due Date:</strong></td><td>{{ due_date|date:"F d, Y" }}</td></tr>{% endif %}
                {% if total_amount %}<tr><td><strong>Amount Due:</strong></td><td><strong>₹{{ total_amount }}</strong></td></tr>{% endif %}
            </table>
        </div>

        <div class="card contact">
            <h3>📞 Contact Us</h3>
            <p><strong>Library:</strong> {{ library_name|default:'Librainian' }}</p>
            <p><strong>Email:</strong> <EMAIL></p>
        </div>

        <p style="margin-top: 30px;">Thank you for being a part of our library community. Your timely payment ensures we continue to serve you better.</p>

        <p>Warm regards,<br><strong>{{ library_name|default:'Librainian' }} Team</strong></p>

        <div class="footer">
            <p>This is an automated message. Please do not reply directly.<br>For support, contact your library staff.</p>
        </div>
    </div>
</body>
</html>