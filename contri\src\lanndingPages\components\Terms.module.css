/* General styling for the card container */
.cardContainer h2 {
  font-size: 1.6rem;
  color: #333;
  display: flex;
  align-items: center;
}

.cardContainer h2 svg {
  margin-right: 10px;
  color: #146c43;
}

.cardContainer p {
  margin: 1rem 0rem;
}

.termsCard {
  width: 100%;
  max-width: 900px;
  background-color: #fff;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto; /* Allows scrolling */
  max-height: 85vh; /* Ensures the card does not exceed 90vh */
  display: flex;
  flex-direction: column;
}

/* Custom Scrollbar */
.termsCard::-webkit-scrollbar {
  width: 12px; /* Adjust width of the scrollbar */
}

.termsCard::-webkit-scrollbar-thumb {
  background-color: #146c43; /* Scrollbar color */
  border-radius: 10px; /* Optional: rounds the scrollbar thumb */
}

.termsCard::-webkit-scrollbar-track {
  background-color: #f0f0f0; /* Track color */
  border-radius: 10px; /* Optional: rounds the track */
}

/* Header and content styling */
.termsHeader {
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  color: #146c43;
  margin-bottom: 20px;
}

.termsContent {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
}

.termsFooter {
  font-size: 1.2rem;
  text-align: center;
  margin-top: 40px;
  color: #777;
}

/* Responsive design */
@media (max-width: 768px) {
  .termsCard {
    padding: 15px;
  }

  .termsHeader {
    font-size: 2rem;
  }

  .cardContainer h2 {
    font-size: 1.4rem;
  }

  .cardContainer p {
    font-size: 1rem;
  }
}
