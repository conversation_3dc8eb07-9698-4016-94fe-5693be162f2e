from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from librarian.models import <PERSON><PERSON><PERSON>oken
from librarian.notification_service import notification_service
import random


class Command(BaseCommand):
    help = 'Test multi-device notification delivery'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user-id',
            type=int,
            help='User ID to test notifications for',
        )
        parser.add_argument(
            '--create-test-tokens',
            action='store_true',
            help='Create test device tokens for the user',
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up test tokens after testing',
        )

    def handle(self, *args, **options):
        user_id = options.get('user_id')
        
        if user_id:
            try:
                user = User.objects.get(id=user_id)
                self.stdout.write(f'Testing notifications for user: {user.username}')
            except User.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'User with ID {user_id} not found'))
                return
        else:
            # Get the first available user
            user = User.objects.filter(is_active=True).first()
            if not user:
                self.stdout.write(self.style.ERROR('No active users found'))
                return
            self.stdout.write(f'Using first available user: {user.username}')

        # Create test tokens if requested
        if options['create_test_tokens']:
            self.create_test_tokens(user)

        # Show current device tokens
        self.show_device_tokens(user)

        # Test notification delivery
        self.test_notification_delivery(user)

        # Cleanup if requested
        if options['cleanup']:
            self.cleanup_test_tokens(user)

    def create_test_tokens(self, user):
        """Create test device tokens for different device types"""
        self.stdout.write('Creating test device tokens...')
        
        test_tokens = [
            {
                'token': f'test_android_token_{random.randint(1000, 9999)}',
                'device_type': 'android',
                'device_name': 'Samsung Galaxy S21 (Android)',
                'user_agent': 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36'
            },
            {
                'token': f'test_ios_token_{random.randint(1000, 9999)}',
                'device_type': 'ios',
                'device_name': 'iPhone 13 Pro (iOS)',
                'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15'
            },
            {
                'token': f'test_web_desktop_token_{random.randint(1000, 9999)}',
                'device_type': 'web',
                'device_name': 'Chrome Desktop (Windows)',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            },
            {
                'token': f'test_web_mobile_token_{random.randint(1000, 9999)}',
                'device_type': 'web',
                'device_name': 'Chrome Mobile (Android)',
                'user_agent': 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36'
            }
        ]
        
        created_count = 0
        for token_data in test_tokens:
            device_token, created = DeviceToken.objects.get_or_create(
                user=user,
                token=token_data['token'],
                defaults={
                    'device_type': token_data['device_type'],
                    'device_name': token_data['device_name'],
                    'user_agent': token_data['user_agent'],
                    'is_active': True
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(f'  ✓ Created: {token_data["device_name"]}')
            else:
                self.stdout.write(f'  - Exists: {token_data["device_name"]}')
        
        self.stdout.write(f'Created {created_count} new test tokens')

    def show_device_tokens(self, user):
        """Display current device tokens for the user"""
        tokens = DeviceToken.objects.filter(user=user, is_active=True).order_by('-last_used')
        
        self.stdout.write(f'\nCurrent device tokens for {user.username}:')
        self.stdout.write(f'Total active devices: {tokens.count()}')
        
        for i, token in enumerate(tokens, 1):
            self.stdout.write(f'  {i}. {token.device_name or token.device_type.title()}')
            self.stdout.write(f'     Type: {token.device_type}')
            self.stdout.write(f'     Token: {token.token[:20]}...')
            self.stdout.write(f'     Last used: {token.last_used}')
            self.stdout.write('')

    def test_notification_delivery(self, user):
        """Test notification delivery to all devices"""
        self.stdout.write('Testing notification delivery...')
        
        # Test QR registration notification
        result = notification_service.send_notification(
            'qr_registration',
            user,
            student_name='Test Student (Multi-Device)',
            student_email='<EMAIL>',
            student_mobile='9876543210',
            course='Computer Science',
            library_name='Test Library',
            registration_date='2024-01-10',
            temp_student_id=999
        )
        
        if result:
            self.stdout.write(self.style.SUCCESS(f'✓ Notification sent successfully (ID: {result.id})'))
            
            # Show delivery details from the notification data
            if 'delivery_summary' in result.data:
                summary = result.data['delivery_summary']
                self.stdout.write(f'  Total devices: {summary["total_devices"]}')
                self.stdout.write(f'  Successful deliveries: {summary["successful_deliveries"]}')
                self.stdout.write(f'  Failed deliveries: {summary["failed_deliveries"]}')
                self.stdout.write(f'  Delivery time: {summary["delivery_timestamp"]}')
            
            self.stdout.write(f'  Status: {result.status}')
            self.stdout.write(f'  Title: {result.title}')
            self.stdout.write(f'  Body: {result.body[:100]}...')
            
        else:
            self.stdout.write(self.style.ERROR('✗ Failed to send notification'))

    def cleanup_test_tokens(self, user):
        """Remove test device tokens"""
        self.stdout.write('Cleaning up test tokens...')
        
        test_tokens = DeviceToken.objects.filter(
            user=user,
            token__startswith='test_'
        )
        
        count = test_tokens.count()
        test_tokens.delete()
        
        self.stdout.write(f'Removed {count} test tokens')

    def show_notification_stats(self, user):
        """Show notification statistics for the user"""
        from librarian.models import NotificationHistory
        
        notifications = NotificationHistory.objects.filter(recipient=user)
        
        self.stdout.write(f'\nNotification statistics for {user.username}:')
        self.stdout.write(f'  Total notifications: {notifications.count()}')
        self.stdout.write(f'  Successful: {notifications.filter(status="sent").count()}')
        self.stdout.write(f'  Failed: {notifications.filter(status="failed").count()}')
        self.stdout.write(f'  Pending: {notifications.filter(status="pending").count()}')
        
        # Recent notifications
        recent = notifications.order_by('-sent_at')[:5]
        if recent:
            self.stdout.write('\nRecent notifications:')
            for notif in recent:
                self.stdout.write(f'  - {notif.title} ({notif.status}) - {notif.sent_at}')
