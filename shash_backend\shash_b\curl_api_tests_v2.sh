#!/bin/bash

# Comprehensive curl testing script for Subscription API v2
# Run this script to test all endpoints with various scenarios

BASE_URL="http://127.0.0.1:8000"
API_BASE="${BASE_URL}/api/packages"

echo "🚀 Subscription API v2 - Comprehensive curl Testing"
echo "=================================================="
echo "Base URL: $BASE_URL"
echo "API Base: $API_BASE"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print test headers
print_test() {
    echo -e "${BLUE}🧪 $1${NC}"
    echo "$(printf '%.0s-' {1..50})"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to print info
print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# Test 1: Package Listing
print_test "Test 1: Package Listing API"
echo "GET $API_BASE/"
echo ""

response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$API_BASE/" \
  -H "Content-Type: application/json")

http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

if [ $http_code -eq 200 ]; then
    print_success "Package listing successful"
    echo "Response: $body" | jq '.[0:2]' 2>/dev/null || echo "$body"
else
    print_error "Package listing failed (HTTP $http_code)"
    echo "Response: $body"
fi
echo ""

# Test 2: Razorpay Configuration
print_test "Test 2: Razorpay Configuration API"
echo "GET $API_BASE/razorpay-config/"
echo ""

response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$API_BASE/razorpay-config/" \
  -H "Content-Type: application/json")

http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

if [ $http_code -eq 200 ]; then
    print_success "Razorpay config retrieved successfully"
    echo "Response: $body" | jq '.' 2>/dev/null || echo "$body"
else
    print_error "Razorpay config failed (HTTP $http_code)"
    echo "Response: $body"
fi
echo ""

# Test 3: Basic Subscription Creation
print_test "Test 3: Basic Subscription Creation (v2)"
echo "POST $API_BASE/v2/create-subscription/"
echo "Data: {\"student\": 11, \"package\": 1}"
echo ""

response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$API_BASE/v2/create-subscription/" \
  -H "Content-Type: application/json" \
  -d '{"student": 11, "package": 1}')

http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

if [ $http_code -eq 201 ]; then
    print_success "Basic subscription created successfully"
    echo "Response: $body" | jq '.' 2>/dev/null || echo "$body"
    
    # Extract subscription_id and razorpay_order_id for later tests
    SUBSCRIPTION_ID=$(echo $body | jq -r '.subscription_id' 2>/dev/null)
    RAZORPAY_ORDER_ID=$(echo $body | jq -r '.razorpay_order_id' 2>/dev/null)
    
    print_info "Subscription ID: $SUBSCRIPTION_ID"
    print_info "Razorpay Order ID: $RAZORPAY_ORDER_ID"
else
    print_error "Basic subscription creation failed (HTTP $http_code)"
    echo "Response: $body"
fi
echo ""

# Test 4: Subscription with Different Package
print_test "Test 4: Subscription with Different Package"
echo "POST $API_BASE/v2/create-subscription/"
echo "Data: {\"student\": 11, \"package\": 3}"
echo ""

response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$API_BASE/v2/create-subscription/" \
  -H "Content-Type: application/json" \
  -d '{"student": 11, "package": 3}')

http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

if [ $http_code -eq 201 ]; then
    print_success "Different package subscription created successfully"
    echo "Response: $body" | jq '.' 2>/dev/null || echo "$body"
else
    print_error "Different package subscription failed (HTTP $http_code)"
    echo "Response: $body"
fi
echo ""

# Test 5: Event Package Subscription
print_test "Test 5: Event Package Subscription"
echo "POST $API_BASE/v2/create-subscription/"
echo "Data: {\"student\": 11, \"package\": 4}"
echo ""

response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$API_BASE/v2/create-subscription/" \
  -H "Content-Type: application/json" \
  -d '{"student": 11, "package": 4}')

http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

if [ $http_code -eq 201 ]; then
    print_success "Event package subscription created successfully"
    echo "Response: $body" | jq '.' 2>/dev/null || echo "$body"
else
    print_error "Event package subscription failed (HTTP $http_code)"
    echo "Response: $body"
fi
echo ""

# Test 6: Subscription Status Check
if [ ! -z "$SUBSCRIPTION_ID" ] && [ "$SUBSCRIPTION_ID" != "null" ]; then
    print_test "Test 6: Subscription Status Check"
    echo "GET $API_BASE/v2/subscription-status/11/"
    echo ""

    response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$API_BASE/v2/subscription-status/11/" \
      -H "Content-Type: application/json")

    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

    if [ $http_code -eq 200 ]; then
        print_success "Subscription status retrieved successfully"
        echo "Response: $body" | jq '.' 2>/dev/null || echo "$body"
    else
        print_error "Subscription status check failed (HTTP $http_code)"
        echo "Response: $body"
    fi
    echo ""
fi

# Error Testing Scenarios
echo -e "${YELLOW}🚨 ERROR TESTING SCENARIOS${NC}"
echo "=================================================="

# Test 7: Invalid Student ID
print_test "Test 7: Invalid Student ID"
echo "POST $API_BASE/v2/create-subscription/"
echo "Data: {\"student\": 99999, \"package\": 1}"
echo ""

response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$API_BASE/v2/create-subscription/" \
  -H "Content-Type: application/json" \
  -d '{"student": 99999, "package": 1}')

http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

if [ $http_code -eq 404 ]; then
    print_success "Invalid student ID correctly rejected (HTTP $http_code)"
    echo "Response: $body" | jq '.' 2>/dev/null || echo "$body"
else
    print_error "Invalid student ID test failed - expected 404, got $http_code"
    echo "Response: $body"
fi
echo ""

# Test 8: Invalid Package ID
print_test "Test 8: Invalid Package ID"
echo "POST $API_BASE/v2/create-subscription/"
echo "Data: {\"student\": 11, \"package\": 99999}"
echo ""

response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$API_BASE/v2/create-subscription/" \
  -H "Content-Type: application/json" \
  -d '{"student": 11, "package": 99999}')

http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

if [ $http_code -eq 404 ]; then
    print_success "Invalid package ID correctly rejected (HTTP $http_code)"
    echo "Response: $body" | jq '.' 2>/dev/null || echo "$body"
else
    print_error "Invalid package ID test failed - expected 404, got $http_code"
    echo "Response: $body"
fi
echo ""

# Test 9: Missing Required Fields
print_test "Test 9: Missing Required Fields"
echo "POST $API_BASE/v2/create-subscription/"
echo "Data: {\"student\": 11}"
echo ""

response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$API_BASE/v2/create-subscription/" \
  -H "Content-Type: application/json" \
  -d '{"student": 11}')

http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

if [ $http_code -eq 400 ]; then
    print_success "Missing fields correctly rejected (HTTP $http_code)"
    echo "Response: $body" | jq '.' 2>/dev/null || echo "$body"
else
    print_error "Missing fields test failed - expected 400, got $http_code"
    echo "Response: $body"
fi
echo ""

# Summary
echo -e "${BLUE}📊 TESTING SUMMARY${NC}"
echo "=================================================="
print_info "All curl tests completed!"
print_info "Check the results above for any failures"
print_info "Monitor Django server logs for detailed debugging information"

if [ ! -z "$RAZORPAY_ORDER_ID" ] && [ "$RAZORPAY_ORDER_ID" != "null" ]; then
    echo ""
    print_info "🎯 Razorpay Dashboard Check:"
    print_info "Login to https://dashboard.razorpay.com/"
    print_info "Look for order: $RAZORPAY_ORDER_ID"
fi

echo ""
print_info "🔧 Next Steps:"
echo "1. Check Django server logs for detailed information"
echo "2. Verify orders on Razorpay dashboard"
echo "3. Check database for created subscriptions and invoices"
echo "4. Test payment verification with actual Razorpay responses"

echo ""
echo -e "${GREEN}✅ curl Testing Complete!${NC}"
