#!/usr/bin/env python3
"""
Check Dependencies and Setup for Membership Expiry Notifications
This script checks all required packages and sets up the system properly
"""

import os
import sys
import subprocess
import importlib
from pathlib import Path

def check_python_packages():
    """Check if all required Python packages are installed"""
    print("📦 CHECKING PYTHON PACKAGES")
    print("=" * 40)
    
    required_packages = {
        'django': 'Django web framework',
        'firebase_admin': 'Firebase Admin SDK for push notifications',
        'requests': 'HTTP library for API calls',
        'pytz': 'Timezone handling',
        'celery': 'Task queue (optional for background tasks)',
        'redis': 'Redis client (optional for Celery)',
    }
    
    missing_packages = []
    installed_packages = []
    
    for package, description in required_packages.items():
        try:
            importlib.import_module(package)
            print(f"✅ {package} - {description}")
            installed_packages.append(package)
        except ImportError:
            print(f"❌ {package} - {description} (MISSING)")
            missing_packages.append(package)
    
    print(f"\n📊 Summary: {len(installed_packages)}/{len(required_packages)} packages installed")
    
    if missing_packages:
        print(f"\n🔧 MISSING PACKAGES TO INSTALL:")
        for package in missing_packages:
            if package == 'firebase_admin':
                print(f"   pip install firebase-admin")
            elif package == 'celery':
                print(f"   pip install celery (optional)")
            elif package == 'redis':
                print(f"   pip install redis (optional)")
            else:
                print(f"   pip install {package}")
        
        print(f"\n🚀 INSTALL ALL MISSING PACKAGES:")
        install_cmd = "pip install " + " ".join([
            'firebase-admin' if pkg == 'firebase_admin' else pkg 
            for pkg in missing_packages if pkg not in ['celery', 'redis']
        ])
        print(f"   {install_cmd}")
        
        return False
    
    return True

def install_missing_packages():
    """Install missing packages automatically"""
    print("\n🔧 INSTALLING MISSING PACKAGES")
    print("=" * 40)
    
    packages_to_install = []
    
    # Check each package and add to install list if missing
    try:
        import firebase_admin
    except ImportError:
        packages_to_install.append('firebase-admin')
    
    try:
        import requests
    except ImportError:
        packages_to_install.append('requests')
    
    try:
        import pytz
    except ImportError:
        packages_to_install.append('pytz')
    
    if packages_to_install:
        print(f"Installing: {', '.join(packages_to_install)}")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + packages_to_install)
            
            print("✅ Packages installed successfully!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install packages: {e}")
            return False
    else:
        print("✅ All required packages are already installed!")
        return True

def check_django_setup():
    """Check Django setup and configuration"""
    print("\n🔧 CHECKING DJANGO SETUP")
    print("=" * 30)
    
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
        import django
        django.setup()
        
        print("✅ Django setup successful")
        
        # Check database connection
        from django.db import connection
        connection.ensure_connection()
        print("✅ Database connection working")
        
        # Check required models
        from membership.models import Membership
        from librarian.models import DeviceToken
        
        membership_count = Membership.objects.count()
        token_count = DeviceToken.objects.filter(is_active=True).count()
        
        print(f"✅ Found {membership_count} memberships")
        print(f"✅ Found {token_count} active FCM tokens")
        
        if token_count == 0:
            print("⚠️ No FCM tokens found - you need to register at least one!")
            print("   Visit: http://localhost:8000/fcm-test/")
        
        return True
        
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def test_notification_system():
    """Test the notification system"""
    print("\n🔔 TESTING NOTIFICATION SYSTEM")
    print("=" * 40)
    
    try:
        # Test manual command
        print("🧪 Testing management command...")
        result = subprocess.run([
            sys.executable, 'manage.py', 'check_membership_expiry', '--dry-run'
        ], capture_output=True, text=True, cwd=Path(__file__).parent)
        
        if result.returncode == 0:
            print("✅ Management command works!")
            print("📋 Output preview:")
            lines = result.stdout.split('\n')[:10]
            for line in lines:
                if line.strip():
                    print(f"   {line}")
        else:
            print("❌ Management command failed!")
            print(f"Error: {result.stderr}")
            return False
        
        # Test notification sending
        print("\n🔔 Testing notification sending...")
        from librarian.notification_utils import send_notification_to_all_users
        
        test_result = send_notification_to_all_users(
            title="🧪 Dependency Check Test",
            body="This is a test notification to verify all dependencies are working correctly.",
            data={
                "type": "dependency_test",
                "timestamp": "2025-07-22",
                "test": True
            }
        )
        
        if test_result and test_result.get('successful_count', 0) > 0:
            print("✅ Test notification sent successfully!")
            print(f"📊 Success: {test_result['successful_count']}, Failed: {test_result['failed_count']}")
            return True
        else:
            print("❌ Test notification failed!")
            print("💡 Make sure you have registered FCM tokens")
            return False
            
    except Exception as e:
        print(f"❌ Notification test failed: {e}")
        return False

def setup_windows_scheduler():
    """Set up Windows Task Scheduler since cron doesn't work on Windows"""
    print("\n🪟 SETTING UP WINDOWS TASK SCHEDULER")
    print("=" * 50)
    
    print("Since you're on Windows, cron jobs don't work. Let's use Task Scheduler instead.")
    
    project_path = Path(__file__).parent.absolute()
    python_path = sys.executable
    
    # Create batch file
    batch_content = f"""@echo off
REM Membership Expiry Notification Task
cd /d "{project_path}"
"{python_path}" manage.py check_membership_expiry >> membership_expiry.log 2>&1
echo Task completed at %date% %time% >> membership_expiry.log
"""
    
    batch_path = project_path / "run_membership_check.bat"
    with open(batch_path, 'w') as f:
        f.write(batch_content)
    
    print(f"✅ Created batch file: {batch_path}")
    
    # Try to create scheduled task
    try:
        task_command = [
            "schtasks", "/create", "/tn", "MembershipExpiryCheck",
            "/tr", str(batch_path), "/sc", "daily", "/st", "17:08", "/f"
        ]
        
        result = subprocess.run(task_command, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Windows Task Scheduler task created successfully!")
            print("⏰ Task will run daily at 5:08 PM")
            
            # Test the task
            print("\n🧪 Testing the scheduled task...")
            test_command = ["schtasks", "/run", "/tn", "MembershipExpiryCheck"]
            test_result = subprocess.run(test_command, capture_output=True, text=True)
            
            if test_result.returncode == 0:
                print("✅ Task test successful!")
                print("🔔 Check your browser for test notifications!")
                return True
            else:
                print("⚠️ Task created but test failed")
                print(f"Error: {test_result.stderr}")
                return False
        else:
            print("❌ Failed to create scheduled task")
            print(f"Error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error setting up Task Scheduler: {e}")
        return False

def show_manual_testing_options():
    """Show manual testing options"""
    print("\n🧪 MANUAL TESTING OPTIONS")
    print("=" * 35)
    
    print("1. 🌐 Django Admin Testing:")
    print("   - Go to: http://localhost:8000/admin/membership/membership/")
    print("   - Select memberships and use admin actions")
    print("   - Change expiry dates and save")
    print()
    
    print("2. 💻 Command Line Testing:")
    print("   - python manage.py check_membership_expiry --verbose")
    print("   - python manage.py check_membership_expiry --dry-run")
    print()
    
    print("3. 📄 Batch File Testing:")
    batch_path = Path(__file__).parent / "run_membership_check.bat"
    print(f"   - Double-click: {batch_path}")
    print("   - Check log file for results")
    print()
    
    print("4. 🪟 Task Scheduler Testing:")
    print("   - Open Task Scheduler (taskschd.msc)")
    print("   - Find 'MembershipExpiryCheck' task")
    print("   - Right-click → Run")
    print("   - Check for notifications")

def main():
    print("🔧 MEMBERSHIP EXPIRY NOTIFICATION SETUP & DEPENDENCY CHECK")
    print("=" * 80)
    print("Checking and fixing all issues with the notification system...")
    print()
    
    # Step 1: Check and install packages
    packages_ok = check_python_packages()
    if not packages_ok:
        print("\n🚀 Installing missing packages...")
        install_success = install_missing_packages()
        if not install_success:
            print("❌ Failed to install packages. Please install manually.")
            return 1
    
    # Step 2: Check Django setup
    django_ok = check_django_setup()
    if not django_ok:
        print("❌ Django setup issues found. Please fix before continuing.")
        return 1
    
    # Step 3: Test notification system
    notifications_ok = test_notification_system()
    
    # Step 4: Set up Windows scheduler (since cron doesn't work on Windows)
    scheduler_ok = setup_windows_scheduler()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 SETUP SUMMARY")
    print("=" * 80)
    
    print(f"📦 Python Packages: {'✅ OK' if packages_ok else '❌ ISSUES'}")
    print(f"🔧 Django Setup: {'✅ OK' if django_ok else '❌ ISSUES'}")
    print(f"🔔 Notifications: {'✅ OK' if notifications_ok else '❌ ISSUES'}")
    print(f"🪟 Task Scheduler: {'✅ OK' if scheduler_ok else '❌ ISSUES'}")
    
    if all([packages_ok, django_ok, notifications_ok, scheduler_ok]):
        print("\n🎉 SUCCESS! Everything is set up and working!")
        print("⏰ Your system will check for membership expiry daily at 5:08 PM")
        print("🔔 Push notifications will be sent to registered users")
    else:
        print("\n⚠️ Some issues found. Please review the output above.")
    
    show_manual_testing_options()
    
    print(f"\n📋 IMPORTANT NOTES:")
    print("• Cron jobs don't work on Windows - we're using Task Scheduler instead")
    print("• Make sure to register FCM tokens at: http://localhost:8000/fcm-test/")
    print("• Test manually first before relying on scheduled tasks")
    print("• Check log files for execution details")

if __name__ == "__main__":
    main()
