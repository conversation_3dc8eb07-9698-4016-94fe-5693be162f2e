#!/usr/bin/env python3
"""
Test Firebase Configuration
Run this script to test if Firebase is properly configured.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def test_firebase():
    """Test Firebase configuration"""
    print("🧪 Testing Firebase Configuration")
    print("=" * 50)
    
    try:
        from librarian.firebase_config import initialize_firebase, get_firebase_app
        from librarian.notification_utils import send_fcm_notification
        from django.contrib.auth.models import User
        from librarian.models import DeviceToken
        
        # Test 1: Firebase Initialization
        print("1️⃣ Testing Firebase Initialization...")
        firebase_initialized = initialize_firebase()
        if firebase_initialized:
            print("   ✅ Firebase initialized successfully")
        else:
            print("   ❌ Firebase initialization failed")
            return False
        
        # Test 2: Firebase App
        print("\n2️⃣ Testing Firebase App...")
        app = get_firebase_app()
        if app:
            print(f"   ✅ Firebase app available: {app.project_id}")
        else:
            print("   ❌ Firebase app not available")
            return False
        
        # Test 3: Device Tokens
        print("\n3️⃣ Checking Device Tokens...")
        token_count = DeviceToken.objects.count()
        print(f"   📱 Total device tokens: {token_count}")
        
        if token_count == 0:
            print("   ⚠️ No device tokens found. Register a device first:")
            print("      Visit: http://localhost:8000/fcm-test/")
            print("      Complete steps 1-6 to register a device token")
            return True
        
        # Test 4: User Check
        print("\n4️⃣ Checking Users...")
        user_count = User.objects.count()
        print(f"   👥 Total users: {user_count}")
        
        if user_count == 0:
            print("   ⚠️ No users found. Create a user first.")
            return True
        
        # Test 5: Send Test Notification
        print("\n5️⃣ Testing Notification Sending...")
        user = User.objects.first()
        user_tokens = DeviceToken.objects.filter(user=user).count()
        
        if user_tokens == 0:
            print(f"   ⚠️ User '{user.username}' has no device tokens")
            print("   💡 Login as this user and register a device token first")
            return True
        
        print(f"   📤 Sending test notification to user: {user.username}")
        print(f"   📱 User has {user_tokens} device token(s)")
        
        try:
            result = send_fcm_notification(
                user=user,
                title="🧪 Firebase Test",
                body="This is a test notification from the Firebase setup script!",
                data={"test": "true", "source": "setup_script"}
            )
            
            if result:
                print("   ✅ Test notification sent successfully!")
                print(f"   📊 Result: {result}")
                return True
            else:
                print("   ❌ Test notification failed")
                return False
                
        except Exception as e:
            print(f"   ❌ Error sending test notification: {e}")
            return False
    
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def show_status():
    """Show current status and next steps"""
    print("\n" + "=" * 50)
    print("📋 Next Steps:")
    print("\n✅ If Firebase is working:")
    print("   1. Visit: http://localhost:8000/fcm-test/")
    print("   2. Test notifications end-to-end")
    print("   3. Check browser console for FCM token")
    print("   4. Try background notification test")
    
    print("\n❌ If Firebase is not working:")
    print("   1. Run: python setup_firebase_dev.py")
    print("   2. Follow the Firebase setup instructions")
    print("   3. Restart Django server")
    print("   4. Run this test again")
    
    print("\n🔧 Troubleshooting:")
    print("   • Check server logs for 'Firebase initialized' message")
    print("   • Verify firebase-service-account.json exists")
    print("   • Check .env file has FIREBASE_PROJECT_ID")
    print("   • Ensure you have device tokens registered")

def main():
    success = test_firebase()
    show_status()
    
    if success:
        print("\n🎉 Firebase test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Firebase test failed. Check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
