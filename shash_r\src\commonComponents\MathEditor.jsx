import React, { useState, useCallback } from 'react';
import { Card, Form, Button, Row, Col, Alert } from 'react-bootstrap';
import MathInput from './MathInput';
import MathPreview from './MathPreview';
import './MathEditor.css';

const MathEditor = ({
  value = '',
  onChange,
  label = 'Mathematical Expression',
  placeholder = 'Enter mathematical expression...',
  showPreview = true,
  showRawLatex = false,
  displayMode = false,
  disabled = false,
  showSaveButton = false,
  onSave,
  className = '',
  // New props for embedded mode
  embeddedMode = false,
  textContent = '',
  onTextContentChange
}) => {
  const [latex, setLatex] = useState(value);
  const [showAlert, setShowAlert] = useState(false);

  const handleLatexChange = useCallback((newLatex) => {
    setLatex(newLatex);
    // In embedded mode, don't auto-update text content on every keystroke
    // Only update when "Insert Math" button is clicked
    if (!embeddedMode && onChange) {
      onChange(newLatex);
    }
  }, [onChange, embeddedMode]);

  const handleSave = useCallback(() => {
    // Simulate saving to backend
    console.log('Saving LaTeX to backend:', latex);
    
    if (onSave) {
      onSave(latex);
    }
    
    // Show success alert
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  }, [latex, onSave]);

  const handleClear = useCallback(() => {
    setLatex('');
    if (onChange) {
      onChange('');
    }
  }, [onChange]);

  const handleInsertMath = useCallback(() => {
    if (embeddedMode && onTextContentChange && latex) {
      const mathDelimiter = displayMode ? '$$' : '$';
      const newTextContent = textContent + ` ${mathDelimiter}${latex}${mathDelimiter}`;
      onTextContentChange(newTextContent);
      setLatex(''); // Clear after inserting
      setShowAlert(true);
      setTimeout(() => setShowAlert(false), 2000);
    }
  }, [embeddedMode, onTextContentChange, latex, textContent, displayMode]);

  return (
    <div className={`math-editor ${className}`}>
      <Card>
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <h6 className="mb-0">{label}</h6>
            <div>
              <Button
                variant="outline-secondary"
                size="sm"
                onClick={handleClear}
                disabled={disabled || !latex}
                className="me-2"
              >
                Clear
              </Button>
              {embeddedMode && (
                <Button
                  variant="success"
                  size="sm"
                  onClick={handleInsertMath}
                  disabled={disabled || !latex}
                  className="me-2"
                >
                  Insert Math
                </Button>
              )}
              {showSaveButton && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleSave}
                  disabled={disabled || !latex}
                >
                  Save
                </Button>
              )}
            </div>
          </div>
        </Card.Header>
        
        <Card.Body>
          {showAlert && (
            <Alert variant="success" className="mb-3">
              Mathematical expression saved successfully!
            </Alert>
          )}
          
          {/* Math Input */}
          <Form.Group className="mb-3">
            <Form.Label>Input</Form.Label>
            <MathInput
              value={latex}
              onChange={handleLatexChange}
              placeholder={placeholder}
              disabled={disabled}
              showVirtualKeyboard={true}
            />
            <Form.Text className="text-muted">
              Use the virtual keyboard or type LaTeX directly.
              Examples: x^2, \frac{1}{2}, \int_0^1 x dx
            </Form.Text>
          </Form.Group>

          {/* Math Preview */}
          {showPreview && (
            <Form.Group className="mb-3">
              <Form.Label>Preview</Form.Label>
              <Card className="bg-light" style={{ minHeight: '80px' }}>
                <Card.Body className="d-flex align-items-center justify-content-center">
                  <MathPreview
                    latex={latex}
                    displayMode={displayMode}
                    showRaw={showRawLatex}
                    placeholder="Math preview will appear here..."
                  />
                </Card.Body>
              </Card>
            </Form.Group>
          )}
          
          {/* Raw LaTeX Display */}
          {showRawLatex && latex && (
            <Form.Group className="mb-3">
              <Form.Label>Generated LaTeX</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                value={latex}
                readOnly
                className="font-monospace"
                style={{ fontSize: '0.875rem' }}
              />
            </Form.Group>
          )}
        </Card.Body>
      </Card>
    </div>
  );
};

export default MathEditor;
