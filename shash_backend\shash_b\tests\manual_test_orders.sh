#!/bin/bash

# Manual Test Script to Create Orders for Razorpay Dashboard
# Run this script to generate test transactions

echo "🚀 Creating Test Orders for Razorpay Dashboard"
echo "=============================================="

BASE_URL="http://127.0.0.1:8000/api/packages"

# Function to create a test order
create_test_order() {
    local student_id=$1
    local package_id=$2
    local description=$3
    
    echo ""
    echo "💳 Creating: $description"
    echo "   Student ID: $student_id, Package ID: $package_id"
    
    response=$(curl -s -X POST "$BASE_URL/v2/create-subscription/" \
        -H "Content-Type: application/json" \
        -d "{\"student\": $student_id, \"package\": $package_id}")
    
    echo "   Response: $response"
    
    # Extract order ID if successful
    order_id=$(echo "$response" | grep -o '"razorpay_order_id":"[^"]*"' | cut -d'"' -f4)
    if [ ! -z "$order_id" ]; then
        echo "   ✅ Order Created: $order_id"
        echo "$order_id" >> test_orders.txt
    else
        echo "   ❌ Failed to create order"
    fi
}

# Clear previous test orders file
> test_orders.txt

echo ""
echo "📊 Testing Razorpay Configuration..."
curl -s "$BASE_URL/razorpay-config/" | head -100

echo ""
echo ""
echo "🎯 Creating Test Orders..."

# Test different scenarios
create_test_order 11 1 "Test Order 1 - Updated Package Name"
sleep 2

create_test_order 11 3 "Test Order 2 - Graiden George Package"
sleep 2

create_test_order 11 4 "Test Order 3 - JEE Mock Test Competition"
sleep 2

create_test_order 11 5 "Test Order 4 - 6 Month Premium Access"
sleep 2

# Try with different student if available
create_test_order 12 1 "Test Order 5 - Different Student"
sleep 2

echo ""
echo "📈 Test Orders Summary"
echo "====================="

if [ -s test_orders.txt ]; then
    echo "✅ Orders created successfully:"
    cat -n test_orders.txt
    
    echo ""
    echo "🔗 Check these orders on your Razorpay Dashboard:"
    echo "   Dashboard: https://dashboard.razorpay.com/"
    echo "   Orders: https://dashboard.razorpay.com/app/orders"
    
    echo ""
    echo "💡 What to look for:"
    echo "   • Orders with 'created' status"
    echo "   • Correct amounts in paise"
    echo "   • Student and package details in notes"
    echo "   • Recent timestamps"
    
else
    echo "❌ No orders were created successfully"
    echo "   Check if Django server is running on port 8000"
    echo "   Verify student and package IDs exist"
fi

echo ""
echo "🧪 To test payments:"
echo "   Use Razorpay test cards:"
echo "   Card: 4111 1111 1111 1111"
echo "   CVV: Any 3 digits"
echo "   Expiry: Any future date"
