#!/usr/bin/env python3
"""
Quick Firebase Fix
This script provides immediate solutions for the Firebase credentials error.
"""

import os
import json

def remove_dummy_file():
    """Remove the dummy service account file"""
    dummy_file = "firebase-service-account.json"
    if os.path.exists(dummy_file):
        try:
            with open(dummy_file, 'r') as f:
                data = json.load(f)
                if data.get('private_key_id') == 'dev-key-id-12345':
                    os.remove(dummy_file)
                    print(f"✅ Removed dummy file: {dummy_file}")
                    return True
        except:
            pass
    return False

def show_immediate_solution():
    """Show the immediate solution"""
    print("🚨 IMMEDIATE FIREBASE FIX")
    print("="*50)
    
    print("\n❌ Current Error:")
    print("   'Your default credentials were not found'")
    print("   'Error sending notification batch'")
    
    print("\n✅ Quick Solution (2 minutes):")
    print("\n1️⃣ **Get Real Firebase Credentials**")
    print("   🌐 Visit: https://console.firebase.google.com/project/librainian-app/settings/serviceaccounts/adminsdk")
    print("   🔑 Click 'Generate new private key'")
    print("   💾 Download the JSON file")
    
    print("\n2️⃣ **Install Credentials**")
    print("   📁 Move downloaded file to your project folder")
    print("   ✏️  Rename it to: firebase-service-account.json")
    print("   📍 Place it next to manage.py")
    
    print("\n3️⃣ **Restart & Test**")
    print("   🔄 Restart Django server")
    print("   🧪 Test: http://localhost:8000/fcm-test/")
    
    print("\n" + "="*50)
    print("⏱️  This should take less than 2 minutes!")
    print("🎯 After this, real FCM notifications will work!")

def show_alternative_solution():
    """Show alternative solution using environment variable"""
    print("\n🔄 ALTERNATIVE SOLUTION")
    print("="*50)
    
    print("\nIf you can't download the file, use environment variable:")
    print("\n1️⃣ **Get JSON Content**")
    print("   🌐 Visit Firebase Console (same link as above)")
    print("   🔑 Generate and download the key")
    print("   📄 Open the JSON file in a text editor")
    print("   📋 Copy the entire JSON content")
    
    print("\n2️⃣ **Create .env File**")
    print("   📝 Create/edit .env file in project root")
    print("   ➕ Add this line:")
    print("   FIREBASE_SERVICE_ACCOUNT_JSON='{\"type\":\"service_account\",...}'")
    print("   ⚠️  Important: Put the entire JSON on one line, no line breaks!")
    
    print("\n3️⃣ **Restart & Test**")
    print("   🔄 Restart Django server")
    print("   🧪 Test notifications")

def check_and_guide():
    """Check current status and provide guidance"""
    print("🔍 Checking current Firebase setup...")
    
    # Remove dummy file if it exists
    removed_dummy = remove_dummy_file()
    if removed_dummy:
        print("🗑️  Removed dummy credentials file")
    
    # Check for real service account file
    service_account_file = "firebase-service-account.json"
    has_real_file = False
    
    if os.path.exists(service_account_file):
        try:
            with open(service_account_file, 'r') as f:
                data = json.load(f)
                if data.get('private_key_id') != 'dev-key-id-12345':
                    has_real_file = True
                    print(f"✅ Real service account file found!")
                    print(f"   Project: {data.get('project_id')}")
                    print(f"   Email: {data.get('client_email')}")
        except:
            pass
    
    # Check environment variable
    env_var = os.getenv('FIREBASE_SERVICE_ACCOUNT_JSON')
    has_env_var = bool(env_var and len(env_var) > 100)
    
    if has_env_var:
        print("✅ Firebase environment variable found!")
    
    # Provide guidance based on current state
    if has_real_file or has_env_var:
        print("\n🎉 Firebase credentials are set up!")
        print("🔄 If you're still getting errors:")
        print("   1. Restart your Django server")
        print("   2. Check server logs for 'Firebase initialized'")
        print("   3. Test: http://localhost:8000/fcm-test/")
    else:
        print("\n❌ No real Firebase credentials found")
        show_immediate_solution()
        show_alternative_solution()

def main():
    print("🚀 Firebase Quick Fix Tool")
    print("Solving: 'Your default credentials were not found' error")
    print()
    
    check_and_guide()
    
    print("\n" + "="*50)
    print("💡 Need help? Run: python get_firebase_credentials.py")
    print("🧪 Test Firebase: python test_firebase.py")
    print("🌐 Test notifications: http://localhost:8000/fcm-test/")

if __name__ == "__main__":
    main()
