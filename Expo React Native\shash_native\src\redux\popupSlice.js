import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import Constants from 'expo-constants';

// Get baseURL from Expo constants
const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;

// Get popup banners
export const getPopUps = createAsyncThunk(
  'popup/getPopUps',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${baseURL}api/popup-banners/`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch popup banners');
    }
  }
);

const popupSlice = createSlice({
  name: 'popup',
  initialState: {
    hasSeenWelcomePopup: false,
    popups: [],
    currentPopup: null,
    isLoading: false,
    error: null,
  },
  reducers: {
    setHasSeenWelcomePopup: (state, action) => {
      state.hasSeenWelcomePopup = action.payload;
    },
    resetPopupState: (state) => {
      state.hasSeenWelcomePopup = false;
    },
    setCurrentPopup: (state, action) => {
      state.currentPopup = action.payload;
    },
    clearCurrentPopup: (state) => {
      state.currentPopup = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // getPopUps
      .addCase(getPopUps.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getPopUps.fulfilled, (state, action) => {
        state.isLoading = false;
        state.popups = action.payload;
      })
      .addCase(getPopUps.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const {
  setHasSeenWelcomePopup,
  resetPopupState,
  setCurrentPopup,
  clearCurrentPopup,
  clearError
} = popupSlice.actions;
export default popupSlice.reducer;
