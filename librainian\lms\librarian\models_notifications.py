"""
Additional models for comprehensive notification system
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from .notification_templates import EVENT_TYPE_CHOICES, PRIORITY_CHOICES, CATEGORY_CHOICES


class CustomNotification(models.Model):
    """Model for custom notifications sent from Django admin"""
    
    title = models.CharField(max_length=200, help_text="Notification title")
    message = models.TextField(help_text="Notification message content")
    priority = models.CharField(
        max_length=20, 
        choices=PRIORITY_CHOICES, 
        default='normal',
        help_text="Notification priority level"
    )
    
    # Recipients
    send_to_all_librarians = models.BooleanField(
        default=True, 
        help_text="Send to all librarians"
    )
    send_to_all_sublibrarians = models.BooleanField(
        default=True, 
        help_text="Send to all sublibrarians"
    )
    specific_recipients = models.ManyToManyField(
        User, 
        blank=True,
        help_text="Specific users to send notification to"
    )
    
    # Scheduling
    send_immediately = models.BooleanField(
        default=True,
        help_text="Send notification immediately"
    )
    scheduled_time = models.DateTimeField(
        null=True, 
        blank=True,
        help_text="Schedule notification for later (leave blank for immediate)"
    )
    
    # Status
    is_sent = models.BooleanField(default=False)
    sent_at = models.DateTimeField(null=True, blank=True)
    sent_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True,
        related_name='sent_notifications'
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = "Custom Notification"
        verbose_name_plural = "Custom Notifications"
    
    def __str__(self):
        return f"{self.title} - {self.get_priority_display()}"
    
    def get_recipients(self):
        """Get all recipients for this notification"""
        recipients = []
        
        if self.send_to_all_librarians:
            librarians = User.objects.filter(
                groups__name='Librarian',
                is_active=True
            )
            recipients.extend(librarians)
        
        if self.send_to_all_sublibrarians:
            sublibrarians = User.objects.filter(
                groups__name='SubLibrarian',
                is_active=True
            )
            recipients.extend(sublibrarians)
        
        # Add specific recipients
        recipients.extend(self.specific_recipients.all())
        
        # Remove duplicates
        return list(set(recipients))
    
    def send_notification(self, sent_by_user=None):
        """Send this custom notification"""
        from .notification_events import notification_events
        
        if self.is_sent:
            return False, "Notification already sent"
        
        try:
            recipients = self.get_recipients()
            
            if not recipients:
                return False, "No recipients found"
            
            success = notification_events.send_custom_notification(
                title=self.title,
                message=self.message,
                recipients=recipients,
                priority=self.priority
            )
            
            if success:
                self.is_sent = True
                self.sent_at = timezone.now()
                self.sent_by = sent_by_user
                self.save()
                
                return True, f"Notification sent to {len(recipients)} recipients"
            else:
                return False, "Failed to send notification"
                
        except Exception as e:
            return False, f"Error sending notification: {str(e)}"


class NotificationSchedule(models.Model):
    """Model for scheduled recurring notifications"""
    
    FREQUENCY_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('custom', 'Custom Cron'),
    ]
    
    name = models.CharField(max_length=200, help_text="Schedule name")
    event_type = models.CharField(
        max_length=50,
        choices=EVENT_TYPE_CHOICES,
        help_text="Type of notification event"
    )
    frequency = models.CharField(
        max_length=20,
        choices=FREQUENCY_CHOICES,
        default='daily'
    )
    cron_expression = models.CharField(
        max_length=100,
        blank=True,
        help_text="Custom cron expression (for custom frequency)"
    )
    
    # Time settings
    time_of_day = models.TimeField(
        default='09:00',
        help_text="Time to send notification"
    )
    
    # Status
    is_active = models.BooleanField(default=True)
    last_run = models.DateTimeField(null=True, blank=True)
    next_run = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True
    )
    
    class Meta:
        ordering = ['name']
        verbose_name = "Notification Schedule"
        verbose_name_plural = "Notification Schedules"
    
    def __str__(self):
        return f"{self.name} - {self.get_frequency_display()}"


class NotificationLog(models.Model):
    """Log of all notification events for analytics"""
    
    event_type = models.CharField(max_length=50)
    recipient_count = models.IntegerField(default=0)
    successful_deliveries = models.IntegerField(default=0)
    failed_deliveries = models.IntegerField(default=0)
    
    # Event details
    event_data = models.JSONField(default=dict, blank=True)
    error_details = models.TextField(blank=True)
    
    # Timing
    triggered_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    duration_seconds = models.FloatField(null=True, blank=True)
    
    # Source
    triggered_by = models.CharField(
        max_length=100,
        default='system',
        help_text="What triggered this notification (system, admin, user, etc.)"
    )
    
    class Meta:
        ordering = ['-triggered_at']
        verbose_name = "Notification Log"
        verbose_name_plural = "Notification Logs"
    
    def __str__(self):
        return f"{self.event_type} - {self.triggered_at.strftime('%Y-%m-%d %H:%M')}"


class NotificationPreference(models.Model):
    """User preferences for notifications"""
    
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='notification_preferences'
    )
    
    # General preferences
    email_notifications = models.BooleanField(default=True)
    push_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)
    
    # Event-specific preferences
    qr_registration_notifications = models.BooleanField(default=True)
    visitor_notifications = models.BooleanField(default=True)
    financial_notifications = models.BooleanField(default=True)
    member_expiry_notifications = models.BooleanField(default=True)
    sales_milestone_notifications = models.BooleanField(default=True)
    galla_reminder_notifications = models.BooleanField(default=True)
    custom_admin_notifications = models.BooleanField(default=True)
    
    # Timing preferences
    quiet_hours_start = models.TimeField(
        default='22:00',
        help_text="Start of quiet hours (no notifications)"
    )
    quiet_hours_end = models.TimeField(
        default='08:00',
        help_text="End of quiet hours"
    )
    weekend_notifications = models.BooleanField(
        default=False,
        help_text="Receive notifications on weekends"
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Notification Preference"
        verbose_name_plural = "Notification Preferences"
    
    def __str__(self):
        return f"{self.user.username} - Notification Preferences"
    
    def should_receive_notification(self, event_type, current_time=None):
        """Check if user should receive notification based on preferences"""
        if current_time is None:
            current_time = timezone.now()
        
        # Check if push notifications are enabled
        if not self.push_notifications:
            return False
        
        # Check quiet hours
        current_time_only = current_time.time()
        if self.quiet_hours_start <= self.quiet_hours_end:
            # Same day quiet hours
            if self.quiet_hours_start <= current_time_only <= self.quiet_hours_end:
                return False
        else:
            # Overnight quiet hours
            if current_time_only >= self.quiet_hours_start or current_time_only <= self.quiet_hours_end:
                return False
        
        # Check weekend notifications
        if not self.weekend_notifications and current_time.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return False
        
        # Check event-specific preferences
        event_preference_map = {
            'qr_registration': self.qr_registration_notifications,
            'visitor_callback_due': self.visitor_notifications,
            'visitor_added': self.visitor_notifications,
            'invoice_created': self.financial_notifications,
            'payment_received': self.financial_notifications,
            'sales_milestone_reached': self.sales_milestone_notifications,
            'monthly_sales_summary': self.sales_milestone_notifications,
            'member_expiry_10_days': self.member_expiry_notifications,
            'member_expiry_5_days': self.member_expiry_notifications,
            'member_expiry_1_day': self.member_expiry_notifications,
            'member_expired': self.member_expiry_notifications,
            'daily_galla_reminder': self.galla_reminder_notifications,
            'custom_admin_notification': self.custom_admin_notifications,
        }
        
        return event_preference_map.get(event_type, True)
