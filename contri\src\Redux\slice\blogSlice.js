import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor; // Adjust this path to match your state structure
  return accessToken;
};

// Thunks

// Create a new blog
// In your createBlog async thunk
export const createBlog = createAsyncThunk(
  'blog/createBlog',
  async (blogData, { getState, rejectWithValue }) => {
    try {
      // Accessing the state to get the token
      const token = getState().contributor.accessToken; // Access token from the correct state path

      const formData = new FormData();

      // Append all fields from blogData to FormData
      Object.keys(blogData).forEach((key) => {
        if (key === 'image' && blogData[key]) {
          formData.append(key, blogData[key]); // Append image as a file
        } else {
          formData.append(key, blogData[key]); // Append other data as text
        }
      });

      // Send the POST request with FormData
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_BLOGS}`, // API endpoint for creating a blog
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data',  // Important for file uploads
          },
        }
      );

      return response.data; // Return the data from the API response
    } catch (error) {
      console.error("Error creating blog:", error);
      return rejectWithValue(error.response?.data || error.message || 'Error creating blog');
    }
  }
);

// Fetch all blogs
export const getAllBlogs = createAsyncThunk(
  'blog/getAllBlogs',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getState().contributor.accessToken; // Retrieve token from Redux state

      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_BLOGS}`, // API endpoint for fetching blogs
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching blogs');
    }
  }
);


// Fetch a specific blog by slug
export const getBlog = createAsyncThunk(
  'blog/getBlog',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getState().contributor.accessToken; // Retrieve token from Redux state

      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_BLOGS}${slug}/`, // API endpoint with the blog slug
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching blog');
    }
  }
);

// Update a specific blog by slug
export const updateBlog = createAsyncThunk(
  'blog/updateBlog',
  async ({ slug, updatedData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);

      const formData = new FormData();

      // Append the updated fields to FormData
      Object.keys(updatedData).forEach((key) => {
        if (key === 'image' && updatedData[key]) {
          formData.append(key, updatedData[key]); // Append image as a file if present
        } else {
          formData.append(key, updatedData[key]); // Append other data as text
        }
      });

      // Send the PUT request with FormData
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_BLOGS}${slug}/`, // API endpoint with the blog slug
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data', // Important for file uploads
          },
        }
      );

      return response.data; // Return the updated blog data
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error updating blog');
    }
  }
);

// Delete a specific blog by slug
export const deleteBlog = createAsyncThunk(
  'blog/deleteBlog',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_BLOGS}${slug}/`, // API endpoint with the blog slug
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return slug;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error deleting blog');
    }
  }
);

// Slice
const blogSlice = createSlice({
  name: 'blog',
  initialState: {
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Handle createBlog actions
      .addCase(createBlog.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createBlog.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(createBlog.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle getAllBlogs actions
      .addCase(getAllBlogs.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getAllBlogs.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(getAllBlogs.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle getBlog actions
      .addCase(getBlog.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getBlog.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(getBlog.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle updateBlog actions
      .addCase(updateBlog.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateBlog.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(updateBlog.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle deleteBlog actions
      .addCase(deleteBlog.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteBlog.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(deleteBlog.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export default blogSlice.reducer;
