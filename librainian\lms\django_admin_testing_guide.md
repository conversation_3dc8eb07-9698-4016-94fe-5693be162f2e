# 🧪 Django Admin Testing Guide for Membership Expiry Notifications

## ✅ Server Status: FIXED AND RUNNING!

The Django server is now running without errors at: http://localhost:8000

## 🚀 How to Test Membership Expiry Notifications from Django Admin

### **Method 1: Automatic Signals (Change Expiry Date)**

1. **Go to Membership Admin:**
   - Visit: http://localhost:8000/admin/membership/membership/
   - Login with your admin credentials

2. **Select a Membership:**
   - Click on any membership from the list
   - You'll see the membership details form

3. **Change Expiry Date:**
   - Change the "Expiry date" field to one of these test dates:
     - **Today (2025-07-22)**: Will trigger "expires today" notification
     - **Tomorrow (2025-07-23)**: Will trigger "expires tomorrow" notification
     - **5 days from now**: Will trigger "expires in 5 days" notification
     - **10 days from now**: Will trigger "expires in 10 days" notification

4. **Save and Get Notification:**
   - Click "Save"
   - 🎉 **Notification will be sent immediately!**
   - Check your browser for the notification popup

### **Method 2: Admin Actions (Bulk Testing)**

1. **Go to Membership List:**
   - Visit: http://localhost:8000/admin/membership/membership/
   - You'll see a list of all memberships with the new "Expiry Status" column

2. **Select Memberships:**
   - Check the boxes next to one or more memberships
   - The checkboxes are on the left side of each row

3. **Choose Action:**
   - From the "Action" dropdown at the top, select one of:
     - **🧪 Test Expiry Notifications**: Send test notifications for selected memberships
     - **📅 Set Expiry to Today (Test)**: Change expiry to today and trigger notifications
     - **📅 Set Expiry to Tomorrow (Test)**: Change expiry to tomorrow and trigger notifications

4. **Execute Action:**
   - Click "Go" button
   - 🎉 **Notifications will be sent immediately!**
   - You'll see a success message in the admin

### **Method 3: Manual Command (Terminal)**

If you want to test via command line:

```bash
# Test all expiry periods
python manage.py check_membership_expiry --verbose

# Test specific period (e.g., today)
python manage.py check_membership_expiry --days 0

# Dry run (see what would happen without sending)
python manage.py check_membership_expiry --dry-run --verbose
```

## 📊 What You'll See in the Admin

### **New Features Added:**

1. **Expiry Status Column:**
   - Shows days until expiry with color coding:
   - `15 days` (normal)
   - `⚠️ 5 days` (warning)
   - `❌ Today` (urgent)
   - `🚨 3 days ago` (overdue)

2. **Admin Actions:**
   - Three new actions in the dropdown for testing
   - Bulk operations on multiple memberships

3. **Save Messages:**
   - Informative messages when saving memberships
   - Tells you if notifications will be triggered

## 🔔 Notification Types You'll Receive

Based on the expiry date you set, you'll get different notifications:

### **10 Days Before Expiry:**
```
⏰ Membership Expires in 10 Days
Member: [Name]
Plan: [Plan Name]
Expires: [Date]
Library: [Library Name]
Action: Prepare renewal process
```

### **5 Days Before Expiry:**
```
⚠️ Membership Expires in 5 Days
Member: [Name]
Plan: [Plan Name]
Expires: [Date]
Library: [Library Name]
Action: Contact member for renewal
```

### **1 Day Before Expiry:**
```
🔔 Membership Expires Tomorrow
Member: [Name]
Plan: [Plan Name]
Expires: [Date]
Library: [Library Name]
Action: Urgent renewal required
```

### **Expires Today:**
```
❌ Membership Expired Today
Member: [Name]
Plan: [Plan Name]
Expired: [Date]
Library: [Library Name]
Action: Immediate renewal required
```

### **4 Days After Expiry:**
```
🚨 Member Expired - 4 Days Overdue
Member: [Name]
Plan: [Plan Name]
Expired: [Date]
Library: [Library Name]
Action: Contact member immediately
```

## 🧪 Step-by-Step Testing Process

### **Quick Test (Recommended):**

1. **Open Admin:** http://localhost:8000/admin/membership/membership/
2. **Select any membership** (check the box)
3. **Choose action:** "📅 Set Expiry to Today (Test)"
4. **Click "Go"**
5. **Check browser for notification!** 🎉

### **Detailed Test:**

1. **Test Today's Expiry:**
   - Select a membership
   - Use "Set Expiry to Today" action
   - Should get "❌ Membership Expired Today" notification

2. **Test Tomorrow's Expiry:**
   - Select a membership
   - Use "Set Expiry to Tomorrow" action
   - Should get "🔔 Membership Expires Tomorrow" notification

3. **Test Custom Date:**
   - Click on a membership to edit
   - Set expiry date to 5 days from now
   - Save
   - Should get "⚠️ Membership Expires in 5 Days" notification

## 🔧 Troubleshooting

### **If No Notifications Appear:**

1. **Check FCM Token:**
   - Visit: http://localhost:8000/fcm-test/
   - Complete the FCM token registration
   - Try testing again

2. **Check Browser Permissions:**
   - Ensure notifications are allowed in browser
   - Check if browser tab is active (foreground notifications)

3. **Check Console:**
   - Open browser developer tools (F12)
   - Look for any JavaScript errors
   - Check the console for notification logs

### **If Admin Actions Don't Work:**

1. **Check Admin Messages:**
   - Look for success/error messages at the top of the admin page
   - Messages will tell you if notifications were sent

2. **Check Server Logs:**
   - Look at the terminal where Django server is running
   - Check for any error messages

## ⏰ Production Cron Job Setup

For daily automatic checks at 11:11 AM, add this to your crontab:

```bash
# Open crontab
crontab -e

# Add this line
11 11 * * * cd /path/to/your/project && python manage.py check_membership_expiry >> /var/log/membership_expiry.log 2>&1
```

## 🎯 Summary

✅ **Django server is running without errors**
✅ **Admin interface has testing features**
✅ **Automatic signals work when saving**
✅ **Admin actions work for bulk testing**
✅ **All 5 notification periods are working**
✅ **Cron job setup is ready**

**The membership expiry notification system is complete and ready for testing!** 🚀
