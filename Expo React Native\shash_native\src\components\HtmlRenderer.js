import React, { useContext } from 'react';
import { useWindowDimensions } from 'react-native';
import RenderHtml from 'react-native-render-html';
import { ThemeContext } from '../context/ThemeContext';

const HtmlRenderer = ({ 
  html, 
  style = {}, 
  fontSize = 16,
  lineHeight = 1.5,
  textAlign = 'left',
  ...props 
}) => {
  const { width } = useWindowDimensions();
  const { isDarkMode } = useContext(ThemeContext);

  // Default styles for HTML tags
  const tagsStyles = {
    body: {
      fontSize: fontSize,
      lineHeight: fontSize * lineHeight,
      color: isDarkMode ? '#e0e0e0' : '#333',
      textAlign: textAlign,
      ...style,
    },
    p: {
      fontSize: fontSize,
      lineHeight: fontSize * lineHeight,
      color: isDarkMode ? '#e0e0e0' : '#333',
      marginVertical: 4,
    },
    span: {
      fontSize: fontSize,
      lineHeight: fontSize * lineHeight,
      color: isDarkMode ? '#e0e0e0' : '#333',
    },
    strong: {
      fontWeight: 'bold',
      color: isDarkMode ? '#fff' : '#000',
    },
    b: {
      fontWeight: 'bold',
      color: isDarkMode ? '#fff' : '#000',
    },
    em: {
      fontStyle: 'italic',
    },
    i: {
      fontStyle: 'italic',
    },
    u: {
      textDecorationLine: 'underline',
    },
    h1: {
      fontSize: fontSize * 1.8,
      fontWeight: 'bold',
      color: isDarkMode ? '#fff' : '#000',
      marginVertical: 8,
    },
    h2: {
      fontSize: fontSize * 1.6,
      fontWeight: 'bold',
      color: isDarkMode ? '#fff' : '#000',
      marginVertical: 6,
    },
    h3: {
      fontSize: fontSize * 1.4,
      fontWeight: 'bold',
      color: isDarkMode ? '#fff' : '#000',
      marginVertical: 4,
    },
    h4: {
      fontSize: fontSize * 1.2,
      fontWeight: 'bold',
      color: isDarkMode ? '#fff' : '#000',
      marginVertical: 4,
    },
    h5: {
      fontSize: fontSize * 1.1,
      fontWeight: 'bold',
      color: isDarkMode ? '#fff' : '#000',
      marginVertical: 2,
    },
    h6: {
      fontSize: fontSize,
      fontWeight: 'bold',
      color: isDarkMode ? '#fff' : '#000',
      marginVertical: 2,
    },
    ul: {
      marginVertical: 4,
    },
    ol: {
      marginVertical: 4,
    },
    li: {
      fontSize: fontSize,
      color: isDarkMode ? '#e0e0e0' : '#333',
      marginVertical: 2,
    },
    a: {
      color: isDarkMode ? '#4a90e2' : '#007bff',
      textDecorationLine: 'underline',
    },
    blockquote: {
      borderLeftWidth: 4,
      borderLeftColor: isDarkMode ? '#666' : '#ccc',
      paddingLeft: 12,
      marginVertical: 8,
      fontStyle: 'italic',
    },
    code: {
      backgroundColor: isDarkMode ? '#2a2a2a' : '#f8f9fa',
      color: isDarkMode ? '#e83e8c' : '#e83e8c',
      paddingHorizontal: 4,
      paddingVertical: 2,
      borderRadius: 4,
      fontFamily: 'monospace',
    },
    pre: {
      backgroundColor: isDarkMode ? '#2a2a2a' : '#f8f9fa',
      padding: 12,
      borderRadius: 8,
      marginVertical: 8,
    },
    table: {
      borderWidth: 1,
      borderColor: isDarkMode ? '#444' : '#dee2e6',
      marginVertical: 8,
    },
    th: {
      backgroundColor: isDarkMode ? '#333' : '#f8f9fa',
      padding: 8,
      fontWeight: 'bold',
      borderWidth: 1,
      borderColor: isDarkMode ? '#444' : '#dee2e6',
    },
    td: {
      padding: 8,
      borderWidth: 1,
      borderColor: isDarkMode ? '#444' : '#dee2e6',
    },
  };

  // Custom renderers for specific elements
  const renderersProps = {
    img: {
      enableExperimentalPercentWidth: true,
    },
  };

  // Clean up the HTML content
  const cleanHtml = html || '';

  // If no HTML content, return null
  if (!cleanHtml.trim()) {
    return null;
  }

  return (
    <RenderHtml
      contentWidth={width}
      source={{ html: cleanHtml }}
      tagsStyles={tagsStyles}
      renderersProps={renderersProps}
      defaultTextProps={{
        selectable: false,
      }}
      {...props}
    />
  );
};

export default HtmlRenderer;
