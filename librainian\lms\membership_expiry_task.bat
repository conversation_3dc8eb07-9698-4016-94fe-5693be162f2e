@echo off
REM Membership Expiry Notification Task
REM Runs daily at 4:51 PM

REM Set environment variables
set DJANGO_SETTINGS_MODULE=Library.settings

REM Change to project directory
cd /d "F:\Pinak Venture\librainian\lms"

REM Run the membership expiry check
"C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" manage.py check_membership_expiry >> membership_expiry.log 2>&1

REM Log the execution
echo Membership expiry check completed at %date% %time% >> membership_expiry.log
