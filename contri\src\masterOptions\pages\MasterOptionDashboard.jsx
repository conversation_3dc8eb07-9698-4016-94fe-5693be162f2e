import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, Link } from 'react-router-dom';
import { toast, Toaster } from 'react-hot-toast';
import { <PERSON><PERSON>, Row, Col, Container } from 'react-bootstrap';
import NavigationBar from "../../commonComponents/NavigationBar";
import CreateMasterOptionForm from '../components/CreateMasterOptionForm';
import ViewMasterOptions from '../components/ViewMasterOptions';
import QuestionsNav from "../../commonComponents/QuestionsNav"

export default function MasterOptionDashboard() {
  const navigate = useNavigate();
  const dispatch = useDispatch(); 
  const accessToken = useSelector((state) => state.contributor.accessToken);

  // Check for access token and redirect if necessary
  useEffect(() => {
    if (!accessToken) {
      toast.error('Please log in as a contributor!');
      const timer = setTimeout(() => {
        navigate('/contributor_login'); 
      }, 2000);
      return () => clearTimeout(timer); 
    }
  }, [accessToken, navigate]);

  // Conditional rendering to prevent rendering when no access token
  if (!accessToken) {
    return (
      <>
        <section>
          <Toaster />
        </section>
      </>
    ); 
  }

  const [searchTerm, setSearchTerm] = useState(''); 
  const [radioValue, setRadioValue] = useState('3');
  const [optionAddedFlag, setOptionAddedFlag] = useState(false); 

  const handleMasterOptionContentChange = (content) => {
    setSearchTerm(content); 
  };


    // Handle change in search bar
    const handleSearchTermChange = (term) => {
      setSearchTerm(term); // Update search term from the search bar
    };
  

  const handleOptionAdded = () => {
    setOptionAddedFlag((prev) => !prev);
  };

  return (
    <>
      <Toaster />
      <NavigationBar />
      <Container className="mt-4">
        <Row>
          <Col xs={12} sm={10} md={5} lg={5}>
            <QuestionsNav 
              radioValue={radioValue} 
              setRadioValue={setRadioValue} 
            />
            <CreateMasterOptionForm
              onMasterOptionContentChange={handleMasterOptionContentChange}
              onMasterOptionCreated={handleOptionAdded}              // Ensure search term is updated here
            />
            <div className="d-flex justify-content-center">
              <Link to="/contributor_dashboard">
                <Button variant="secondary" className="mt-3 text-center">
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </Col>
          <Col>
            <ViewMasterOptions
              searchTerm={searchTerm} // Ensure the search term is passed here
              optionAddedFlag={optionAddedFlag}
              onSearchTermChange={handleSearchTermChange}
            />
          </Col>
        </Row>
      </Container>
    </>
  );
}
