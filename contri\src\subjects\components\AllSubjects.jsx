import React, { useState, useEffect, useMemo } from "react";
import PaginationComponent from "../../commonComponents/PaginationComponent";
import {
  Card,
  Button,
  Row,
  Col,
  Form,
  Container,
  Modal,
  Dropdown,
} from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import {
  getSubjects,
  updateSubject,
  deleteSubject,
} from "../../redux/slice/subjectSlice";
import ViewModal from "../../commonComponents/ViewModal";
// import { FaPlusCircle } from "react-icons/fa";
import { BsPencilSquare, BsTrash } from "react-icons/bs";
import { Link } from "react-router-dom";
import toast, { Toaster } from "react-hot-toast";
import Swal from "sweetalert2";
import CourseCardsSkeleton from "../../commonComponents/CourseCardsSkeleton";

const AllSubjects = ({ subjectAdded }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(0);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [subjectData, setSubjectData] = useState({ name: "", description: "" });
  const [loadingSubject, setLoadingSubject] = useState(false);
  const [subjectsPerPage, setSubjectsPerPage] = useState(6); // Updated to state

  const dispatch = useDispatch();
  const { subjects, isLoading, error } = useSelector((state) => state.subject);

  useEffect(() => {
    dispatch(getSubjects());
  }, [dispatch, subjectAdded]);

  // for view modal

  const handleViewSubject = (subject) => {
    setSelectedSubject(subject);
    setShowViewModal(true);
  };

  const handleEditSubject = (slug) => {
    setLoadingSubject(true);
    const subject = subjects.find((s) => s.slug === slug);
    if (subject) {
      setSubjectData({ name: subject.name, description: subject.description });
      setSelectedSubject(subject);
      setLoadingSubject(false);
      setShowEditModal(true);
    }
  };
  const handleDeleteSubject = async (slug) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        await dispatch(deleteSubject(slug));
        dispatch(getSubjects()); // Reload the subjects list
        toast.success("Subject deleted successfully!");
      } catch (error) {
        console.error("Error deleting subject", error);
        toast.error("Failed to delete the subject. Please try again.");
      }
    }
  };

  const handleUpdateSubject = () => {
    const updatedSubject = {
      name: subjectData.name,
      description: subjectData.description,
    };
    dispatch(
      updateSubject({ slug: selectedSubject.slug, updatedData: updatedSubject })
    ).then(() => {
      setShowEditModal(false);
      dispatch(getSubjects());
    });
  };

  const handleSearch = () => {
    setCurrentPage(0);
  };

  const handleItemsPerPageChange = (items) => {
    setSubjectsPerPage(items);
    setCurrentPage(0); // Reset to the first page
  };

  const filteredSubjects = useMemo(() => {
    return (
      subjects?.filter(
        (subject) =>
          subject.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          subject.description.toLowerCase().includes(searchQuery.toLowerCase())
      ) || []
    );
  }, [subjects, searchQuery]);

  const handlePageChange = (page) => {
    setCurrentPage(page - 1); // Adjust for 0-based index
  };

  const indexOfLastSubject = (currentPage + 1) * subjectsPerPage;
  const indexOfFirstSubject = indexOfLastSubject - subjectsPerPage;
  const currentSubjects = filteredSubjects.slice(
    indexOfFirstSubject,
    indexOfLastSubject
  );

  return (
    <Container>
      <h2
        className="text-center text-success"
        style={{ fontSize: "1.9rem", margin: "1rem" }}
      >
        Dashboard / Subjects,{" "}
        <small className="h4"> See live updated here. </small>
      </h2>

      <Row className="mb-4 justify-content-center">
        <Col
          xs={12}
          sm={8}
          md={6}
          lg={5}
          className="d-flex justify-content-between"
        >
          <Form.Control
            type="text"
            placeholder="Search subjects..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Dropdown>
            <Dropdown.Toggle
              variant="success mx-2"
            >
              {subjectsPerPage} per page
            </Dropdown.Toggle>
            <Dropdown.Menu>
              <Dropdown.Item onClick={() => handleItemsPerPageChange(5)}>
                5 per page
              </Dropdown.Item>
              <Dropdown.Item onClick={() => handleItemsPerPageChange(25)}>
                25 per page
              </Dropdown.Item>
              <Dropdown.Item onClick={() => handleItemsPerPageChange(100)}>
                100 per page
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() => handleItemsPerPageChange(filteredSubjects.length)}
              >
                All
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </Col>
      </Row>

      {error && (
        <div className="text-center text-danger">
          <p>{error}</p>
        </div>
      )}
      {isLoading ? (
        <Row>
          {Array.from({ length: subjectsPerPage }).map((_, index) => (
            <Col key={index} xs={12}>
              <Card className="mb-4 shadow-sm rounded-3" style={{ backgroundColor: "#e6ffe6" }}>
                <Card.Body>
                  <div className="placeholder-glow">
                    <div
                      className="placeholder w-100"
                      style={{ height: "100px", backgroundColor: "#c4f7c4", borderRadius: "5px" }}
                    ></div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      ) : (
        <Row>
          {currentSubjects.map((subject) => (
            <Col key={subject.slug} xs={12}>
              <Card className="mb-4 shadow rounded-3">
                <Card.Body className="d-flex align-items-center justify-content-between">
                  <div className="flex-grow-1">
                    <div className="d-flex flex-wrap align-items-center">
                      <span className="text-success fw-bold text-truncate">
                        {subject.name}
                      </span>
                      <span className="mx-2">|</span>
                      <span
                        className="text-muted"
                        style={{
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                          overflow: "hidden",
                        }}
                      >
                        {subject.description}
                      </span>
                    </div>
                  </div>
                  <div className="d-flex flex-wrap justify-content-end">
                    <Link to={`/subject/${subject.slug}`}>
                      <Button variant="outline-primary" className="m-1 fs-6">
                        Topics
                      </Button>
                    </Link>
                    <Button
                      variant="outline-info"
                      className="m-1 fs-6"
                      onClick={() => handleViewSubject(subject)}
                    >
                      View
                    </Button>
                    <Button
                      variant="outline-success"
                      className="m-1 fs-6"
                      onClick={() => handleEditSubject(subject.slug)}
                    >
                      <BsPencilSquare />
                    </Button>
                    <Button
                      variant="outline-danger"
                      className="m-1 fs-6"
                      onClick={() => handleDeleteSubject(subject.slug)}
                    >
                      <BsTrash />
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      )}

      <Row className="mt-4 justify-content-center">
        <Col xs={12} className="text-center">
          <PaginationComponent
            totalPages={Math.ceil(filteredSubjects.length / subjectsPerPage)}
            currentPage={currentPage + 1} // Adjust for 1-based index
            handlePageChange={handlePageChange}
          />
        </Col>
      </Row>

      <Modal
        show={showEditModal && !loadingSubject}
        onHide={() => setShowEditModal(false)}
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>Edit Subject</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group controlId="subjectName">
              <Form.Label>Subject Name</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter subject name"
                value={subjectData.name}
                onChange={(e) =>
                  setSubjectData({ ...subjectData, name: e.target.value })
                }
              />
            </Form.Group>
            <Form.Group controlId="subjectDescription">
              <Form.Label>Subject Description</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter subject description"
                value={subjectData.description}
                onChange={(e) =>
                  setSubjectData({
                    ...subjectData,
                    description: e.target.value,
                  })
                }
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="outline-success"
            onClick={() => setShowEditModal(false)}
          >
            Close
          </Button>
          <Button variant="success" onClick={handleUpdateSubject}>
            Save Changes
          </Button>
        </Modal.Footer>
      </Modal>

      <ViewModal
        show={showViewModal}
        onHide={() => setShowViewModal(false)}
        content={selectedSubject}
      />

      {/* <Link to="/add_subjects">
        <Button
          className="position-fixed bottom-0 end-0 m-3"
          style={{
            backgroundColor: "#28a745",
            borderRadius: "50%",
            width: "70px",
            height: "70px",
            fontSize: "2rem",
            padding: "0",
          }}
          variant="success"
        >
          <FaPlusCircle style={{ color: "white" }} />
        </Button>
      </Link> */}
    </Container>
  );
};

export default AllSubjects;
