import React, { useContext, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
  ScrollView,
  Linking,
  Image,
  TouchableOpacity,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { getStudentProfile } from '../../redux/authSlice';
import { ThemeContext } from '../../context/ThemeContext';
import { MaterialIcons, FontAwesome, MaterialCommunityIcons } from '@expo/vector-icons';
import BottomTabBar from '../../components/BottomTabBar';
import ProgressScreen from '../ProgressScreen';
import PracticeStatsComponent from '../../components/PracticeStatsComponent';
import PushTokenDisplay from '../../components/PushTokenDisplay';
import EditProfileModal from '../../components/EditProfileModal';

const ProfileScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const { isDarkMode } = useContext(ThemeContext);
  const { student, profile, loading, error } = useSelector((state) => state.auth);
  const [showEditModal, setShowEditModal] = useState(false);

  useEffect(() => {
    if (student?.id) {
      dispatch(getStudentProfile({ id: student.id }));
    }
  }, [dispatch, student]);

  if (loading) {
    return (
      <SafeAreaView style={[styles.center, isDarkMode && styles.containerDark]}>
        <ActivityIndicator size="large" color={isDarkMode ? "#fff" : "#000"} />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.center, isDarkMode && styles.containerDark]}>
        <Text style={[styles.errorText, isDarkMode && styles.valueDark]}>
          {error.toString()}
        </Text>
      </SafeAreaView>
    );
  }

  if (!profile || !profile.student) {
    return (
      <SafeAreaView style={[styles.center, isDarkMode && styles.containerDark]}>
        <Text style={[styles.value, isDarkMode && styles.valueDark]}>
          No profile data available.
        </Text>
      </SafeAreaView>
    );
  }

  const { user, phone, course, subscription_type } = profile.student;
  const { daily_practice, weekly_practice, monthly_practice, continuous_practice, tickets } = profile;

  // Profile Header Card
  const renderProfileHeader = () => (
    <View style={[styles.headerCard, isDarkMode && styles.headerCardDark]}>
      <View style={styles.profileSection}>
        <View style={[styles.avatarContainer, isDarkMode && styles.avatarContainerDark]}>
          <View style={styles.iconContainer}>
            <MaterialCommunityIcons name="account" size={45} color={isDarkMode ? '#4a90e2' : '#333'} />
          </View>
        </View>
        <View style={styles.profileInfo}>
          <Text style={[styles.welcomeText, isDarkMode && styles.textDark]}>
            Hi, <Text style={styles.username}>{user.username || 'User_name'}!</Text>
          </Text>
          <Text style={[styles.welcomeSubtext, isDarkMode && styles.textDark]}>
            Welcome to your profile
          </Text>
        </View>
      </View>
      <TouchableOpacity 
        style={styles.editButton}
        onPress={() => setShowEditModal(true)}
      >
        <MaterialIcons name="edit" size={20} color="#fff" />
        <Text style={styles.editButtonText}>Edit Profile</Text>
      </TouchableOpacity>
    </View>
  );

  // Practice Stats Cards - Using reusable component
  // const renderPracticeStats = () => (
  //   <PracticeStatsComponent
  //     dailyPractice={daily_practice || 0}
  //     weeklyPractice={weekly_practice || 0}
  //     monthlyPractice={monthly_practice || 0}
  //     continuousPractice={continuous_practice || 0}
  //     title="Practice Statistics"
  //     showTitle={false}
  //   />
  // );

  const renderTicket = (ticket) => (
    <View key={ticket.id} style={[styles.ticketCard, isDarkMode && styles.ticketCardDark]}>
      <Text style={[styles.ticketTitle, isDarkMode && styles.textDark]}>
        Subject: {ticket.subject}
      </Text>
      <Text style={[styles.ticketDescription, isDarkMode && styles.textDark]}>
        {ticket.description}
      </Text>
      <View style={styles.ticketMeta}>
        <Text style={[styles.ticketStatus, { color: ticket.ticket_status === 'open' ? '#e74c3c' : '#2ecc71' }]}>
          {ticket.ticket_status}
        </Text>
        <Text style={[styles.ticketPriority, isDarkMode && styles.textDark]}>
          Priority: {ticket.priority}
        </Text>
      </View>
      {ticket.resolve_summary && (
        <Text style={[styles.ticketResolution, isDarkMode && styles.textDark]}>
          Resolution: {ticket.resolve_summary}
        </Text>
      )}
      {ticket.attachments && (
        <TouchableOpacity
          onPress={() => Linking.openURL(`https://api.shashtrarth.com${ticket.attachments}`)}
          style={styles.attachmentButton}
        >
          <MaterialIcons name="attachment" size={20} color="#4a90e2" />
          <Text style={styles.attachmentText}>View Attachment</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <BottomTabBar>
      <SafeAreaView style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#1a1a1a' : '#fff' }
      ]}>
        <ScrollView contentContainerStyle={styles.content}>
          {renderProfileHeader()}
          
          <View style={styles.infoSection}>
            <View style={styles.infoRow}>
              <View style={[styles.infoCard, isDarkMode && styles.infoCardDark]}>
                <MaterialIcons name="person" size={24} color={isDarkMode ? '#ccc' : '#666'} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, isDarkMode && styles.labelDark]}>Name</Text>
                  <Text style={[styles.infoValue, isDarkMode && styles.valueDark]}>
                    {`${user.first_name} ${user.last_name}`}
                  </Text>
                </View>
              </View>
              <View style={[styles.infoCard, isDarkMode && styles.infoCardDark]}>
                <MaterialIcons name="phone" size={24} color={isDarkMode ? '#ccc' : '#666'} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, isDarkMode && styles.labelDark]}>Phone</Text>
                  <Text style={[styles.infoValue, isDarkMode && styles.valueDark]}>{phone || '—'}</Text>
                </View>
              </View>
            </View>

            <View style={styles.infoRow}>
              <View style={[styles.infoCard, isDarkMode && styles.infoCardDark]}>
                <MaterialIcons name="school" size={24} color={isDarkMode ? '#ccc' : '#666'} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, isDarkMode && styles.labelDark]}>Course</Text>
                  <Text style={[styles.infoValue, isDarkMode && styles.valueDark]}>{course || '—'}</Text>
                </View>
              </View>
              <View style={[styles.infoCard, isDarkMode && styles.infoCardDark]}>
                <MaterialIcons name="card-membership" size={24} color={isDarkMode ? '#ccc' : '#666'} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, isDarkMode && styles.labelDark]}>Subscription</Text>
                  <Text style={[styles.infoValue, isDarkMode && styles.valueDark]}>
                    {subscription_type || '—'}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* TODO: Remove this section after successful push notification testing */}
          <PushTokenDisplay />

          {/* {renderPracticeStats()} */}

          {/* Progress Section */}
          <View style={styles.progressSection}>
            <Text style={[styles.sectionTitle, isDarkMode && styles.textDark]}>
              <MaterialCommunityIcons name="chart-line" size={20} /> Progress & Friends
            </Text>
            <ProgressScreen isEmbedded={true} />
          </View>

          {tickets?.length > 0 && (
            <View style={styles.ticketsSection}>
              <Text style={[styles.sectionTitle, isDarkMode && styles.textDark]}>
                <FontAwesome name="ticket" size={20} /> My Raised Queries
              </Text>
              {tickets.map(renderTicket)}
            </View>
          )}
        </ScrollView>
      </SafeAreaView>

      {/* Edit Profile Modal */}
      <EditProfileModal
        visible={showEditModal}
        onClose={() => setShowEditModal(false)}
        profileData={profile}
      />
    </BottomTabBar>
  );
};

export default ProfileScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  content: {
    padding: 16,
  },
  headerCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  headerCardDark: {
    backgroundColor: '#1e1e1e',
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#4a90e2',
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarContainerDark: {
    borderColor: '#4a90e2',
    backgroundColor: '#1e1e1e',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
  profileInfo: {
    marginLeft: 16,
    flex: 1,
  },
  welcomeText: {
    fontSize: 20,
    color: '#333',
  },
  username: {
    fontWeight: 'bold',
    color: '#4a90e2',
  },
  welcomeSubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  editButton: {
    backgroundColor: '#4a90e2',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    borderRadius: 8,
  },
  editButtonText: {
    color: '#fff',
    marginLeft: 8,
    fontWeight: '500',
  },
  infoSection: {
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    flex: 1,
    marginHorizontal: 4,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  infoCardDark: {
    backgroundColor: '#1e1e1e',
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statCardDark: {
    backgroundColor: '#1e1e1e',
  },
  dailyCard: {
    borderColor: '#4a90e2',
    borderWidth: 1,
  },
  weeklyCard: {
    borderColor: '#2ecc71',
    borderWidth: 1,
  },
  monthlyCard: {
    borderColor: '#f1c40f',
    borderWidth: 1,
  },
  continuousCard: {
    borderColor: '#e74c3c',
    borderWidth: 1,
  },
  statTitle: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
  },
  statTitleDark: {
    color: '#aaa',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 4,
  },
  statValueDark: {
    color: '#fff',
  },
  progressSection: {
    marginBottom: 16,
  },
  ticketsSection: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  ticketCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  ticketCardDark: {
    backgroundColor: '#1e1e1e',
  },
  ticketTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  ticketDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  ticketMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  ticketStatus: {
    fontSize: 14,
    fontWeight: '500',
  },
  ticketPriority: {
    fontSize: 14,
    color: '#666',
  },
  ticketResolution: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  attachmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  attachmentText: {
    color: '#4a90e2',
    marginLeft: 8,
    fontSize: 14,
  },
  textDark: {
    color: '#eee',
  },
  labelDark: {
    color: '#aaa',
  },
  valueDark: {
    color: '#fff',
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#ff3b30',
  },
});
