#!/usr/bin/env python3
"""
Setup Cron Job for Membership Expiry Notifications
This script helps you set up a cron job that runs daily at 11:11 AM
"""

import os
import sys
import subprocess
from pathlib import Path

def get_project_path():
    """Get the absolute path to the Django project"""
    return Path(__file__).parent.absolute()

def get_python_path():
    """Get the Python executable path"""
    return sys.executable

def create_cron_command():
    """Create the cron command string"""
    project_path = get_project_path()
    python_path = get_python_path()
    
    # Cron command that runs daily at 11:11 AM
    cron_command = f"8 17 * * * cd {project_path} && {python_path} manage.py check_membership_expiry >> /var/log/membership_expiry.log 2>&1"
    
    return cron_command

def create_cron_script():
    """Create a shell script for the cron job"""
    project_path = get_project_path()
    python_path = get_python_path()
    
    script_content = f"""#!/bin/bash
# Membership Expiry Notification Cron Job
# Runs daily at 11:11 AM

# Set environment variables
export DJANGO_SETTINGS_MODULE=Library.settings
export PATH={os.environ.get('PATH', '')}

# Change to project directory
cd {project_path}

# Run the membership expiry check
{python_path} manage.py check_membership_expiry

# Log the execution
echo "Membership expiry check completed at $(date)" >> /var/log/membership_expiry.log
"""
    
    script_path = project_path / "membership_expiry_cron.sh"
    
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # Make the script executable
    os.chmod(script_path, 0o755)
    
    return script_path

def show_cron_setup_instructions():
    """Show instructions for setting up the cron job"""
    project_path = get_project_path()
    python_path = get_python_path()
    cron_command = create_cron_command()
    script_path = create_cron_script()
    
    print("🕚 MEMBERSHIP EXPIRY CRON JOB SETUP")
    print("=" * 60)
    print("This will set up a daily cron job that runs at 11:11 AM")
    print()
    
    print("📁 Project Information:")
    print(f"   Project Path: {project_path}")
    print(f"   Python Path: {python_path}")
    print(f"   Script Created: {script_path}")
    print()
    
    print("⚙️ OPTION 1: Direct Cron Command")
    print("=" * 40)
    print("Add this line to your crontab:")
    print()
    print(f"   {cron_command}")
    print()
    print("Steps:")
    print("   1. Open crontab: crontab -e")
    print("   2. Add the above line")
    print("   3. Save and exit")
    print()
    
    print("⚙️ OPTION 2: Using Shell Script (Recommended)")
    print("=" * 50)
    print("Add this line to your crontab:")
    print()
    print(f"   11 11 * * * {script_path}")
    print()
    print("Steps:")
    print("   1. Open crontab: crontab -e")
    print("   2. Add the above line")
    print("   3. Save and exit")
    print()
    
    print("📋 MANUAL SETUP STEPS:")
    print("=" * 30)
    print("1. Open terminal")
    print("2. Run: crontab -e")
    print("3. Add one of the cron lines above")
    print("4. Save and exit (Ctrl+X, then Y, then Enter in nano)")
    print("5. Verify: crontab -l")
    print()
    
    print("🧪 TESTING:")
    print("=" * 15)
    print("Test the command manually first:")
    print(f"   cd {project_path}")
    print(f"   {python_path} manage.py check_membership_expiry --verbose")
    print()
    
    print("📊 MONITORING:")
    print("=" * 20)
    print("Check cron job logs:")
    print("   tail -f /var/log/membership_expiry.log")
    print("   # or")
    print("   tail -f /var/log/cron")
    print("   # or")
    print("   journalctl -u cron -f")
    print()
    
    print("⏰ SCHEDULE DETAILS:")
    print("=" * 25)
    print("   Time: 11:11 AM daily")
    print("   Cron Format: 11 11 * * *")
    print("   Meaning: minute=11, hour=11, day=*, month=*, weekday=*")
    print()
    
    print("🔧 ALTERNATIVE SCHEDULES:")
    print("=" * 30)
    print("   Every day at 9:00 AM:  0 9 * * *")
    print("   Every day at 12:00 PM: 0 12 * * *")
    print("   Every day at 6:00 PM:  0 18 * * *")
    print("   Twice daily (9AM,6PM): 0 9,18 * * *")
    print()
    
    print("✅ VERIFICATION:")
    print("=" * 20)
    print("After setting up the cron job:")
    print("1. Check if cron service is running: systemctl status cron")
    print("2. List your cron jobs: crontab -l")
    print("3. Test manually first to ensure it works")
    print("4. Check logs after the scheduled time")
    print()
    
    print("🚨 IMPORTANT NOTES:")
    print("=" * 25)
    print("• Make sure Django server is not required to be running")
    print("• The command runs independently of the web server")
    print("• Logs will be written to /var/log/membership_expiry.log")
    print("• Make sure the log directory is writable")
    print("• Test the command manually before setting up cron")

def create_systemd_service():
    """Create a systemd service as an alternative to cron"""
    project_path = get_project_path()
    python_path = get_python_path()
    
    service_content = f"""[Unit]
Description=Membership Expiry Notification Check
After=network.target

[Service]
Type=oneshot
User=www-data
Group=www-data
WorkingDirectory={project_path}
Environment=DJANGO_SETTINGS_MODULE=Library.settings
ExecStart={python_path} manage.py check_membership_expiry
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"""
    
    timer_content = """[Unit]
Description=Run Membership Expiry Check Daily at 11:11 AM
Requires=membership-expiry.service

[Timer]
OnCalendar=*-*-* 11:11:00
Persistent=true

[Install]
WantedBy=timers.target
"""
    
    print("\n🔧 SYSTEMD SERVICE ALTERNATIVE:")
    print("=" * 40)
    print("If you prefer systemd over cron, create these files:")
    print()
    print("1. /etc/systemd/system/membership-expiry.service:")
    print(service_content)
    print()
    print("2. /etc/systemd/system/membership-expiry.timer:")
    print(timer_content)
    print()
    print("Then run:")
    print("   sudo systemctl daemon-reload")
    print("   sudo systemctl enable membership-expiry.timer")
    print("   sudo systemctl start membership-expiry.timer")
    print("   sudo systemctl status membership-expiry.timer")

def main():
    print("🚀 MEMBERSHIP EXPIRY CRON JOB SETUP UTILITY")
    print("=" * 70)
    print("This utility helps you set up automated daily membership expiry checks")
    print()
    
    try:
        show_cron_setup_instructions()
        create_systemd_service()
        
        print("\n" + "=" * 70)
        print("✅ SETUP COMPLETE!")
        print("=" * 70)
        print("Choose one of the methods above to set up automated daily checks.")
        print("The system will check for membership expiry notifications daily at 11:11 AM.")
        print()
        print("🎯 RECOMMENDED: Use Option 2 (Shell Script) for better reliability.")
        
    except Exception as e:
        print(f"❌ Error during setup: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
