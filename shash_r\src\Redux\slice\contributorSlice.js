import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor; // Adjust this path to match your state structure
  return accessToken;
};

// Thunk to register a contributor
export const registerContributor = createAsyncThunk(
  'contributors/register',
  async (contributorData, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_REGISTER_CONTRIBUTOR}`,
        contributorData
      );
      if (response.status === 200) {
        return response.data;
      } else {
        return rejectWithValue('Registration failed');
      }
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Something went wrong');
    }
  }
);

// Thunk to log in a contributor
export const loginContributor = createAsyncThunk(
  'contributors/login',
  async (loginData, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_LOGIN_CONTRIBUTOR}`,
        loginData
      );
      if (response.status === 200) {
        return response.data;
      } else {
        return rejectWithValue('Invalid credentials or unexpected error');
      }
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Invalid credentials');
    }
  }
);

// Thunk to log out a contributor
export const logoutContributor = createAsyncThunk(
  'contributors/logout',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { accessToken, refreshToken } = getState().contributor;

      if (!accessToken || !refreshToken) {
        return rejectWithValue('Tokens are missing');
      }

      const formData = new FormData();
      formData.append('refresh', refreshToken);

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_LOGOUT_CONTRIBUTOR}`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      if (response.status === 204) {
        return true; // Return success status
      } else {
        return rejectWithValue('Logout failed');
      }
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Something went wrong during logout'
      );
    }
  }
);

// Thunk to fetch contributor details
export const getContributons = createAsyncThunk(
  'contributors/getContributons',
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_CONTRIBUTONS}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        return response.data; // Return the response data upon success
      } else {
        return rejectWithValue('Failed to fetch contributor details');
      }
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Something went wrong');
    }
  }
);

const contributorSlice = createSlice({
  name: 'contributors',
  initialState: {
    profile: null,
    role: null,
    contributorProfileId: null,
    accessToken: null,
    refreshToken: null,
    contribution: null, // New field to store fetched contributor details
    loading: false,
    error: null,
  },
  reducers: {
    clearContributorState: (state) => {
      state.profile = null;
      state.role = null;
      state.contributorProfileId = null;
      state.accessToken = null;
      state.refreshToken = null;
      state.contribution = null; // Clear fetched contributor details
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(registerContributor.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerContributor.fulfilled, (state) => {
        state.loading = false;
        // Registration successful - user might need to login separately
      })
      .addCase(registerContributor.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(loginContributor.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginContributor.fulfilled, (state, action) => {
        state.loading = false;
        const { profile, role, contributor_profile_id, access, refresh } = action.payload;
        state.profile = profile;
        state.role = role;
        state.contributorProfileId = contributor_profile_id;
        state.accessToken = access;
        state.refreshToken = refresh;
      })
      .addCase(loginContributor.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(logoutContributor.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(logoutContributor.fulfilled, (state) => {
        state.loading = false;
        state.profile = null;
        state.role = null;
        state.accessToken = null;
        state.refreshToken = null;
        state.contribution = null;
      })
      .addCase(logoutContributor.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(getContributons.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getContributons.fulfilled, (state, action) => {
        state.loading = false;
        state.contribution = action.payload; // Store fetched contributor details
      })
      .addCase(getContributons.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearContributorState } = contributorSlice.actions;

export default contributorSlice.reducer;
