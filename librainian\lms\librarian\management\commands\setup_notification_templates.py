from django.core.management.base import BaseCommand
from librarian.models import NotificationCategory, NotificationTemplate


class Command(BaseCommand):
    help = 'Setup default notification categories and templates'

    def handle(self, *args, **options):
        self.stdout.write('Setting up notification categories and templates...')
        
        # Create categories
        categories_data = [
            {
                'name': 'Registration',
                'description': 'Student registration and QR code related notifications',
                'icon': '📝',
                'color': '#007bff'
            }
        ]
        
        for cat_data in categories_data:
            category, created = NotificationCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            if created:
                self.stdout.write(f'Created category: {category.name}')
            else:
                self.stdout.write(f'Category already exists: {category.name}')
        
        # Create templates
        templates_data = [
            {
                'event_type': 'qr_registration',
                'category_name': 'Registration',
                'title_template': '📝 New QR Registration Received',
                'body_template': 'Hi {{user_name}}, a new student registration has been submitted via QR code.\n\nStudent Details:\n• Name: {{student_name}}\n• Email: {{student_email}}\n• Mobile: {{student_mobile}}\n• Course: {{course}}\n• Date: {{registration_date}}\n\nPlease review and approve the registration.',
                'priority': 'high',
                'action_url': '/students/temp-students/'
            }
        ]
        
        for template_data in templates_data:
            category = NotificationCategory.objects.get(name=template_data['category_name'])
            template_data['category'] = category
            del template_data['category_name']
            
            template, created = NotificationTemplate.objects.get_or_create(
                event_type=template_data['event_type'],
                defaults=template_data
            )
            if created:
                self.stdout.write(f'Created template: {template.event_type}')
            else:
                self.stdout.write(f'Template already exists: {template.event_type}')
        
        self.stdout.write(self.style.SUCCESS('Successfully setup notification system!'))
