import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor; // Adjust this path to match your state structure
  return accessToken;
};

// Thunks
// Create a master question
export const createMasterQuestion = createAsyncThunk(
  'masterQuestion/createMasterQuestion',
  async (questionData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MASTER_QUESTION}`,
        questionData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data; // This will be handled in the component, but not stored in Redux
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error creating master question');
    }
  }
);

// Fetch all master questions
export const getAllMasterQuestions = createAsyncThunk(
  'masterQuestion/getAllMasterQuestions',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MASTER_QUESTION}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data; // This will be handled in the component, but not stored in Redux
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching master questions');
    }
  }
);

// Fetch a single master question by slug
export const getMasterQuestion = createAsyncThunk(
  'masterQuestion/getMasterQuestion',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MASTER_QUESTION}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data; // This will be handled in the component, but not stored in Redux
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching master question');
    }
  }
);

// Update a master question
export const updateMasterQuestion = createAsyncThunk(
  'masterQuestion/updateMasterQuestion',
  async ({ slug, updatedData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MASTER_QUESTION}${slug}/`,
        updatedData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data; // This will be handled in the component, but not stored in Redux
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error updating master question');
    }
  }
);

// Delete a master question
export const deleteMasterQuestion = createAsyncThunk(
  'masterQuestion/deleteMasterQuestion',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MASTER_QUESTION}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return slug; // Return the slug to handle in the component, no need to store it in Redux
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error deleting master question');
    }
  }
);

// Slice
const masterQuestionSlice = createSlice({
  name: 'masterQuestion',
  initialState: {
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Handle createMasterQuestion
      .addCase(createMasterQuestion.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createMasterQuestion.fulfilled, (state) => {
        state.isLoading = false;
        // Do not store the created question in Redux state
      })
      .addCase(createMasterQuestion.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle getAllMasterQuestions
      .addCase(getAllMasterQuestions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getAllMasterQuestions.fulfilled, (state) => {
        state.isLoading = false;
        // Do not store the list of questions in Redux state
      })
      .addCase(getAllMasterQuestions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle getMasterQuestion
      .addCase(getMasterQuestion.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getMasterQuestion.fulfilled, (state) => {
        state.isLoading = false;
        // Do not store the single question in Redux state
      })
      .addCase(getMasterQuestion.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle updateMasterQuestion
      .addCase(updateMasterQuestion.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateMasterQuestion.fulfilled, (state) => {
        state.isLoading = false;
        // Do not store the updated question in Redux state
      })
      .addCase(updateMasterQuestion.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle deleteMasterQuestion
      .addCase(deleteMasterQuestion.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteMasterQuestion.fulfilled, (state) => {
        state.isLoading = false;
        // Do not store the result of the deleted question in Redux state
      })
      .addCase(deleteMasterQuestion.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export default masterQuestionSlice.reducer;
