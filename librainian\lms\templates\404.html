<!DOCTYPE html>
<html lang="en">
<head>

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5L8WZWCC');</script>
<!-- End Google Tag Manager -->

    <meta charset="utf-8">
    <meta name="google" content="notranslate">
    <meta name="description" content="Page Not Found | Librainian - The #1 Library Management System. Return to our homepage to explore our library management tools.">
    <meta name="keywords" content="librainian, library management system, lms, page not found, 404 error, library software, library management tool">
    <meta name="robots" content="noindex, follow">
    <link rel="canonical" href="https://www.librainian.com/404">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="content-language" content="en">
    <meta name="geo.region" content="IN">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Librainian">
    <meta property="og:title" content="Page Not Found | Librainian">
    <meta property="og:description" content="The page you're looking for doesn't exist. Return to our homepage to explore our library management tools.">
    <meta property="og:url" content="https://www.librainian.com/404">
    <meta property="og:image" content="https://www.librainian.com/static/img/error.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:title" content="Page Not Found | Librainian">
    <meta name="twitter:description" content="The page you're looking for doesn't exist. Return to our homepage to explore our library management tools.">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/error.jpg">

    <!-- Author and Date Info -->
    <meta itemprop="author" content="Librainian Team">
    <meta itemprop="datePublished" content="2024-07-01">
    <meta itemprop="dateModified" content="2024-07-07">

    <title>Page Not Found | Librainian</title>

    <!-- WebPage Schema for 404 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Page Not Found | Librainian",
        "description": "The page you're looking for doesn't exist. Return to our homepage to explore our library management tools.",
        "url": "https://librainian.com/404/",
        "isPartOf": {
            "@type": "WebSite",
            "name": "Librainian",
            "url": "https://librainian.com/"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://librainian.com/static/img/logo_trans_name.png",
                "width": 300,
                "height": 100
            },
            "url": "https://librainian.com/"
        },
        "mainEntity": {
            "@type": "Thing",
            "name": "404 Error Page",
            "description": "This page indicates that the requested resource could not be found on the server."
        }
    }
    </script>

    <!-- BreadcrumbList Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://librainian.com/"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "404 - Page Not Found",
                "item": "https://librainian.com/404/"
            }
        ]
    }
    </script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <script>
        // Inline loader script to show loader immediately when page starts loading
        (function() {
            // Check for dark mode preference immediately
            var isDarkMode = localStorage.getItem('darkMode') === 'enabled';
            var loaderBg = isDarkMode ? 'linear-gradient(135deg, #1f2937 0%, #111827 100%)' : 'rgba(0, 0, 0, 0.85)';

            // Create loader HTML with FSEX300 font and LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: ${loaderBg}; z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                    <style>
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('/static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                        }

                        @keyframes blink {
                            0%, 100% { opacity: 1; }
                            50% { opacity: 0; }
                        }

                        @keyframes dots {
                            0% { content: ""; }
                            25% { content: "."; }
                            50% { content: ".."; }
                            75% { content: "..."; }
                            100% { content: ""; }
                        }

                        .loader-text {
                            font-family: 'FSEX300', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            margin-bottom: 20px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                        }

                        .loader-dots::after {
                            content: "";
                            animation: dots 1.5s infinite;
                        }

                        .loader-bar {
                            width: 300px;
                            height: 20px;
                            background-color: rgba(255, 255, 255, 0.2);
                            border-radius: 10px;
                            overflow: hidden;
                            margin: 20px auto;
                        }

                        .loader-progress {
                            width: 0%;
                            height: 100%;
                            background-color: ${isDarkMode ? '#6366f1' : '#6200ee'};
                            border-radius: 10px;
                            animation: progress 2s infinite;
                        }

                        @keyframes progress {
                            0% { width: 0%; }
                            50% { width: 100%; }
                            100% { width: 0%; }
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN<span class="loader-dots"></span></div>
                    <div class="loader-bar">
                        <div class="loader-progress"></div>
                    </div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>


    <!-- Structured Data / JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Page Not Found | Librainian",
        "description": "The page you're looking for doesn't exist. Return to our homepage to explore our library management tools.",
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://www.librainian.com/static/img/librainian-logo-black-transparent.png"
            }
        },
        "isPartOf": {
            "@type": "WebSite",
            "name": "Librainian",
            "url": "https://www.librainian.com/"
        },
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://www.librainian.com/"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Page Not Found",
                    "item": "https://www.librainian.com/404"
                }
            ]
        }
    }
    </script>

    <style>


        body {
            -webkit-user-select: none; /* Disable text selection */
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
                margin: 0;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            font-family: 'Comfortaa', Arial, sans-serif;
        }
        .error-container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
            border-radius: 10px;
        }
        .error-code {
            font-size: 96px;
            font-weight: bold;
            color: #dc3545;
        }
        .error-message {
            font-size: 24px;
            margin: 20px 0;
        }
        .home-button {
            margin-top: 20px;
        }
    </style>
</head>
<body>    
    
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5L8WZWCC"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

    <div class="error-container">
        <img src="/static/img/error.jpg" alt="Error 404" width="70%" height="70%" loading="lazy">
        <div class="error-code">404</div>
        <div class="error-message">Oops! The page you are looking for does not exist.</div>
        {% if role %}
        <a href="/{{role}}/dashboard/" class="btn btn-primary home-button">Return to Home Page</a>
        {% else %}
        <a href="/" class="btn btn-primary home-button">Return to Home Page</a>
        {% endif %}
    </div>
  
</body>
</html>