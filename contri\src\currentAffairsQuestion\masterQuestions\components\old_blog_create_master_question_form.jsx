import React, { useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { Form, Button, Container, Card } from 'react-bootstrap';
import { useDispatch, useSelector } from 'react-redux';
import { createMasterQuestion } from '../../../redux/slice/masterQuestionSlice'; 


export default function BlogCreateMasterQuestionForm({ onMasterQuestionContentChange, onMasterQuestionCreated }) {
  const dispatch = useDispatch();
  const contributorProfileId = useSelector((state) => state.contributor.contributorProfileId || null);
  const { blogId } = useParams();  

  const { isLoading, error } = useSelector((state) => state.masterQuestion);

  // State for form fields
  const [title, setTitle] = useState('');
  const [passageContent, setPassageContent] = useState('');

  // Form submit handler
  const handleSubmit = (e) => {
    e.preventDefault();

    // Form data structure
    const masterQuestionData = {
      author: contributorProfileId,
      title,
      passage_content: passageContent,
      current_affairs: blogId,
      is_current_affairs: true,
    };

    // Dispatch the createMasterQuestion thunk
    dispatch(createMasterQuestion(masterQuestionData))
      .then(() => {
        // After successful creation, dispatch getAllMasterQuestions
        if (onMasterQuestionCreated) {
          onMasterQuestionCreated(); // Fetch all master questions
        }
        // Reset form fields and update the search term to the new title
        setTitle('');
        setPassageContent('');
      });
  };

  return (
    <Container className="mt-4">
      <Card className="shadow-lg p-4">
        <Form onSubmit={handleSubmit}>
          {/* Title Input */}
          <Form.Group controlId="formTitle" className="mb-3">
            <Form.Label>Title</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter title"
              value={title}
              onChange={(e) => {
                setTitle(e.target.value);
                onMasterQuestionContentChange(e.target.value);  // Update search term as title changes
              }}
              required
            />
          </Form.Group>

          {/* Passage Content Textarea */}
          <Form.Group controlId="formPassageContent" className="mb-3">
            <Form.Label>Passage Content</Form.Label>
            <Form.Control
              as="textarea"
              rows={5}
              placeholder="Enter passage content"
              value={passageContent}
              onChange={(e) => setPassageContent(e.target.value)}
              required
            />
          </Form.Group>

          {/* Submit Button */}
          <div className="d-flex justify-content-center mt-3">
            <Button type="submit" variant="outline-success" disabled={isLoading}>
              {isLoading ? 'Submitting...' : 'Create Master Question'}
            </Button>
          </div>

          {/* Error Message */}
          {error && (
            <div className="text-danger mt-3">
              <strong>Error:</strong> {error}
            </div>
          )}
        </Form>
      </Card>
    </Container>
  );
}
