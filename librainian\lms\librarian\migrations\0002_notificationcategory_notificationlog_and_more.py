# Generated by Django 4.2.13 on 2025-07-17 11:49

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('librarian', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(default='🔔', max_length=100)),
                ('color', models.CharField(default='#007bff', max_length=7)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Notification Categories',
            },
        ),
        migrations.CreateModel(
            name='NotificationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(max_length=50)),
                ('recipient_count', models.IntegerField(default=0)),
                ('successful_deliveries', models.IntegerField(default=0)),
                ('failed_deliveries', models.IntegerField(default=0)),
                ('event_data', models.JSONField(blank=True, default=dict)),
                ('error_details', models.TextField(blank=True)),
                ('triggered_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('duration_seconds', models.FloatField(blank=True, null=True)),
                ('triggered_by', models.CharField(default='system', help_text='What triggered this notification (system, admin, user, etc.)', max_length=100)),
            ],
            options={
                'verbose_name': 'Notification Log',
                'verbose_name_plural': 'Notification Logs',
                'ordering': ['-triggered_at'],
            },
        ),
        migrations.AlterModelOptions(
            name='devicetoken',
            options={'ordering': ['-last_used']},
        ),
        migrations.AddField(
            model_name='devicetoken',
            name='device_name',
            field=models.CharField(blank=True, help_text='Device name or browser info', max_length=100),
        ),
        migrations.AddField(
            model_name='devicetoken',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Whether this token is still valid'),
        ),
        migrations.AddField(
            model_name='devicetoken',
            name='last_used',
            field=models.DateTimeField(auto_now=True, help_text='Last time this token was used'),
        ),
        migrations.AddField(
            model_name='devicetoken',
            name='user_agent',
            field=models.TextField(blank=True, help_text='User agent string for web devices'),
        ),
        migrations.AddField(
            model_name='librarian_param',
            name='proof_of_identity_enabled',
            field=models.BooleanField(default=False, help_text='Enable proof of identity requirement for students'),
        ),
        migrations.AddField(
            model_name='librarian_param',
            name='security_deposit_amount',
            field=models.IntegerField(default=0, help_text='Security deposit amount (max 9999)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(9999)]),
        ),
        migrations.AddField(
            model_name='librarian_param',
            name='security_deposit_enabled',
            field=models.BooleanField(default=False, help_text='Enable security deposit requirement for students'),
        ),
        migrations.AlterField(
            model_name='contactmessage',
            name='name',
            field=models.CharField(max_length=225),
        ),
        migrations.AlterField(
            model_name='librarian_param',
            name='librarian_role',
            field=models.CharField(default='Librarian', max_length=225),
        ),
        migrations.AlterField(
            model_name='librarian_param',
            name='library_name',
            field=models.CharField(max_length=225),
        ),
        migrations.CreateModel(
            name='UserNotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_notifications', models.BooleanField(default=True)),
                ('push_notifications', models.BooleanField(default=True)),
                ('sms_notifications', models.BooleanField(default=False)),
                ('quiet_hours_start', models.TimeField(default='22:00', help_text='Start of quiet hours (no notifications)')),
                ('quiet_hours_end', models.TimeField(default='08:00', help_text='End of quiet hours')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('categories', models.ManyToManyField(blank=True, help_text='Categories user wants to receive', to='librarian.notificationcategory')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='user_notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('qr_registration', 'QR Registration Notification')], max_length=50, unique=True)),
                ('title_template', models.CharField(help_text='Use {variable_name} for dynamic content', max_length=200)),
                ('body_template', models.TextField(help_text='Use {variable_name} for dynamic content')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent')], default='normal', max_length=10)),
                ('icon_url', models.URLField(blank=True, null=True)),
                ('action_url', models.CharField(blank=True, help_text='URL to open when notification is clicked', max_length=200)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='librarian.notificationcategory')),
            ],
            options={
                'ordering': ['event_type'],
            },
        ),
        migrations.CreateModel(
            name='NotificationSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Schedule name', max_length=200)),
                ('event_type', models.CharField(choices=[('qr_registration', 'QR Registration'), ('visitor_callback_due', 'Visitor Callback Due'), ('admission_processed', 'Admission Processed'), ('member_expiry_10_days', 'Member Expiry - 10 Days'), ('member_expiry_5_days', 'Member Expiry - 5 Days'), ('member_expiry_1_day', 'Member Expiry - 1 Day'), ('member_expired', 'Member Expired'), ('invoice_created', 'Invoice Created'), ('sales_milestone_reached', 'Sales Milestone Reached'), ('monthly_sales_summary', 'Monthly Sales Summary'), ('visitor_added', 'Visitor Added'), ('daily_galla_reminder', 'Daily Galla Reminder'), ('custom_admin_notification', 'Custom Admin Notification'), ('payment_received', 'Payment Received'), ('facility_booked', 'Facility Booked'), ('system_maintenance', 'System Maintenance'), ('book_overdue', 'Book Overdue'), ('book_reserved', 'Book Reserved'), ('staff_attendance', 'Staff Attendance'), ('emergency_alert', 'Emergency Alert')], help_text='Type of notification event', max_length=50)),
                ('frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('custom', 'Custom Cron')], default='daily', max_length=20)),
                ('cron_expression', models.CharField(blank=True, help_text='Custom cron expression (for custom frequency)', max_length=100)),
                ('time_of_day', models.TimeField(default='09:00', help_text='Time to send notification')),
                ('is_active', models.BooleanField(default=True)),
                ('last_run', models.DateTimeField(blank=True, null=True)),
                ('next_run', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification Schedule',
                'verbose_name_plural': 'Notification Schedules',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_notifications', models.BooleanField(default=True)),
                ('push_notifications', models.BooleanField(default=True)),
                ('sms_notifications', models.BooleanField(default=False)),
                ('qr_registration_notifications', models.BooleanField(default=True)),
                ('visitor_notifications', models.BooleanField(default=True)),
                ('financial_notifications', models.BooleanField(default=True)),
                ('member_expiry_notifications', models.BooleanField(default=True)),
                ('sales_milestone_notifications', models.BooleanField(default=True)),
                ('galla_reminder_notifications', models.BooleanField(default=True)),
                ('custom_admin_notifications', models.BooleanField(default=True)),
                ('quiet_hours_start', models.TimeField(default='22:00', help_text='Start of quiet hours (no notifications)')),
                ('quiet_hours_end', models.TimeField(default='08:00', help_text='End of quiet hours')),
                ('weekend_notifications', models.BooleanField(default=False, help_text='Receive notifications on weekends')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification Preference',
                'verbose_name_plural': 'Notification Preferences',
            },
        ),
        migrations.CreateModel(
            name='NotificationHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('body', models.TextField()),
                ('data', models.JSONField(blank=True, default=dict)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('delivered', 'Delivered'), ('failed', 'Failed'), ('clicked', 'Clicked')], default='pending', max_length=20)),
                ('fcm_message_id', models.CharField(blank=True, max_length=200, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('clicked_at', models.DateTimeField(blank=True, null=True)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='librarian.notificationtemplate')),
            ],
            options={
                'verbose_name_plural': 'Notification History',
                'ordering': ['-sent_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Notification title', max_length=200)),
                ('message', models.TextField(help_text='Notification message content')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent')], default='normal', help_text='Notification priority level', max_length=20)),
                ('send_to_all_librarians', models.BooleanField(default=True, help_text='Send to all librarians')),
                ('send_to_all_sublibrarians', models.BooleanField(default=True, help_text='Send to all sublibrarians')),
                ('send_immediately', models.BooleanField(default=True, help_text='Send notification immediately')),
                ('scheduled_time', models.DateTimeField(blank=True, help_text='Schedule notification for later (leave blank for immediate)', null=True)),
                ('is_sent', models.BooleanField(default=False)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sent_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_notifications', to=settings.AUTH_USER_MODEL)),
                ('specific_recipients', models.ManyToManyField(blank=True, help_text='Specific users to send notification to', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Custom Notification',
                'verbose_name_plural': 'Custom Notifications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AnalyticsCache',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('students_this_month', models.IntegerField(default=0)),
                ('students_growth_percent', models.FloatField(default=0.0)),
                ('students_growth_positive', models.BooleanField(default=True)),
                ('total_students_growth_percent', models.FloatField(default=0.0)),
                ('total_students_growth_positive', models.BooleanField(default=True)),
                ('male_growth_percent', models.FloatField(default=0.0)),
                ('male_growth_positive', models.BooleanField(default=True)),
                ('female_growth_percent', models.FloatField(default=0.0)),
                ('female_growth_positive', models.BooleanField(default=True)),
                ('new_registrations_this_month', models.IntegerField(default=0)),
                ('registrations_growth_percent', models.FloatField(default=0.0)),
                ('registrations_growth_positive', models.BooleanField(default=True)),
                ('todays_collection', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('growth_months', models.TextField(default='[]', help_text='JSON array of month names')),
                ('growth_counts', models.TextField(default='[]', help_text='JSON array of student counts')),
                ('revenue_months', models.TextField(default='[]', help_text='JSON array of month names')),
                ('revenue_amounts', models.TextField(default='[]', help_text='JSON array of revenue amounts')),
                ('visitor_days', models.TextField(default='[]', help_text='JSON array of day labels')),
                ('visitor_counts', models.TextField(default='[]', help_text='JSON array of visitor counts')),
                ('last_calculated', models.DateTimeField(auto_now=True)),
                ('last_student_activity', models.DateTimeField(blank=True, null=True)),
                ('last_invoice_activity', models.DateTimeField(blank=True, null=True)),
                ('last_visitor_activity', models.DateTimeField(blank=True, null=True)),
                ('force_recalculate', models.BooleanField(default=False)),
                ('librarian', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics_cache', to='librarian.librarian_param')),
            ],
            options={
                'verbose_name': 'Analytics Cache',
                'verbose_name_plural': 'Analytics Caches',
            },
        ),
    ]
