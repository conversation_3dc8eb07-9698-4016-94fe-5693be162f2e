<!DOCTYPE html>
<html lang="en">
<head>
 <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5L8WZWCC');</script>
<!-- End Google Tag Manager -->

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="google" content="notranslate">
    <title>Pricing Plans - Librainian</title>

    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <!-- Favicon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">

    <!-- Import master CSS variables from code_temp.html -->
    {% include "code_temp.html" %}

    <!-- Modern Styles -->
    <style>
        :root {
            /* Package-specific overrides only */
        }

        body {
            font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--gray-900);
            min-height: 100vh;
        }

        .pricing-section {
            padding: 4rem 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            font-family: 'Comfortaa', sans-serif;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--gray-600);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.7;
        }

        .pricing-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            overflow: hidden;
            transition: var(--transition-slow);
            height: 100%;
            position: relative;
            transform: translateY(0);
        }

        .pricing-card:hover {
            box-shadow: var(--shadow-xl);
            transform: translateY(-8px);
            border-color: var(--primary);
        }

        .pricing-card.featured {
            border: 2px solid var(--primary);
            transform: scale(1.05);
            z-index: 10;
        }

        .pricing-card.featured:hover {
            transform: scale(1.05) translateY(-8px);
        }

        .card-header-modern {
            padding: 2rem;
            text-align: center;
            position: relative;
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
        }

        .pricing-card.featured .card-header-modern {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-family: 'Comfortaa', sans-serif;
        }

        .plan-price {
            display: flex;
            align-items: baseline;
            justify-content: center;
            gap: 0.5rem;
            margin: 1.5rem 0;
        }

        .price-original {
            font-size: 1rem;
            color: var(--gray-400);
            text-decoration: line-through;
        }

        .pricing-card.featured .price-original {
            color: rgba(255, 255, 255, 0.7);
        }

        .price-current {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--gray-900);
            font-family: 'Comfortaa', sans-serif;
        }

        .pricing-card.featured .price-current {
            color: white;
        }

        .price-duration {
            font-size: 1rem;
            color: var(--gray-600);
            font-weight: 500;
        }

        .pricing-card.featured .price-duration {
            color: rgba(255, 255, 255, 0.9);
        }

        .card-body-modern {
            padding: 2rem;
        }

        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .features-list li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 0;
            font-size: 0.95rem;
            color: var(--gray-700);
            border-bottom: 1px solid var(--gray-100);
        }

        .features-list li:last-child {
            border-bottom: none;
        }

        .feature-icon {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
            flex-shrink: 0;
        }

        .btn-modern {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 1rem;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            width: 100%;
            margin-top: 1.5rem;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: white;
        }

        .btn-secondary-modern {
            background: var(--gray-100);
            color: var(--gray-700);
            border: 2px solid var(--gray-200);
        }

        .btn-secondary-modern:hover {
            background: var(--gray-200);
            border-color: var(--gray-300);
            transform: translateY(-2px);
        }

        .ribbon-modern {
            position: absolute;
            top: 1rem;
            right: -0.5rem;
            background: linear-gradient(135deg, var(--accent) 0%, #d97706 100%);
            color: white;
            padding: 0.5rem 1.5rem;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-radius: var(--border-radius) 0 0 var(--border-radius);
            box-shadow: var(--shadow-md);
            z-index: 20;
        }

        .ribbon-modern.recommended {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            top: 1rem;
            right: -0.5rem;
        }

        .ribbon-modern::after {
            content: '';
            position: absolute;
            right: -0.5rem;
            top: 100%;
            width: 0;
            height: 0;
            border-left: 0.5rem solid #d97706;
            border-bottom: 0.5rem solid transparent;
        }

        .ribbon-modern.recommended::after {
            border-left-color: var(--primary-dark);
        }



        @media (max-width: 991.98px) {
            .section-title {
                font-size: 2.5rem;
            }

            .pricing-card.featured {
                transform: none;
                margin-bottom: 2rem;
            }

            .pricing-card.featured:hover {
                transform: translateY(-8px);
            }

            .pricing-section {
                padding: 2rem 0;
            }
        }

        @media (max-width: 767.98px) {
            .section-title {
                font-size: 2rem;
            }

            .card-header-modern,
            .card-body-modern {
                padding: 1.5rem;
            }

            .price-current {
                font-size: 2rem;
            }

            .back-button-top {
                top: 0.5rem !important;
                left: 0.5rem !important;
                padding: 0.5rem 0.75rem;
                font-size: 0.9rem;
            }

            .section-title {
                margin-top: 2rem;
            }
            
            .back-button-top i {
                font-size: 1rem;
            }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .slide-up {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Back Button */
        .back-button-top {
            position: fixed;
            top: 2rem;
            left: 2rem;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid var(--gray-200);
            border-radius: 50px;
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: var(--transition);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            text-decoration: none;
            color: var(--gray-700);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
        }

        .back-button-top:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.05);
            color: var(--gray-700);
            text-decoration: none;
        }

        .back-button-top i {
            font-size: 1.25rem;
            transition: var(--transition);
        }

        /* Dark Mode Styles */
        body.dark-mode {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            color: #ffffff;
        }

        body.dark-mode .pricing-card {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        body.dark-mode .pricing-card:hover {
            border-color: var(--primary);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .card-header-modern {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        }

        body.dark-mode .pricing-card.featured .card-header-modern {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        }

        body.dark-mode .section-title {
            background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        body.dark-mode .section-subtitle {
            color: rgba(255, 255, 255, 0.7);
        }

        body.dark-mode .plan-name {
            color: #ffffff;
        }

        body.dark-mode .price-current {
            color: #ffffff;
        }

        body.dark-mode .price-duration {
            color: rgba(255, 255, 255, 0.7);
        }

        body.dark-mode .price-original {
            color: rgba(255, 255, 255, 0.5);
        }

        body.dark-mode .pricing-card.featured .price-original {
            color: rgba(255, 255, 255, 0.7);
        }

        body.dark-mode .features-list li {
            color: rgba(255, 255, 255, 0.8);
            border-bottom-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .btn-secondary-modern {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border-color: rgba(255, 255, 255, 0.2);
        }

        body.dark-mode .btn-secondary-modern:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            color: #ffffff;
        }

        body.dark-mode .back-button-top {
            background: rgba(17, 24, 39, 0.9);
            border-color: rgba(255, 255, 255, 0.2);
            color: #ffffff;
        }

        body.dark-mode .back-button-top:hover {
            background: rgba(17, 24, 39, 1);
            color: #ffffff;
        }
    </style>
</head>
<body>
  <!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5L8WZWCC"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

    <!-- Back Button -->
    <a href="javascript:history.back()" class="back-button-top">
        <i class="fas fa-arrow-left"></i>
        <span>Back</span>
    </a>
    {% comment %} Plans {% endcomment %}
    {% if plans %}
    <section class="pricing-section">
        <div class="container">
            <div class="section-header fade-in">
                <h1 class="section-title">Choose Your Plan</h1>
                <p class="section-subtitle">Select the perfect plan for your library management needs. Upgrade or downgrade at any time.</p>
            </div>

            <div class="row g-4 justify-content-center">
                {% for plan in plans %}
                <div class="col-12 col-md-6 col-lg-4">
                    <div class="pricing-card {% if plan.recommended %}featured{% endif %} slide-up" style="animation-delay: 0.{{ forloop.counter }}s">
                        {% if plan.recommended %}
                        <div class="ribbon-modern recommended">
                            <i class="fas fa-star me-1"></i>
                            Recommended
                        </div>
                        {% else %}
                        <div class="ribbon-modern">
                            <i class="fas fa-tags me-1"></i>
                            Sale
                        </div>
                        {% endif %}

                        <div class="card-header-modern">
                            <h3 class="plan-name">{{ plan.name }}</h3>
                            <div class="plan-price">
                                <span class="price-original">₹{{ plan.price }}</span>
                                <span class="price-current">₹{{ plan.discount_price }}</span>
                                <span class="price-duration">/ {{ plan.duration_months }} month{{ plan.duration_months|pluralize }}</span>
                            </div>
                        </div>

                        <div class="card-body-modern">
                            <ul class="features-list">
                                {% if plan.description_line_01 %}
                                <li>
                                    <span class="feature-icon">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    {{ plan.description_line_01 }}
                                </li>
                                {% endif %}
                                {% if plan.description_line_02 %}
                                <li>
                                    <span class="feature-icon">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    {{ plan.description_line_02 }}
                                </li>
                                {% endif %}
                                {% if plan.description_line_03 %}
                                <li>
                                    <span class="feature-icon">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    {{ plan.description_line_03 }}
                                </li>
                                {% endif %}
                                {% if plan.description_line_04 %}
                                <li>
                                    <span class="feature-icon">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    {{ plan.description_line_04 }}
                                </li>
                                {% endif %}
                                {% if plan.description_line_05 %}
                                <li>
                                    <span class="feature-icon">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    {{ plan.description_line_05 }}
                                </li>
                                {% endif %}
                            </ul>

                            <a href="/membership/pre-package/{{ plan.id }}" class="btn-modern {% if plan.recommended %}btn-primary-modern{% else %}btn-secondary-modern{% endif %}">
                                <i class="fas fa-shopping-cart me-2"></i>
                                Get Started
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>
    {% endif %}

    {% comment %} SMS Plans {% endcomment %}
    {% if smsplans %}
    <section class="pricing-section">
        <div class="container">
            <div class="section-header fade-in">
                <h1 class="section-title">SMS Plans</h1>
                <p class="section-subtitle">Affordable SMS communication plans for libraries and study centers. Streamline your messaging and enhance member engagement.</p>
            </div>

            <div class="row g-4 justify-content-center">
                {% for plan in smsplans %}
                <div class="col-12 col-md-6 col-lg-4">
                    <div class="pricing-card {% if plan.recommended %}featured{% endif %} slide-up" style="animation-delay: 0.{{ forloop.counter }}s">
                        {% if plan.recommended %}
                        <div class="ribbon-modern recommended">
                            <i class="fas fa-star me-1"></i>
                            Recommended
                        </div>
                        {% else %}
                        <div class="ribbon-modern">
                            <i class="fas fa-tags me-1"></i>
                            Sale
                        </div>
                        {% endif %}

                        <div class="card-header-modern">
                            <h3 class="plan-name">{{ plan.name }}</h3>
                            <div class="plan-price">
                                <span class="price-original">₹{{ plan.price }}</span>
                                <span class="price-current">₹{{ plan.discount_price }}</span>
                                <span class="price-duration">/ {{ plan.duration_months }} month{{ plan.duration_months|pluralize }}</span>
                            </div>
                        </div>

                        <div class="card-body-modern">
                            <ul class="features-list">
                                {% if plan.description_line_01 %}
                                <li>
                                    <span class="feature-icon">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    {{ plan.description_line_01 }}
                                </li>
                                {% endif %}
                                {% if plan.description_line_02 %}
                                <li>
                                    <span class="feature-icon">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    {{ plan.description_line_02 }}
                                </li>
                                {% endif %}
                                {% if plan.description_line_03 %}
                                <li>
                                    <span class="feature-icon">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    {{ plan.description_line_03 }}
                                </li>
                                {% endif %}
                            </ul>

                            <a href="/membership/create_sms_order/{{ plan.id }}" class="btn-modern {% if plan.recommended %}btn-primary-modern{% else %}btn-secondary-modern{% endif %}">
                                <i class="fas fa-sms me-2"></i>
                                Get SMS Plan
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>
    {% endif %}



    <!-- Bootstrap 5.3.3 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Animation Script -->
    <script>
        // Initialize dark mode on page load based on existing preference
        function initializeDarkMode() {
            const darkMode = localStorage.getItem('darkMode');
            const body = document.body;

            if (darkMode === 'enabled') {
                body.classList.add('dark-mode');
            } else {
                body.classList.remove('dark-mode');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize dark mode based on stored preference
            initializeDarkMode();

            // Add staggered animation delays
            const cards = document.querySelectorAll('.slide-up');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${(index + 1) * 0.1}s`;
            });

            // Add smooth scroll behavior
            document.documentElement.style.scrollBehavior = 'smooth';

            // Add loading animation
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1
            });

            // Observe all pricing cards
            document.querySelectorAll('.pricing-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>