/**
 * React Native JWT Authentication Service
 * Provides secure token storage, automatic refresh, and API request handling
 * 
 * Dependencies:
 * - @react-native-async-storage/async-storage
 * - react-native-keychain (for secure token storage)
 * - axios
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Keychain from 'react-native-keychain';
import axios from 'axios';

// Storage keys
const ACCESS_TOKEN_KEY = 'jwt_access_token';
const REFRESH_TOKEN_KEY = 'jwt_refresh_token';
const TOKEN_EXPIRY_KEY = 'jwt_token_expiry';
const USER_DATA_KEY = 'user_data';
const USER_TYPE_KEY = 'user_type';

// Keychain service name for secure storage
const KEYCHAIN_SERVICE = 'JWTAuthService';

// API endpoints for different user types
const API_ENDPOINTS = {
  students: {
    login: '/api/students/login/',
    refresh: '/api/students/token/refresh/',
    logout: '/api/students/logout/',
  },
  contributor: {
    login: '/api/contributor/login/',
    refresh: '/api/contributor/token/refresh/',
    logout: '/api/contributor/logout/',
  },
  customrcare: {
    login: '/api/customrcare/login/',
    refresh: '/api/customrcare/token/refresh/',
    logout: '/api/customrcare/logout/',
  },
};

class JWTAuthService {
  constructor() {
    this.refreshPromise = null;
    this.apiClient = null;
    this.userType = 'students';
    this.authStateListeners = [];
    
    this.initializeApiClient();
  }

  // Initialize axios instance with interceptors
  initializeApiClient() {
    this.apiClient = axios.create({
      baseURL: __DEV__ ? 'http://localhost:8000' : 'https://your-production-api.com',
      timeout: 10000,
    });

    // Request interceptor
    this.apiClient.interceptors.request.use(
      async (config) => {
        const tokens = await this.getStoredTokens();
        
        if (tokens.accessToken) {
          // Check if token is expired
          if (this.isTokenExpired(tokens.expiry)) {
            try {
              const newTokens = await this.refreshToken();
              config.headers.Authorization = `Bearer ${newTokens.access}`;
            } catch (error) {
              console.error('Failed to refresh token in request interceptor:', error);
            }
          } else {
            config.headers.Authorization = `Bearer ${tokens.accessToken}`;
          }
        }
        
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.apiClient.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;
        
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          const errorCode = error.response.data?.code;
          
          if (errorCode === 'TOKEN_EXPIRED' || errorCode === 'INVALID_TOKEN') {
            try {
              const newTokens = await this.refreshToken();
              originalRequest.headers.Authorization = `Bearer ${newTokens.access}`;
              return this.apiClient(originalRequest);
            } catch (refreshError) {
              console.error('Token refresh failed in response interceptor:', refreshError);
              await this.logout();
              return Promise.reject(refreshError);
            }
          }
        }
        
        return Promise.reject(error);
      }
    );
  }

  // Secure token storage using Keychain
  async storeTokensSecurely(tokenData) {
    try {
      // Store sensitive tokens in Keychain
      await Keychain.setInternetCredentials(
        KEYCHAIN_SERVICE,
        tokenData.access,
        tokenData.refresh
      );
      
      // Store non-sensitive data in AsyncStorage
      const expiryTime = Date.now() + (tokenData.expires_in - 60) * 1000;
      await AsyncStorage.multiSet([
        [TOKEN_EXPIRY_KEY, expiryTime.toString()],
        [USER_TYPE_KEY, this.userType],
      ]);
      
      return true;
    } catch (error) {
      console.error('Error storing tokens securely:', error);
      return false;
    }
  }

  // Retrieve tokens from secure storage
  async getStoredTokens() {
    try {
      // Get tokens from Keychain
      const credentials = await Keychain.getInternetCredentials(KEYCHAIN_SERVICE);
      
      if (!credentials || credentials === false) {
        return { accessToken: null, refreshToken: null, expiry: null };
      }
      
      // Get expiry from AsyncStorage
      const expiry = await AsyncStorage.getItem(TOKEN_EXPIRY_KEY);
      const userType = await AsyncStorage.getItem(USER_TYPE_KEY);
      
      if (userType) {
        this.userType = userType;
      }
      
      return {
        accessToken: credentials.username, // We store access token as username
        refreshToken: credentials.password, // We store refresh token as password
        expiry: expiry ? parseInt(expiry) : null,
      };
    } catch (error) {
      console.error('Error retrieving tokens:', error);
      return { accessToken: null, refreshToken: null, expiry: null };
    }
  }

  // Clear all stored authentication data
  async clearTokens() {
    try {
      await Promise.all([
        Keychain.resetInternetCredentials(KEYCHAIN_SERVICE),
        AsyncStorage.multiRemove([
          TOKEN_EXPIRY_KEY,
          USER_DATA_KEY,
          USER_TYPE_KEY,
        ]),
      ]);
    } catch (error) {
      console.error('Error clearing tokens:', error);
    }
  }

  // Check if token is expired
  isTokenExpired(expiry) {
    if (!expiry) return true;
    return Date.now() >= expiry;
  }

  // Refresh access token
  async refreshToken() {
    // Prevent multiple simultaneous refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this._performTokenRefresh();
    
    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.refreshPromise = null;
    }
  }

  async _performTokenRefresh() {
    try {
      const tokens = await this.getStoredTokens();
      
      if (!tokens.refreshToken) {
        throw new Error('No refresh token available');
      }

      const endpoints = API_ENDPOINTS[this.userType] || API_ENDPOINTS.students;
      const response = await axios.post(endpoints.refresh, {
        refresh: tokens.refreshToken,
      });

      const tokenData = response.data;
      
      // Store new tokens
      if (await this.storeTokensSecurely(tokenData)) {
        console.log('Tokens refreshed successfully');
        this.notifyAuthStateChange({ type: 'TOKEN_REFRESHED', data: tokenData });
        return tokenData;
      } else {
        throw new Error('Failed to store new tokens');
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      
      // Clear invalid tokens and logout
      await this.clearTokens();
      this.notifyAuthStateChange({ type: 'LOGOUT', reason: 'TOKEN_REFRESH_FAILED' });
      
      throw error;
    }
  }

  // Login function
  async login(credentials, userType = 'students') {
    try {
      this.userType = userType;
      const endpoints = API_ENDPOINTS[userType] || API_ENDPOINTS.students;
      
      const response = await axios.post(endpoints.login, credentials);
      const data = response.data;
      
      // Extract token data based on user type
      let tokenData;
      let userData;
      
      if (userType === 'students') {
        tokenData = data.JWT_Token;
        userData = data.student;
      } else if (userType === 'contributor') {
        tokenData = {
          access: data.access,
          refresh: data.refresh,
          expires_in: 900, // 15 minutes
        };
        userData = data.profile;
      } else if (userType === 'customrcare') {
        tokenData = {
          access: data.access,
          refresh: data.refresh,
          expires_in: 900, // 15 minutes
        };
        userData = data.user;
      }
      
      // Store tokens and user data
      const tokensStored = await this.storeTokensSecurely(tokenData);
      const userDataStored = await AsyncStorage.setItem(USER_DATA_KEY, JSON.stringify(userData));
      
      if (tokensStored) {
        this.notifyAuthStateChange({ 
          type: 'LOGIN_SUCCESS', 
          user: userData, 
          userType: userType 
        });
        
        return { success: true, user: userData, tokens: tokenData };
      } else {
        throw new Error('Failed to store authentication data');
      }
    } catch (error) {
      console.error('Login failed:', error);
      
      this.notifyAuthStateChange({ 
        type: 'LOGIN_FAILED', 
        error: error.response?.data || error.message 
      });
      
      return { 
        success: false, 
        error: error.response?.data?.message || 'Login failed' 
      };
    }
  }

  // Logout function
  async logout() {
    try {
      const tokens = await this.getStoredTokens();
      
      if (tokens.refreshToken) {
        const endpoints = API_ENDPOINTS[this.userType] || API_ENDPOINTS.students;
        
        // Attempt server logout
        try {
          await this.apiClient.post(endpoints.logout, {
            refresh: tokens.refreshToken,
          });
        } catch (error) {
          console.warn('Server logout failed, proceeding with local logout:', error);
        }
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local data
      await this.clearTokens();
      this.notifyAuthStateChange({ type: 'LOGOUT' });
    }
  }

  // Check authentication status
  async checkAuthStatus() {
    try {
      const tokens = await this.getStoredTokens();
      
      if (!tokens.accessToken || !tokens.refreshToken) {
        return { isAuthenticated: false, user: null };
      }
      
      // Get user data
      const userDataString = await AsyncStorage.getItem(USER_DATA_KEY);
      const userData = userDataString ? JSON.parse(userDataString) : null;
      
      // Check if token needs refresh
      if (this.isTokenExpired(tokens.expiry)) {
        try {
          await this.refreshToken();
        } catch (error) {
          console.error('Token refresh failed during auth check:', error);
          return { isAuthenticated: false, user: null };
        }
      }
      
      return { isAuthenticated: true, user: userData };
    } catch (error) {
      console.error('Auth status check failed:', error);
      return { isAuthenticated: false, user: null };
    }
  }

  // Auth state management
  addAuthStateListener(listener) {
    this.authStateListeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(listener);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  notifyAuthStateChange(event) {
    this.authStateListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in auth state listener:', error);
      }
    });
  }

  // Get API client instance
  getApiClient() {
    return this.apiClient;
  }
}

// Export singleton instance
export default new JWTAuthService();
