// Firebase Service Worker for background notifications
importScripts("https://www.gstatic.com/firebasejs/8.6.3/firebase-app.js");
importScripts("https://www.gstatic.com/firebasejs/8.6.3/firebase-messaging.js");

// Firebase configuration - using librainian-app project
const firebaseConfig = {
  apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
  authDomain: "librainian-app.firebaseapp.com",
  projectId: "librainian-app",
  storageBucket: "librainian-app.firebasestorage.app",
  messagingSenderId: "623132670328",
  appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
  measurementId: "G-XNDKJL6JWH"
};

firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

// Handle background messages (when page is not active)
messaging.setBackgroundMessageHandler(function (payload) {

  const notificationTitle = payload.notification?.title || 'New Notification';
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new message',
    icon: payload.notification?.icon || '/static/favicon.ico',
    badge: '/static/favicon.ico',
    tag: 'fcm-background',
    data: payload.data || {},
    requireInteraction: true,
    actions: [
      {
        action: 'open',
        title: 'Open App',
        icon: '/static/favicon.ico'
      },
      {
        action: 'close',
        title: 'Dismiss',
        icon: '/static/favicon.ico'
      }
    ]
  };

  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click events
self.addEventListener('notificationclick', function(event) {

  event.notification.close();

  if (event.action === 'close') {
    // Just close the notification
    return;
  }

  // Default action or 'open' action
  const urlToOpen = event.notification.data?.url || '/librarian/dashboard/';

  event.waitUntil(
    clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    }).then(function(clientList) {
      // Check if there's already a window/tab open with the target URL
      for (let i = 0; i < clientList.length; i++) {
        const client = clientList[i];
        if (client.url.includes('localhost:8000') && 'focus' in client) {
          return client.focus();
        }
      }

      // If no window is open, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
});

// Handle notification close events
self.addEventListener('notificationclose', function(event) {
  // Notification closed - no action needed
});
