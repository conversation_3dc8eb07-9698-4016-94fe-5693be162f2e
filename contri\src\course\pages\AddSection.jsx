import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Form, Container, Row, Col, Spinner } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { createSection } from "../../redux/slice/sectionSlice"; // Redux action to create a section
import { getTestPatterns } from "../../redux/slice/paperEngineSlice"; // Redux action to fetch test patterns
import { Link, useNavigate, useParams } from "react-router-dom";
import NavigationBar from "../../commonComponents/NavigationBar";
import { toast } from "react-hot-toast";
import AllSections from "../components/AllSections" 

const AddSection = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { paperSlug } = useParams(); // Get paperSlug from URL parameters
  const accessToken = useSelector((state) => state.contributor.accessToken);
  const { testPatterns, status: testPatternsStatus } = useSelector(
    (state) => state.paperEngine
  ); // Access test patterns from Redux store

  const [sectionName, setSectionName] = useState("");
  const [sectionDescription, setSectionDescription] = useState("");
  const [testPatternValue, setTestPatternValue] = useState(""); // State to hold selected test pattern ID
  const [maxMarks, setMaxMarks] = useState(""); // Max marks for the section
  const [numberOfQuestions, setNumberOfQuestions] = useState(""); // Number of questions in the section
  const [sectionAdded, setSectionAdded] = useState(false); // State to track section addition
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!accessToken) {
      toast.error("Please log in as a contributor!");
      const timer = setTimeout(() => {
        navigate("/contributor_login");
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [accessToken, navigate]);

  useEffect(() => {
    if (testPatternsStatus === "idle") {
      dispatch(getTestPatterns()); // Fetch test patterns when the component mounts
    }
  }, [dispatch, testPatternsStatus]);

  if (!accessToken) {
    return (
      <Container
        className="d-flex justify-content-center align-items-center text-success"
        style={{ height: "100vh" }}
      >
        <Spinner animation="border" />
      </Container>
    );
  }

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const sectionData = {
      paper: paperSlug,
      name: sectionName,
      description: sectionDescription,
      max_marks: parseInt(maxMarks, 10),
      number_of_questions: parseInt(numberOfQuestions, 10),
      ...(testPatternValue && testPatternValue !== "none" && { test_pattern: testPatternValue }),
    };

    dispatch(createSection({ data: sectionData }))
      .unwrap()
      .then(() => {
        setSectionName("");
        setSectionDescription("");
        setMaxMarks(""); // Reset max marks
        setNumberOfQuestions(""); // Reset number of questions
        setTestPatternValue(""); // Reset test pattern selection
        setSectionAdded(true); // Set sectionAdded to true
        toast.success("Section added successfully!");
        setTimeout(() => setSectionAdded(false), 2000); // Reset sectionAdded after delay
      })
      .catch((error) => {
        console.error("Error creating section:", error);
        toast.error("Failed to create section. Please try again.");
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  return (
    <>
      <NavigationBar />
      <Container>
        <Row>
          <Col xs={12} md={4} lg={4}>
            <Form
              onSubmit={handleSubmit}
              className="border mt-5 p-4 rounded shadow-lg"
            >
              <Form.Group controlId="sectionName" className="mb-3">
                <Form.Label>Section Name</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter section name"
                  value={sectionName}
                  onChange={(e) => setSectionName(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="sectionDescription" className="mb-3">
                <Form.Label>Section Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={4}
                  placeholder="Enter section description"
                  value={sectionDescription}
                  onChange={(e) => setSectionDescription(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="testPattern" className="mb-3">
                <Form.Label>Test Pattern</Form.Label>
                {testPatternsStatus === "loading" ? (
                  <Spinner animation="border" variant="primary" />
                ) : (
                  <Form.Control
                    as="select"
                    value={testPatternValue}
                    onChange={(e) => setTestPatternValue(e.target.value)}
                  >
                    <option value="" disabled>
                      Select a Test Pattern
                    </option>
                    <option value="none">None</option>
                    {testPatterns.map((pattern) => (
                      <option key={pattern.pattern_id} value={pattern.pattern_id}>
                        {pattern.name} (Version: {pattern.version})
                      </option>
                    ))}
                  </Form.Control>
                )}
              </Form.Group>
              <Row>
                <Col xs={6}>
                  <Form.Group controlId="maxMarks" className="mb-3">
                  <Form.Label>Max Marks</Form.Label>
                  <Form.Control
                    type="number"
                    placeholder="Enter max marks"
                    value={maxMarks}
                    onChange={(e) => setMaxMarks(e.target.value)}
                    required
                    />
                  </Form.Group>
                </Col>
                <Col>
                  <Form.Group controlId="numberOfQuestions" className="mb-3">
                  <Form.Label>Number of Questions</Form.Label>
                  <Form.Control
                    type="number"
                    placeholder="Enter number of questions"
                    value={numberOfQuestions}
                    onChange={(e) => setNumberOfQuestions(e.target.value)}
                    required
                    />
                  </Form.Group>
                </Col>
              </Row>          

              <Button
                variant="success"
                type="submit"
                className="w-100"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Add Section"}
              </Button>
            </Form>

            <div className="d-flex justify-content-center align-items-center">
              <Link to="/contributor_dashboard">
                <Button variant="secondary" className="mt-5 text-center">
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </Col>

          <Col xs={12} md={8} lg={8} className="border-left">
            <AllSections sectionAdded={sectionAdded} />
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default AddSection;
