#!/usr/bin/env python3
"""
Test different payment scenarios for Razorpay dashboard
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from packages_and_subscriptions.models import Package, Subscription
from students.models import Student

BASE_URL = "http://127.0.0.1:8000/api/packages"

def test_razorpay_config():
    """Test Razorpay configuration"""
    print("🔧 Testing Razorpay Configuration")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/razorpay-config/", timeout=10)
        if response.status_code == 200:
            config = response.json()
            print(f"✅ Razorpay Key: {config.get('razorpay_key')}")
            print(f"✅ Currency: {config.get('currency')}")
            print(f"✅ Company: {config.get('company_name')}")
            return True
        else:
            print(f"❌ Config Error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Config Failed: {e}")
        return False

def create_order_for_package(student_id, package_id, package_name, amount):
    """Create an order for a specific package"""
    print(f"\n💳 Creating order for {package_name} (₹{amount})")
    
    try:
        data = {
            'student': student_id,
            'package': package_id
        }
        
        response = requests.post(
            f"{BASE_URL}/v2/create-subscription/",
            json=data,
            timeout=15
        )
        
        if response.status_code == 201:
            result = response.json()
            order_id = result.get('razorpay_order_id')
            subscription_id = result.get('subscription_id')
            
            print(f"   ✅ Order Created!")
            print(f"   📋 Subscription ID: {subscription_id}")
            print(f"   💳 Razorpay Order ID: {order_id}")
            print(f"   💰 Amount: ₹{result.get('final_price')}")
            
            return {
                'success': True,
                'order_id': order_id,
                'subscription_id': subscription_id,
                'amount': result.get('final_price'),
                'package_name': package_name
            }
        else:
            print(f"   ❌ Failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return {'success': False}
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {'success': False}

def simulate_payment_verification(order_id, subscription_id, amount):
    """Simulate payment verification with test data"""
    print(f"\n🔐 Simulating payment verification for order {order_id}")
    
    # Generate mock payment data (for testing purposes)
    mock_payment_id = f"pay_mock_{order_id.split('_')[-1]}"
    mock_signature = "mock_signature_for_testing"
    
    try:
        data = {
            'razorpay_order_id': order_id,
            'razorpay_payment_id': mock_payment_id,
            'razorpay_signature': mock_signature,
            'subscription_id': subscription_id
        }
        
        response = requests.post(
            f"{BASE_URL}/v2/verify-payment/",
            json=data,
            timeout=15
        )
        
        print(f"   📊 Verification Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Payment Verified!")
            print(f"   📝 Message: {result.get('message')}")
            return True
        else:
            print(f"   ⚠️ Verification Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"   ❌ Verification Error: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive payment testing"""
    print("🧪 COMPREHENSIVE PAYMENT TESTING")
    print("=" * 50)
    
    # Test configuration first
    if not test_razorpay_config():
        print("❌ Razorpay configuration failed. Stopping tests.")
        return
    
    # Get test data
    try:
        students = list(Student.objects.all()[:3])
        packages = list(Package.objects.filter(is_active=True))
        
        if not students:
            print("❌ No students found for testing")
            return
        
        if not packages:
            print("❌ No packages found for testing")
            return
        
        print(f"\n📊 Test Data Available:")
        print(f"   Students: {len(students)}")
        print(f"   Packages: {len(packages)}")
        
    except Exception as e:
        print(f"❌ Failed to get test data: {e}")
        return
    
    # Test scenarios
    test_results = []
    
    print(f"\n🎯 TESTING DIFFERENT PACKAGE TYPES")
    print("=" * 50)
    
    # Test each package type
    for i, package in enumerate(packages[:5]):  # Test first 5 packages
        student = students[i % len(students)]  # Rotate through students
        
        print(f"\n📦 Test {i+1}: {package.name}")
        print(f"   Type: {package.package_type}")
        print(f"   Price: ₹{package.discount_price}")
        print(f"   Student: {student.user.username}")
        
        # Create order
        result = create_order_for_package(
            student.id, 
            package.id, 
            package.name, 
            package.discount_price
        )
        
        if result.get('success'):
            test_results.append(result)
            
            # For test mode, try payment verification
            if package.discount_price > 0:  # Only for paid packages
                simulate_payment_verification(
                    result['order_id'],
                    result['subscription_id'],
                    result['amount']
                )
    
    # Summary
    print(f"\n📈 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    successful_orders = [r for r in test_results if r.get('success')]
    
    print(f"✅ Orders Created: {len(successful_orders)}")
    print(f"💰 Total Test Amount: ₹{sum(r.get('amount', 0) for r in successful_orders)}")
    
    if successful_orders:
        print(f"\n🎯 ORDERS TO CHECK ON RAZORPAY DASHBOARD:")
        print("-" * 50)
        for i, order in enumerate(successful_orders, 1):
            print(f"{i}. {order['order_id']} - {order['package_name']} (₹{order['amount']})")
    
    print(f"\n🔗 Razorpay Dashboard URLs:")
    print(f"   Test Dashboard: https://dashboard.razorpay.com/")
    print(f"   Orders: https://dashboard.razorpay.com/app/orders")
    print(f"   Payments: https://dashboard.razorpay.com/app/payments")
    
    print(f"\n💡 What to look for on dashboard:")
    print(f"   - Orders in 'created' status")
    print(f"   - Correct amounts in paise (₹1 = 100 paise)")
    print(f"   - Student and package info in order notes")
    print(f"   - Timestamps matching test execution time")

if __name__ == "__main__":
    run_comprehensive_test()
