from django.contrib import admin
from django.utils import timezone
from django.contrib import messages
from datetime import timedelta
from .models import *

# Register your models here.


def test_membership_expiry_notification(modeladmin, request, queryset):
    """Admin action to test membership expiry notifications immediately"""
    try:
        from django.contrib.auth.models import User
        from librarian.notification_events import notification_events

        # Get librarians to notify
        librarians = User.objects.filter(
            groups__name__in=['Librarian', 'SubLibrarian']
        )

        if not librarians.exists():
            librarians = User.objects.filter(is_superuser=True)

        notifications_sent = 0
        today = timezone.now().date()

        for membership in queryset:
            days_diff = (membership.expiry_date - today).days

            # Determine event type based on days difference
            if days_diff == 10:
                event_type = 'member_expiry_10_days'
                description = 'expires in 10 days'
            elif days_diff == 5:
                event_type = 'member_expiry_5_days'
                description = 'expires in 5 days'
            elif days_diff == 1:
                event_type = 'member_expiry_1_day'
                description = 'expires tomorrow'
            elif days_diff == 0:
                event_type = 'member_expired'
                description = 'expires today'
            elif days_diff == -4:
                event_type = 'member_expired_4_days'
                description = 'expired 4 days ago'
            else:
                # Send a general test notification
                from librarian.notification_utils import send_notification_to_all_users

                if days_diff > 0:
                    status = f"expires in {days_diff} days"
                elif days_diff == 0:
                    status = "expires today"
                else:
                    status = f"expired {abs(days_diff)} days ago"

                result = send_notification_to_all_users(
                    title=f"🧪 Test: Membership {status.title()}",
                    body=f"TEST NOTIFICATION from Django Admin:\n\nMember: {membership.librarian.user.get_full_name() or membership.librarian.user.username}\nPlan: {membership.plan.name}\nExpiry: {membership.expiry_date.strftime('%Y-%m-%d')}\nStatus: {status}\nLibrary: {getattr(membership.librarian, 'library_name', 'Library')}\n\nThis is a test notification triggered from Django admin.",
                    data={
                        "type": "membership_test",
                        "member_id": membership.pk,
                        "expiry_date": membership.expiry_date.isoformat(),
                        "days_diff": days_diff,
                        "trigger_source": "admin_action"
                    }
                )

                if result and result.get('successful_count', 0) > 0:
                    notifications_sent += 1
                continue

            # Send standard notification
            result = notification_events.send_notification(
                event_type,
                librarians,
                member_name=membership.librarian.user.get_full_name() or membership.librarian.user.username,
                member_email=membership.librarian.user.email,
                member_mobile='Not available',
                course='Librarian Membership',
                expiry_date=membership.expiry_date.strftime('%Y-%m-%d'),
                days_info=description,
                member_id=membership.pk,
                unique_id=f"LIB_{membership.pk}",
                plan_name=membership.plan.name,
                library_name=getattr(membership.librarian, 'library_name', 'Library'),
                start_date=membership.start_date.strftime('%Y-%m-%d'),
                membership_duration=(membership.expiry_date - membership.start_date).days,
                trigger_source='admin_action'
            )

            notifications_sent += 1

        messages.success(
            request,
            f'✅ Test notifications sent for {notifications_sent} memberships! Check your browser for notifications.'
        )

    except Exception as e:
        messages.error(request, f'❌ Error sending test notifications: {e}')

test_membership_expiry_notification.short_description = "🧪 Test Expiry Notifications"


def set_expiry_to_today(modeladmin, request, queryset):
    """Admin action to set membership expiry to today for testing"""
    today = timezone.now().date()
    count = queryset.update(expiry_date=today)
    messages.success(
        request,
        f'✅ Set {count} memberships to expire today ({today}). Notifications will be triggered automatically!'
    )

set_expiry_to_today.short_description = "📅 Set Expiry to Today (Test)"


def set_expiry_to_tomorrow(modeladmin, request, queryset):
    """Admin action to set membership expiry to tomorrow for testing"""
    tomorrow = timezone.now().date() + timedelta(days=1)
    count = queryset.update(expiry_date=tomorrow)
    messages.success(
        request,
        f'✅ Set {count} memberships to expire tomorrow ({tomorrow}). Notifications will be triggered automatically!'
    )

set_expiry_to_tomorrow.short_description = "📅 Set Expiry to Tomorrow (Test)"


class MembershipAdmin(admin.ModelAdmin):
    list_display = ['librarian', 'plan', 'start_date', 'expiry_date', 'days_until_expiry']
    list_filter = ['plan', 'start_date', 'expiry_date']
    search_fields = ['librarian__user__username', 'librarian__user__email', 'plan__name']
    actions = [
        test_membership_expiry_notification,
        set_expiry_to_today,
        set_expiry_to_tomorrow,
    ]

    def days_until_expiry(self, obj):
        """Calculate days until membership expires"""
        from django.utils import timezone
        today = timezone.now().date()
        days = (obj.expiry_date - today).days

        if days > 10:
            return f"{days} days"
        elif days > 0:
            return f"⚠️ {days} days"
        elif days == 0:
            return "❌ Today"
        else:
            return f"🚨 {abs(days)} days ago"

    days_until_expiry.short_description = "Expiry Status"

    def save_model(self, request, obj, form, change):
        """Override save to show notification info"""
        super().save_model(request, obj, form, change)

        today = timezone.now().date()
        days_diff = (obj.expiry_date - today).days

        if days_diff in [10, 5, 1, 0, -4]:
            messages.info(
                request,
                f'💡 This membership will trigger automatic notifications! '
                f'Expiry: {obj.expiry_date} ({days_diff} days from today)'
            )
        else:
            messages.info(
                request,
                f'ℹ️ Membership saved. Expiry: {obj.expiry_date} ({days_diff} days from today). '
                f'Use "Test Expiry Notifications" action to test notifications.'
            )


# Register with custom admin
admin.site.register(Membership, MembershipAdmin)
admin.site.register(Plan)
admin.site.register(SmsPlan)
admin.site.register(Coupon)
