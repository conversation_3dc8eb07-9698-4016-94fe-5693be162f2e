from django.utils import timezone
from django.db import models
from django.contrib.auth.models import User
from manager.models import *
from django.utils.text import slugify
from django.urls import reverse
from django.core.validators import MaxValueValidator, MinValueValidator
import json


class Librarian_param(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    manager = models.ForeignKey(Manager_param, on_delete=models.CASCADE)
    library_name = models.CharField(max_length=225)
    librarian_phone_num = models.BigIntegerField(default=9999999999)
    librarian_role = models.CharField(max_length=225, default="Librarian")
    librarian_address = models.TextField()
    discount_available = models.BooleanField(default=False)
    discount_amount = models.IntegerField(default=0)
    description = models.TextField(null=True, blank=True)
    is_librarian = models.BooleanField(default=False)
    image = models.ImageField(upload_to="librarian_images/", null=True, blank=True)
    google_map_url = models.URLField(max_length=510, blank=True, null=True)
    slug = models.SlugField(max_length=250, blank=True)

    # Security Deposit Settings
    security_deposit_enabled = models.BooleanField(default=False, help_text="Enable security deposit requirement for students")
    security_deposit_amount = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(9999)],
        help_text="Security deposit amount (max 9999)"
    )

    # Proof of Identity Settings
    proof_of_identity_enabled = models.BooleanField(default=False, help_text="Enable proof of identity requirement for students")

    def __str__(self):
        return f"{self.library_name} | {self.user.first_name} {self.user.last_name} | {self.librarian_role}"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.user.username)

        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse("library-details", args=[str(self.slug)])


class QRCode(models.Model):
    librarian = models.OneToOneField(Librarian_param, on_delete=models.CASCADE)
    qr_code = models.TextField()  # Store the QR code as a base64 string

    def __str__(self):
        return f"{self.librarian.library_name} | QR Code"


class DailyTransaction(models.Model):
    librarian = models.ForeignKey(Librarian_param, on_delete=models.CASCADE)
    date = models.DateField(default=timezone.now)
    opening_balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    closing_balance_online = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )  # type: ignore
    closing_balance_cash = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    sales_cash = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_online = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    other_income_cash = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    other_income_online = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )  # type: ignore
    deposit = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_return_cash = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_return_online = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_expenses_cash = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_expenses_online = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    transaction_count = models.IntegerField(default=0)
    total_student_fees_collection = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    total_invoice_collection = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    total_daily_collection = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )

    def __str__(self):
        return f"Transaction Date : {self.date}"


def get_current_month():
    return timezone.now().strftime("%B")  # Returns the full month name, e.g., 'July'


def get_current_year():
    return timezone.now().year  # Returns the current year, e.g., 2024


class MonthlyTransaction(models.Model):
    librarian = models.ForeignKey(Librarian_param, on_delete=models.CASCADE)
    month = models.CharField(max_length=10, default=get_current_month)
    year = models.IntegerField(default=get_current_year)
    top_month = models.CharField(max_length=10, blank=True, null=True)
    low_month = models.CharField(max_length=10, blank=True, null=True)
    expensive_month = models.CharField(max_length=10, blank=True, null=True)
    affordable_month = models.CharField(max_length=10, blank=True, null=True)
    most_student_registration_month = models.CharField(
        max_length=10, blank=True, null=True
    )
    most_visitor_registration_month = models.CharField(
        max_length=10, blank=True, null=True
    )

    sales_cash_monthly = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_online_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_income_cash_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_income_online_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    deposit_monthly = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    sales_return_cash_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    sales_return_online_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_expenses_cash_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    other_expenses_online_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    transaction_count_monthly = models.IntegerField(default=0)
    total_student_fees_collection_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    total_invoice_collection_monthly = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    total_monthly_collection = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )

    def __str__(self):
        return f"Monthly Transaction : {self.month} {self.year}"


class ContactMessage(models.Model):
    library = models.ForeignKey(Librarian_param, on_delete=models.CASCADE)
    name = models.CharField(max_length=225)
    email = models.EmailField()
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    slug = models.SlugField(unique=True, max_length=255, blank=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            if not self.created_at:
                self.created_at = timezone.now()
            base_slug = slugify(
                f"{self.name}-{self.created_at.strftime('%Y%m%d%H%M%S')}"
            )
            slug = base_slug
            counter = 1
            while ContactMessage.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1
            self.slug = slug
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Message from {self.name} to {self.library.user.first_name}"


class ComplaintTicket(models.Model):
    ticket_number = models.CharField(
        max_length=20, unique=True, blank=True, editable=False
    )
    issue_type = models.CharField(max_length=20)
    librarian = models.ForeignKey(Librarian_param, on_delete=models.CASCADE)
    subject = models.CharField(max_length=255)
    description = models.TextField()
    status = models.CharField(max_length=20, default="Open")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.ticket_number:
            self.ticket_number = self.generate_unique_ticket_number()
        super().save(*args, **kwargs)

    def generate_unique_ticket_number(self):
        """
        Generate a unique incrementing ticket number.
        """
        last_ticket = ComplaintTicket.objects.order_by("id").last()
        if last_ticket:
            last_number = int(last_ticket.ticket_number.split("-")[-1])
            new_number = last_number + 1
        else:
            new_number = 1
        return f"TICKET-{new_number:04d}"

    def __str__(self):
        return f"Ticket {self.ticket_number} - {self.subject}"


class DeviceToken(models.Model):
    """
    Model to store Firebase Cloud Messaging device tokens
    Enhanced to support multiple devices per user
    """

    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="device_tokens"
    )
    token = models.CharField(max_length=500, unique=True)
    device_type = models.CharField(
        max_length=20, choices=[("android", "Android"), ("ios", "iOS"), ("web", "Web")]
    )
    device_name = models.CharField(max_length=100, blank=True, help_text="Device name or browser info")
    user_agent = models.TextField(blank=True, help_text="User agent string for web devices")
    is_active = models.BooleanField(default=True, help_text="Whether this token is still valid")
    last_used = models.DateTimeField(auto_now=True, help_text="Last time this token was used")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        device_info = f" ({self.device_name})" if self.device_name else ""
        return f"{self.user.username}'s {self.device_type} token{device_info}"

    def mark_invalid(self):
        """Mark this token as invalid and inactive"""
        self.is_active = False
        self.save()

    @classmethod
    def cleanup_invalid_tokens(cls):
        """Remove tokens that have been marked as invalid by FCM"""
        invalid_count = cls.objects.filter(is_active=False).count()
        cls.objects.filter(is_active=False).delete()
        return invalid_count

    @classmethod
    def get_active_tokens_for_user(cls, user):
        """Get all active tokens for a user"""
        return cls.objects.filter(user=user, is_active=True)

    class Meta:
        unique_together = ("user", "token")
        ordering = ['-last_used']


class NotificationCategory(models.Model):
    """
    Categories for different types of notifications
    """
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=100, default="🔔")
    color = models.CharField(max_length=7, default="#007bff")  # Hex color
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Notification Categories"


class NotificationTemplate(models.Model):
    """
    Templates for different notification types
    """
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    EVENT_TYPE_CHOICES = [
        ('qr_registration', 'QR Registration Notification'),
    ]

    event_type = models.CharField(max_length=50, choices=EVENT_TYPE_CHOICES, unique=True)
    category = models.ForeignKey(NotificationCategory, on_delete=models.CASCADE)
    title_template = models.CharField(max_length=200, help_text="Use {variable_name} for dynamic content")
    body_template = models.TextField(help_text="Use {variable_name} for dynamic content")
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='normal')
    icon_url = models.URLField(blank=True, null=True)
    action_url = models.CharField(max_length=200, blank=True, help_text="URL to open when notification is clicked")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.get_event_type_display()} - {self.title_template[:50]}"

    class Meta:
        ordering = ['event_type']


class NotificationHistory(models.Model):
    """
    Track all sent notifications
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('clicked', 'Clicked'),
    ]

    template = models.ForeignKey(NotificationTemplate, on_delete=models.CASCADE)
    recipient = models.ForeignKey(User, on_delete=models.CASCADE)
    title = models.CharField(max_length=200)
    body = models.TextField()
    data = models.JSONField(default=dict, blank=True)  # Additional data sent with notification
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    fcm_message_id = models.CharField(max_length=200, blank=True, null=True)
    error_message = models.TextField(blank=True, null=True)
    sent_at = models.DateTimeField(auto_now_add=True)
    delivered_at = models.DateTimeField(blank=True, null=True)
    clicked_at = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return f"{self.title} to {self.recipient.username} - {self.status}"

    class Meta:
        ordering = ['-sent_at']
        verbose_name_plural = "Notification History"


class UserNotificationPreference(models.Model):
    """
    User preferences for notifications
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='user_notification_preferences')
    categories = models.ManyToManyField(NotificationCategory, blank=True, help_text="Categories user wants to receive")
    email_notifications = models.BooleanField(default=True)
    push_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)
    quiet_hours_start = models.TimeField(default='22:00', help_text="Start of quiet hours (no notifications)")
    quiet_hours_end = models.TimeField(default='08:00', help_text="End of quiet hours")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s notification preferences"


class AnalyticsCache(models.Model):
    """
    Model to cache analytics data and track when it needs to be updated.
    This prevents unnecessary recalculations on every dashboard load.
    """
    librarian = models.OneToOneField(
        Librarian_param,
        on_delete=models.CASCADE,
        related_name='analytics_cache'
    )

    # Cached analytics data (stored as JSON)
    students_this_month = models.IntegerField(default=0)
    students_growth_percent = models.FloatField(default=0.0)
    students_growth_positive = models.BooleanField(default=True)

    # Total students growth data
    total_students_growth_percent = models.FloatField(default=0.0)
    total_students_growth_positive = models.BooleanField(default=True)

    # Gender-specific growth data
    male_growth_percent = models.FloatField(default=0.0)
    male_growth_positive = models.BooleanField(default=True)
    female_growth_percent = models.FloatField(default=0.0)
    female_growth_positive = models.BooleanField(default=True)

    new_registrations_this_month = models.IntegerField(default=0)
    registrations_growth_percent = models.FloatField(default=0.0)
    registrations_growth_positive = models.BooleanField(default=True)

    todays_collection = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Chart data (stored as JSON strings)
    growth_months = models.TextField(default='[]', help_text="JSON array of month names")
    growth_counts = models.TextField(default='[]', help_text="JSON array of student counts")

    revenue_months = models.TextField(default='[]', help_text="JSON array of month names")
    revenue_amounts = models.TextField(default='[]', help_text="JSON array of revenue amounts")

    visitor_days = models.TextField(default='[]', help_text="JSON array of day labels")
    visitor_counts = models.TextField(default='[]', help_text="JSON array of visitor counts")

    # Timestamps for tracking updates
    last_calculated = models.DateTimeField(auto_now=True)
    last_student_activity = models.DateTimeField(null=True, blank=True)
    last_invoice_activity = models.DateTimeField(null=True, blank=True)
    last_visitor_activity = models.DateTimeField(null=True, blank=True)

    # Force recalculation flag
    force_recalculate = models.BooleanField(default=False)

    class Meta:
        verbose_name = "Analytics Cache"
        verbose_name_plural = "Analytics Caches"

    def __str__(self):
        return f"Analytics Cache for {self.librarian.library_name}"

    def get_growth_months(self):
        """Return growth months as Python list"""
        try:
            return json.loads(self.growth_months)
        except (json.JSONDecodeError, TypeError):
            return []

    def set_growth_months(self, months_list):
        """Set growth months from Python list"""
        self.growth_months = json.dumps(months_list)

    def get_growth_counts(self):
        """Return growth counts as Python list"""
        try:
            return json.loads(self.growth_counts)
        except (json.JSONDecodeError, TypeError):
            return []

    def set_growth_counts(self, counts_list):
        """Set growth counts from Python list"""
        self.growth_counts = json.dumps(counts_list)

    def get_revenue_months(self):
        """Return revenue months as Python list"""
        try:
            return json.loads(self.revenue_months)
        except (json.JSONDecodeError, TypeError):
            return []

    def set_revenue_months(self, months_list):
        """Set revenue months from Python list"""
        self.revenue_months = json.dumps(months_list)

    def get_revenue_amounts(self):
        """Return revenue amounts as Python list"""
        try:
            return json.loads(self.revenue_amounts)
        except (json.JSONDecodeError, TypeError):
            return []

    def set_revenue_amounts(self, amounts_list):
        """Set revenue amounts from Python list"""
        self.revenue_amounts = json.dumps(amounts_list)

    def get_visitor_days(self):
        """Return visitor days as Python list"""
        try:
            return json.loads(self.visitor_days)
        except (json.JSONDecodeError, TypeError):
            return []

    def set_visitor_days(self, days_list):
        """Set visitor days from Python list"""
        self.visitor_days = json.dumps(days_list)

    def get_visitor_counts(self):
        """Return visitor counts as Python list"""
        try:
            return json.loads(self.visitor_counts)
        except (json.JSONDecodeError, TypeError):
            return []

    def set_visitor_counts(self, counts_list):
        """Set visitor counts from Python list"""
        self.visitor_counts = json.dumps(counts_list)

    def needs_recalculation(self):
        """
        Check if analytics data needs to be recalculated based on:
        1. Force recalculation flag
        2. New activity since last calculation
        3. Cache age (older than 1 hour)
        """
        if self.force_recalculate:
            return True

        # Check if cache is older than 1 hour
        if self.last_calculated:
            cache_age = timezone.now() - self.last_calculated
            if cache_age.total_seconds() > 3600:  # 1 hour
                return True

        # Check for new activity since last calculation
        if self.last_calculated:
            if (self.last_student_activity and
                self.last_student_activity > self.last_calculated):
                return True
            if (self.last_invoice_activity and
                self.last_invoice_activity > self.last_calculated):
                return True
            if (self.last_visitor_activity and
                self.last_visitor_activity > self.last_calculated):
                return True

        return False

    def mark_student_activity(self):
        """Mark that student-related activity occurred"""
        self.last_student_activity = timezone.now()
        self.save(update_fields=['last_student_activity'])

    def mark_invoice_activity(self):
        """Mark that invoice-related activity occurred"""
        self.last_invoice_activity = timezone.now()
        self.save(update_fields=['last_invoice_activity'])

    def mark_visitor_activity(self):
        """Mark that visitor-related activity occurred"""
        self.last_visitor_activity = timezone.now()
        self.save(update_fields=['last_visitor_activity'])

    def force_refresh(self):
        """Force recalculation on next access"""
        self.force_recalculate = True
        self.save(update_fields=['force_recalculate'])


# Import additional notification models
from .models_notifications import *
