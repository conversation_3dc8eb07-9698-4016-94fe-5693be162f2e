#!/usr/bin/env python3
"""
Views and API Testing Script for Course-Related Django Components
This script performs comprehensive testing of all course-related views and API endpoints.
"""

import os
import sys
import django
import traceback
import json
from datetime import timedelta

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Override ALLOWED_HOSTS for testing
from django.conf import settings
if 'testserver' not in settings.ALLOWED_HOSTS:
    settings.ALLOWED_HOSTS.append('testserver')

# Import after Django setup
from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from questions.models import (
    Course, SubCourse, Tier, Paper, Section, Module, Subject, Topic, SubTopic,
    MasterQuestion, MasterOption, Question, Option, PreviousYearQuestion
)
from contributor.models import ContributorProfile
from students.models import Student


class TestResults:
    """Class to track test results."""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"✅ PASS: {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"❌ FAIL: {test_name} - {error}")
    
    def summary(self):
        total = self.passed + self.failed
        print(f"\n{'='*60}")
        print(f"API VIEWS TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success Rate: {(self.passed/total*100):.1f}%" if total > 0 else "No tests run")
        
        if self.errors:
            print(f"\n{'='*60}")
            print(f"FAILED TESTS:")
            print(f"{'='*60}")
            for error in self.errors:
                print(f"- {error}")


def test_course_list_view():
    """Test CourseListView API endpoint."""
    results = TestResults()
    
    try:
        client = APIClient()
        
        # Setup test data
        course1 = Course.objects.create(name='Course 1', description='Description 1')
        course2 = Course.objects.create(name='Course 2', description='Description 2')
        
        # Test 1: GET courses list
        response = client.get('/api/questions/courses/')
        if response.status_code == 200:
            results.add_pass("Course list GET request")
            
            data = response.json()
            if len(data) >= 2:
                results.add_pass("Course list returns multiple courses")
            else:
                results.add_fail("Course list returns multiple courses", f"Expected >= 2, got {len(data)}")
        else:
            results.add_fail("Course list GET request", f"Status code: {response.status_code}")
        
        # Test 2: POST new course
        course_data = {
            'name': 'New Course',
            'description': 'New Description'
        }
        response = client.post('/api/questions/courses/', data=course_data)
        if response.status_code == 201:
            results.add_pass("Course creation POST request")
            
            data = response.json()
            if data['name'] == 'New Course':
                results.add_pass("Course creation returns correct data")
            else:
                results.add_fail("Course creation returns correct data", f"Expected 'New Course', got {data.get('name')}")
        else:
            results.add_fail("Course creation POST request", f"Status code: {response.status_code}")
        
        # Cleanup
        Course.objects.filter(name__in=['Course 1', 'Course 2', 'New Course']).delete()
        
    except Exception as e:
        results.add_fail("Course list view tests", f"Exception: {str(e)}")
    
    return results


def test_course_detail_view():
    """Test CourseDetailView API endpoint."""
    results = TestResults()
    
    try:
        client = APIClient()
        
        # Setup test data
        course = Course.objects.create(name='Test Course', description='Test Description')
        
        # Test 1: GET course detail
        response = client.get(f'/api/questions/courses/{course.slug}/')
        if response.status_code == 200:
            results.add_pass("Course detail GET request")
            
            data = response.json()
            if data['name'] == 'Test Course':
                results.add_pass("Course detail returns correct data")
            else:
                results.add_fail("Course detail returns correct data", f"Expected 'Test Course', got {data.get('name')}")
        else:
            results.add_fail("Course detail GET request", f"Status code: {response.status_code}")
        
        # Test 2: PUT course update
        update_data = {
            'name': 'Updated Course',
            'description': 'Updated Description'
        }
        response = client.put(f'/api/questions/courses/{course.slug}/', data=update_data)
        if response.status_code == 200:
            results.add_pass("Course update PUT request")
            
            data = response.json()
            if data['name'] == 'Updated Course':
                results.add_pass("Course update returns correct data")
            else:
                results.add_fail("Course update returns correct data", f"Expected 'Updated Course', got {data.get('name')}")
        else:
            results.add_fail("Course update PUT request", f"Status code: {response.status_code}")
        
        # Test 3: DELETE course
        response = client.delete(f'/api/questions/courses/{course.slug}/')
        if response.status_code == 200:
            results.add_pass("Course delete DELETE request")
            
            # Verify course is deleted
            if not Course.objects.filter(slug=course.slug).exists():
                results.add_pass("Course actually deleted")
            else:
                results.add_fail("Course actually deleted", "Course still exists")
        else:
            results.add_fail("Course delete DELETE request", f"Status code: {response.status_code}")
        
        # Test 4: GET non-existent course
        response = client.get('/api/questions/courses/non-existent-slug/')
        if response.status_code == 404:
            results.add_pass("Course detail 404 for non-existent course")
        else:
            results.add_fail("Course detail 404 for non-existent course", f"Status code: {response.status_code}")
        
    except Exception as e:
        results.add_fail("Course detail view tests", f"Exception: {str(e)}")
    
    return results


def test_subject_list_view():
    """Test SubjectListCreateView API endpoint."""
    results = TestResults()
    
    try:
        client = APIClient()
        
        # Setup test data
        subject1 = Subject.objects.create(name='Mathematics', rank=1)
        subject2 = Subject.objects.create(name='Physics', rank=2)
        
        # Test 1: GET subjects list
        response = client.get('/api/questions/subjects/')
        if response.status_code == 200:
            results.add_pass("Subject list GET request")
            
            data = response.json()
            if 'data' in data and len(data['data']) >= 2:
                results.add_pass("Subject list returns multiple subjects")
                
                # Check if subjects are ordered by rank
                subjects = data['data']
                if subjects[0]['rank'] <= subjects[1]['rank']:
                    results.add_pass("Subject list ordered by rank")
                else:
                    results.add_fail("Subject list ordered by rank", "Subjects not ordered correctly")
            else:
                results.add_fail("Subject list returns multiple subjects", f"Expected >= 2, got {len(data.get('data', []))}")
        else:
            results.add_fail("Subject list GET request", f"Status code: {response.status_code}")
        
        # Test 2: POST new subject (requires authentication)
        # Create a user for authentication
        import uuid
        unique_username = f'testuser_{uuid.uuid4().hex[:8]}'
        user = User.objects.create_user(username=unique_username, password='test123')
        client.force_authenticate(user=user)
        
        subject_data = {
            'name': 'Chemistry',
            'description': 'Chemistry subject',
            'rank': 3
        }
        response = client.post('/api/questions/subjects/', data=subject_data)
        if response.status_code == 201:
            results.add_pass("Subject creation POST request")
            
            data = response.json()
            if data['data']['name'] == 'Chemistry':
                results.add_pass("Subject creation returns correct data")
            else:
                results.add_fail("Subject creation returns correct data", f"Expected 'Chemistry', got {data.get('data', {}).get('name')}")
        else:
            results.add_fail("Subject creation POST request", f"Status code: {response.status_code}")
        
        # Cleanup
        Subject.objects.filter(name__in=['Mathematics', 'Physics', 'Chemistry']).delete()
        user.delete()
        
    except Exception as e:
        results.add_fail("Subject list view tests", f"Exception: {str(e)}")
    
    return results


def test_subject_detail_view():
    """Test SubjectDetailView API endpoint."""
    results = TestResults()
    
    try:
        client = APIClient()
        
        # Setup test data
        subject = Subject.objects.create(name='Mathematics', description='Math subject', rank=1)
        
        # Test 1: GET subject detail
        response = client.get(f'/api/questions/subjects/{subject.slug}/')
        if response.status_code == 200:
            results.add_pass("Subject detail GET request")
            
            data = response.json()
            if data['data']['name'] == 'Mathematics':
                results.add_pass("Subject detail returns correct data")
            else:
                results.add_fail("Subject detail returns correct data", f"Expected 'Mathematics', got {data.get('data', {}).get('name')}")
        else:
            results.add_fail("Subject detail GET request", f"Status code: {response.status_code}")
        
        # Test 2: PUT subject update
        update_data = {
            'name': 'Advanced Mathematics',
            'description': 'Advanced Math subject',
            'rank': 1
        }
        response = client.put(f'/api/questions/subjects/{subject.slug}/', data=update_data)
        if response.status_code == 200:
            results.add_pass("Subject update PUT request")
            
            data = response.json()
            if data['data']['name'] == 'Advanced Mathematics':
                results.add_pass("Subject update returns correct data")
            else:
                results.add_fail("Subject update returns correct data", f"Expected 'Advanced Mathematics', got {data.get('data', {}).get('name')}")
        else:
            results.add_fail("Subject update PUT request", f"Status code: {response.status_code}")
        
        # Test 3: DELETE subject
        response = client.delete(f'/api/questions/subjects/{subject.slug}/')
        if response.status_code == 204:
            results.add_pass("Subject delete DELETE request")
            
            # Verify subject is deleted
            if not Subject.objects.filter(slug=subject.slug).exists():
                results.add_pass("Subject actually deleted")
            else:
                results.add_fail("Subject actually deleted", "Subject still exists")
        else:
            results.add_fail("Subject delete DELETE request", f"Status code: {response.status_code}")
        
    except Exception as e:
        results.add_fail("Subject detail view tests", f"Exception: {str(e)}")
    
    return results


def test_courses_with_subcourses_view():
    """Test CourseWithSubCoursesAPIView endpoint."""
    results = TestResults()
    
    try:
        client = APIClient()
        
        # Setup test data
        course1 = Course.objects.create(name='Course 1')
        course2 = Course.objects.create(name='Course 2')
        subcourse1 = SubCourse.objects.create(course=course1, name='SubCourse 1')
        subcourse2 = SubCourse.objects.create(course=course2, name='SubCourse 2')
        
        # Test 1: POST with course slugs
        request_data = {
            'course_slugs': [course1.slug, course2.slug]
        }
        response = client.post('/api/questions/courses-with-sub-courses/', data=request_data, format='json')
        if response.status_code == 200:
            results.add_pass("Courses with subcourses POST request")
            
            data = response.json()
            if len(data) == 2:
                results.add_pass("Courses with subcourses returns correct count")
                
                # Check if subcourses are included
                has_subcourses = any('sub_courses' in course and len(course['sub_courses']) > 0 for course in data)
                if has_subcourses:
                    results.add_pass("Courses with subcourses includes subcourses")
                else:
                    results.add_fail("Courses with subcourses includes subcourses", "No subcourses found")
            else:
                results.add_fail("Courses with subcourses returns correct count", f"Expected 2, got {len(data)}")
        else:
            results.add_fail("Courses with subcourses POST request", f"Status code: {response.status_code}")
        
        # Test 2: POST with empty course_slugs
        request_data = {'course_slugs': []}
        response = client.post('/api/questions/courses-with-sub-courses/', data=request_data, format='json')
        if response.status_code == 400:
            results.add_pass("Courses with subcourses empty slugs validation")
        else:
            results.add_fail("Courses with subcourses empty slugs validation", f"Status code: {response.status_code}")
        
        # Cleanup
        SubCourse.objects.filter(course__in=[course1, course2]).delete()
        Course.objects.filter(name__in=['Course 1', 'Course 2']).delete()
        
    except Exception as e:
        results.add_fail("Courses with subcourses view tests", f"Exception: {str(e)}")
    
    return results


def main():
    """Run all view and API tests."""
    print("🚀 Starting Comprehensive Views and API Tests")
    print("="*60)
    
    all_results = TestResults()
    
    # Run all test functions
    test_functions = [
        test_course_list_view,
        test_course_detail_view,
        test_subject_list_view,
        test_subject_detail_view,
        test_courses_with_subcourses_view
    ]
    
    for test_func in test_functions:
        print(f"\n📋 Running {test_func.__name__}...")
        try:
            result = test_func()
            all_results.passed += result.passed
            all_results.failed += result.failed
            all_results.errors.extend(result.errors)
        except Exception as e:
            all_results.add_fail(test_func.__name__, f"Test function failed: {str(e)}")
            print(f"💥 Error in {test_func.__name__}: {str(e)}")
            traceback.print_exc()
    
    # Print final summary
    all_results.summary()


if __name__ == '__main__':
    main()
