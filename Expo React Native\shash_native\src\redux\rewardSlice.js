import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import Constants from 'expo-constants';

// Get baseURL from Expo constants
const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;

// Helper to get token
const getAuthToken = (getState) => {
  const state = getState();
  return state?.auth?.JWT_Token?.access;
};

// Get all scratch cards
export const getAllScratchCards = createAsyncThunk(
  'reward/getAllScratchCards',
  async (_, { getState, rejectWithValue }) => {
    try {      const accessToken = getAuthToken(getState);
      if (!accessToken) {
        throw new Error('No access token found');
      }
      const response = await axios.get(`${baseURL}api/students/scratch-cards/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch scratch cards');
    }
  }
);

// Scratch a scratch card
export const scratchScratchCard = createAsyncThunk(
  'reward/scratchScratchCard',
  async (cardId, { getState, rejectWithValue }) => {
    try {      const accessToken = getAuthToken(getState);
      if (!accessToken) {
        throw new Error('No access token found');
      }
      const response = await axios.post(
        `${baseURL}api/students/scratch-cards/scratch/`,
        { card_id: cardId },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to scratch card');
    }
  }
);

// Get wallet data
export const getWalletData = createAsyncThunk(
  'reward/getWalletData',
  async (_, { getState, rejectWithValue }) => {
    try {      const accessToken = getAuthToken(getState);
      if (!accessToken) {
        throw new Error('No access token found');
      }
      const response = await axios.get(`${baseURL}api/wallet/wallet-dashboard/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch wallet data');
    }
  }
);

// Raise withdrawal request
export const raiseWithdrawalRequest = createAsyncThunk(
  'reward/raiseWithdrawalRequest',
  async (data, { getState, rejectWithValue }) => {
    try {      const accessToken = getAuthToken(getState);
      if (!accessToken) {
        throw new Error('No access token found');
      }
      const response = await axios.post(
        `${baseURL}api/wallet/withdraw/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to raise withdrawal request');
    }
  }
);

const rewardSlice = createSlice({
  name: 'reward',
  initialState: {
    loading: false,
    error: null,
    scratchedResult: null,
    withdrawalStatus: null,
    walletData: null,
    scratchCards: [],
  },reducers: {
    resetScratchedResult: (state) => {
      state.scratchedResult = null;
      state.error = null;
    },
    resetWithdrawalStatus: (state) => {
      state.withdrawalStatus = null;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get All Scratch Cards
      .addCase(getAllScratchCards.pending, (state) => {
        state.loading = true;
        state.error = null;
      })      .addCase(getAllScratchCards.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.scratchCards = action.payload;
      })
      .addCase(getAllScratchCards.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.scratchCards = [];
      })

      // Scratch a Scratch Card
      .addCase(scratchScratchCard.pending, (state) => {
        state.loading = true;
        state.error = null;
      })      .addCase(scratchScratchCard.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.scratchedResult = action.payload;
      })
      .addCase(scratchScratchCard.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get Wallet Data
      .addCase(getWalletData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })      .addCase(getWalletData.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.walletData = action.payload;
      })
      .addCase(getWalletData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Raise Withdrawal Request
      .addCase(raiseWithdrawalRequest.pending, (state) => {
        state.loading = true;
        state.error = null;
      })      .addCase(raiseWithdrawalRequest.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.withdrawalStatus = action.payload;
      })
      .addCase(raiseWithdrawalRequest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { resetScratchedResult, resetWithdrawalStatus, clearError } = rewardSlice.actions;
export default rewardSlice.reducer;
