"""
Django management command to check membership expiry notifications
This command should be run daily via cron job to check for membership expiries
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Check membership expiry and send notifications to librarians'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually sending notifications',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output',
        )
        parser.add_argument(
            '--days',
            type=int,
            help='Check specific number of days (e.g., --days 10 for 10 days before expiry)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        verbose = options['verbose']
        specific_days = options.get('days')
        
        self.stdout.write('MEMBERSHIP EXPIRY NOTIFICATION CHECK')
        self.stdout.write('=' * 50)
        self.stdout.write(f'Current time: {timezone.now()}')
        self.stdout.write(f'Mode: {"DRY RUN" if dry_run else "LIVE"}')
        
        try:
            from membership.models import Membership
            from librarian.notification_events import notification_events
            from django.contrib.auth.models import User
            
            today = timezone.now().date()
            total_notifications = 0
            
            # Get librarians who will receive notifications
            librarians = User.objects.filter(
                groups__name__in=['Librarian', 'SubLibrarian']
            )

            # If no librarians found in groups, get all superusers as fallback
            if not librarians.exists():
                librarians = User.objects.filter(is_superuser=True)
                if verbose:
                    self.stdout.write('⚠️ No librarians found in groups, using superusers as fallback')
            
            if verbose:
                self.stdout.write(f'\nLibrarians to notify: {librarians.count()}')
                for librarian in librarians:
                    self.stdout.write(f'   - {librarian.username} ({librarian.email})')
            
            # Define expiry periods
            expiry_periods = [
                (10, 'member_expiry_10_days', '10 days before expiry'),
                (5, 'member_expiry_5_days', '5 days before expiry'),
                (1, 'member_expiry_1_day', '1 day before expiry'),
                (0, 'member_expired', 'expires today'),
                (-4, 'member_expired_4_days', '4 days after expiry')
            ]
            
            # If specific days specified, only check that period
            if specific_days is not None:
                expiry_periods = [(specific_days, f'member_expiry_{abs(specific_days)}_days', f'{abs(specific_days)} days {"before" if specific_days >= 0 else "after"} expiry')]
            
            for days, event_type, description in expiry_periods:
                target_date = today + timedelta(days=days)
                
                self.stdout.write(f'\nChecking {description} ({target_date}):')

                # Find memberships for this target date
                memberships = Membership.objects.filter(expiry_date=target_date)

                self.stdout.write(f'   Found {memberships.count()} memberships')

                if verbose and memberships.exists():
                    for membership in memberships:
                        self.stdout.write(f'   ID: {membership.pk} | Librarian: {membership.librarian.user.username} | Plan: {membership.plan.name}')
                
                if not dry_run and memberships.exists():
                    # Send notifications for each membership
                    for membership in memberships:
                        try:
                            # Send notification to all librarians
                            from librarian.notification_events import notification_events

                            # Send notification using the notification events system
                            notification_result = notification_events.send_notification(
                                event_type,
                                librarians,
                                member_name=membership.librarian.user.get_full_name() or membership.librarian.user.username,
                                member_email=membership.librarian.user.email,
                                member_mobile='Not available',
                                course='Librarian Membership',
                                expiry_date=membership.expiry_date.strftime('%Y-%m-%d'),
                                days_info=description,
                                member_id=membership.pk,
                                unique_id=f"LIB_{membership.pk}",
                                plan_name=membership.plan.name,
                                library_name=getattr(membership.librarian, 'library_name', 'Library'),
                                start_date=membership.start_date.strftime('%Y-%m-%d'),
                                membership_duration=(membership.expiry_date - membership.start_date).days
                            )
                            
                            if notification_result:
                                total_notifications += 1
                                self.stdout.write(f'   SUCCESS: Notification sent for {membership.librarian.user.username}')
                            else:
                                self.stdout.write(f'   FAILED: Failed to send notification for {membership.librarian.user.username}')

                        except Exception as e:
                            self.stdout.write(f'   ERROR: Error sending notification for membership {membership.pk}: {e}')
                            logger.error(f'Error sending membership expiry notification: {e}')

                elif dry_run and memberships.exists():
                    self.stdout.write(f'   DRY RUN: Would send {memberships.count()} notifications')
                    total_notifications += memberships.count()
            
            # Summary
            self.stdout.write('\n' + '=' * 50)
            self.stdout.write('SUMMARY')
            self.stdout.write('=' * 50)

            if dry_run:
                self.stdout.write(f'DRY RUN: Would send {total_notifications} notifications')
            else:
                self.stdout.write(f'LIVE: Sent {total_notifications} notifications')

            self.stdout.write(f'Checked date: {today}')
            self.stdout.write(f'Librarians notified: {librarians.count()}')

            if total_notifications > 0:
                self.stdout.write('SUCCESS: Membership expiry notifications processed!')
            else:
                self.stdout.write('INFO: No memberships expiring in the checked periods')
            
            # Usage instructions
            self.stdout.write('\nUSAGE INSTRUCTIONS:')
            self.stdout.write('   - Run daily: python manage.py check_membership_expiry')
            self.stdout.write('   - Dry run: python manage.py check_membership_expiry --dry-run')
            self.stdout.write('   - Verbose: python manage.py check_membership_expiry --verbose')
            self.stdout.write('   - Specific: python manage.py check_membership_expiry --days 10')

            # Cron job suggestion
            self.stdout.write('\nSCHEDULED JOB SETUP:')
            self.stdout.write('   Add this to your task scheduler to run daily:')
            self.stdout.write('   python manage.py check_membership_expiry')
            
        except Exception as e:
            self.stdout.write(f'❌ Command failed: {e}')
            logger.error(f'Membership expiry check command failed: {e}')
            raise e
