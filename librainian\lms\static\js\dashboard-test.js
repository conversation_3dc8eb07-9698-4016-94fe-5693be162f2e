/**
 * Dashboard Analytics Testing Suite
 * 
 * This script provides testing utilities for the dashboard analytics system.
 * It validates UI rendering, responsiveness, and data accuracy.
 */

// Dashboard Testing Namespace
const DashboardTest = {
    // Test status tracking
    testResults: {
        passed: 0,
        failed: 0,
        warnings: 0
    },
    
    // Test UI rendering and data accuracy
    testUI: function() {
        console.log('%c📊 Testing Dashboard UI...', 'font-size: 14px; font-weight: bold; color: #10b981;');
        
        // Reset test results
        this.testResults = { passed: 0, failed: 0, warnings: 0 };
        
        // Test card rendering
        this.testCardRendering();
        
        // Test chart rendering
        this.testChartRendering();
        
        // Test responsive layout
        this.testResponsiveLayout();
        
        // Test data accuracy
        this.testDataAccuracy();
        
        // Report results
        this.reportResults();
    },
    
    // Test card rendering
    testCardRendering: function() {
        console.log('Testing card rendering...');
        
        // Test cards exist
        const cards = document.querySelectorAll('.stats-card');
        this.assert(cards.length >= 4, 'All dashboard cards are rendered', 'Missing dashboard cards');
        
        // Test card content
        cards.forEach(card => {
            const value = card.querySelector('.stats-value');
            const label = card.querySelector('.stats-label');
            const trend = card.querySelector('.stats-trend');
            
            this.assert(value, `Card has value: ${value ? value.textContent : 'missing'}`, 'Card missing value');
            this.assert(label, `Card has label: ${label ? label.textContent : 'missing'}`, 'Card missing label');
            this.assert(trend, `Card has trend: ${trend ? trend.textContent.trim() : 'missing'}`, 'Card missing trend');
        });
        
        // Test loading states
        const loadingCards = document.querySelectorAll('.stats-card.loading');
        this.assert(loadingCards.length === 0, 'No cards in loading state', 'Cards still in loading state');
    },
    
    // Test chart rendering
    testChartRendering: function() {
        console.log('Testing chart rendering...');
        
        // Test revenue chart
        const revenueChart = document.getElementById('monthlySalesChart');
        this.assert(revenueChart, 'Revenue chart exists', 'Revenue chart missing');
        
        if (revenueChart) {
            const chartInstance = Chart.getChart(revenueChart);
            this.assert(chartInstance, 'Revenue chart initialized', 'Revenue chart not initialized');
            
            if (chartInstance) {
                this.assert(chartInstance.data.datasets.length > 0, 'Revenue chart has datasets', 'Revenue chart missing datasets');
                this.assert(chartInstance.data.labels.length > 0, 'Revenue chart has labels', 'Revenue chart missing labels');
            }
        }
        
        // Test student growth chart
        const growthChart = document.getElementById('studentGrowthChart');
        this.assert(growthChart, 'Student growth chart exists', 'Student growth chart missing');
        
        if (growthChart) {
            const chartInstance = Chart.getChart(growthChart);
            this.assert(chartInstance, 'Student growth chart initialized', 'Student growth chart not initialized');
        }
        
        // Test visitor analytics chart
        const visitorChart = document.getElementById('visitorAnalyticsChart');
        this.assert(visitorChart, 'Visitor analytics chart exists', 'Visitor analytics chart missing');
    },
    
    // Test responsive layout
    testResponsiveLayout: function() {
        console.log('Testing responsive layout...');
        
        // Get current viewport width
        const width = window.innerWidth;
        console.log(`Current viewport width: ${width}px`);
        
        // Test card layout
        const cards = document.querySelectorAll('.stats-card');
        
        if (width < 768) {
            // Mobile layout tests
            cards.forEach(card => {
                const computedStyle = window.getComputedStyle(card);
                const cardWidth = parseFloat(computedStyle.width);
                
                this.assert(
                    cardWidth > width * 0.7, 
                    `Card width (${cardWidth.toFixed(0)}px) is appropriate for mobile viewport`, 
                    `Card width (${cardWidth.toFixed(0)}px) may be too small for mobile`
                );
            });
            
            // Test chart responsiveness
            const charts = document.querySelectorAll('canvas');
            charts.forEach(chart => {
                const computedStyle = window.getComputedStyle(chart);
                const chartWidth = parseFloat(computedStyle.width);
                
                this.assert(
                    chartWidth > width * 0.7,
                    `Chart width (${chartWidth.toFixed(0)}px) is appropriate for mobile viewport`,
                    `Chart width (${chartWidth.toFixed(0)}px) may be too small for mobile`
                );
            });
        } else {
            // Desktop layout tests
            this.assert(
                cards.length > 0 && cards[0].offsetWidth < width * 0.5,
                'Cards have appropriate width for desktop',
                'Cards may be too wide for desktop layout'
            );
        }
    },
    
    // Test data accuracy
    testDataAccuracy: function() {
        console.log('Testing data accuracy...');
        
        // Test students this month
        const studentsMonthValue = document.querySelector('[data-card="students-month"] .stats-value');
        if (studentsMonthValue) {
            const value = parseInt(studentsMonthValue.textContent.replace(/[^\d]/g, '')) || 0;
            this.assert(value >= 0, `Students this month (${value}) is valid`, `Invalid students this month value: ${value}`);
            
            // Test growth indicator
            const trendElement = studentsMonthValue.closest('.stats-card').querySelector('.stats-trend small');
            if (trendElement) {
                const isPositive = trendElement.classList.contains('text-success');
                const isNegative = trendElement.classList.contains('text-danger');
                const trendText = trendElement.textContent.trim();
                
                if (trendText.includes('+')) {
                    this.assert(isPositive, 'Positive trend has correct styling', 'Positive trend has incorrect styling');
                } else if (trendText.includes('-')) {
                    this.assert(isNegative, 'Negative trend has correct styling', 'Negative trend has incorrect styling');
                }
            }
        }
        
        // Test today's collection
        const todaysCollectionValue = document.querySelector('[data-card="todays-collection"] .stats-value');
        if (todaysCollectionValue) {
            const hasRupeeSymbol = todaysCollectionValue.textContent.includes('₹');
            this.assert(hasRupeeSymbol, 'Today\'s collection shows currency symbol', 'Today\'s collection missing currency symbol');
        }
    },
    
    // Assert helper
    assert: function(condition, successMessage, failureMessage) {
        if (condition) {
            console.log(`%c✅ ${successMessage}`, 'color: #10b981');
            this.testResults.passed++;
        } else {
            console.log(`%c❌ ${failureMessage}`, 'color: #ef4444');
            this.testResults.failed++;
        }
    },
    
    // Report test results
    reportResults: function() {
        const total = this.testResults.passed + this.testResults.failed + this.testResults.warnings;
        const passRate = (this.testResults.passed / total * 100).toFixed(1);
        
        console.log('%c📊 Dashboard Test Results:', 'font-size: 14px; font-weight: bold;');
        console.log(`%c✅ Passed: ${this.testResults.passed}`, 'color: #10b981');
        console.log(`%c❌ Failed: ${this.testResults.failed}`, 'color: #ef4444');
        console.log(`%c⚠️ Warnings: ${this.testResults.warnings}`, 'color: #f59e0b');
        console.log(`%c📈 Pass Rate: ${passRate}%`, 'font-weight: bold; color: ' + (passRate >= 90 ? '#10b981' : '#f59e0b'));
        
        return {
            passed: this.testResults.passed,
            failed: this.testResults.failed,
            warnings: this.testResults.warnings,
            passRate: passRate
        };
    },
    
    // Simulate data changes to test reactivity
    simulateDataChanges: function() {
        console.log('%c🔄 Simulating data changes...', 'font-size: 14px; font-weight: bold; color: #3b82f6;');
        
        // Simulate new data
        const newData = {
            students_this_month: Math.floor(Math.random() * 100) + 50,
            new_registrations_this_month: Math.floor(Math.random() * 50) + 20,
            todays_collection: Math.floor(Math.random() * 10000) + 5000
        };
        
        console.log('New data:', newData);
        
        // Update dashboard if updateDashboardData function exists
        if (typeof updateDashboardData === 'function') {
            updateDashboardData(newData);
            console.log('%c✅ Dashboard updated with simulated data', 'color: #10b981');
        } else {
            console.log('%c❌ updateDashboardData function not found', 'color: #ef4444');
        }
    }
};

// Add test button to dashboard (only in development)
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        const dashboardContent = document.querySelector('.dashboard-content');
        if (dashboardContent) {
            const testButton = document.createElement('button');
            testButton.className = 'btn-primary-modern mt-4';
            testButton.innerHTML = '<i class="fas fa-vial me-2"></i>Run Dashboard Tests';
            testButton.style.position = 'fixed';
            testButton.style.bottom = '20px';
            testButton.style.right = '20px';
            testButton.style.zIndex = '1000';
            
            testButton.addEventListener('click', function() {
                DashboardTest.testUI();
            });
            
            dashboardContent.appendChild(testButton);
            
            // Add simulation button
            const simulateButton = document.createElement('button');
            simulateButton.className = 'btn-secondary-modern mt-4';
            simulateButton.innerHTML = '<i class="fas fa-random me-2"></i>Simulate Data Changes';
            simulateButton.style.position = 'fixed';
            simulateButton.style.bottom = '20px';
            simulateButton.style.right = '200px';
            simulateButton.style.zIndex = '1000';
            
            simulateButton.addEventListener('click', function() {
                DashboardTest.simulateDataChanges();
            });
            
            dashboardContent.appendChild(simulateButton);
        }
    }
});
