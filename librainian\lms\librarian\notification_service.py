"""
Comprehensive Notification Service for LMS
This service provides a unified interface for sending notifications across different events.
"""

from django.contrib.auth.models import User
from django.utils import timezone
from .notification_utils import (
    send_event_notification,
    send_bulk_event_notifications,
    notify_qr_registration,
)
import logging

logger = logging.getLogger(__name__)


class NotificationService:
    """
    Main notification service class that provides easy-to-use methods
    for sending notifications for various events in the LMS system.
    """
    
    @staticmethod
    def send_notification(event_type, recipient, **kwargs):
        """
        Generic method to send any type of notification
        
        :param event_type: Type of event (from NotificationTemplate.EVENT_TYPE_CHOICES)
        :param recipient: User instance, user ID, or username
        :param kwargs: Context data for the notification template
        :return: NotificationHistory instance or None
        """
        return send_event_notification(event_type, recipient, kwargs)
    
    @staticmethod
    def send_bulk_notification(event_type, recipients, context_data=None, force_send=False):
        """
        Send the same notification to multiple recipients
        
        :param event_type: Type of event
        :param recipients: List of User instances or user IDs
        :param context_data: Context data (dict or list of dicts)
        :param force_send: Ignore user preferences if True
        :return: List of NotificationHistory instances
        """
        return send_bulk_event_notifications(event_type, recipients, context_data, force_send)
    
    # QR Registration Notification
    @staticmethod
    def qr_registration_submitted(temp_student, recipient=None):
        """Send notification when someone submits registration via QR code"""
        # Send notification to the librarian
        if recipient is None:
            if temp_student.librarian:
                recipient = temp_student.librarian.user
            else:
                logger.warning(f"No librarian found for QR registration notification: {temp_student.name}")
                return None

        if not recipient:
            logger.warning(f"No recipient found for QR registration notification: {temp_student.name}")
            return None

        context_data = {
            'student_name': temp_student.name,
            'student_email': temp_student.email or 'Not provided',
            'student_mobile': str(temp_student.mobile) if temp_student.mobile else 'Not provided',
            'course': temp_student.course.name if temp_student.course else 'Not specified',
            'student_city': temp_student.city or 'Not provided',
            'student_age': temp_student.age or 'Not provided',
            'student_gender': temp_student.gender or 'Not provided',
            'library_name': temp_student.librarian.library_name if temp_student.librarian else 'Library',
            'registration_date': temp_student.registration_date.strftime('%Y-%m-%d') if temp_student.registration_date else 'Not set',
            'temp_student_id': temp_student.pk,
        }

        return send_event_notification('qr_registration', recipient, context_data)


# Convenience instance for easy importing
notification_service = NotificationService()
