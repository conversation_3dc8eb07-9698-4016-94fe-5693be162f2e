#!/usr/bin/env python3
"""
Firebase Credentials Setup Helper
This script helps you get the real Firebase service account credentials.
"""

import os
import json
import webbrowser
from urllib.parse import quote

def open_firebase_console():
    """Open Firebase Console in browser"""
    project_id = "librainian-app"
    url = f"https://console.firebase.google.com/project/{project_id}/settings/serviceaccounts/adminsdk"
    
    print(f"🌐 Opening Firebase Console for project: {project_id}")
    print(f"📍 URL: {url}")
    
    try:
        webbrowser.open(url)
        print("✅ Browser opened successfully")
    except Exception as e:
        print(f"❌ Could not open browser: {e}")
        print(f"📋 Please manually visit: {url}")

def show_step_by_step_guide():
    """Show detailed step-by-step guide"""
    print("\n" + "="*60)
    print("🔥 FIREBASE SERVICE ACCOUNT SETUP GUIDE")
    print("="*60)
    
    print("\n📋 Step-by-Step Instructions:")
    print("\n1️⃣ **Open Firebase Console**")
    print("   • The browser should open automatically")
    print("   • If not, visit: https://console.firebase.google.com/")
    print("   • Select project: 'librainian-app'")
    
    print("\n2️⃣ **Navigate to Service Accounts**")
    print("   • Click the ⚙️ gear icon (Project Settings)")
    print("   • Click 'Service Accounts' tab")
    print("   • You should see 'Firebase Admin SDK' section")
    
    print("\n3️⃣ **Generate Private Key**")
    print("   • Click 'Generate new private key' button")
    print("   • A dialog will appear asking for confirmation")
    print("   • Click 'Generate key' to confirm")
    print("   • A JSON file will be downloaded automatically")
    
    print("\n4️⃣ **Save the Credentials File**")
    print("   • Find the downloaded JSON file (usually in Downloads folder)")
    print("   • The filename will be like: 'librainian-app-firebase-adminsdk-xxxxx-xxxxxxxxxx.json'")
    print("   • Copy this file to your project directory")
    print("   • Rename it to: 'firebase-service-account.json'")
    
    print("\n5️⃣ **Verify File Location**")
    print("   • The file should be in the same folder as 'manage.py'")
    print("   • Full path should be: ./firebase-service-account.json")
    
    print("\n6️⃣ **Restart Django Server**")
    print("   • Stop your current Django server (Ctrl+C)")
    print("   • Start it again: python manage.py runserver")
    print("   • Look for 'Firebase initialized' in the logs")

def check_current_setup():
    """Check current Firebase setup"""
    print("\n" + "="*60)
    print("🔍 CURRENT SETUP STATUS")
    print("="*60)
    
    # Check if service account file exists
    service_account_file = "firebase-service-account.json"
    if os.path.exists(service_account_file):
        print(f"✅ Service account file found: {service_account_file}")
        
        # Check if it's the dummy file
        try:
            with open(service_account_file, 'r') as f:
                data = json.load(f)
                if data.get('private_key_id') == 'dev-key-id-12345':
                    print("⚠️  WARNING: This is a dummy/template file!")
                    print("   You need to replace it with real Firebase credentials")
                    return False
                else:
                    print("✅ Appears to be a real service account file")
                    print(f"   Project ID: {data.get('project_id', 'Unknown')}")
                    print(f"   Client Email: {data.get('client_email', 'Unknown')}")
                    return True
        except Exception as e:
            print(f"❌ Error reading service account file: {e}")
            return False
    else:
        print(f"❌ Service account file not found: {service_account_file}")
        return False

def show_troubleshooting():
    """Show troubleshooting tips"""
    print("\n" + "="*60)
    print("🔧 TROUBLESHOOTING")
    print("="*60)
    
    print("\n❓ Common Issues:")
    print("\n🔸 'Your default credentials were not found'")
    print("   → You need to download the real Firebase service account key")
    print("   → The dummy file I created won't work")
    
    print("\n🔸 'Firebase not available, using mock response'")
    print("   → Firebase service account file is missing or invalid")
    print("   → Check file location and content")
    
    print("\n🔸 'Permission denied' or 'Invalid credentials'")
    print("   → Service account key might be corrupted")
    print("   → Download a fresh key from Firebase Console")
    
    print("\n🔸 'Project not found'")
    print("   → Make sure project ID is 'librainian-app'")
    print("   → Check if you have access to the Firebase project")
    
    print("\n💡 Quick Fixes:")
    print("   1. Delete the dummy firebase-service-account.json file")
    print("   2. Download fresh credentials from Firebase Console")
    print("   3. Rename and place in project root")
    print("   4. Restart Django server")
    print("   5. Test with: python test_firebase.py")

def main():
    print("🚀 Firebase Credentials Setup Helper")
    print("This will help you get real Firebase credentials for notifications")
    
    # Check current setup
    has_real_credentials = check_current_setup()
    
    if has_real_credentials:
        print("\n🎉 You already have real Firebase credentials!")
        print("If notifications still don't work, restart your Django server.")
        return
    
    # Show guide
    show_step_by_step_guide()
    
    # Ask if user wants to open Firebase Console
    print("\n" + "="*60)
    response = input("🌐 Open Firebase Console now? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        open_firebase_console()
        print("\n⏳ Complete the steps in the browser, then come back here...")
        input("📋 Press Enter when you've downloaded the JSON file...")
        
        # Check again
        if check_current_setup():
            print("\n🎉 Great! Firebase credentials are now set up!")
            print("🔄 Please restart your Django server to apply changes:")
            print("   1. Stop server: Ctrl+C")
            print("   2. Start server: python manage.py runserver")
            print("   3. Test notifications: http://localhost:8000/fcm-test/")
        else:
            print("\n❌ Credentials not found. Please follow the troubleshooting guide.")
            show_troubleshooting()
    else:
        print("\n📋 Manual Setup:")
        print("   Visit: https://console.firebase.google.com/project/librainian-app/settings/serviceaccounts/adminsdk")
        print("   Follow the step-by-step guide above")
    
    show_troubleshooting()

if __name__ == "__main__":
    main()
