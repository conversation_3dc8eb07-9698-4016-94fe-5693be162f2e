import React from "react";
import { Container, <PERSON>, Col, ListGroup, Form, Button, InputGroup } from "react-bootstrap";
import { FaSearch } from "react-icons/fa";

const Footer = () => {
  return (
    <footer className="bg-dark text-white pt-5 pb-4" id="footer">
      <Container className="px-3"> 
        <Row className="justify-content-between">
          {/* Left Column: Links */}
          <Col md={4} xs={12} className="mb-md-0 mb-4">
            <h5>Quick Links</h5>
            <ListGroup variant="flush">
              <ListGroup.Item action href="#" className="bg-dark text-white">
                Terms of Use
              </ListGroup.Item>
              <ListGroup.Item action href="#" className="bg-dark text-white">
                Contact Us
              </ListGroup.Item>
              <ListGroup.Item action href="#" className="bg-dark text-white">
                Parent Company
              </ListGroup.Item>
              <ListGroup.Item action href="#" className="bg-dark text-white">
                About Us
              </ListGroup.Item>
              <ListGroup.Item action href="#" className="bg-dark text-white">
                Privacy Policy
              </ListGroup.Item>
              <ListGroup.Item action href="#" className="bg-dark text-white">
                User Policy
              </ListGroup.Item>
              <ListGroup.Item action href="#" className="bg-dark text-white">
                Refer and Earn
              </ListGroup.Item>
              <ListGroup.Item action href="#" className="bg-dark text-white">
                Download Our App
              </ListGroup.Item>
            </ListGroup>
          </Col>

          {/* Center Column: Address and Email */}
          <Col md={4} xs={12} className="text-md-center mb-md-0 mb-4">
            <h5>Contact Info</h5>
            <p>Address: 1234 Example St, City, Country</p>
            <p>Email: <EMAIL></p>
          </Col>

          {/* Right Column: Follow Us and Search */}
          <Col md={4} xs={12} className="mb-md-0 mb-4">
            <h5>Follow Us</h5>
            <p>Follow us on social media to stay updated!</p>
            <Button variant="outline-light" className="me-2">Facebook</Button>
            <Button variant="outline-light" className="me-2">Twitter</Button>
            <Button variant="outline-light" className="me-2">Instagram</Button>
            <br />
            <h5 className="mt-4">Search</h5>
            <InputGroup>
              <Form.Control
                placeholder="Search..."
                aria-label="Search"
                aria-describedby="basic-addon2"
              />
              <Button variant="outline-light" id="button-addon2">
                <FaSearch />
              </Button>
            </InputGroup>
          </Col>
        </Row>

        {/* Bottom Row: Copyright */}
        <Row className="mt-4">
          <Col className="text-center">
            <p className="pb-2">&copy; 2025 shashtrarth. All Rights Reserved.</p>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default Footer;
