{"name": "s<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.2", "main": "index.js", "scripts": {"start": "react-native start", "android": "react-native run-android", "ios": "react-native run-ios", "clean": "react-native clean", "reset-cache": "react-native start --reset-cache"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-masked-view/masked-view": "^0.3.2", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/drawer": "^7.4.1", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.13", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "lottie-react-native": "^7.2.2", "react": "19.0.0", "react-native": "^0.79.4", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.0.3", "react-native-view-shot": "^3.8.0", "react-native-webview": "^13.8.6", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "^13.6.6", "@react-native-community/cli-platform-android": "^13.6.6", "@react-native/metro-config": "^0.80.1"}, "private": true}