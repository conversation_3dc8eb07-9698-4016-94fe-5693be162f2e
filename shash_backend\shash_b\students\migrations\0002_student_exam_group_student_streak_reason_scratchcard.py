# Generated by Django 5.1.1 on 2025-07-23 09:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customrcare', '0002_examgrouplevel_walkaroundimage_and_more'),
        ('students', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='student',
            name='exam_group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customrcare.examgrouplevel'),
        ),
        migrations.AddField(
            model_name='student',
            name='streak_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='ScratchCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.IntegerField(default=0)),
                ('is_scratched', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('scratched_at', models.DateTimeField(blank=True, null=True)),
                ('WithdrawalRequest', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scratch_cards', to='students.withdrawalrequest')),
                ('referral', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scratch_cards', to='students.referral')),
                ('referrer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scratch_cards', to='students.student')),
            ],
        ),
    ]
