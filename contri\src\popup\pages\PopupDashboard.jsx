import React, { useState, useEffect } from "react";
import {
  Row,
  Col,
  Container,
  DropdownButton,
  Dropdown,
  Card,
  Form,
  Alert,
  Spinner
} from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchPopups,
  createPopup,
  deletePopup,
  updatePopup,
} from "../../Redux/slice/popupSlice";
import CreatePopupCard from "../components/CreatePopupCard";
import PopupCard from "../components/PopupCard";
import NavigationBar from "../../commonComponents/NavigationBar";
import toast from "react-hot-toast";
import Skeleton from "react-loading-skeleton";
import PaginationComponent from "../../commonComponents/PaginationComponent";
import Swal from "sweetalert2";

const PopupDashboard = () => {
  const dispatch = useDispatch();
  const { isLoading, error } = useSelector((state) => state.popups);
  
  const [allPopups, setAllPopups] = useState([]);
  const [filteredPopups, setFilteredPopups] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [contentTypeFilter, setContentTypeFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(6);

  useEffect(() => {
    fetchPopupsData();
  }, []);

  useEffect(() => {
    filterPopups();
  }, [allPopups, searchTerm, statusFilter, priorityFilter, contentTypeFilter]);

  const fetchPopupsData = async () => {
    try {
      const response = await dispatch(fetchPopups());
      if (response?.meta?.requestStatus === "fulfilled") {
        setAllPopups(response.payload || []);
      }
    } catch (error) {
      console.error("Error fetching popups:", error);
      toast.error("Failed to fetch popups");
    }
  };

  const filterPopups = () => {
    let filtered = [...allPopups];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(popup =>
        popup.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        popup.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(popup => popup.approval_status === statusFilter);
    }

    // Priority filter
    if (priorityFilter !== "all") {
      filtered = filtered.filter(popup => popup.priority === priorityFilter);
    }

    // Content type filter
    if (contentTypeFilter !== "all") {
      filtered = filtered.filter(popup => popup.content_type === contentTypeFilter);
    }

    setFilteredPopups(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleCreatePopup = async (popupData) => {
    try {
      const response = await dispatch(createPopup(popupData));
      if (response?.meta?.requestStatus === "fulfilled") {
        toast.success("Popup created successfully!");
        fetchPopupsData();
      } else {
        toast.error("Failed to create popup");
      }
    } catch (error) {
      console.error("Error creating popup:", error);
      toast.error(error?.message || "Failed to create popup. Please try again!");
    }
  };

  const handleUpdatePopup = async (id, popupData) => {
    try {
      const response = await dispatch(updatePopup({ id, popupData }));
      if (response?.meta?.requestStatus === "fulfilled") {
        toast.success("Popup updated successfully!");
        fetchPopupsData();
      } else {
        toast.error("Failed to update popup");
      }
    } catch (error) {
      console.error("Error updating popup:", error);
      toast.error(error?.message || "Failed to update popup. Please try again!");
    }
  };

  const handleDeletePopup = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        const response = await dispatch(deletePopup(id));
        if (response?.meta?.requestStatus === "fulfilled") {
          toast.success("Popup deleted successfully!");
          fetchPopupsData();
        } else {
          toast.error("Failed to delete popup");
        }
      } catch (error) {
        console.error("Error deleting popup:", error);
        toast.error(error?.message || "Failed to delete popup. Please try again!");
      }
    }
  };

  const handleDropdownChange = (eventKey) => {
    setItemsPerPage(parseInt(eventKey));
    setCurrentPage(1);
  };

  // Pagination
  const totalPages = Math.ceil(filteredPopups.length / itemsPerPage);
  const indexOfLastPopup = currentPage * itemsPerPage;
  const indexOfFirstPopup = indexOfLastPopup - itemsPerPage;
  const currentPopups = filteredPopups.slice(indexOfFirstPopup, indexOfLastPopup);

  return (
    <>
      <NavigationBar />
      <Container>
        <Row className="mt-5">
          <Col md={4}>
            <CreatePopupCard 
              onCreatePopup={handleCreatePopup}
              isLoading={isLoading}
            />
            {error && (
              <Alert variant="danger" className="mt-3">
                {typeof error === 'string' ? error : 'Something went wrong!'}
              </Alert>
            )}
          </Col>
          
          <Col md={8}>
            {/* Search and Filters */}
            <Card className="mb-3 shadow rounded-3">
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Form.Control
                      type="text"
                      placeholder="Search by title or description"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </Col>
                  <Col md={6}>
                    <DropdownButton
                      id="dropdown-items-per-page"
                      title={`Items per page: ${itemsPerPage}`}
                      variant="success"
                      onSelect={handleDropdownChange}
                    >
                      {[6, 12, 18, 24, 30].map((number) => (
                        <Dropdown.Item key={number} eventKey={number}>
                          {number}
                        </Dropdown.Item>
                      ))}
                    </DropdownButton>
                  </Col>
                </Row>
                
                <Row className="mt-3">
                  <Col md={4}>
                    <Form.Select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                    >
                      <option value="all">All Status</option>
                      <option value="pending">Pending</option>
                      <option value="approved">Approved</option>
                      <option value="rejected">Rejected</option>
                    </Form.Select>
                  </Col>
                  <Col md={4}>
                    <Form.Select
                      value={priorityFilter}
                      onChange={(e) => setPriorityFilter(e.target.value)}
                    >
                      <option value="all">All Priority</option>
                      <option value="high">High</option>
                      <option value="medium">Medium</option>
                      <option value="low">Low</option>
                    </Form.Select>
                  </Col>
                  <Col md={4}>
                    <Form.Select
                      value={contentTypeFilter}
                      onChange={(e) => setContentTypeFilter(e.target.value)}
                    >
                      <option value="all">All Types</option>
                      <option value="text_only">Text Only</option>
                      <option value="image_only">Image Only</option>
                      <option value="text_image">Text + Image</option>
                      <option value="text_link">Text + Link</option>
                      <option value="link_anchor">Link Anchor</option>
                    </Form.Select>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Results Summary */}
            <div className="d-flex justify-content-between align-items-center mb-3">
              <span className="text-muted">
                Showing {indexOfFirstPopup + 1}-{Math.min(indexOfLastPopup, filteredPopups.length)} of {filteredPopups.length} popups
              </span>
            </div>

            {/* Popup Cards */}
            {isLoading ? (
              <Row>
                {[...Array(6)].map((_, index) => (
                  <Col key={index} md={6} className="mb-3">
                    <Card className="shadow rounded-3">
                      <Card.Body>
                        <div className="d-flex flex-column align-items-center">
                          <Skeleton
                            width="70%"
                            height={20}
                            baseColor="#e6ffe6"
                            highlightColor="#c4f7c4"
                          />
                          <div className="d-flex justify-content-center w-100 mt-2">
                            <Skeleton
                              width={60}
                              height={30}
                              baseColor="#e6ffe6"
                              highlightColor="#c4f7c4"
                              className="m-1"
                            />
                            <Skeleton
                              width={60}
                              height={30}
                              baseColor="#e6ffe6"
                              highlightColor="#c4f7c4"
                              className="m-1"
                            />
                          </div>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            ) : currentPopups.length > 0 ? (
              <Row>
                {currentPopups.map((popup) => (
                  <Col key={popup.id} md={6} className="mb-3">
                    <PopupCard
                      popup={popup}
                      onUpdatePopup={handleUpdatePopup}
                      onDeletePopup={handleDeletePopup}
                      isLoading={isLoading}
                    />
                  </Col>
                ))}
              </Row>
            ) : (
              <Card className="text-center py-5">
                <Card.Body>
                  <h5 className="text-muted">No popups found</h5>
                  <p className="text-muted">
                    {searchTerm || statusFilter !== "all" || priorityFilter !== "all" || contentTypeFilter !== "all"
                      ? "Try adjusting your search or filters"
                      : "Create your first popup banner to get started"
                    }
                  </p>
                </Card.Body>
              </Card>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="d-flex justify-content-center mt-4">
                <PaginationComponent
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                />
              </div>
            )}
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default PopupDashboard;
