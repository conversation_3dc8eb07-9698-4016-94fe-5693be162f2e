import React, { useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/FontAwesome';
import { ThemeContext } from '../context/ThemeContext';

const CartSummary = ({ 
  onCheckout, 
  showCheckoutButton = true,
  showDiscounts = true,
  appliedCoupon = null,
  appliedGiftCard = null,
  discount = 0,
  discountType = 'amount',
  checkoutButtonText = 'Proceed to Checkout',
  loading = false,
  items: propItems,
  total: propTotal,
  itemCount: propItemCount
}) => {
  const { isDarkMode } = useContext(ThemeContext);
  // Use props if provided, else fallback to redux
  const reduxCart = useSelector((state) => state.cart);
  const items = propItems !== undefined ? propItems : reduxCart.items;
  const total = propTotal !== undefined ? propTotal : reduxCart.total;
  const itemCount = propItemCount !== undefined ? propItemCount : reduxCart.itemCount;

  const formatPrice = (price) => {
    return `₹${price.toLocaleString('en-IN')}`;
  };

  const calculateDiscount = () => {
    if (!discount) return 0;
    return discountType === 'amount' ? discount : (total * discount / 100);
  };

  const discountAmount = calculateDiscount();
  const finalTotal = Math.max(0, total - discountAmount);
  const taxRate = 0;
  const taxAmount = finalTotal * taxRate;
  const grandTotal = finalTotal + taxAmount;

  if (itemCount === 0) {
    return null;
  }

  return (
    <View style={[
      styles.container,
      isDarkMode && styles.containerDark
    ]}>
      <View style={styles.header}>
        <Text style={[
          styles.title,
          isDarkMode && styles.textDark
        ]}>
          Order Summary
        </Text>
        <View style={styles.itemCount}>
          <Icon
            name="shopping-cart"
            size={16}
            color={isDarkMode ? '#4CAF50' : '#198754'}
          />
          <Text style={[
            styles.itemCountText,
            isDarkMode && styles.textDark
          ]}>
            {itemCount} {itemCount === 1 ? 'item' : 'items'}
          </Text>
        </View>
      </View>

      <View style={styles.divider} />

      <View style={styles.summaryRow}>
        <Text style={[
          styles.summaryLabel,
          isDarkMode && styles.textSecondaryDark
        ]}>
          Subtotal
        </Text>
        <Text style={[
          styles.summaryValue,
          isDarkMode && styles.textDark
        ]}>
          {formatPrice(total)}
        </Text>
      </View>

      {showDiscounts && appliedCoupon && (
        <View style={styles.summaryRow}>
          <View style={styles.discountInfo}>
            <Text style={[
              styles.summaryLabel,
              styles.discountLabel
            ]}>
              Coupon ({appliedCoupon})
            </Text>
            <Icon name="tag" size={12} color="#198754" />
          </View>
          <Text style={[
            styles.summaryValue,
            styles.discountValue
          ]}>
            -{formatPrice(discountAmount)}
          </Text>
        </View>
      )}

      {showDiscounts && appliedGiftCard && (
        <View style={styles.summaryRow}>
          <View style={styles.discountInfo}>
            <Text style={[
              styles.summaryLabel,
              styles.discountLabel
            ]}>
              Gift Card
            </Text>
            <Icon name="gift" size={12} color="#198754" />
          </View>
          <Text style={[
            styles.summaryValue,
            styles.discountValue
          ]}>
            -{formatPrice(discountAmount)}
          </Text>
        </View>
      )}

      {taxAmount > 0 && (
        <View style={styles.summaryRow}>
          <Text style={[
            styles.summaryLabel,
            isDarkMode && styles.textSecondaryDark
          ]}>
            Tax
          </Text>
          <Text style={[
            styles.summaryValue,
            isDarkMode && styles.textDark
          ]}>
            {formatPrice(taxAmount)}
          </Text>
        </View>
      )}

      <View style={[
        styles.divider,
        styles.totalDivider
      ]} />

      <View style={styles.summaryRow}>
        <Text style={[
          styles.totalLabel,
          isDarkMode && styles.textDark
        ]}>
          Total
        </Text>
        <Text style={[
          styles.totalValue,
          isDarkMode && styles.textDark
        ]}>
          {formatPrice(grandTotal)}
        </Text>
      </View>

      {showCheckoutButton && (
        <TouchableOpacity
          style={[
            styles.checkoutButton,
            isDarkMode && styles.checkoutButtonDark,
            loading && styles.checkoutButtonDisabled
          ]}
          onPress={onCheckout}
          disabled={loading || itemCount === 0}
          activeOpacity={0.8}
        >
          <Text style={styles.checkoutButtonText}>
            {loading ? 'Processing...' : checkoutButtonText}
          </Text>
          {!loading && (
            <Icon
              name="arrow-right"
              size={16}
              color="#fff"
              style={styles.checkoutIcon}
            />
          )}
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    margin: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  containerDark: {
    backgroundColor: '#1e1e1e',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
  },
  itemCount: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemCountText: {
    fontSize: 14,
    marginLeft: 6,
    color: '#666',
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 12,
  },
  totalDivider: {
    backgroundColor: '#198754',
    height: 2,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 6,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#666',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  discountInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  discountLabel: {
    color: '#198754',
    marginRight: 6,
  },
  discountValue: {
    color: '#198754',
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
  },
  totalValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#198754',
  },
  textDark: {
    color: '#fff',
  },
  textSecondaryDark: {
    color: '#999',
  },
  checkoutButton: {
    backgroundColor: '#198754',
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  checkoutButtonDark: {
    backgroundColor: '#4CAF50',
  },
  checkoutButtonDisabled: {
    backgroundColor: '#ccc',
    opacity: 0.6,
  },
  checkoutButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
  },
  checkoutIcon: {
    marginLeft: 8,
  },
});

export default CartSummary;
