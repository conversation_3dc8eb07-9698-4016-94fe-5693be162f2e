import React, { useContext, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/FontAwesome';
import { ThemeContext } from '../context/ThemeContext';
import { getOrderHistory } from '../redux/subscriptionSlice';
import { LoadingSpinner } from '../components/LoadingSpinner';

const OrderHistoryScreen = ({ navigation }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const { orderHistory, loading, error } = useSelector((state) => state.subscription);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchOrderHistory();
  }, []);

  const fetchOrderHistory = async () => {
    try {
      await dispatch(getOrderHistory()).unwrap();
    } catch (error) {
      Alert.alert('Error', 'Failed to fetch order history');
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchOrderHistory();
    setRefreshing(false);
  };

  const handleOrderPress = (order) => {
    navigation.navigate('OrderDetailsScreen', { orderId: order.id });
  };

  const formatPrice = (price) => {
    return `₹${price.toLocaleString('en-IN')}`;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'paid':
        return '#198754';
      case 'pending':
        return '#ffc107';
      case 'failed':
      case 'cancelled':
        return '#dc3545';
      default:
        return isDarkMode ? '#999' : '#666';
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'paid':
        return 'check-circle';
      case 'pending':
        return 'clock-o';
      case 'failed':
      case 'cancelled':
        return 'times-circle';
      default:
        return 'info-circle';
    }
  };

  const renderOrderItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.orderCard,
        isDarkMode && styles.orderCardDark
      ]}
      onPress={() => handleOrderPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.orderHeader}>
        <View style={styles.orderInfo}>
          <Text style={[
            styles.orderId,
            isDarkMode && styles.textDark
          ]}>
            Order #{item.id || item.order_number}
          </Text>
          <Text style={[
            styles.orderDate,
            isDarkMode && styles.orderDateDark
          ]}>
            {formatDate(item.created_at || item.date)}
          </Text>
        </View>
        <View style={[
          styles.statusBadge,
          { backgroundColor: getStatusColor(item.status) }
        ]}>
          <Icon
            name={getStatusIcon(item.status)}
            size={12}
            color="#fff"
            style={styles.statusIcon}
          />
          <Text style={styles.statusText}>
            {item.status?.toUpperCase() || 'UNKNOWN'}
          </Text>
        </View>
      </View>

      <View style={styles.orderDetails}>
        <View style={styles.orderRow}>
          <Text style={[
            styles.orderLabel,
            isDarkMode && styles.orderLabelDark
          ]}>
            Items:
          </Text>
          <Text style={[
            styles.orderValue,
            isDarkMode && styles.textDark
          ]}>
            {item.items_count || item.items?.length || 1}
          </Text>
        </View>

        <View style={styles.orderRow}>
          <Text style={[
            styles.orderLabel,
            isDarkMode && styles.orderLabelDark
          ]}>
            Total:
          </Text>
          <Text style={[
            styles.orderValue,
            styles.orderAmount,
            isDarkMode && styles.textDark
          ]}>
            {formatPrice(item.total_amount || item.amount)}
          </Text>
        </View>

        {item.payment_method && (
          <View style={styles.orderRow}>
            <Text style={[
              styles.orderLabel,
              isDarkMode && styles.orderLabelDark
            ]}>
              Payment:
            </Text>
            <Text style={[
              styles.orderValue,
              isDarkMode && styles.textDark
            ]}>
              {item.payment_method}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.orderFooter}>
        <Text style={[
          styles.viewDetailsText,
          isDarkMode && styles.viewDetailsTextDark
        ]}>
          Tap to view details
        </Text>
        <Icon
          name="chevron-right"
          size={14}
          color={isDarkMode ? '#666' : '#999'}
        />
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={[
      styles.emptyContainer,
      isDarkMode && styles.emptyContainerDark
    ]}>
      <Icon
        name="shopping-bag"
        size={80}
        color={isDarkMode ? '#555' : '#ccc'}
      />
      <Text style={[
        styles.emptyTitle,
        isDarkMode && styles.emptyTextDark
      ]}>
        No Orders Yet
      </Text>
      <Text style={[
        styles.emptySubtitle,
        isDarkMode && styles.emptySubtitleDark
      ]}>
        Your order history will appear here once you make your first purchase
      </Text>
      <TouchableOpacity
        style={[
          styles.shopButton,
          isDarkMode && styles.shopButtonDark
        ]}
        onPress={() => navigation.navigate('PackagesScreen')}
        activeOpacity={0.8}
      >
        <Icon name="cube" size={16} color="#fff" style={styles.shopIcon} />
        <Text style={styles.shopButtonText}>Browse Packages</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading && !refreshing) {
    return (
      <View style={[
        styles.loadingContainer,
        isDarkMode && styles.loadingContainerDark
      ]}>
        <LoadingSpinner />
      </View>
    );
  }

  return (
    <View style={[
      styles.container,
      isDarkMode && styles.containerDark
    ]}>
      <View style={[
        styles.header,
        isDarkMode && styles.headerDark
      ]}>
        <Text style={[
          styles.headerTitle,
          isDarkMode && styles.headerTitleDark
        ]}>
          Order History
        </Text>
        {orderHistory.length > 0 && (
          <Text style={[
            styles.headerSubtitle,
            isDarkMode && styles.headerSubtitleDark
          ]}>
            {orderHistory.length} {orderHistory.length === 1 ? 'order' : 'orders'}
          </Text>
        )}
      </View>

      {orderHistory.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={orderHistory}
          renderItem={renderOrderItem}
          keyExtractor={(item) => item.id?.toString() || item.order_number}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#198754']}
              tintColor={isDarkMode ? '#4CAF50' : '#198754'}
            />
          }
          contentContainerStyle={styles.listContent}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingContainerDark: {
    backgroundColor: '#121212',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#1e1e1e',
    borderBottomColor: '#333',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
  },
  headerTitleDark: {
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  headerSubtitleDark: {
    color: '#999',
  },
  listContent: {
    paddingBottom: 20,
  },
  orderCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginVertical: 6,
    marginHorizontal: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  orderCardDark: {
    backgroundColor: '#1e1e1e',
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  orderDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  orderDateDark: {
    color: '#999',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  orderDetails: {
    marginBottom: 12,
  },
  orderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  orderLabel: {
    fontSize: 14,
    color: '#666',
  },
  orderLabelDark: {
    color: '#999',
  },
  orderValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  orderAmount: {
    color: '#198754',
    fontSize: 16,
  },
  textDark: {
    color: '#fff',
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  viewDetailsText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  viewDetailsTextDark: {
    color: '#999',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyContainerDark: {
    backgroundColor: '#121212',
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333',
    marginTop: 20,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  emptyTextDark: {
    color: '#fff',
  },
  emptySubtitleDark: {
    color: '#999',
  },
  shopButton: {
    backgroundColor: '#198754',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  shopButtonDark: {
    backgroundColor: '#4CAF50',
  },
  shopIcon: {
    marginRight: 8,
  },
  shopButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default OrderHistoryScreen;
