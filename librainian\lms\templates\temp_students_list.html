{% extends "base.html" %}

{% block title %}Pending Students - Librainian{% endblock %}

{% block page_title %}Pending Students{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Pending Students</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced Glassmorphism Pending Students Theme */
    .pending-students-content {
        min-height: calc(100vh - 160px);
        padding: 1rem;
        margin: 0;
        position: relative;
    }

    /* Remove padding on mobile */
    @media (max-width: 767.98px) {
        .pending-students-content {
            padding: 0.5rem !important;
            min-height: calc(100vh - 120px);
        }
    }

    @media (max-width: 575.98px) {
        .pending-students-content {
            padding: 0.25rem !important;
        }
    }

    .pending-students-content::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    .modern-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset,
            0 2px 4px rgba(255, 255, 255, 0.1) inset;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .modern-card:hover {
        transform: translateY(-5px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .modern-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        opacity: 0.8;
    }

    .modern-card-header {
        padding: 1.5rem 2rem 1rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
    }

    .modern-card-body {
        padding: 2rem;
        position: relative;
        z-index: 2;
    }

    .modern-card-header h5 {
        color: white;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        font-size: 1.1rem;
    }

    .card-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .pending-count-badge {
        background: rgba(245, 158, 11, 0.3);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(245, 158, 11, 0.5);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        font-size: 0.875rem;
    }

    /* Table Styling */
    .table-glass {
        width: 100%;
        margin: 0;
        background: transparent;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table-glass th {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        font-weight: 700;
        border: none;
        padding: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        font-size: 0.95rem;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .table-glass td {
        color: white;
        border: none;
        padding: 1rem;
        vertical-align: middle;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .table-row-pending {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .table-row-pending:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: scale(1.01);
    }

    /* Student Info Pending */
    .student-info-pending {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .student-avatar-pending {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.4);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .student-details-pending {
        display: flex;
        flex-direction: column;
    }

    .student-name-pending {
        font-weight: 600;
        font-size: 0.95rem;
        color: white;
    }

    .student-id-pending {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.7);
    }

    .contact-info-pending,
    .location-course-pending {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .contact-item-pending,
    .location-item-pending,
    .course-item-pending {
        font-size: 0.85rem;
        color: rgba(255, 255, 255, 0.9);
    }

    /* Status Badges */
    .status-badge-pending {
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.85rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .status-active {
        background: rgba(16, 185, 129, 0.3);
        border-color: rgba(16, 185, 129, 0.5);
        color: #10b981;
    }

    .status-pending {
        background: rgba(245, 158, 11, 0.3);
        border-color: rgba(245, 158, 11, 0.5);
        color: #f59e0b;
    }

    .status-completed {
        background: rgba(59, 130, 246, 0.3);
        border-color: rgba(59, 130, 246, 0.5);
        color: #3b82f6;
    }

    /* Action Buttons */
    .action-buttons-pending {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }

    .btn-action-approve-pending,
    .btn-action-edit-pending,
    .btn-action-delete-pending {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        border: 1px solid;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        font-size: 0.9rem;
        text-decoration: none;
    }

    .btn-action-approve-pending {
        background: rgba(16, 185, 129, 0.3);
        border-color: rgba(16, 185, 129, 0.5);
        color: #10b981;
    }

    .btn-action-approve-pending:hover {
        background: rgba(16, 185, 129, 0.5);
        transform: scale(1.1);
        color: #10b981;
    }

    .btn-action-edit-pending {
        background: rgba(245, 158, 11, 0.3);
        border-color: rgba(245, 158, 11, 0.5);
        color: #f59e0b;
    }

    .btn-action-edit-pending:hover {
        background: rgba(245, 158, 11, 0.5);
        transform: scale(1.1);
        color: #f59e0b;
    }

    .btn-action-delete-pending {
        background: rgba(239, 68, 68, 0.3);
        border-color: rgba(239, 68, 68, 0.5);
        color: #ef4444;
    }

    .btn-action-delete-pending:hover {
        background: rgba(239, 68, 68, 0.5);
        transform: scale(1.1);
        color: #ef4444;
    }

    .empty-state-pending {
        color: rgba(255, 255, 255, 0.8);
        text-align: center;
        padding: 3rem 1rem;
    }

    .empty-state-pending i {
        color: rgba(255, 255, 255, 0.5);
        margin-bottom: 1rem;
    }

    .empty-state-pending h5 {
        color: white;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    /* Mobile Card Layout */
    .mobile-pending-cards {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .mobile-pending-card {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        -webkit-backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        border-radius: 16px;
        padding: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .mobile-pending-card:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    .mobile-pending-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 100%);
        pointer-events: none;
    }

    .mobile-pending-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
    }

    .student-info-mobile {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;
    }

    .student-avatar-mobile {
        width: 45px;
        height: 45px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 1.1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.4);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .student-details-mobile {
        display: flex;
        flex-direction: column;
    }

    .student-name-mobile {
        color: white;
        font-weight: 700;
        font-size: 1rem;
        margin: 0 0 0.25rem 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .student-id-mobile {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.8rem;
        font-weight: 500;
    }

    .status-mobile {
        flex-shrink: 0;
    }

    .mobile-pending-body {
        position: relative;
        z-index: 2;
    }

    .mobile-field {
        margin-bottom: 0.75rem;
    }

    .mobile-field label {
        display: block;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.75rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .mobile-value {
        color: white;
        font-size: 0.85rem;
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .mobile-contact {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .contact-item-mobile {
        color: white;
        font-size: 0.85rem;
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
    }

    .contact-item-mobile i {
        color: rgba(255, 255, 255, 0.8);
        width: 16px;
    }

    .mobile-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-start;
    }

    .mobile-actions .btn-action-approve-pending,
    .mobile-actions .btn-action-edit-pending,
    .mobile-actions .btn-action-delete-pending {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }

    .empty-state-mobile {
        text-align: center;
        padding: 3rem 1rem;
        color: rgba(255, 255, 255, 0.8);
    }

    .empty-state-mobile i {
        color: rgba(255, 255, 255, 0.5);
        margin-bottom: 1rem;
    }

    .empty-state-mobile h5 {
        color: white;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    /* Mobile Responsive */
    @media (max-width: 767.98px) {
        .pending-students-content {
            padding: 0.5rem !important;
            margin: 0 !important;
            min-height: calc(100vh - 120px);
        }

        .row.g-4 {
            margin: 0 !important;
            --bs-gutter-x: 0.5rem;
            --bs-gutter-y: 0.5rem;
        }

        .col-12 {
            padding-left: 0.25rem !important;
            padding-right: 0.25rem !important;
        }

        .modern-card {
            margin-bottom: 0.5rem;
            border-radius: 16px;
        }

        .modern-card-body {
            padding: 1rem;
        }

        .modern-card-header {
            padding: 1rem 1rem 0.5rem 1rem;
        }

        .modern-card-header h5 {
            font-size: 1rem;
        }

        .card-subtitle {
            font-size: 0.8rem;
        }

        .pending-count-badge {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }

        /* Mobile card improvements */
        .mobile-pending-cards {
            gap: 0.75rem;
            padding: 0;
        }

        .mobile-pending-card {
            padding: 0.75rem;
            border-radius: 12px;
            margin-bottom: 0.5rem;
        }

        .mobile-pending-header {
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .student-avatar-mobile {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .student-name-mobile {
            font-size: 0.9rem;
        }

        .student-id-mobile {
            font-size: 0.75rem;
        }

        .mobile-field {
            margin-bottom: 0.5rem;
        }

        .mobile-field label {
            font-size: 0.7rem;
            margin-bottom: 0.2rem;
        }

        .mobile-value {
            font-size: 0.8rem;
        }

        .contact-item-mobile {
            font-size: 0.8rem;
        }

        .mobile-actions .btn-action-approve-pending,
        .mobile-actions .btn-action-edit-pending,
        .mobile-actions .btn-action-delete-pending {
            width: 32px;
            height: 32px;
            font-size: 0.8rem;
        }
    }

    /* Extra small screens */
    @media (max-width: 575.98px) {
        .pending-students-content {
            padding: 0.25rem !important;
        }

        .row.g-4 {
            --bs-gutter-x: 0.25rem;
            --bs-gutter-y: 0.25rem;
        }

        .col-12 {
            padding-left: 0.125rem !important;
            padding-right: 0.125rem !important;
        }

        .mobile-pending-cards {
            gap: 0.5rem;
        }

        .mobile-pending-card {
            padding: 0.5rem;
            border-radius: 10px;
        }

        .mobile-pending-header {
            margin-bottom: 0.5rem;
            padding-bottom: 0.4rem;
        }

        .student-avatar-mobile {
            width: 35px;
            height: 35px;
            font-size: 0.9rem;
        }

        .student-name-mobile {
            font-size: 0.85rem;
        }

        .student-id-mobile {
            font-size: 0.7rem;
        }
    }

    /* Search and Pagination Styles */
    .search-box-pending {
        position: relative;
        flex: 1;
        max-width: 300px;
    }

    .search-box-pending input {
        padding-left: 1rem;
        padding-right: 3rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.9);
        border-radius: 20px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .search-box-pending input::placeholder {
        color: rgba(255, 255, 255, 0.6);
        padding-left: 0.5rem;
    }

    .search-box-pending input:focus {
        border-color: var(--primary-color);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        transform: translateY(-1px);
    }

    .search-box-pending .search-icon {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: rgba(255, 255, 255, 0.6);
        pointer-events: none;
        font-size: 0.875rem;
    }

    .pagination-container {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 1.5rem;
        margin-top: 1rem;
    }

    /* Custom Pagination Styling */
    .custom-pagination {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        gap: 0.25rem;
    }

    .custom-page-item {
        display: flex;
    }

    .custom-page-link {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 8px;
        padding: 8px 16px;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
    }

    .custom-page-link:hover {
        background: rgba(59, 130, 246, 0.3);
        border-color: rgba(59, 130, 246, 0.5);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        text-decoration: none;
    }

    .custom-page-item.active .custom-page-link {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border-color: #3b82f6;
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        transform: translateY(-1px);
    }

    .custom-page-item.disabled .custom-page-link {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.4);
        cursor: not-allowed;
        pointer-events: none;
    }

    .pagination-info {
        margin-top: 0.5rem;
    }

    /* Hidden state for search */
    .pending-student-row.hidden,
    .pending-student-mobile.hidden {
        display: none !important;
    }

    /* Mobile responsive adjustments for search */
    @media (max-width: 768px) {
        .search-box-pending {
            max-width: 100%;
            margin-bottom: 1rem;
        }

        .d-flex.justify-content-between {
            flex-direction: column;
            align-items: stretch !important;
        }

        .pending-count-badge {
            margin-left: 0 !important;
            margin-top: 0.5rem;
            text-align: center;
        }
        }

        .mobile-field {
            margin-bottom: 0.4rem;
        }

        .mobile-field label {
            font-size: 0.65rem;
            margin-bottom: 0.15rem;
        }

        .mobile-value {
            font-size: 0.75rem;
        }

        .contact-item-mobile {
            font-size: 0.75rem;
        }

        .mobile-actions .btn-action-approve-pending,
        .mobile-actions .btn-action-edit-pending,
        .mobile-actions .btn-action-delete-pending {
            width: 28px;
            height: 28px;
            font-size: 0.75rem;
        }

        .status-badge-pending {
            font-size: 0.7rem;
            padding: 0.3rem 0.6rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="pending-students-content fade-in">
    <!-- Alert Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Pending Students Management Section -->
    <div class="row g-4">
        <!-- Pending Students Table -->
        <div class="col-12">
            <div class="modern-card">
                <div class="modern-card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6 col-12">
                            <h5 class="mb-0">
                                <i class="fas fa-user-clock me-2"></i>
                                Pending Student Applications
                            </h5>
                            <p class="card-subtitle mb-0">Review and approve student registrations</p>
                        </div>
                        <div class="col-md-6 col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="search-box-pending">
                                    <input type="text" id="pendingStudentSearch" placeholder="Search students..." class="form-control">
                                    <i class="fas fa-search search-icon"></i>
                                </div>
                                <div class="pending-count-badge ms-3">
                                    <i class="fas fa-users me-1"></i>
                                    <span id="pendingCount">{{ temp_students|length }}</span> Pending
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modern-card-body">
                    <!-- Desktop Table View -->
                    <div class="table-responsive d-none d-md-block">
                        <table class="table-glass table-hover">
                            <thead>
                                <tr>
                                    <th>Student Details</th>
                                    <th>Contact</th>
                                    <th>Location & Course</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for student in temp_students %}
                                <tr class="table-row-pending pending-student-row"
                                    data-student-name="{{ student.name|lower }}"
                                    data-student-email="{{ student.email|lower }}"
                                    data-student-mobile="{{ student.mobile }}"
                                    data-student-state="{{ student.state.name|lower }}"
                                    data-student-course="{{ student.course.name|lower }}"
                                    data-student-id="{{ student.id }}">
                                    <td>
                                        <div class="student-info-pending">
                                            <div class="student-avatar-pending">
                                                {{ student.name|first|upper }}
                                            </div>
                                            <div class="student-details-pending">
                                                <div class="student-name-pending">{{ student.name }}</div>
                                                <div class="student-id-pending">ID: #{{ student.id }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="contact-info-pending">
                                            <div class="contact-item-pending">
                                                <i class="fas fa-envelope me-2"></i>
                                                {{ student.email }}
                                            </div>
                                            <div class="contact-item-pending">
                                                <i class="fas fa-phone me-2"></i>
                                                {{ student.mobile }}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="location-course-pending">
                                            <div class="location-item-pending">
                                                <i class="fas fa-map-marker-alt me-2"></i>
                                                {{ student.state.name }}
                                            </div>
                                            <div class="course-item-pending">
                                                <i class="fas fa-graduation-cap me-2"></i>
                                                {{ student.course.name }}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-badge-pending status-pending">
                                            <i class="fas fa-clock me-1"></i>
                                            Pending
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons-pending">
                                            <form method="POST" class="d-inline">
                                                {% csrf_token %}
                                                <input type="hidden" name="student_id" value="{{ student.id }}">
                                                <button type="submit" name="status" value="completed"
                                                        class="btn-action-approve-pending"
                                                        title="Complete Registration">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            <a href="{% url 'update_temp_student' student.id %}"
                                               class="btn-action-edit-pending" title="Edit Student">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'delete_temp_student' student.id %}"
                                               class="btn-action-delete-pending"
                                               onclick="return confirm('Are you sure you want to delete this student?')"
                                               title="Delete Student">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="empty-state-pending">
                                            <i class="fas fa-user-check fa-3x mb-3"></i>
                                            <h5>No Pending Applications</h5>
                                            <p>All student applications have been processed.</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        <!-- Pagination for Desktop -->
                        <div class="pagination-container mt-4 d-none d-md-block">
                            <nav aria-label="Pending students pagination">
                                <ul class="custom-pagination justify-content-center" id="pendingPagination">
                                    <!-- Pagination will be generated by JavaScript -->
                                </ul>
                            </nav>
                            <div class="pagination-info text-center">
                                <small class="text-muted" id="pendingPageInfo">Showing students</small>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Card View -->
                    <div class="mobile-pending-cards d-md-none">
                        {% for student in temp_students %}
                        <div class="mobile-pending-card pending-student-mobile"
                             data-student-name="{{ student.name|lower }}"
                             data-student-email="{{ student.email|lower }}"
                             data-student-mobile="{{ student.mobile }}"
                             data-student-state="{{ student.state.name|lower }}"
                             data-student-course="{{ student.course.name|lower }}"
                             data-student-id="{{ student.id }}">
                            <div class="mobile-pending-header">
                                <div class="student-info-mobile">
                                    <div class="student-avatar-mobile">
                                        {{ student.name|first|upper }}
                                    </div>
                                    <div class="student-details-mobile">
                                        <h6 class="student-name-mobile">{{ student.name }}</h6>
                                        <span class="student-id-mobile">ID: #{{ student.id }}</span>
                                    </div>
                                </div>
                                <div class="status-mobile">
                                    <span class="status-badge-pending status-pending">
                                        <i class="fas fa-clock me-1"></i>
                                        Pending
                                    </span>
                                </div>
                            </div>

                            <div class="mobile-pending-body">
                                <div class="row g-2">
                                    <div class="col-12">
                                        <div class="mobile-field">
                                            <label>Contact Information</label>
                                            <div class="mobile-contact">
                                                <div class="contact-item-mobile">
                                                    <i class="fas fa-envelope me-2"></i>
                                                    <span>{{ student.email }}</span>
                                                </div>
                                                <div class="contact-item-mobile">
                                                    <i class="fas fa-phone me-2"></i>
                                                    <span>{{ student.mobile }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="mobile-field">
                                            <label>Location</label>
                                            <div class="mobile-value">
                                                <i class="fas fa-map-marker-alt me-2"></i>
                                                {{ student.state.name }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="mobile-field">
                                            <label>Course</label>
                                            <div class="mobile-value">
                                                <i class="fas fa-graduation-cap me-2"></i>
                                                {{ student.course.name }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mobile-field">
                                            <label>Actions</label>
                                            <div class="mobile-actions">
                                                <form method="POST" class="d-inline">
                                                    {% csrf_token %}
                                                    <input type="hidden" name="student_id" value="{{ student.id }}">
                                                    <button type="submit" name="status" value="completed"
                                                            class="btn-action-approve-pending"
                                                            title="Complete Registration">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                                <a href="{% url 'update_temp_student' student.id %}"
                                                   class="btn-action-edit-pending" title="Edit Student">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'delete_temp_student' student.id %}"
                                                   class="btn-action-delete-pending"
                                                   onclick="return confirm('Are you sure you want to delete this student?')"
                                                   title="Delete Student">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="empty-state-mobile">
                            <i class="fas fa-user-check fa-3x mb-3"></i>
                            <h5>No Pending Applications</h5>
                            <p>All student applications have been processed.</p>
                        </div>
                        {% endfor %}

                        <!-- Pagination for Mobile -->
                        <div class="pagination-container mt-4 d-md-none">
                            <nav aria-label="Pending students mobile pagination">
                                <ul class="custom-pagination justify-content-center" id="pendingMobilePagination">
                                    <!-- Pagination will be generated by JavaScript -->
                                </ul>
                            </nav>
                            <div class="pagination-info text-center">
                                <small class="text-muted" id="pendingMobilePageInfo">Showing students</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add staggered animation delays
        const cards = document.querySelectorAll('.modern-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${(index + 1) * 0.1}s`;
            card.classList.add('slide-up');
        });

        // Removed page load success message

        // Initialize search and pagination for pending students
        initializePendingStudentsSearch();
    });

    // Pending Students Search and Pagination
    function initializePendingStudentsSearch() {
        const searchInput = document.getElementById('pendingStudentSearch');
        const pendingCount = document.getElementById('pendingCount');

        if (!searchInput) return;

        // Desktop pagination
        const desktopPagination = new PendingStudentsPagination(
            '.table-glass tbody',
            '.pending-student-row',
            '#pendingPagination',
            '#pendingPageInfo',
            10 // items per page
        );

        // Mobile pagination
        const mobilePagination = new PendingStudentsPagination(
            '.mobile-pending-cards',
            '.pending-student-mobile',
            '#pendingMobilePagination',
            '#pendingMobilePageInfo',
            5 // items per page
        );

        // Search functionality
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            // Search desktop rows
            const desktopRows = document.querySelectorAll('.pending-student-row');
            let visibleDesktopCount = 0;

            desktopRows.forEach(row => {
                const searchData = [
                    row.dataset.studentName,
                    row.dataset.studentEmail,
                    row.dataset.studentMobile,
                    row.dataset.studentState,
                    row.dataset.studentCourse,
                    row.dataset.studentId
                ].join(' ').toLowerCase();

                if (searchData.includes(searchTerm)) {
                    row.classList.remove('hidden');
                    visibleDesktopCount++;
                } else {
                    row.classList.add('hidden');
                }
            });

            // Search mobile cards
            const mobileCards = document.querySelectorAll('.pending-student-mobile');
            let visibleMobileCount = 0;

            mobileCards.forEach(card => {
                const searchData = [
                    card.dataset.studentName,
                    card.dataset.studentEmail,
                    card.dataset.studentMobile,
                    card.dataset.studentState,
                    card.dataset.studentCourse,
                    card.dataset.studentId
                ].join(' ').toLowerCase();

                if (searchData.includes(searchTerm)) {
                    card.classList.remove('hidden');
                    visibleMobileCount++;
                } else {
                    card.classList.add('hidden');
                }
            });

            // Update count
            const totalVisible = Math.max(visibleDesktopCount, visibleMobileCount);
            pendingCount.textContent = totalVisible;

            // Reset pagination
            desktopPagination.reset();
            mobilePagination.reset();
        });
    }

    // Pagination class for pending students
    class PendingStudentsPagination {
        constructor(containerSelector, itemSelector, paginationSelector, pageInfoSelector, itemsPerPage) {
            this.container = document.querySelector(containerSelector);
            this.itemSelector = itemSelector;
            this.pagination = document.querySelector(paginationSelector);
            this.pageInfo = document.querySelector(pageInfoSelector);
            this.itemsPerPage = itemsPerPage;
            this.currentPage = 1;

            if (this.container && this.pagination) {
                this.init();
            }
        }

        init() {
            this.updateDisplay();
        }

        reset() {
            this.currentPage = 1;
            this.updateDisplay();
        }

        updateDisplay() {
            const items = Array.from(this.container.querySelectorAll(`${this.itemSelector}:not(.hidden)`));
            const totalItems = items.length;
            const totalPages = Math.ceil(totalItems / this.itemsPerPage);
            const startIndex = (this.currentPage - 1) * this.itemsPerPage;
            const endIndex = startIndex + this.itemsPerPage;

            // Hide all items first
            this.container.querySelectorAll(this.itemSelector).forEach(item => {
                item.style.display = 'none';
            });

            // Show only current page items
            items.slice(startIndex, endIndex).forEach(item => {
                item.style.display = '';
            });

            this.updatePagination(totalPages);
            this.updatePageInfo(startIndex + 1, Math.min(endIndex, totalItems), totalItems);
        }

        updatePagination(totalPages) {
            if (!this.pagination) return;

            this.pagination.innerHTML = '';

            if (totalPages <= 1) return;

            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `custom-page-item ${this.currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = '<a class="custom-page-link" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>';
            if (this.currentPage > 1) {
                prevLi.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.currentPage--;
                    this.updateDisplay();
                });
            }
            this.pagination.appendChild(prevLi);

            // Page numbers (show max 5 pages)
            const startPage = Math.max(1, this.currentPage - 2);
            const endPage = Math.min(totalPages, startPage + 4);

            for (let i = startPage; i <= endPage; i++) {
                const li = document.createElement('li');
                li.className = `custom-page-item ${i === this.currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="custom-page-link" href="#">${i}</a>`;
                li.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.currentPage = i;
                    this.updateDisplay();
                });
                this.pagination.appendChild(li);
            }

            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `custom-page-item ${this.currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = '<a class="custom-page-link" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>';
            if (this.currentPage < totalPages) {
                nextLi.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.currentPage++;
                    this.updateDisplay();
                });
            }
            this.pagination.appendChild(nextLi);
        }

        updatePageInfo(start, end, total) {
            if (this.pageInfo) {
                this.pageInfo.textContent = `Showing ${start}-${end} of ${total} students`;
            }
        }
    }
</script>
{% endblock %}
