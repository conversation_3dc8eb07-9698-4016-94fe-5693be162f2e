import React, { useEffect, useState } from "react";
import { Container, Row, Col, Image, Badge } from "react-bootstrap";
import { motion } from "framer-motion";
import { FaCalendarAlt, FaUserCircle, FaArrowLeft } from "react-icons/fa";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux"; // Import for dispatch and state selection
import { getBlog } from "../../redux/slice/blogSlice"; // Adjust the import based on your file structure
import { toast } from "react-hot-toast"; // Assuming you use toast for notifications
import defaultImage from "../../assets/blog.png";
import NavigationBar from "../../commonComponents/NavigationBar";

const ViewBlog = () => {
  const { id } = useParams(); // Get blog ID from URL params
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [blog, setBlog] = useState(null); // Store the blog data in state
  const blogData = useSelector((state) => state.blog); // Get blog from Redux store
  
  // Fetch blog data by ID when the component mounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await dispatch(getBlog(id)); // Dispatch getBlog action with ID
        if (response?.payload) {
          setBlog(response.payload); // Set the blog data in state
        } else {
          toast.error("Error fetching blog");
        }
      } catch (error) {
        toast.error("Error fetching blog");
      }
    };

    fetchData();
  }, [dispatch, id]); // Fetch when component mounts or `id` changes

  if (!blog) {
    return <div>Loading...</div>; // Show a loading state while fetching
  }

  return (
    <>
    <NavigationBar/>
    <Container className="my-5">
      {/* Back Button */}
      <motion.div
        whileHover={{ scale: 1.1 }}
        className="mb-4 d-flex align-items-center back-button"
        onClick={() => navigate(-1)}
        style={{ cursor: "pointer", color: "#28a745" }}
      >
        <FaArrowLeft size={20} className="me-2" />
        <span>Back to Blogs</span>
      </motion.div>

      {/* Blog Header */}
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-4"
      >
        <h1 className="blog-title fw-bold">{blog.title}</h1>
        <div className="d-flex justify-content-center align-items-center text-muted my-2">
          <FaUserCircle className="me-2" size={18} />
          <span className="me-4">By {blog.author}</span>
          <FaCalendarAlt className="me-2" size={16} />
          <span>{new Date(blog.published_date).toDateString()}</span>
        </div>
        <Badge bg="success" className="px-3 py-2">
          Featured Blog
        </Badge>
      </motion.div>

      {/* Blog Image */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8 }}
        className="text-center mb-4"
      >
        <Image
        src={blog.image ? `${import.meta.env.VITE_BASE_URL}/${blog.image}` : defaultImage}
          alt={blog.image_caption || 'Blog Image'}
          fluid
          className="rounded blog-image"
        />
        <p className="text-muted mt-2">{blog.image_caption}</p>
      </motion.div>

      {/* Blog Content */}
      <Row>
        <Col md={{ span: 8, offset: 2 }}>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1 }}
            className="blog-content"
          >
            <h4 className="mb-3">{blog.introduction}</h4>
            <p className="lead">{blog.content}</p>
          </motion.div>
        </Col>
      </Row>
    </Container>
    </>
  );
};

export default ViewBlog;
