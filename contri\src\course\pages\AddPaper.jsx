import React, { useState, useEffect } from "react";
import { But<PERSON>, Form, Container, Row, Col, Spinner } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { createPaper} from "../../redux/slice/paperSlice";
import { getTestPatterns } from "../../redux/slice/paperEngineSlice"; // Redux action to fetch test patterns
import { Link, useNavigate, useParams } from "react-router-dom";
import NavigationBar from "../../commonComponents/NavigationBar";
import { toast } from "react-hot-toast";
import AllPapers from "../components/AllPapers";

const AddPaper = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { tierSlug } = useParams(); // Get `tierSlug` from URL parameters
  const accessToken = useSelector((state) => state.contributor.accessToken);
  const { testPatterns, status: testPatternsStatus } = useSelector((state) => state.paperEngine); // Access test patterns from Redux store

  useEffect(() => {
    if (!accessToken) {
      toast.error("Please log in as a contributor!");
      const timer = setTimeout(() => {
        navigate("/contributor_login");
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [accessToken, navigate]);

  useEffect(() => {
    if (testPatternsStatus === "idle") {
      dispatch(getTestPatterns()); // Fetch test patterns when the component mounts
    }
  }, [dispatch, testPatternsStatus]);

  if (!accessToken) {
    return (
      <Container
        className="d-flex justify-content-center align-items-center text-success"
        style={{ height: "100vh" }}
      >
        <Spinner animation="border" />
      </Container>
    );
  }

  const [paperName, setPaperName] = useState("");
  const [description, setDescription] = useState("");
  const [testPatternValue, setTestPatternValue] = useState(""); // State to hold selected test pattern ID
  const [maxMarks, setMaxMarks] = useState("");
  const [duration, setDuration] = useState("");
  const [paperAdded, setPaperAdded] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
  
    setIsSubmitting(true);
  
    // Prepare paper data
    const paperData = {
      tier: tierSlug, // Convert tierSlug to an integer
      name: paperName,
      description,
      max_marks: parseInt(maxMarks, 10),
      duration: parseInt(duration, 10), // Convert duration to an integer
      ...(testPatternValue && testPatternValue !== "none" && { test_pattern: testPatternValue }), // Add test_pattern only if not "none"
    };
  
    dispatch(createPaper({ data: paperData }))
      .unwrap()
      .then(() => {
        setPaperAdded(true)
        setPaperName("");
        setDescription("");
        setTestPatternValue(""); // Reset test pattern selection
        setMaxMarks("");
        setDuration("");
        toast.success("Paper added successfully!");
        setTimeout(() => setPaperAdded(false), 1000); // Reset tierAdded after delay
      })
      .catch((error) => {
        console.error("Error creating paper:", error);
        toast.error("Failed to create paper. Please try again.");
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  return (
    <>
      <NavigationBar /> 
      <Container>
        <Row>
          <Col xs={12} md={4} lg={4}>
            <Form
              onSubmit={handleSubmit}
              className="border mt-5 p-4 rounded shadow-lg"
            >
              <Form.Group controlId="paperName" className="mb-3">
                <Form.Label>Paper Name</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter paper name"
                  value={paperName}
                  onChange={(e) => setPaperName(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="description" className="mb-3">
                <Form.Label>Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={4}
                  placeholder="Enter paper description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="testPattern" className="mb-3">
                <Form.Label>Test Pattern</Form.Label>
                {testPatternsStatus === "loading" ? (
                  <Spinner animation="border" variant="primary" />
                ) : (
                  <Form.Control
                    as="select"
                    value={testPatternValue}
                    onChange={(e) => setTestPatternValue(e.target.value)}
                  >
                    <option value="none">None</option> {/* Explicit "None" option */}
                    {testPatterns.map((pattern) => (
                      <option key={pattern.pattern_id} value={pattern.pattern_id}>
                        {pattern.name} (Version: {pattern.version})
                      </option>
                    ))}
                  </Form.Control>
                )}
              </Form.Group>
              <Row>
                <Col xs={6} md={6} lg={6}>
                  <Form.Group controlId="maxMarks" className="mb-3">
                  <Form.Label>Max Marks</Form.Label>
                  <Form.Control
                    type="number"
                    placeholder="Enter maximum marks"
                    value={maxMarks}
                    onChange={(e) => setMaxMarks(e.target.value)}
                    required
                  />
                  </Form.Group>
                </Col>
                <Col xs={6} md={6} lg={6}>
                  <Form.Group controlId="duration" className="mb-3">
                  <Form.Label>Duration (in minutes)</Form.Label>
                  <Form.Control
                    type="number"
                    placeholder="Enter duration"
                    value={duration}
                    onChange={(e) => setDuration(e.target.value)}
                    required
                  />
                 </Form.Group>
                </Col>
              </Row>

              <Button
                variant="success"
                type="submit"
                className="w-100"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Add Paper"}
              </Button>
            </Form>

            <div className="d-flex justify-content-center align-items-center">
              <Link to="/contributor_dashboard">
                <Button variant="secondary" className="mt-5 text-center">
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </Col>

          <Col xs={12} md={8} lg={8} className="border-left">
            <AllPapers paperAdded={paperAdded} />
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default AddPaper;
