import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'; 
import axios from 'axios';
import Constants from 'expo-constants';

// Get baseURL from Expo constants
const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;

// Hardcoded token for testing
// const TEST_AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzNjU3MTA4LCJpYXQiOjE3NTM2MzkxMDgsImp0aSI6ImZkMGU4ZDM4Njc4YTRhMGNiZjZmYjI3MjE3ZGRmMzMwIiwidXNlcl9pZCI6MTJ9.AQadLqi-amGN3dQGq_2Y_KHaB_yCVn6QxGfeohc9lp8';

// Get walk around images
export const getWalkArounds = createAsyncThunk(
  'walkAround/getWalkArounds',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${baseURL}api/customrcare/walk-around-images/`, {
        // headers: {
        //   Authorization: `Bearer ${TEST_AUTH_TOKEN}`,
        // },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error || 'Failed to fetch walk around images');
    }
  }
);

const initialState = {
  walkArounds: [],
  isLoading: false,
  error: null,
};

const walkAroundSlice = createSlice({
  name: 'walkAround',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // getWalkArounds
      .addCase(getWalkArounds.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getWalkArounds.fulfilled, (state, action) => {
        state.isLoading = false;
        state.walkArounds = action.payload;
      })
      .addCase(getWalkArounds.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError } = walkAroundSlice.actions;
export default walkAroundSlice.reducer;
