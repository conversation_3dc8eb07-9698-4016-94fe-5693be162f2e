# Global Authentication System

## Overview

This application now includes a **global authentication wrapper** that automatically handles token expiration without requiring changes to individual components.

## How It Works

### 1. Global Authentication Wrapper

The `GlobalAuthWrapper` component (`src/components/GlobalAuthWrapper.jsx`) wraps the entire application and:

- **Monitors all HTTP responses** via Axios interceptor
- **Detects 401/403 errors** automatically
- **Validates tokens on route changes**
- **<PERSON><PERSON> redirects globally**
- **Prevents multiple simultaneous redirects**

### 2. Automatic Token Validation

The system automatically:

1. **Checks token on app startup**
2. **Validates token on route changes**
3. **Intercepts API responses for auth errors**
4. **Redirects to login when token expires**

### 3. No Component Changes Required

Your existing components work exactly as before! The authentication is handled globally, so you don't need to:

- Add authentication hooks to every component
- Check tokens manually
- Handle redirects in individual components
- Modify existing API calls

## Implementation Details

### App Structure

```
App.jsx
├── BrowserRouter
    ├── GlobalAuthWrapper  ← Handles all authentication
        ├── Routes
        │   ├── All your existing routes
        │   └── Components work unchanged
        └── Toaster
```

### Protected vs Public Routes

The system automatically distinguishes between:

**Public Routes** (no authentication required):
- `/` - Home page
- `/contributor_login` - Login page
- `/contributor_signup` - Signup page

**Protected Routes** (authentication required):
- All other routes automatically require authentication

### Token Expiration Detection

The system detects expired tokens through:

1. **HTTP Response Codes**:
   - 401 Unauthorized → Token expired/invalid
   - 403 Forbidden (with token-related message) → Token issues

2. **JWT Token Validation**:
   - Checks expiration time in JWT payload
   - Validates on route changes
   - Prevents expired tokens from being used

3. **Route Protection**:
   - Checks token presence on protected routes
   - Validates token format and expiration

## Benefits

### ✅ **Zero Component Changes**
- Your existing components work unchanged
- No need to add authentication code to every component
- Existing API calls continue to work

### ✅ **Automatic Handling**
- Token expiration detected automatically
- Users redirected immediately when token expires
- Consistent behavior across the entire app

### ✅ **Better User Experience**
- Immediate feedback when session expires
- Prevents users from seeing errors
- Smooth redirect to login page

### ✅ **Centralized Logic**
- All authentication logic in one place
- Easy to maintain and update
- Consistent error handling

## How Token Expiration Works

### Scenario 1: API Call with Expired Token

1. User makes an API call (e.g., creating a popup)
2. Server returns 401 Unauthorized
3. GlobalAuthWrapper intercepts the response
4. User session is cleared
5. Toast message: "Your session has expired. Please log in again."
6. User is redirected to login page after 1.5 seconds

### Scenario 2: Navigation with Expired Token

1. User navigates to a protected route
2. GlobalAuthWrapper checks token validity
3. If token is expired (JWT validation)
4. User session is cleared
5. User is redirected to login page

### Scenario 3: App Startup with Expired Token

1. User opens the app
2. GlobalAuthWrapper checks stored token
3. If token is expired or invalid
4. User is redirected to login page

## Technical Details

### Files Added/Modified

**New Files:**
- `src/components/GlobalAuthWrapper.jsx` - Main authentication wrapper
- `src/utils/authUtils.js` - Token validation utilities
- `src/docs/GLOBAL_AUTH.md` - This documentation

**Modified Files:**
- `src/App.jsx` - Wrapped with GlobalAuthWrapper

### Axios Interceptor

The system sets up a global Axios response interceptor that:

```javascript
axios.interceptors.response.use(
  (response) => response, // Pass through successful responses
  (error) => {
    if (error.response?.status === 401) {
      // Handle token expiration
      handleTokenExpiration('Your session has expired. Please log in again.');
    }
    return Promise.reject(error);
  }
);
```

### Token Validation

Uses JWT token validation to check expiration with a **10-minute safety buffer**:

```javascript
// Check if JWT token is expired (with 10-minute safety buffer)
const isTokenValid = (token, safetyBufferMinutes = 10) => {
  const payload = JSON.parse(atob(token.split('.')[1]));
  const currentTime = Date.now() / 1000;
  const safetyBufferSeconds = safetyBufferMinutes * 60;
  const adjustedCurrentTime = currentTime + safetyBufferSeconds;

  // Token is considered expired 10 minutes before actual expiration
  return payload.exp > adjustedCurrentTime;
};
```

### Safety Buffer for Clock Differences

The system includes a **10-minute safety buffer** to handle clock synchronization issues:

- **Problem**: Client and server clocks may not be perfectly synchronized
- **Solution**: Tokens are considered expired 10 minutes before their actual expiration time
- **Benefit**: Prevents authentication errors due to minor clock differences
- **Example**: If a token expires at 3:00 PM, the system treats it as expired at 2:50 PM

## Migration

### Before (Manual Authentication in Every Component)

```javascript
const MyComponent = () => {
  const navigate = useNavigate();
  const accessToken = useSelector((state) => state.contributor.accessToken);

  useEffect(() => {
    if (!accessToken) {
      toast.error('Please log in as a contributor!');
      setTimeout(() => navigate('/contributor_login'), 2000);
    }
  }, [accessToken, navigate]);

  if (!accessToken) return null;
  
  return <div>Component content</div>;
};
```

### After (No Changes Required)

```javascript
const MyComponent = () => {
  // No authentication code needed!
  // GlobalAuthWrapper handles everything automatically
  
  return <div>Component content</div>;
};
```

## Troubleshooting

### Common Issues

1. **Multiple redirects**: The system prevents this with a redirect flag
2. **Public routes redirecting**: Check the `publicRoutes` array in GlobalAuthWrapper
3. **Token not being cleared**: Check Redux DevTools for state changes

### Debug Information

To debug authentication issues:

1. Check browser console for interceptor logs
2. Monitor Redux state in DevTools
3. Check Network tab for 401/403 responses
4. Verify token format and expiration

## Configuration

### Adjusting Safety Buffer

If you need to adjust the 10-minute safety buffer, you can modify the constant in `src/utils/authUtils.js`:

```javascript
// Change the default safety buffer (in minutes)
export const DEFAULT_SAFETY_BUFFER_MINUTES = 15; // 15 minutes instead of 10
```

Or pass a custom value to individual functions:

```javascript
import { isTokenValid } from '../utils/authUtils';

// Use 5-minute safety buffer instead of default 10
const isValid = isTokenValid(token, 5);
```

### Why 10 Minutes?

The 10-minute safety buffer was chosen because:

1. **Clock drift**: Computer clocks can drift by several minutes
2. **Network delays**: API calls may take time to process
3. **User experience**: Better to log out early than show confusing errors
4. **Safety margin**: Provides comfortable buffer for most scenarios

## Future Enhancements

Possible future improvements:

1. **Token refresh**: Automatically refresh tokens before expiration
2. **Warning notifications**: Warn users before token expires
3. **Remember me**: Extended session management
4. **Role-based routing**: Automatic role-based access control
5. **Configurable safety buffer**: UI to adjust safety buffer per environment

## Summary

The global authentication system provides:

- ✅ **Automatic token expiration handling**
- ✅ **No component changes required**
- ✅ **Consistent user experience**
- ✅ **Centralized authentication logic**
- ✅ **Better error handling**

Your existing components continue to work exactly as before, but now with automatic authentication handling!
