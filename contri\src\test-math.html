<!DOCTYPE html>
<html>
<head>
    <title>Math Rendering Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        // Simple test of the math parsing logic
        function cleanTextForMath(text) {
            if (!text) return '';
            return text
                // Remove zero-width spaces and other invisible characters
                .replace(/[\u200B-\u200D\uFEFF]/g, '')

                // Fix common copy-paste issues from formatted text
                // Handle cases where superscripts become separate numbers
                .replace(/(\w+)\s*\n\s*(\d+)\s*\n/g, '$1^{$2} ')  // Handle line breaks in superscripts
                .replace(/(\w+)\s*\u2009\s*(\d+)/g, '$1^{$2}')  // Thin space before superscript
                .replace(/(\w+)\s*\u00A0\s*(\d+)/g, '$1^{$2}')  // Non-breaking space before superscript

                // Handle integral bounds that get separated by line breaks
                .replace(/∫\s*\n\s*(\d+)\s*\n\s*(\w+)/g, '∫_{$1}^{$2}')
                .replace(/∫\s+(\d+)\s+(\w+)\s*​/g, '∫_{$1}^{$2}')  // Zero-width space after bounds

                // Normalize whitespace (but preserve structure within math expressions)
                .replace(/\s+/g, ' ')
                .trim();
        }

        function cleanMathExpression(mathContent) {
            if (!mathContent) return '';
            return mathContent
                // Convert Unicode integral symbol to LaTeX
                .replace(/∫/g, '\\int')

                // Fix integral bounds patterns
                .replace(/\\int\s*(\d+)\s*(\w+)/g, '\\int_{$1}^{$2}')
                .replace(/∫\s*(\d+)\s*(\w+)/g, '\\int_{$1}^{$2}')

                // Fix common superscript patterns (when copy-pasted from formatted text)
                .replace(/(\w+)\s*(\d+)\s+dt/g, '$1^{$2} dt')  // t 2 dt → t^{2} dt
                .replace(/(\w+)\s*(\d+)\s+d(\w+)/g, '$1^{$2} d$3')  // t 2 dx → t^{2} dx
                .replace(/(\w+)\s*(\d+)\s*$/g, '$1^{$2}')  // t 2 at end → t^{2}
                .replace(/(\w+)\s*(\d+)\s*([+\-*/=])/g, '$1^{$2}$3')  // t 2 + → t^{2} +

                // Fix subscript patterns
                .replace(/(\w+)\s*_\s*(\d+)/g, '$1_{$2}')  // x _ 0 → x_{0}

                // Fix fraction patterns
                .replace(/(\d+)\s*\/\s*(\d+)/g, '\\frac{$1}{$2}')  // 1 / 2 → \frac{1}{2}

                // Fix square root patterns
                .replace(/sqrt\s*\(\s*([^)]+)\s*\)/g, '\\sqrt{$1}')  // sqrt(x) → \sqrt{x}
                .replace(/√\s*\(\s*([^)]+)\s*\)/g, '\\sqrt{$1}')  // √(x) → \sqrt{x}
                .replace(/√([a-zA-Z0-9]+)/g, '\\sqrt{$1}')  // √x → \sqrt{x}

                // Fix power notation
                .replace(/\^(\d+)/g, '^{$1}')  // ^2 → ^{2}
                .replace(/\*\*(\d+)/g, '^{$1}')  // **2 → ^{2}

                // Fix common function names
                .replace(/\bsin\b/g, '\\sin')
                .replace(/\bcos\b/g, '\\cos')
                .replace(/\btan\b/g, '\\tan')
                .replace(/\bln\b/g, '\\ln')
                .replace(/\blog\b/g, '\\log')
                .replace(/\blim\b/g, '\\lim')

                // Clean up multiple spaces
                .replace(/\s+/g, ' ')
                .trim();
        }

        function parseTextWithMath(text) {
            const parts = [];
            let currentIndex = 0;
            const cleanedText = cleanTextForMath(text);
            const mathRegex = /(\$\$[\s\S]*?\$\$|\$[^$\n]*?\$)/g;
            let match;

            while ((match = mathRegex.exec(cleanedText)) !== null) {
                if (match.index > currentIndex) {
                    const textContent = cleanedText.slice(currentIndex, match.index);
                    if (textContent) {
                        parts.push({ type: 'text', content: textContent });
                    }
                }

                const mathExpression = match[0];
                if (mathExpression.startsWith('$$') && mathExpression.endsWith('$$')) {
                    const mathContent = mathExpression.slice(2, -2).trim();
                    parts.push({ type: 'display-math', content: cleanMathExpression(mathContent) });
                } else if (mathExpression.startsWith('$') && mathExpression.endsWith('$')) {
                    const mathContent = mathExpression.slice(1, -1).trim();
                    parts.push({ type: 'inline-math', content: cleanMathExpression(mathContent) });
                }

                currentIndex = match.index + match[0].length;
            }

            if (currentIndex < cleanedText.length) {
                const remainingText = cleanedText.slice(currentIndex);
                if (remainingText) {
                    parts.push({ type: 'text', content: remainingText });
                }
            }

            if (parts.length === 0) {
                parts.push({ type: 'text', content: cleanedText });
            }

            return parts;
        }

        function TestComponent() {
            const testCases = [
                "This is not the correct option $f(x)=∫ 0 x​3t 2 dt,$",
                "$f(x) = ∫ 0 x 3t 2 dt$",
                "$x 2 + 2x + 1$",
                "$sin(x) + cos 2 (x)$",
                "$1 / 2 + sqrt(x)$",
                "$f(x) = x**2 + 3x^2$"
            ];

            return (
                <div style={{padding: '20px'}}>
                    <h3>Math Parsing Test - Enhanced for Copy-Paste Issues</h3>

                    {testCases.map((testCase, i) => {
                        const parts = parseTextWithMath(testCase);
                        return (
                            <div key={i} style={{marginBottom: '20px', padding: '10px', border: '1px solid #ccc'}}>
                                <div><strong>Input {i+1}:</strong> <code>{testCase}</code></div>
                                <div><strong>Parsed Parts:</strong></div>
                                <pre style={{fontSize: '12px', background: '#f5f5f5', padding: '5px'}}>
                                    {JSON.stringify(parts, null, 2)}
                                </pre>

                                <div><strong>Rendered:</strong></div>
                                <div style={{background: '#e8f5e8', padding: '5px'}}>
                                    {parts.map((part, index) => {
                                        if (part.type === 'text') {
                                            return <span key={index}>{part.content}</span>;
                                        } else if (part.type === 'inline-math') {
                                            return <span key={index} style={{color: 'blue', fontWeight: 'bold'}}>
                                                [MATH: {part.content}]
                                            </span>;
                                        }
                                        return <span key={index}>{part.content}</span>;
                                    })}
                                </div>
                            </div>
                        );
                    })}

                    <div style={{marginTop: '30px', padding: '15px', background: '#fff3cd', border: '1px solid #ffeaa7'}}>
                        <h4>How to Use:</h4>
                        <ol>
                            <li><strong>Copy mathematical text</strong> from any source (PDF, webpage, document)</li>
                            <li><strong>Paste it into the question/option field</strong> - even if formatting looks broken</li>
                            <li><strong>Wrap it in $ signs</strong> - like: $your pasted math here$</li>
                            <li><strong>The system will automatically fix:</strong>
                                <ul>
                                    <li>Superscripts: "t 2" → "t^{2}"</li>
                                    <li>Integrals: "∫ 0 x" → "\\int_{0}^{x}"</li>
                                    <li>Fractions: "1 / 2" → "\\frac{1}{2}"</li>
                                    <li>Functions: "sin", "cos", "ln" → "\\sin", "\\cos", "\\ln"</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<TestComponent />, document.getElementById('root'));
    </script>
</body>
</html>
