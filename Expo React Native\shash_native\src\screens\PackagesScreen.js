import React, { useEffect, useState, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Dimensions,
  Modal,
} from 'react-native';
import { GestureHandlerRootView, PanGestureHandler } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import { getPackages, setSelectedPackageId } from '../redux/packageSlice';
import { addToCart, removeFromCart } from '../redux/cartSlice';
import Icon from 'react-native-vector-icons/FontAwesome';
import { ThemeContext } from '../context/ThemeContext';
import BottomTabBar from '../components/BottomTabBar';
import Toast from 'react-native-toast-message';
import { logEvent, logScreenView, logViewContent } from '../utils/analytics';

const { width } = Dimensions.get('window');

const PackagesScreen = ({ navigation }) => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  // const isAuthenticated = useSelector((state) => state.auth.isAuthenticated);
  const [packages, setPackages] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [checkoutDetails, setCheckoutDetails] = useState(null);
  const { loading = false } = useSelector((state) => state.packages || {});
  const { isAuthenticated } = useSelector((state) => state.auth);
  const { items: cartItems } = useSelector((state) => state.cart);

  useEffect(() => {
    // Log screen view
    logScreenView('Packages', 'PackagesScreen');

    const fetchPackages = async () => {
      try {
        const response = await dispatch(getPackages()).unwrap();
        setPackages(response);

        // Log packages loaded event
        logEvent('packages_loaded', {
          packages_count: response.length,
          screen: 'Packages',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error('Error fetching packages:', error);
        logEvent('packages_load_failed', {
          error: error.message,
          screen: 'Packages',
          timestamp: new Date().toISOString(),
        });
      }
    };
    fetchPackages();
  }, [dispatch]);

  const handleNext = () => {
    const newIndex = (currentIndex + 1) % packages.length;
    setCurrentIndex(newIndex);

    // Log package navigation
    logEvent('package_navigation', {
      direction: 'next',
      from_index: currentIndex,
      to_index: newIndex,
      package_name: packages[newIndex]?.name,
      screen: 'Packages',
      timestamp: new Date().toISOString(),
    });
  };

  const handlePrevious = () => {
    const newIndex = currentIndex === 0 ? packages.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);

    // Log package navigation
    logEvent('package_navigation', {
      direction: 'previous',
      from_index: currentIndex,
      to_index: newIndex,
      package_name: packages[newIndex]?.name,
      screen: 'Packages',
      timestamp: new Date().toISOString(),
    });
  };

  const handleShowModal = () => setShowModal(true);
  const handleCloseModal = () => setShowModal(false);

  // Check if package is already in cart
  const isPackageInCart = (packageId) => {
    return cartItems.some(item => item.packageId === packageId && item.type === 'package');
  };

  const handleAddToCart = (packageItem) => {
    const cartItem = {
      id: packageItem.id,
      name: packageItem.name,
      price: packageItem.discount_price,
      quantity: 1,
      type: 'package',
      packageId: packageItem.id,
      description: [
        packageItem?.description_line_01,
        packageItem?.description_line_02,
        packageItem?.description_line_03,
        packageItem?.description_line_04,
        packageItem?.description_line_05,
      ].filter(Boolean).join(', '),
      maxQuantity: 1, // Packages typically have quantity limit of 1
    };

    dispatch(addToCart(cartItem));

    // Log add to cart analytics
    logEvent('add_to_cart', {
      item_id: packageItem.id,
      item_name: packageItem.name,
      item_category: 'package',
      price: packageItem.discount_price,
      currency: 'INR',
      screen: 'Packages',
      timestamp: new Date().toISOString(),
    });

    Toast.show({
      type: 'success',
      text1: 'Added to Cart',
      text2: `${packageItem.name} has been added to your cart`,
      position: 'bottom',
      visibilityTime: 2000,
    });
  };

  const handleRemoveFromCart = (packageItem) => {
    dispatch(removeFromCart({
      id: packageItem.id,
      type: 'package'
    }));

    // Log remove from cart analytics
    logEvent('remove_from_cart', {
      item_id: packageItem.id,
      item_name: packageItem.name,
      item_category: 'package',
      price: packageItem.discount_price,
      currency: 'INR',
      screen: 'Packages',
      timestamp: new Date().toISOString(),
    });

    Toast.show({
      type: 'success',
      text1: 'Removed from Cart',
      text2: `${packageItem.name} has been removed from your cart`,
      position: 'bottom',
      visibilityTime: 2000,
    });
  };

  const handleBuyNow = (packageItem) => {
    // Log buy now analytics
    logEvent('buy_now_clicked', {
      item_id: packageItem.id,
      item_name: packageItem.name,
      item_category: 'package',
      price: packageItem.discount_price,
      currency: 'INR',
      is_authenticated: isAuthenticated,
      screen: 'Packages',
      timestamp: new Date().toISOString(),
    });

    logViewContent('package', packageItem.id, packageItem.name);

    if (!isAuthenticated) {
      // Store package info and redirect to login
      dispatch(setSelectedPackageId(packageItem.id));

      logEvent('redirect_to_login', {
        reason: 'buy_now_unauthenticated',
        package_id: packageItem.id,
        package_name: packageItem.name,
        screen: 'Packages',
      });

      navigation.getParent().navigate('Auth', {
        screen: 'Login',
        params: { redirectTo: 'PackagesScreen' }
      });
      return;
    }

    // User is authenticated, proceed to checkout
    const descriptions = [
      packageItem?.description_line_01,
      packageItem?.description_line_02,
      packageItem?.description_line_03,
      packageItem?.description_line_04,
      packageItem?.description_line_05,
    ].filter(Boolean);

    const checkoutDetails = {
      packageName: packageItem?.name,
      originalPrice: packageItem?.discount_price,
      descriptions,
      packageId: packageItem?.id,
    };

    // Use parent navigation to ensure correct stack
    const parent = navigation.getParent();
    if (parent) {
      parent.navigate('MainTabs', {
        screen: 'HomeTab', // Change to the correct tab/stack if needed
        params: {
          screen: 'CheckoutScreen',
          params: { checkoutDetails }
        }
      });
    } else {
      navigation.navigate('CheckoutScreen', { checkoutDetails });
    }
  };

  const onGestureEvent = (event) => {
    const { nativeEvent } = event;
    if (nativeEvent.state === 5) { // State.END
      if (nativeEvent.translationX > 100) {
        handlePrevious();
      } else if (nativeEvent.translationX < -100) {
        handleNext();
      }
    }
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <BottomTabBar>
        <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}> 
          <View style={styles.content}>
            <Text style={[styles.title, { color: theme.colors.primary }]}>Choose Your Package</Text>
            <View style={styles.carouselContainer}>
              {packages.length > 0 && (
                <>
                  <TouchableOpacity
                    onPress={handlePrevious}
                    style={[styles.arrowButton, styles.leftArrow]}
                  >
                    <Icon name="arrow-left" size={24} color={theme.colors.primary} />
                  </TouchableOpacity>
                  <PanGestureHandler
                    onGestureEvent={onGestureEvent}
                    onHandlerStateChange={onGestureEvent}
                    activeOffsetX={[-20, 20]}
                  >
                    <View style={[styles.packageCard, { backgroundColor: theme.colors.surface }]}> 
                      <Text style={[styles.packageName, { color: theme.colors.text }]}>{packages[currentIndex]?.name}</Text>
                      <Text style={[styles.price, { color: theme.colors.primary }]}>₹{packages[currentIndex]?.discount_price}</Text>
                      <Text style={[styles.packageSubtitle, { color: theme.colors.text }]}>What you get on this Package</Text>
                      <View style={styles.descriptionContainer}>
                        {[
                          packages[currentIndex]?.description_line_01,
                          packages[currentIndex]?.description_line_02,
                          packages[currentIndex]?.description_line_03,
                          packages[currentIndex]?.description_line_04,
                          packages[currentIndex]?.description_line_05,
                        ].filter(Boolean).map((desc, index) => (
                          <View key={index} style={styles.descriptionItem}>
                            <Icon name="check" size={16} color={theme.colors.primary} style={styles.checkIcon} />
                            <Text style={[styles.descriptionText, { color: theme.colors.text }]}>{desc}</Text>
                          </View>
                        ))}
                      </View>
                      <View style={styles.buttonContainer}>
                        {isPackageInCart(packages[currentIndex]?.id) ? (
                          <TouchableOpacity
                            style={[styles.removeFromCartButton, { borderColor: '#dc3545', backgroundColor: '#dc3545' }]}
                            onPress={() => handleRemoveFromCart(packages[currentIndex])}
                          >
                            <Icon name="trash" size={16} color="#fff" />
                            <Text style={[styles.removeFromCartButtonText]}>
                              Remove from Cart
                            </Text>
                          </TouchableOpacity>
                        ) : (
                          <TouchableOpacity
                            style={[styles.addToCartButton, { borderColor: theme.colors.primary }]}
                            onPress={() => handleAddToCart(packages[currentIndex])}
                          >
                            <Icon name="shopping-cart" size={16} color={theme.colors.primary} />
                            <Text style={[styles.addToCartButtonText, { color: theme.colors.primary }]}>
                              Add to Cart
                            </Text>
                          </TouchableOpacity>
                        )}

                        <TouchableOpacity
                          style={[styles.buyButton, { backgroundColor: theme.colors.primary }]}
                          onPress={() => handleBuyNow(packages[currentIndex])}
                        >
                          <Text style={styles.buyButtonText}>Buy Now</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </PanGestureHandler>
                  <TouchableOpacity
                    onPress={handleNext}
                    style={[styles.arrowButton, styles.rightArrow]}
                  >
                    <Icon name="arrow-right" size={24} color={theme.colors.primary} />
                  </TouchableOpacity>
                </>
              )}
            </View>
            <View style={styles.dotsContainer}>
              {packages.map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.dot,
                    { backgroundColor: index === currentIndex ? theme.colors.primary : theme.colors.disabled }
                  ]}
                />
              ))}
            </View>
          </View>
          {showModal && (
            <Modal
              animationType="slide"
              transparent={true}
              visible={showModal}
              onRequestClose={handleCloseModal}
            >
              <View style={[styles.modalContainer, { backgroundColor: theme.colors.backdrop }]}> 
                <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}> 
                  <Text style={[styles.modalTitle, { color: theme.colors.text }]}>{checkoutDetails?.packageName}</Text>
                  <Text style={[styles.modalPrice, { color: theme.colors.primary }]}>₹{checkoutDetails?.originalPrice}</Text>
                  <View style={styles.modalDescriptionContainer}>
                    {checkoutDetails?.descriptions?.map((desc, index) => (
                      <View key={index} style={styles.modalDescriptionItem}>
                        <Icon name="check" size={16} color={theme.colors.primary} style={styles.modalCheckIcon} />
                        <Text style={[styles.modalDescriptionText, { color: theme.colors.text }]}>{desc}</Text>
                      </View>
                    ))}
                  </View>
                  <TouchableOpacity
                    style={[styles.modalBuyButton, { backgroundColor: theme.colors.primary }]}
                    onPress={handleCloseModal}
                  >
                    <Text style={styles.modalBuyButtonText}>Proceed to Checkout</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.modalCloseButton, { backgroundColor: theme.colors.disabled }]}
                    onPress={handleCloseModal}
                  >
                    <Text style={[styles.modalCloseButtonText, { color: theme.colors.text }]}>Close</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Modal>
          )}
        </ScrollView>
      </BottomTabBar>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
  },
  carouselContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  arrowButton: {
    padding: 10,
  },
  leftArrow: {
    marginRight: 10,
  },
  rightArrow: {
    marginLeft: 10,
  },
  packageCard: {
    borderRadius: 10,
    padding: 20,
    width: width - 100,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    transform: [{ scale: 1 }],
    zIndex: 1,
    elevation: 5,
  },
  packageName: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  packageSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  price: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
  },
  descriptionContainer: {
    marginBottom: 20,
  },
  descriptionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkIcon: {
    marginRight: 10,
  },
  descriptionText: {
    fontSize: 14,
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 10,
  },
  addToCartButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 5,
    borderWidth: 2,
    backgroundColor: 'transparent',
  },
  addToCartButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  removeFromCartButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 5,
    borderWidth: 2,
  },
  removeFromCartButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
    color: '#fff',
  },
  buyButton: {
    flex: 1,
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
  },
  buyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    borderRadius: 10,
    padding: 20,
    width: width - 40,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  modalPrice: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
  },
  modalDescriptionContainer: {
    marginBottom: 20,
  },
  modalDescriptionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  modalCheckIcon: {
    marginRight: 10,
  },
  modalDescriptionText: {
    fontSize: 14,
    flex: 1,
  },
  modalBuyButton: {
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginBottom: 10,
  },
  modalBuyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalCloseButton: {
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
  },
  modalCloseButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default PackagesScreen;
