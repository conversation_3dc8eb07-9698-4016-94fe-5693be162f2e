"""
Django Management Command to Check and Send Notification Events
This command should be run daily via cron job to check for various notification triggers
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from librarian.notification_events import notification_events
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Check and send notification events (visitor callbacks, member expiry, sales milestones, etc.)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--event-type',
            type=str,
            help='Specific event type to check (visitor_callbacks, member_expiry, subscription_expiry, sales_milestones, monthly_summary, galla_reminder, seat_occupancy)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending notifications',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output',
        )

    def handle(self, *args, **options):
        event_type = options.get('event_type')
        dry_run = options.get('dry_run', False)
        verbose = options.get('verbose', False)
        
        if dry_run:
            self.stdout.write(self.style.WARNING('🧪 DRY RUN MODE - No notifications will be sent'))
        
        self.stdout.write(f'🔔 Checking notification events at {timezone.now()}')
        
        total_notifications = 0
        
        try:
            if event_type == 'visitor_callbacks' or event_type is None:
                self.stdout.write('\n📞 Checking visitor callbacks...')
                count = notification_events.check_visitor_callbacks_today()
                total_notifications += count
                self.stdout.write(f'   Found {count} visitor callbacks due today')
            
            if event_type == 'member_expiry' or event_type is None:
                self.stdout.write('\n⏰ Checking member expiry notifications...')
                count = notification_events.check_member_expiry_notifications()
                total_notifications += count
                self.stdout.write(f'   Sent {count} member expiry notifications')

            if event_type == 'membership_expiry' or event_type is None:
                self.stdout.write('\n🏛️ Checking librarian membership expiry notifications...')
                count = notification_events.check_membership_expiry_notifications()
                total_notifications += count
                self.stdout.write(f'   Sent {count} librarian membership expiry notifications')

            if event_type == 'subscription_expiry' or event_type is None:
                self.stdout.write('\n📅 Checking student subscription expiry notifications...')
                count = notification_events.check_student_subscription_expiry_notifications()
                total_notifications += count
                self.stdout.write(f'   Sent {count} student subscription expiry notifications')

            if event_type == 'seat_occupancy' or event_type is None:
                self.stdout.write('\n🪑 Checking seat occupancy alerts...')
                count = notification_events.check_seat_occupancy()
                total_notifications += count
                self.stdout.write(f'   Sent {count} seat occupancy alerts')
            
            if event_type == 'sales_milestones' or event_type is None:
                self.stdout.write('\n💰 Checking sales milestones...')
                current_sales = notification_events.check_sales_milestones()
                self.stdout.write(f'   Current monthly sales: ₹{current_sales:,}')
            
            if event_type == 'monthly_summary' or event_type is None:
                self.stdout.write('\n📊 Checking monthly sales summary...')
                sent = notification_events.send_monthly_sales_summary()
                if sent:
                    self.stdout.write('   Monthly sales summary sent')
                    total_notifications += 1
                else:
                    self.stdout.write('   Not last day of month - no summary sent')
            
            if event_type == 'galla_reminder' or event_type is None:
                self.stdout.write('\n📝 Sending daily galla reminders...')
                count = notification_events.send_daily_galla_reminder()
                total_notifications += count
                self.stdout.write(f'   Sent {count} galla reminders')
            
            # Summary
            self.stdout.write(f'\n✅ Notification check completed')
            self.stdout.write(f'📊 Total notifications processed: {total_notifications}')
            
            if verbose:
                self.stdout.write('\n📋 Event Summary:')
                self.stdout.write('   - Visitor callbacks: Daily check for callbacks due today')
                self.stdout.write('   - Member expiry: Check for 10, 5, 1 day warnings and expired members')
                self.stdout.write('   - Subscription expiry: Enhanced check including 4 days after expiry')
                self.stdout.write('   - Sales milestones: Check for 50k, 100k, 150k, 200k milestones')
                self.stdout.write('   - Monthly summary: Send on last day of month')
                self.stdout.write('   - Galla reminders: Daily reminders to sublibrarians')
                self.stdout.write('   - Seat occupancy: Alert when any shift reaches 80% capacity')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error during notification check: {e}')
            )
            logger.error(f'Notification check error: {e}')
            
        self.stdout.write(self.style.SUCCESS('🎉 Notification event check completed!'))


# Example cron job entries:
"""
# Add these to your crontab (crontab -e) for automatic notification checking:

# Check all events daily at 9:00 AM
0 9 * * * cd /path/to/your/project && python manage.py check_notification_events

# Check visitor callbacks every 2 hours during business hours
0 9,11,13,15,17 * * * cd /path/to/your/project && python manage.py check_notification_events --event-type visitor_callbacks

# Send galla reminders at 6:00 PM daily
0 18 * * * cd /path/to/your/project && python manage.py check_notification_events --event-type galla_reminder

# Check member expiry at 8:00 AM daily
0 8 * * * cd /path/to/your/project && python manage.py check_notification_events --event-type member_expiry

# Check sales milestones every 4 hours during business hours
0 10,14,18 * * * cd /path/to/your/project && python manage.py check_notification_events --event-type sales_milestones

# Send monthly summary on last day of month at 11:00 PM
0 23 28-31 * * cd /path/to/your/project && python manage.py check_notification_events --event-type monthly_summary
"""
