{"name": "shash_native_javascript", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/config-plugins": "~10.1.1", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/masked-view": "^0.1.11", "@react-native-google-signin/google-signin": "^15.0.0", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/drawer": "^7.5.3", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "eas-cli": "^16.17.0", "expo": "53.0.20", "expo-asset": "^11.1.7", "expo-auth-session": "^6.2.1", "expo-av": "^15.1.7", "expo-constants": "^17.1.7", "expo-contacts": "^14.2.5", "expo-device": "^7.1.4", "expo-file-system": "^18.1.11", "expo-firebase-analytics": "^8.0.0", "expo-font": "^13.3.2", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-notifications": "^0.31.4", "expo-random": "^14.0.1", "expo-screen-orientation": "^8.1.7", "expo-status-bar": "~2.2.3", "expo-web-browser": "^14.2.0", "lottie-react-native": "^7.2.2", "react": "19.0.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "^15.11.2", "react-native-toast-message": "^2.3.3", "react-native-vector-icons": "^10.2.0", "react-native-view-shot": "^4.0.3", "react-native-webview": "^13.13.5", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}