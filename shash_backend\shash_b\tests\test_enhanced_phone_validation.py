#!/usr/bin/env python3
"""
Test enhanced phone number validation with # and * support
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from contacts.models import normalize_phone_number


def test_enhanced_phone_normalization():
    """Test the enhanced phone number normalization"""
    print("🔍 Testing Enhanced Phone Number Validation")
    print("=" * 60)
    
    # Test cases: (input, expected_output, description)
    test_cases = [
        # Valid regular numbers
        ('+919876543210', '9876543210', 'Valid number with +91'),
        ('91-9876543210', '9876543210', 'Valid number with 91-'),
        ('9876543210', '9876543210', 'Valid 10-digit number'),
        ('+91 9876 543 210', '9876543210', 'Valid number with spaces'),
        ('919876543210', '9876543210', 'Valid 12-digit with 91'),
        
        # Invalid regular numbers (less than 10 digits)
        ('123456789', None, 'Only 9 digits - should be rejected'),
        ('12345', None, 'Only 5 digits - should be rejected'),
        ('987654321', None, 'Only 9 digits - should be rejected'),
        ('', None, 'Empty string - should be rejected'),
        ('abc', None, 'Non-numeric - should be rejected'),
        
        # Invalid regular numbers (wrong prefix)
        ('1234567890', None, '10 digits but starts with 1 - should be rejected'),
        ('5876543210', None, '10 digits but starts with 5 - should be rejected'),
        ('0876543210', None, '10 digits but starts with 0 - should be rejected'),
        
        # Special numbers with # and *
        ('*123#', '*123#', 'USSD code with * and #'),
        ('*121#', '*121#', 'Balance check code'),
        ('*555*123#', '*555*123#', 'Complex USSD code'),
        ('123*456#', '123*456#', 'Mixed special number'),
        ('*#06#', '*#06#', 'IMEI check code'),
        ('##123##', '##123##', 'Double hash code'),
        ('***123***', '***123***', 'Multiple asterisk code'),
        
        # Special numbers with formatting
        ('*1 2 3#', '*123#', 'USSD with spaces'),
        ('* 121 #', '*121#', 'Balance check with spaces'),
        
        # Invalid special numbers (too short/long)
        ('*#', None, 'Too short special number'),
        ('*' + '1' * 20 + '#', None, 'Too long special number'),
        
        # Edge cases
        ('91987654321', None, '11 digits starting with 91 - invalid'),
        ('919876543210123', None, 'Too many digits'),
        ('+91987654321', None, '11 digits with +91 - invalid'),
        ('9876543210123', None, '13 digits - invalid'),
        
        # Mixed valid/invalid
        ('9876543210abc', '9876543210', 'Valid number with letters'),
        ('+91-9876-543-210', '9876543210', 'Valid number with dashes'),
        ('(+91) 9876 543 210', '9876543210', 'Valid number with parentheses'),
    ]
    
    passed = 0
    failed = 0
    
    for input_phone, expected, description in test_cases:
        result = normalize_phone_number(input_phone)
        
        if result == expected:
            status = "✅ PASS"
            passed += 1
        else:
            status = "❌ FAIL"
            failed += 1
        
        print(f"{status} | Input: '{input_phone}' → Output: '{result}' | Expected: '{expected}'")
        print(f"      | {description}")
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Enhanced validation is working correctly.")
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
    
    return failed == 0


def test_special_number_examples():
    """Test real-world special number examples"""
    print("\n🔍 Testing Real-World Special Numbers")
    print("=" * 60)
    
    special_numbers = [
        '*121#',      # Balance check
        '*123#',      # Prepaid balance
        '*555#',      # Full talktime
        '*141#',      # Last call details
        '*543#',      # Validity check
        '*#06#',      # IMEI number
        '##123##',    # Some service codes
        '*99#',       # Mobile banking
        '*444#',      # Airtel services
        '*567#',      # Jio services
        '*199#',      # Vodafone services
        '*#*#4636#*#*',  # Android service menu
    ]
    
    print("Testing common USSD and service codes:")
    for number in special_numbers:
        result = normalize_phone_number(number)
        status = "✅" if result == number else "❌"
        print(f"{status} {number} → {result}")
    
    print("\nTesting special numbers with formatting:")
    formatted_specials = [
        ('* 1 2 1 #', '*121#'),
        ('* 123 #', '*123#'),
        ('*#0 6#', '*#06#'),
        ('* 99 #', '*99#'),
    ]
    
    for input_num, expected in formatted_specials:
        result = normalize_phone_number(input_num)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{input_num}' → '{result}' (expected: '{expected}')")


def test_edge_cases():
    """Test edge cases and boundary conditions"""
    print("\n🔍 Testing Edge Cases")
    print("=" * 60)
    
    edge_cases = [
        (None, None, "None input"),
        ('', None, "Empty string"),
        ('   ', None, "Whitespace only"),
        ('0' * 9, None, "9 zeros"),
        ('1' * 10, None, "10 ones (invalid prefix)"),
        ('9' * 10, '9999999999', "10 nines (valid)"),
        ('8' * 10, '8888888888', "10 eights (valid)"),
        ('7' * 10, '7777777777', "10 sevens (valid)"),
        ('6' * 10, '6666666666', "10 sixes (valid)"),
        ('5' * 10, None, "10 fives (invalid prefix)"),
        ('*', None, "Single asterisk"),
        ('#', None, "Single hash"),
        ('*#', None, "Just * and #"),
        ('*1#', '*1#', "Minimal valid special"),
        ('*' + '1' * 18 + '#', '*' + '1' * 18 + '#', "Max length special"),
        ('*' + '1' * 19 + '#', None, "Over max length special"),
    ]
    
    for input_val, expected, description in edge_cases:
        result = normalize_phone_number(input_val)
        status = "✅" if result == expected else "❌"
        print(f"{status} {description}: '{input_val}' → '{result}'")


def main():
    """Run all tests"""
    print("🚀 Enhanced Phone Number Validation Test Suite")
    print("=" * 80)
    
    success = True
    
    try:
        # Run all test suites
        success &= test_enhanced_phone_normalization()
        test_special_number_examples()
        test_edge_cases()
        
        print("\n" + "=" * 80)
        if success:
            print("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
            print("✅ Enhanced phone validation is working correctly")
            print("✅ Support for # and * characters implemented")
            print("✅ Filtering of numbers with less than 10 digits working")
        else:
            print("❌ SOME TESTS FAILED!")
            print("Please review the implementation")
        
        print("\n📋 Summary of Features:")
        print("• ✅ Filters out numbers with less than 10 digits")
        print("• ✅ Supports special numbers with # and * characters")
        print("• ✅ Validates Indian mobile number prefixes (6,7,8,9)")
        print("• ✅ Removes country codes and formatting")
        print("• ✅ Handles edge cases and invalid inputs")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
