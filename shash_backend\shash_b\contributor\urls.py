from django.urls import path
from .views import (
    ContributorDashboardAPIView,
    RegisterView,
    LoginView,
    LogoutView,
    TokenRefreshView,
    BannerListCreateView,
    BannerRetrieveUpdateDestroyView,
    PageVisitors,
    get_all_model_counts,
    # PopupBanner views
    ContributorPopupBannerListCreateView,
    ContributorPopupBannerDetailView,
    PopupBannerStatsView,
    # Earning management views
    ContributorEarningListView,
    ContributorEarningDetailView,
    ContributorEarningSummaryView,
    ContributorTotalEarningsView,
    RecalculateEarningsView,
    ContributorPointsConfigView,
)


urlpatterns = [
    path("register/", RegisterView.as_view(), name="register"),
    path("register/<slug:slug>/", RegisterView.as_view(), name="register-detail"),
    path("login/", LoginView.as_view(), name="login"),
    path("logout/", LogoutView.as_view(), name="logout"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path(
        "dashboard/",
        ContributorDashboardAPIView.as_view(),
        name="contributor-dashboard",
    ),
    path('banners/', BannerListCreateView.as_view(), name='banner-list-create'),
    path('banners/<int:pk>/', BannerRetrieveUpdateDestroyView.as_view(), name='banner-retrieve-update-destroy'),
    path("track-page-view/", PageVisitors, name="track_page_view"),
    path('get-all-model-counts/', get_all_model_counts, name='get-all-model-counts'),

    # PopupBanner URLs for contributors
    path('popup-banners/', ContributorPopupBannerListCreateView.as_view(), name='contributor-popup-banner-list-create'),
    path('popup-banners/<int:pk>/', ContributorPopupBannerDetailView.as_view(), name='contributor-popup-banner-detail'),
    path('popup-banners/stats/', PopupBannerStatsView.as_view(), name='contributor-popup-banner-stats'),

    # Earning Management URLs
    path('earnings/', ContributorEarningListView.as_view(), name='contributor-earning-list'),
    path('earnings/<int:pk>/', ContributorEarningDetailView.as_view(), name='contributor-earning-detail'),
    path('earnings/summary/', ContributorEarningSummaryView.as_view(), name='contributor-earning-summary'),
    path('earnings/total/', ContributorTotalEarningsView.as_view(), name='contributor-total-earnings'),
    path('earnings/recalculate/', RecalculateEarningsView.as_view(), name='contributor-recalculate-earnings'),
    path('points-config/', ContributorPointsConfigView.as_view(), name='contributor-points-config'),
]
