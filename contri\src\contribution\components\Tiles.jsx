import React from "react";
import PropTypes from "prop-types";
import { Card, Col, Row } from "react-bootstrap";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import ContributionTileSkeleton from "../../commonComponents/ContributionTileSkeleton";

const Tiles = ({ tilesData, isLoading }) => {
  return (
    <Row xs={1} sm={2} md={3} lg={4} className="g-4">
      {isLoading
        ? // Show skeleton placeholders
          <ContributionTileSkeleton number={8}/>
        : // Show actual tile data
          tilesData.map((tile, index) => (
            <Col key={index}>
              <Card
                style={{
                  backgroundColor: tile.color,
                  borderRadius: "10px",
                  color: tile.textColor || "#fff",
                  height: "100%",
                }}
              >
                <Card.Body className="text-center">
                  {tile.icon && (
                    <tile.icon size="3em" style={{ marginBottom: "10px" }} />
                  )}
                  <Card.Title>{tile.title}</Card.Title>
                  <Card.Text>{tile.count}</Card.Text>
                </Card.Body>
              </Card>
            </Col>
          ))}
    </Row>
  );
};

Tiles.propTypes = {
  tilesData: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      count: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
        .isRequired, // Accepts both string and number
      icon: PropTypes.elementType.isRequired, // Accepts a React component
      color: PropTypes.string.isRequired,
      textColor: PropTypes.string, // Optional text color
    })
  ).isRequired,
  isLoading: PropTypes.bool.isRequired,
};

export default Tiles;
