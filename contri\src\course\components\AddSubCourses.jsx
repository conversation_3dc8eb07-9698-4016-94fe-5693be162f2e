import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { useDispatch } from "react-redux";
import { createSubCourse } from "../../redux/slice/subCourseSlice"; // Redux action to create sub-course
import { Button, Form, Card } from "react-bootstrap";
import toast from "react-hot-toast";

  const AddSubCourses = ({ slug, onSubCourseAdded }) => {
    const dispatch = useDispatch();
  
    const [subCourseName, setSubCourseName] = useState("");
    const [subCourseDescription, setSubCourseDescription] = useState("");
  
    const handleAddSubCourse = async (event) => {
      event.preventDefault(); // Prevent default form submission
    
      if (!subCourseName || !subCourseDescription) {
        toast.error("Please provide all fields for the sub-course.");
        return;
      }
    
      try {
        const actionResult = await dispatch(
          createSubCourse({
            subCourseData: {
              course: slug,
              name: subCourseName,
              description: subCourseDescription,
            },
          })
        );
    
        // Check if the action was fulfilled
        if (actionResult.meta.requestStatus === "fulfilled") {
          toast.success("Sub-course added successfully!");
          onSubCourseAdded(); // Notify parent to refresh sub-courses
    
          // Reset the form only if successful
          setSubCourseName("");
          setSubCourseDescription("");
        } else {
          // Handle failure case
          const errorMessage =
            actionResult.payload?.error || "Failed to add sub-course.";
          toast.error(errorMessage);
          console.error("Error response:", actionResult);
        }
      } catch (error) {
        console.error("Error adding sub-course:", error);
        toast.error("An unexpected error occurred while adding the sub-course.");
      }
    };
    
  

  return (
    <>
      <Card className="shadow-lg rounded-3 mt-5">
        <Card.Body>
          <Card.Title className="text-center text-success">Add Sub-course</Card.Title>
          <Form onSubmit={handleAddSubCourse}> {/* Use onSubmit for form submission */}
            <Form.Group controlId="subCourseName">
              <Form.Label>Sub-course Name</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter sub-course name"
                value={subCourseName}
                onChange={(e) => setSubCourseName(e.target.value)}
              />
            </Form.Group>

            <Form.Group controlId="subCourseDescription" className="mt-2">
              <Form.Label>Sub-course Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={4}
                placeholder="Enter sub-course description"
                value={subCourseDescription}
                onChange={(e) => setSubCourseDescription(e.target.value)}
              />
            </Form.Group>

            <Button variant="success" className="w-100 mt-3" type="submit">
              Add Sub-course
            </Button>
          </Form>
        </Card.Body>
      </Card>

      <div className="d-flex justify-content-center align-items-center">
        <Link to="/contributor_dashboard">
          <Button variant="secondary" className="mt-5 text-center">
            Back to Dashboard
          </Button>
        </Link>
      </div>
    </>
  );
};

export default AddSubCourses;
