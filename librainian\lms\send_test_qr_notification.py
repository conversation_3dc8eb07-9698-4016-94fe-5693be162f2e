#!/usr/bin/env python3
"""
Send Test QR Notification
This script sends a test QR notification with real data to verify the fix.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def send_test_qr_notification():
    """Send a test QR notification with real data"""
    print("🔔 Sending Test QR Registration Notification")
    print("=" * 50)
    
    try:
        from librarian.notification_utils import send_fcm_notification
        from librarian.models import DeviceToken
        from django.contrib.auth.models import User
        
        # Find user with device token
        device_token = DeviceToken.objects.filter(is_active=True).first()
        if not device_token:
            print("❌ No active device tokens found!")
            return False
        
        user = device_token.user
        print(f"📱 Sending to user: {user.username}")
        print(f"🎫 Device: {device_token.device_name}")
        
        # Send test notification with complete data
        result = send_fcm_notification(
            user=user,
            title="📝 New QR Registration - FIXED!",
            body="""New student registration: <PERSON>
Course: Computer Science Engineering
Email: <EMAIL>
Mobile: +91-9876543210
City: Mumbai, Maharashtra
Registration Date: 2025-01-15
Status: Pending Approval""",
            data={
                "type": "qr_registration_test",
                "student_name": "John Doe Smith",
                "student_email": "<EMAIL>",
                "student_mobile": "+91-9876543210",
                "course": "Computer Science Engineering",
                "student_city": "Mumbai",
                "registration_date": "2025-01-15",
                "action_url": "/students/temp-students/",
                "test_fix": "template_variables_working"
            }
        )
        
        if result and result.get('successful_count', 0) > 0:
            print("✅ Test QR notification sent successfully!")
            print(f"📊 Success: {result['successful_count']}, Failed: {result['failed_count']}")
            
            if result.get('successful_sends'):
                for send in result['successful_sends']:
                    print(f"📨 Message ID: {send.get('message_id', 'N/A')}")
            
            print("\n💡 Check your browser for the notification!")
            print("   • If browser tab is ACTIVE: Custom popup notification")
            print("   • If browser tab is MINIMIZED: System notification")
            
            return True
        else:
            print("❌ Test notification failed")
            if result and result.get('failed_sends'):
                for fail in result['failed_sends']:
                    print(f"❌ Error: {fail.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error sending test notification: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🧪 QR Registration Notification Fix - Live Test")
    print("=" * 60)
    
    success = send_test_qr_notification()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS: Fixed QR notification sent!")
        print("\n📋 What you should see:")
        print("   ✅ Proper student name instead of {student_name}")
        print("   ✅ Actual course name instead of {course}")
        print("   ✅ Real email address instead of {student_email}")
        print("   ✅ Complete student information displayed")
        
        print("\n🔍 Compare with your previous notification:")
        print("   ❌ Before: 'New student registration: {student_name}'")
        print("   ✅ After:  'New student registration: John Doe Smith'")
        
    else:
        print("❌ Test failed. Check the errors above.")
    
    print("\n🌐 Next Steps:")
    print("   1. Register a new student via QR code")
    print("   2. Verify the notification shows real data")
    print("   3. All template variables should be properly replaced")

if __name__ == "__main__":
    main()
