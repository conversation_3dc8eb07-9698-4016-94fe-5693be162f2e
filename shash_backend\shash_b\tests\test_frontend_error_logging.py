#!/usr/bin/env python3
"""
Comprehensive test script for the enhanced frontend error logging system
"""

import os
import sys
import django
import json
import time
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status

from customrcare.models import FrontendError
from customrcare.views import FrontendErrorView, FrontendErrorAnalyticsView


class TestResults:
    """Class to track test results"""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"✅ PASS: {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"❌ FAIL: {test_name} - {error}")
    
    def print_summary(self):
        total = self.passed + self.failed
        print(f"\n{'='*60}")
        print(f"FRONTEND ERROR LOGGING TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success Rate: {(self.passed/total*100):.1f}%" if total > 0 else "No tests run")
        
        if self.errors:
            print(f"\nFAILED TESTS:")
            for error in self.errors:
                print(f"  - {error}")


def test_enhanced_frontend_error_model():
    """Test the enhanced FrontendError model"""
    results = TestResults()
    
    try:
        # Create test user with unique username
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        user = User.objects.create_user(
            username=f'frontend_test_user_{unique_id}',
            email=f'frontend_test_{unique_id}@example.com',
            password='testpass123'
        )
        
        # Test creating comprehensive frontend error
        error_data = {
            'error_type': 'JAVASCRIPT',
            'severity': 'HIGH',
            'error_message': 'Test JavaScript error',
            'stack_trace': 'Error: Test error\n    at testFunction (test.js:10:5)',
            'user': user,
            'session_id': 'test_session_123',
            'user_agent': 'Mozilla/5.0 (Test Browser)',
            'ip_address': '127.0.0.1',
            'browser_name': 'CHROME',
            'browser_version': '91.0',
            'device_type': 'desktop',
            'screen_resolution': '1920x1080',
            'page_url': 'https://example.com/test',
            'page_title': 'Test Page',
            'referrer_url': 'https://example.com/home',
            'component_name': 'TestComponent',
            'function_name': 'testFunction',
            'line_number': 10,
            'column_number': 5,
            'error_data': {'custom_field': 'test_value'},
            'user_actions': [{'type': 'click', 'target': 'button#test'}],
            'console_logs': [{'level': 'error', 'message': 'Test console error'}]
        }
        
        frontend_error = FrontendError.objects.create(**error_data)
        results.add_pass("Enhanced FrontendError model creation")
        
        # Test string representation
        str_repr = str(frontend_error)
        if 'JAVASCRIPT' in str_repr and 'Test JavaScript error' in str_repr:
            results.add_pass("FrontendError string representation")
        else:
            results.add_fail("FrontendError string representation", f"Unexpected format: {str_repr}")
        
        # Test model fields
        assert frontend_error.error_type == 'JAVASCRIPT'
        assert frontend_error.severity == 'HIGH'
        assert frontend_error.occurrence_count == 1
        assert frontend_error.resolved == False
        results.add_pass("FrontendError field validation")
        
        # Test duplicate error handling
        duplicate_error = FrontendError.objects.create(
            error_message='Test JavaScript error',
            page_url='https://example.com/test',
            user=user,
            error_type='JAVASCRIPT'
        )
        
        # Check if occurrence count was incremented instead of creating new error
        original_error = FrontendError.objects.get(id=frontend_error.id)
        if original_error.occurrence_count > 1:
            results.add_pass("Duplicate error handling (occurrence count)")
        else:
            results.add_fail("Duplicate error handling", "Occurrence count not incremented")
        
    except Exception as e:
        results.add_fail("Enhanced FrontendError model tests", str(e))
    
    return results


def test_enhanced_frontend_error_api():
    """Test the enhanced frontend error API endpoints"""
    results = TestResults()
    
    try:
        # Create authenticated user for protected endpoints
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        auth_user = User.objects.create_user(
            username=f'api_test_user_{unique_id}',
            email=f'api_test_{unique_id}@example.com',
            password='testpass123'
        )

        # Create Student profile for the user to pass permission checks
        from students.models import Student
        try:
            Student.objects.create(
                user=auth_user,
                phone_number='1234567890',
                # Add any other required fields for Student model
            )
        except Exception:
            # If Student creation fails, create CustomrcareProfile instead
            from customrcare.models import CustomrcareProfile
            CustomrcareProfile.objects.create(
                user=auth_user,
                role='customrcare',
                contact='1234567890'  # Add required contact field
            )

        client = APIClient()

        # Test comprehensive error logging
        error_data = {
            'error_message': 'Test API error logging',
            'error_type': 'NETWORK',
            'severity': 'MEDIUM',
            'stack_trace': 'NetworkError: Failed to fetch',
            'page_url': 'https://example.com/api-test',
            'page_title': 'API Test Page',
            'referrer_url': 'https://example.com/home',
            'component_name': 'ApiComponent',
            'function_name': 'fetchData',
            'line_number': 25,
            'column_number': 10,
            'screen_resolution': '1366x768',
            'additional_data': {
                'api_endpoint': '/api/test',
                'status_code': 500
            },
            'user_actions': [
                {'type': 'click', 'target': 'button#fetch-data', 'timestamp': '2023-01-01T10:00:00Z'}
            ],
            'console_logs': [
                {'level': 'error', 'message': 'Network request failed', 'timestamp': '2023-01-01T10:00:01Z'}
            ]
        }
        
        response = client.post('/api/customrcare/log-error/', error_data, format='json')

        if response.status_code == 201:
            results.add_pass("Enhanced error logging API endpoint")

            response_data = response.json()
            if 'id' in response_data and 'error_type' in response_data:
                results.add_pass("API response format")
            else:
                results.add_fail("API response format", f"Missing fields in response: {response_data}")
        else:
            results.add_fail("Enhanced error logging API endpoint", f"Status: {response.status_code}, Response: {response.content}")

        # Test error retrieval with filters (authenticate for protected endpoint)
        client.force_authenticate(user=auth_user)
        response = client.get('/api/customrcare/get-errors/?error_type=NETWORK&severity=MEDIUM')

        if response.status_code == 200:
            results.add_pass("Error retrieval with filters")

            response_data = response.json()
            if 'errors' in response_data and 'total_count' in response_data:
                results.add_pass("Filtered error response format")
            else:
                results.add_fail("Filtered error response format", f"Missing fields: {response_data}")
        else:
            results.add_fail("Error retrieval with filters", f"Status: {response.status_code}")

        # Test analytics endpoint (already authenticated)
        response = client.get('/api/customrcare/error-analytics/?days=7')
        
        if response.status_code == 200:
            results.add_pass("Error analytics endpoint")
            
            response_data = response.json()
            expected_fields = ['total_errors', 'by_type', 'by_severity', 'by_browser', 'error_trends']
            missing_fields = [field for field in expected_fields if field not in response_data]
            
            if not missing_fields:
                results.add_pass("Analytics response format")
            else:
                results.add_fail("Analytics response format", f"Missing fields: {missing_fields}")
        else:
            results.add_fail("Error analytics endpoint", f"Status: {response.status_code}")
        
    except Exception as e:
        results.add_fail("Enhanced frontend error API tests", str(e))
    
    return results


def test_error_resolution_workflow():
    """Test error resolution and management workflow"""
    results = TestResults()
    
    try:
        # Create admin user with unique username
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        admin_user = User.objects.create_superuser(
            username=f'frontend_admin_{unique_id}',
            email=f'frontend_admin_{unique_id}@example.com',
            password='adminpass123'
        )
        
        client = APIClient()
        client.force_authenticate(user=admin_user)
        
        # Create test error
        error = FrontendError.objects.create(
            error_type='VALIDATION',
            severity='LOW',
            error_message='Test validation error',
            page_url='https://example.com/form',
            user=admin_user
        )
        
        # Test error resolution
        resolution_data = {
            'resolution_notes': 'Fixed validation logic in form component'
        }
        
        response = client.post(f'/api/customrcare/error-resolution/{error.id}/', resolution_data, format='json')

        if response.status_code == 200:
            results.add_pass("Error resolution endpoint")

            # Verify error was marked as resolved
            error.refresh_from_db()
            if error.resolved and error.resolved_by == admin_user:
                results.add_pass("Error resolution status update")
            else:
                results.add_fail("Error resolution status update", "Error not properly marked as resolved")
        else:
            results.add_fail("Error resolution endpoint", f"Status: {response.status_code}")

        # Test bulk actions
        error2 = FrontendError.objects.create(
            error_type='NETWORK',
            severity='MEDIUM',
            error_message='Test network error',
            page_url='https://example.com/api',
            user=admin_user
        )

        bulk_data = {
            'action': 'resolve',
            'error_ids': [error.id, error2.id],
            'resolution_notes': 'Bulk resolution test'
        }

        response = client.post('/api/customrcare/error-bulk-actions/', bulk_data, format='json')
        
        if response.status_code == 200:
            results.add_pass("Bulk error resolution")
            
            response_data = response.json()
            if response_data.get('affected_count') == 2:
                results.add_pass("Bulk action count verification")
            else:
                results.add_fail("Bulk action count verification", f"Expected 2, got {response_data.get('affected_count')}")
        else:
            results.add_fail("Bulk error resolution", f"Status: {response.status_code}")
        
    except Exception as e:
        results.add_fail("Error resolution workflow tests", str(e))
    
    return results


def test_error_categorization_and_severity():
    """Test automatic error categorization and severity determination"""
    results = TestResults()
    
    try:
        client = APIClient()
        
        # Test different error types and their automatic categorization
        test_cases = [
            {
                'error_message': 'NetworkError: Failed to fetch data from API',
                'expected_type': 'NETWORK',
                'expected_severity': 'MEDIUM'
            },
            {
                'error_message': 'Authentication failed: Invalid token',
                'expected_type': 'AUTHENTICATION',
                'expected_severity': 'HIGH'
            },
            {
                'error_message': 'Permission denied: Access forbidden',
                'expected_type': 'PERMISSION',
                'expected_severity': 'HIGH'
            },
            {
                'error_message': 'Request timeout: Server did not respond',
                'expected_type': 'TIMEOUT',
                'expected_severity': 'MEDIUM'
            },
            {
                'error_message': 'Critical system failure detected',
                'expected_type': 'JAVASCRIPT',
                'expected_severity': 'CRITICAL'
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            error_data = {
                'error_message': test_case['error_message'],
                'page_url': f'https://example.com/test-{i}'
            }
            
            response = client.post('/api/customrcare/log-error/', error_data, format='json')
            
            if response.status_code == 201:
                response_data = response.json()
                
                if response_data.get('error_type') == test_case['expected_type']:
                    results.add_pass(f"Error type categorization: {test_case['expected_type']}")
                else:
                    results.add_fail(
                        f"Error type categorization: {test_case['expected_type']}", 
                        f"Expected {test_case['expected_type']}, got {response_data.get('error_type')}"
                    )
                
                if response_data.get('severity') == test_case['expected_severity']:
                    results.add_pass(f"Severity determination: {test_case['expected_severity']}")
                else:
                    results.add_fail(
                        f"Severity determination: {test_case['expected_severity']}", 
                        f"Expected {test_case['expected_severity']}, got {response_data.get('severity')}"
                    )
            else:
                results.add_fail(f"Error logging for test case {i}", f"Status: {response.status_code}")
        
    except Exception as e:
        results.add_fail("Error categorization and severity tests", str(e))
    
    return results


def main():
    """Run all frontend error logging tests"""
    print("🚀 Starting Enhanced Frontend Error Logging Tests")
    print("="*60)
    
    all_results = TestResults()
    
    # Run all test functions
    test_functions = [
        test_enhanced_frontend_error_model,
        test_enhanced_frontend_error_api,
        test_error_resolution_workflow,
        test_error_categorization_and_severity,
    ]
    
    for test_func in test_functions:
        print(f"\n📋 Running {test_func.__name__}...")
        try:
            result = test_func()
            all_results.passed += result.passed
            all_results.failed += result.failed
            all_results.errors.extend(result.errors)
        except Exception as e:
            all_results.add_fail(test_func.__name__, f"Test function failed: {str(e)}")
            print(f"💥 Error in {test_func.__name__}: {str(e)}")
    
    # Print final summary
    all_results.print_summary()
    
    # Return exit code
    return 0 if all_results.failed == 0 else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
