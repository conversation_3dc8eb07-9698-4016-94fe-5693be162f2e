import React, { useState } from "react";
import { Navbar, Nav, Container, <PERSON><PERSON>, Modal } from "react-bootstrap";
import { FaHeadset, FaBook, FaExclamationCircle } from "react-icons/fa";
import { Link, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { logoutCustomer, clearAuthToken } from "../redux/slice/customerCareSlice";
import { persistStore } from "redux-persist";
import { toast, Toaster } from "react-hot-toast";
import { persistor, store } from "../redux/store";
import Terms from "../lanndingPages/components/Terms";
import Sidebar from "./Sidebar";
import SOPModal from "./SOPModal";

const NavigationBar = () => {
  const [showInstructionModal, setShowInstructionModal] = useState(false);
  const [showSOPModal, setShowSOPModal] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const accessTokenCustomer = useSelector((state) => state.customerCare?.access);

  // Customer Care Logout handler
  const handleLogoutCustomer = async () => {
    try {
      await dispatch(logoutCustomer());
      dispatch(clearAuthToken());

      // Clear only Customer Care persisted and normal state
      store.dispatch({ type: "LOGOUT_CUSTOMER_CARE" });

      // Remove Customer Care data from persisted storage
      await persistor.flush();
      await persistor.purge();

      toast.success("Customer Care logged out successfully!");
      navigate("/");
    } catch (error) {
      toast.error("Customer Care logout failed!");
    }
  };

  return (
    <>
      {/* Sidebar Component - Only shows when user is logged in */}
      <Sidebar />

      <Navbar expand="lg" bg="light" variant="light" sticky="top" className="shadow-sm">
        <Container>
          {/* Logo Section */}
          <Navbar.Brand as={Link} to="/">
            <img src="/logoB.png" alt="Logo" width="180" height="auto" className="d-inline-block align-top" />
          </Navbar.Brand>

          {/* Responsive Toggle */}
          <Navbar.Toggle aria-controls="navbar-nav" />

          <Navbar.Collapse id="navbar-nav">
            <Nav className="ms-auto align-items-start align-items-md-center">
              {/* Customer Care Section */}
              {accessTokenCustomer ? (
                <Nav.Link
                  as={Link}
                  to="/customer_care_dashboard"
                  className="text-primary fw-bold"
                  style={{ fontSize: "0.9rem" }}
                >
                  Customer Care Dashboard
                </Nav.Link>
              ) : null}

              {/* Customer Care Logout Button */}
              {accessTokenCustomer && (
                <Button variant="outline-danger" className="m-1" onClick={handleLogoutCustomer}>
                  <FaHeadset className="m-1" />
                  Customer Care Logout
                </Button>
              )}

              {/* Exam Button */}
              <Button
                as="a"
                href="https://exam.shashtrath.com"
                target="_blank"
                rel="noopener noreferrer"
                variant="outline-success"
                className="m-1 d-flex align-items-center gap-1"
              >
                <FaBook size={16} />
                Exam
              </Button>

              {/* Instruction Button */}
              {/* <Button
                variant="outline-primary"
                className="m-1 d-flex align-items-center gap-1"
                onClick={() => setShowInstructionModal(!showInstructionModal)}
              >
                <FaExclamationCircle size={16} />
                Instruction
              </Button> */}

              {/* SOP Button - Only show if logged in */}
              {accessTokenCustomer && (
                <Button
                  variant="outline-secondary"
                  className="m-1 d-flex align-items-center gap-1"
                  onClick={() => setShowSOPModal(true)}
                >
                  SOP
                </Button>
              )}
            </Nav>
          </Navbar.Collapse>
        </Container>

        {/* Instructions Modal */}
        <Modal show={showInstructionModal} onHide={() => setShowInstructionModal(false)} size="lg">
          <Modal.Header closeButton>
            <Modal.Title>Instructions</Modal.Title>
          </Modal.Header>
          <Modal.Body className="overflow-y-auto m-0 p-0">
            <Terms />
          </Modal.Body>
        </Modal>

        {/* SOP Modal */}
        <SOPModal isOpen={showSOPModal} onRequestClose={() => setShowSOPModal(false)} />

        <Toaster />
      </Navbar>
    </>
  );
};

export default NavigationBar;
