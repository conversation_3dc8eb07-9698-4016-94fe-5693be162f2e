import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Nav } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
import { useSelector } from "react-redux";
import {
  FaBars,
  FaTachometerAlt,
  FaBook,
  FaQuestionCircle,
  FaClipboardList,
  FaUsers,
  FaComments,
  FaGlobeAmericas,
  FaChalkboardTeacher,
  FaShapes,
  FaBookOpen,
  FaBlogger,
  FaBullhorn
} from "react-icons/fa";

const Sidebar = () => {
  const [show, setShow] = useState(false);
  const accessTokenContributor = useSelector((state) => state.contributor?.accessToken);

  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const navigationItems = [
    {
      name: "Dashboard",
      path: "/contributor_dashboard",
      icon: FaTachometerAlt,
      variant: "primary"
    },
    {
      name: "Contribution",
      path: "/contribution",
      icon: FaUsers,
      variant: "success"
    },
    {
      name: "Normal Question",
      path: "/questions_dashboard",
      icon: FaB<PERSON>,
      variant: "primary"
    },
    {
      name: "Master Question",
      path: "/master_questions_dashboard",
      icon: FaQuestionCircle,
      variant: "success"
    },
    {
      name: "Master Options",
      path: "/master_options_dashboard",
      icon: FaClipboardList,
      variant: "info"
    },
    {
      name: "Prev. Year Ques.",
      path: "/previous_year_questions_dashboard",
      icon: FaComments,
      variant: "warning"
    },
    {
      name: "Current Affairs",
      path: "/all_blogs",
      icon: FaGlobeAmericas,
      variant: "info"
    },
    {
      name: "Courses",
      path: "/add_courses",
      icon: FaChalkboardTeacher,
      variant: "secondary"
    },
    {
      name: "Test Patterns",
      path: "/test_patterns_dashboard",
      icon: FaShapes,
      variant: "primary"
    },
    {
      name: "Subjects",
      path: "/subjects_dashboard",
      icon: FaBookOpen,
      variant: "warning"
    },
    {
      name: "Blogs",
      path: "/blogs_dashboard",
      icon: FaBlogger,
      variant: "success"
    },
    {
      name: "POP UP Dashboard",
      path: "/popup_dashboard",
      icon: FaBullhorn,
      variant: "danger"
    }
  ];

  if (!accessTokenContributor) return null;

  return (
    <>
      {/* Floating Sidebar Toggle Button */}
      <Button
        variant="primary"
        onClick={handleShow}
        style={{
          position: "fixed",
          top: "7px",
          left: "20px",
          zIndex: 1060,
          borderRadius: "50%",
          width: "50px",
          height: "50px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          boxShadow: "0 4px 8px rgba(0,0,0,0.2)"
        }}
        title="Open Navigation Menu"
      >
        <FaBars size={18} />
      </Button>

      {/* Sidebar Offcanvas */}
      <Offcanvas
        show={show}
        onHide={handleClose}
        placement="start"
        style={{ zIndex: 1070 }}
        className="sidebar-offcanvas"
      >
        <Offcanvas.Header closeButton className="bg-primary text-white">
          <Offcanvas.Title>
            <FaTachometerAlt className="me-2" />
            Contributor Menu
          </Offcanvas.Title>
        </Offcanvas.Header>

        <Offcanvas.Body className="p-0">
          <Nav className="flex-column">
            {navigationItems.map((item, index) => {
              const Icon = item.icon;
              return (
                <Nav.Link
                  key={index}
                  as={Link}
                  to={item.path}
                  onClick={handleClose}
                  className="d-flex align-items-center p-3 border-bottom text-decoration-none sidebar-nav-link"
                  style={{
                    transition: "all 0.2s ease",
                    color: "#333"
                  }}
                >
                  <Icon
                    size={18}
                    className="me-3"
                    style={{ color: `var(--bs-${item.variant})` }}
                  />
                  <span className="fw-medium">{item.name}</span>
                </Nav.Link>
              );
            })}
          </Nav>
        </Offcanvas.Body>
      </Offcanvas>

      {/* Custom Styles */}
      <style jsx>{`
        .sidebar-nav-link:hover {
          background-color: #f8f9fa !important;
          transform: translateX(5px);
        }

        .sidebar-offcanvas {
          width: 280px !important;
        }

        @media (max-width: 768px) {
          .sidebar-offcanvas {
            width: 250px !important;
          }
        }
      `}</style>
    </>
  );
};

export default Sidebar;
