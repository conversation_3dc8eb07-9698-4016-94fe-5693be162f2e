import React from "react";
import { <PERSON><PERSON>, <PERSON>, Button } from "react-bootstrap";
import RichTextEditor from "./RichTextEditor";

const MasterOptionPassageModal = ({
  show,
  handleClose,
  updatedData,
  handleInputChange,
  handleSubmit,
}) => {
  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit Option</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group controlId="formTitle">
            <RichTextEditor
              label="Title"
              name="title"
              value={updatedData.title}
              onChange={handleInputChange}
              placeholder="Enter title"
              rows={2}
              required
            />
          </Form.Group>
          <Form.Group controlId="formOptionContent" className="mt-3">
            <RichTextEditor
              label="Option Content"
              name="option_content"
              value={updatedData.option_content}
              onChange={handleInputChange}
              placeholder="Enter option content"
              rows={4}
              required
            />
          </Form.Group>
          <Form.Group controlId="formConditions" className="mt-3">
            <RichTextEditor
              label="Conditions"
              name="conditions"
              value={updatedData.conditions}
              onChange={handleInputChange}
              placeholder="Enter conditions"
              rows={3}
              required
            />
          </Form.Group>
          <Button variant="success" type="submit" className="mt-3">
            Save Changes
          </Button>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default MasterOptionPassageModal;
