import React, { useEffect, useState, useCallback  } from 'react'; 
import { useSelector, useDispatch } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';

import { getContributons } from '../../redux/slice/contributorSlice';
import { toast } from 'react-hot-toast';
import { Container, Row, Col, Spinner, Card } from 'react-bootstrap';
import Tiles from '../components/Tiles'; // Full-width tiles component
import SmallTiles from '../components/SmallTiles'; // Smaller tiles component
import BigCard from '../components/BigCard'; // Questions Summary Card
import MonthSummary from '../components/MonthSummary'; // Month Summary Component
import ColumnChart from '../components/ColumnChart'; // Column Chart Component
import PieChart from '../components/PieChart'; // Pie Chart Component
import { FaQuestionCircle, FaTasks, FaCheckSquare, FaBlog, FaHistory, FaStar, FaUser, FaEnvelope } from 'react-icons/fa';
import NavigationBar from '../../commonComponents/NavigationBar';

const ContributionDashboard = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [contributionData, setContributionData] = useState(null);
  const [selectedTile, setSelectedTile] = useState('questions');  // Track selected tile type 
  const [selectedPrevMonthTile, setselectedPrevMonthTile] = useState('questions');  // Track selected tile type 
  const [selectedThirdMonthTile, setselectedThirdMonthTile] = useState('questions');  // Track selected tile type 

  const [loading, setLoading] = useState(true);
  const accessToken = useSelector((state) => state.contributor.accessToken);
  const contributor = useSelector((state) => state.contributor);

  useEffect(() => {
    if (!accessToken) {
      toast.error('Please log in as a contributor!');
      setTimeout(() => {
        navigate('/contributor_login');
      }, 2000);
    }
  }, [accessToken, navigate]);
  
  useEffect(() => {
    const fetchContributionData = async () => {
      if (!accessToken) return;
  
      setLoading(true);
      try {
        const response = await dispatch(getContributons()).unwrap();
        setContributionData(response);
      } catch (error) {
        toast.error(`Error fetching contribution data: ${error.message || error}`);
      } finally {
        setLoading(false);
      }
    };
  
    fetchContributionData();
  }, [dispatch, accessToken]);
  
  // Instead of returning early, render a loading state if accessToken is missing
  if (!accessToken) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: '100vh' }}>
        <Spinner animation="border" />
      </Container>
    );
  }


  // Ensure the required data is available before rendering the ColumnChart

  const currentMonthData = contributionData?.current_month_data || {};
  const previousMonthData = contributionData?.previous_month_data || {};
  const thirdMonthData = contributionData?.third_month_data || {};

  // Points Data for each month, now includes contributor name and email
  const getTilesData = (monthData) => [
    {
      title: 'Contributor',
      count: `${contributor?.profile?.first_name} ${contributor?.profile?.last_name}` || 'N/A',
      icon: FaUser,
      color: '#4caf50',
      textColor: '#fff',
    },
    {
      title: 'Email',
      count: contributor?.profile?.email || 'N/A',
      icon: FaEnvelope,
      color: '#2196f3',
      textColor: '#fff',
    },
    {
      title: 'Normal Questions',
      count: monthData?.normal_questions || 0,
      icon: FaQuestionCircle,
      color: '#3366cc',
      textColor: '#fff',
    },
    {
      title: 'Master Questions',
      count: monthData?.master_questions || 0,
      icon: FaTasks,
      color: '#80e27e',
      textColor: '#fff',
    },
    {
      title: 'Master Options',
      count: monthData?.master_options || 0,
      icon: FaCheckSquare,
      color: '#ff7043',
      textColor: '#fff',
    },
    {
      title: 'Blogs',
      count: monthData?.blogs || 0,
      icon: FaBlog,
      color: '#ffb74d',
      textColor: '#fff',
    },
    {
      title: 'Previous Questions',
      count: monthData?.previous_questions || 0,
      icon: FaHistory,
      color: '#ab47bc',
      textColor: '#fff',
    },
    {
      title: 'Total Points',
      count: monthData?.total_points || 0,
      icon: FaStar,
      color: '#fdd835',
      textColor: '#fff',
    },
  ];

  const handleCurrentMonthTileClick = (tileType) => {
    setSelectedTile(tileType);  // Set the selected tile type (e.g., 'normal_questions', 'master_questions')
  };

  const handlePrevMonthTileClick = (tileType) => {
    setselectedPrevMonthTile(tileType);  // Set the selected tile type (e.g., 'normal_questions', 'master_questions')
  };

  const handleThirdMonthTileClick = (tileType) => {
    setselectedThirdMonthTile(tileType);  // Set the selected tile type (e.g., 'normal_questions', 'master_questions')
  };

  return (
    <>
      <NavigationBar />
      <Container>     
        {/* Full-Width Tiles for Current Month Points */}
        <h4 className="text-center my-3" style={{ fontSize: "1.3rem", color: "#146c43" }}>Current Month Points</h4>
        
        <section className="mb-5">
          <Tiles tilesData={getTilesData(contributionData?.current_month_points)} isLoading={loading} />
        </section>

        {/* Current Month Card */}
        <Card className="my-2 shadow">
          <Card.Body>
            <Row className="d-flex align-items-center">
              <Col md={7}>
                <h5 className="mb-3" style={{ fontSize: "1.3rem", color: "#146c43" }}>Current Month Summary</h5>
                <MonthSummary
                month="Current Month"
                data={contributionData?.current_month_data?.[selectedTile] || {}}  // Dynamically pass the data
                isLoading={loading}
              />
              </Col>
              <Col md={5}>
                <h5 className="text-center mb-3" style={{ fontSize: "1.3rem", color: "#146c43" }}>Current Month Points</h5>
                <SmallTiles
                  normalQuestions={contributionData?.current_month_points?.normal_questions || 0}
                  masterQuestions={contributionData?.current_month_points?.master_questions || 0}
                  masterOptions={contributionData?.current_month_points?.master_options || 0}
                  blogs={contributionData?.current_month_points?.blogs || 0}
                  previousQuestions={contributionData?.current_month_points?.previous_questions || 0}
                  totalPoints={contributionData?.current_month_points?.total_points || 0}
                  onTileClick={handleCurrentMonthTileClick}
                  isLoading={loading} 
                />                
              </Col> 
            </Row>
          </Card.Body>
        </Card>

        {/* Previous Month Card */}
        <Card className="my-2 shadow">
          <Card.Body>
            <Row>
              <Col md={7}>
                <h5 className="mb-3" style={{ fontSize: "1.3rem", color: "#146c43" }}>Previous Month Summary</h5>
                <MonthSummary
                  month="Current Month"
                  data={contributionData?.previous_month_data?.[selectedTile] || {}}  // Dynamically pass the data
                  isLoading={loading}
                />
              </Col>
              <Col md={5}>
                <h5 className="text-center mb-3" style={{ fontSize: "1.3rem", color: "#146c43" }}>Previous Month Points</h5>
                <SmallTiles
                  normalQuestions={contributionData?.previous_month_points?.normal_questions || 0}
                  masterQuestions={contributionData?.previous_month_points?.master_questions || 0}
                  masterOptions={contributionData?.previous_month_points?.master_options || 0}
                  blogs={contributionData?.previous_month_points?.blogs || 0}
                  previousQuestions={contributionData?.previous_month_points?.previous_questions || 0}
                  totalPoints={contributionData?.previous_month_points?.total_points || 0}
                  onTileClick={handlePrevMonthTileClick} 
                  isLoading={loading} 
                />
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Third Month Card */}
        <Card className="my-2 shadow">
          <Card.Body>
            <Row>
              <Col md={7}>
                <h5 className="mb-3" style={{ fontSize: "1.3rem", color: "#146c43" }}>Third Month Summary</h5>
                <MonthSummary month="Third Month" data={contributionData?.third_month_data?.questions || {}} isLoading={loading}/>
                <MonthSummary
                  month="Current Month"
                  data={contributionData?.third_month_data?.[selectedTile] || {}}  // Dynamically pass the data
                  isLoading={loading}
                  
                />
                </Col>
              <Col md={5}>
                <h5 className="text-center mb-3" style={{ fontSize: "1.3rem", color: "#146c43" }}>Third Month Points</h5>
                <SmallTiles
                  normalQuestions={contributionData?.third_month_points?.normal_questions || 0}
                  masterQuestions={contributionData?.third_month_points?.master_questions || 0}
                  masterOptions={contributionData?.third_month_points?.master_options || 0}
                  blogs={contributionData?.third_month_points?.blogs || 0}
                  previousQuestions={contributionData?.third_month_points?.previous_questions || 0}
                  totalPoints={contributionData?.third_month_points?.total_points || 0}
                  onTileClick={handleThirdMonthTileClick}
                  isLoading={loading} 
                  />
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Column Chart */}
        <div className="shadow my-5 p-5">
          <ColumnChart
            currentMonthData={contributionData?.current_month_data}
            previousMonthData={contributionData?.previous_month_data}
            thirdMonthData={contributionData?.third_month_data}
            isLoading={loading}
          />
        </div>

        {/* Questions Summary Section */}
        <BigCard questionsSummary={contributionData?.questions_summary} isLoading={loading} />
      </Container>
    </>
  );
};

export default ContributionDashboard;
