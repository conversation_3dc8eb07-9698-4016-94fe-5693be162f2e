importScripts("https://www.gstatic.com/firebasejs/9.9.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.9.0/firebase-messaging-compat.js");

// Initialize Firebase App inside the service worker
const firebaseConfig = {
  apiKey: "AIzaSyBJSLcv459JwCzNhkgOndDQ7wzuAx81YwU",
  authDomain: "shashtrarth-fcm.firebaseapp.com",
  projectId: "shashtrarth-fcm",
  storageBucket: "shashtrarth-fcm.firebasestorage.app",
  messagingSenderId: "826207026444",
  appId: "1:826207026444:web:ea2f8a6287c93ecebc1d29",
  measurementId: "G-Y8GGEV9RWX"
};

firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log("[firebase-messaging-sw.js] Received background message:", payload);

  const notificationTitle = payload.notification?.title || "New Notification";
  const notificationBody = payload.notification?.body || "You have a new message.";
  const notificationImage = payload.notification?.image || "/notification_logo.png";

  const notificationOptions = {
    body: notificationBody,
    icon: notificationImage, // Use the image as the icon if provided
  };

  // Handle extra data if available
  const extraData = payload.data || {}; // Firebase stores custom data in `data` field
  let extraDataText = "";

  if (extraData.key1) {
    extraDataText += `Key1: ${extraData.key1}\n`;
  }
  if (extraData.key2) {
    extraDataText += `Key2: ${extraData.key2}`;
  }

  if (extraDataText) {
    notificationOptions.body += `\n\n${extraDataText}`;
  }

  console.log("Extra Data:", extraData);

  // Show the notification
  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Optional: Handle notification click event
self.addEventListener("notificationclick", (event) => {
  console.log("[Service Worker] Notification click received.", event);
  event.notification.close();

  event.waitUntil(
    clients.matchAll({ type: "window", includeUncontrolled: true }).then((clientList) => {
      if (clientList.length > 0) {
        return clientList[0].focus();
      }
      return clients.openWindow("/");
    })
  );
});
