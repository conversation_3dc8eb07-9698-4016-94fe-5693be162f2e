import React, { useState } from 'react';
import { Form, Button, Container, Row, Col, Card } from 'react-bootstrap';
import { useDispatch, useSelector } from 'react-redux';
import { registerContributor } from '../../redux/slice/contributorSlice';
import { FaChalkboardTeacher } from 'react-icons/fa';
import NavigationBar from '../../commonComponents/NavigationBar';

const ContributorSignupPage = () => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    password: '',
    security_question: '',
    security_answer: '',
  });

  const dispatch = useDispatch();
  const { loading, error } = useSelector((state) => state.contributor);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const payload = {
      ...formData,
      contributor_profile: {
        security_question: formData.security_question,
        security_answer: formData.security_answer,
      },
    };
    dispatch(registerContributor(payload));
  };

  return (
    <>
    <NavigationBar/>
    <Container className="d-flex flex-column justify-content-center" style={{marginTop: "4rem"}}>
      
      <Row className="w-100 justify-content-center">
        <Col md={8} lg={6}>
          <Card className="shadow-lg rounded-3 p-4">
            <Card.Body>
              <Form onSubmit={handleSubmit}>
                <Row>
                  <Col sm={12} md={6} className="mb-3">
                    <Form.Group controlId="username">
                      <Form.Label>Username</Form.Label>
                      <Form.Control
                        type="text"
                        name="username"
                        value={formData.username}
                        onChange={handleChange}
                        placeholder="Enter username"
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col sm={12} md={6} className="mb-3">
                    <Form.Group controlId="email">
                      <Form.Label>Email</Form.Label>
                      <Form.Control
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="Enter email"
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col sm={12} md={6} className="mb-3">
                    <Form.Group controlId="first_name">
                      <Form.Label>First Name</Form.Label>
                      <Form.Control
                        type="text"
                        name="first_name"
                        value={formData.first_name}
                        onChange={handleChange}
                        placeholder="Enter first name"
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col sm={12} md={6} className="mb-3">
                    <Form.Group controlId="last_name">
                      <Form.Label>Last Name</Form.Label>
                      <Form.Control
                        type="text"
                        name="last_name"
                        value={formData.last_name}
                        onChange={handleChange}
                        placeholder="Enter last name"
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col sm={12} md={6} className="mb-3">
                    <Form.Group controlId="password">
                      <Form.Label>Password</Form.Label>
                      <Form.Control
                        type="password"
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        placeholder="Enter password"
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col sm={12} md={6} className="mb-3">
                    <Form.Group controlId="security_question">
                      <Form.Label>Security Question</Form.Label>
                      <Form.Control
                        type="text"
                        name="security_question"
                        value={formData.security_question}
                        onChange={handleChange}
                        placeholder="Enter security question"
                        required
                      />
                    </Form.Group>
                    
                  </Col>
                  
                </Row>
                <Row>
                  <Col sm={12} md={6} className="mb-3">
                    <Form.Group controlId="security_answer">
                      <Form.Label>Security Answer</Form.Label>
                      <Form.Control
                        type="text"
                        name="security_answer"
                        value={formData.security_answer}
                        onChange={handleChange}
                        placeholder="Enter security answer"
                        required
                      />
                    </Form.Group>
                    
                  </Col>
                  <Col>
                  <div className="text-center mb-4">
                    <FaChalkboardTeacher size={80} color="#146c43" />
                    
                  </div>
                  </Col>
                </Row>
                {/* {error && <p className="text-danger">{error}</p>} */}
                <Button
                  variant="outline-success"
                  type="submit"
                  className="w-100"
                  disabled={loading}
                >
                  {loading ? 'Loading...' : 'Register Contributor'}
                </Button>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
    </>
  );
};

export default ContributorSignupPage;
