from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import CustomrcareProfile, Ticket, FrontendError, TemporaryImage, WalkAroundImage, SOP
import json

# Register your models here.

admin.site.register(CustomrcareProfile)
admin.site.register(Ticket)
admin.site.register(TemporaryImage)


@admin.register(WalkAroundImage)
class WalkAroundImageAdmin(admin.ModelAdmin):
    """Admin interface for WalkAroundImage model"""

    list_display = [
        'id', 'user', 'title', 'status', 'image_thumbnail', 'created_at', 'updated_at'
    ]

    list_filter = ['status', 'created_at', 'updated_at']

    search_fields = ['user__username', 'user__email', 'title', 'description']

    readonly_fields = ['created_at', 'updated_at', 'image_preview']

    fields = [
        'user', 'title', 'description', 'image', 'image_preview',
        'status', 'created_at', 'updated_at'
    ]

    ordering = ['-created_at']
    list_per_page = 25

    def image_thumbnail(self, obj):
        """Display small thumbnail in list view"""
        if obj.image:
            return format_html(
                '<img src="{}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;" />',
                obj.image.url
            )
        return "-"
    image_thumbnail.short_description = 'Image'

    def image_preview(self, obj):
        """Display larger image preview in detail view"""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 300px; max-height: 300px; object-fit: contain;" />',
                obj.image.url
            )
        return "-"
    image_preview.short_description = 'Image Preview'

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('user')


@admin.register(FrontendError)
class FrontendErrorAdmin(admin.ModelAdmin):
    """Enhanced admin interface for frontend errors"""

    list_display = [
        'error_type', 'severity_display', 'error_message_short',
        'user', 'page_url_short', 'browser_info', 'occurrence_count',
        'resolved_display', 'created_at'
    ]

    list_filter = [
        'error_type', 'severity', 'browser_name', 'device_type',
        'resolved', 'created_at'
    ]

    search_fields = [
        'error_message', 'page_url', 'user__username', 'user__email',
        'component_name', 'function_name', 'ip_address'
    ]

    readonly_fields = [
        'error_type', 'severity', 'error_message', 'stack_trace',
        'user', 'session_id', 'user_agent', 'ip_address',
        'browser_name', 'browser_version', 'device_type', 'screen_resolution',
        'page_url', 'page_title', 'referrer_url',
        'component_name', 'function_name', 'line_number', 'column_number',
        'error_data_display', 'user_actions_display', 'console_logs_display',
        'occurrence_count', 'first_occurrence', 'last_occurrence', 'created_at'
    ]

    fields = [
        'error_type', 'severity', 'error_message', 'stack_trace',
        'user', 'session_id', 'user_agent', 'ip_address',
        'browser_name', 'browser_version', 'device_type', 'screen_resolution',
        'page_url', 'page_title', 'referrer_url',
        'component_name', 'function_name', 'line_number', 'column_number',
        'error_data_display', 'user_actions_display', 'console_logs_display',
        'occurrence_count', 'first_occurrence', 'last_occurrence',
        'resolved', 'resolved_by', 'resolved_at', 'resolution_notes',
        'created_at'
    ]

    date_hierarchy = 'created_at'
    ordering = ['-created_at']
    list_per_page = 50
    actions = ['mark_resolved', 'mark_unresolved', 'delete_selected']

    def severity_display(self, obj):
        """Display severity with color coding"""
        colors = {
            'LOW': 'green',
            'MEDIUM': 'orange',
            'HIGH': 'red',
            'CRITICAL': 'darkred'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.severity, 'black'),
            obj.severity
        )
    severity_display.short_description = 'Severity'
    severity_display.admin_order_field = 'severity'

    def error_message_short(self, obj):
        """Display truncated error message"""
        if len(obj.error_message) > 100:
            return obj.error_message[:100] + '...'
        return obj.error_message
    error_message_short.short_description = 'Error Message'

    def page_url_short(self, obj):
        """Display truncated page URL"""
        if len(obj.page_url) > 50:
            return obj.page_url[:50] + '...'
        return obj.page_url
    page_url_short.short_description = 'Page URL'

    def browser_info(self, obj):
        """Display browser and device information"""
        return f"{obj.browser_name} {obj.browser_version} ({obj.device_type})"
    browser_info.short_description = 'Browser Info'

    def resolved_display(self, obj):
        """Display resolution status"""
        if obj.resolved:
            return format_html(
                '<span style="color: green;">✓ Resolved</span>'
            )
        return format_html(
            '<span style="color: red;">✗ Unresolved</span>'
        )
    resolved_display.short_description = 'Status'
    resolved_display.admin_order_field = 'resolved'

    def error_data_display(self, obj):
        """Display formatted error data"""
        if obj.error_data:
            return format_html(
                '<pre style="max-height: 200px; overflow-y: auto;">{}</pre>',
                json.dumps(obj.error_data, indent=2)
            )
        return "-"
    error_data_display.short_description = 'Error Data'

    def user_actions_display(self, obj):
        """Display formatted user actions"""
        if obj.user_actions:
            return format_html(
                '<pre style="max-height: 200px; overflow-y: auto;">{}</pre>',
                json.dumps(obj.user_actions, indent=2)
            )
        return "-"
    user_actions_display.short_description = 'User Actions'

    def console_logs_display(self, obj):
        """Display formatted console logs"""
        if obj.console_logs:
            return format_html(
                '<pre style="max-height: 200px; overflow-y: auto;">{}</pre>',
                json.dumps(obj.console_logs, indent=2)
            )
        return "-"
    console_logs_display.short_description = 'Console Logs'

    def mark_resolved(self, request, queryset):
        """Mark selected errors as resolved"""
        queryset.update(
            resolved=True,
            resolved_by=request.user,
            resolved_at=timezone.now()
        )
        self.message_user(request, f"Marked {queryset.count()} errors as resolved.")
    mark_resolved.short_description = "Mark selected errors as resolved"

    def mark_unresolved(self, request, queryset):
        """Mark selected errors as unresolved"""
        queryset.update(
            resolved=False,
            resolved_by=None,
            resolved_at=None,
            resolution_notes=''
        )
        self.message_user(request, f"Marked {queryset.count()} errors as unresolved.")
    mark_unresolved.short_description = "Mark selected errors as unresolved"

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('user', 'resolved_by')


@admin.register(SOP)
class SOPAdmin(admin.ModelAdmin):
    """Admin interface for SOP model"""

    list_display = [
        'name', 'access', 'pdf_link', 'created_by', 'created', 'last_update'
    ]

    list_filter = ['access', 'created', 'last_update', 'created_by']

    search_fields = ['name', 'created_by__username']

    readonly_fields = ['created', 'last_update', 'slug']

    fields = [
        'name', 'pdf', 'access', 'created_by', 'updated_by',
        'created', 'last_update', 'slug'
    ]

    ordering = ['-created']
    list_per_page = 25

    def pdf_link(self, obj):
        """Display PDF download link"""
        if obj.pdf:
            return format_html(
                '<a href="{}" target="_blank">📄 Download PDF</a>',
                obj.pdf.url
            )
        return "-"
    pdf_link.short_description = 'PDF File'

    def save_model(self, request, obj, form, change):
        """Set created_by and updated_by fields"""
        if not change:  # Creating new object
            obj.created_by = request.user
        else:  # Updating existing object
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('created_by', 'updated_by')
