import React, { useState, useEffect } from "react";
import { Form, Button, Container, Row, Col, Card } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { loginContributor } from "../../redux/slice/contributorSlice";
import { FaChalkboardTeacher } from "react-icons/fa";
import toast, { Toaster } from "react-hot-toast";
import { Link, useNavigate } from "react-router-dom";
import NavigationBar from "../../commonComponents/NavigationBar";
import Footer from "../../commonComponents/Footer";
import SideInfo from "../../commonComponents/SideInfo";

const ContributorLogin = () => {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
  });

  const dispatch = useDispatch();
  const { loading, error, accessToken } = useSelector(
    (state) => state.contributor || {}
  );
  const navigate = useNavigate();

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  // Handle form submission and basic validation
  const handleSubmit = (e) => {
    e.preventDefault();

    if (!formData.username || !formData.password) {
      toast.error("Please enter username and password");
      return;
    }

    // Dispatch login action
    dispatch(loginContributor(formData));
  };

  // Effect to handle login success check and navigation
  useEffect(() => {
    if (accessToken) {
      toast.success("Login successful!");
      navigate("/contributor_dashboard"); // Navigate to dashboard after login
    }
  }, [accessToken, navigate]);

  // Effect to handle login error and reset form
  useEffect(() => {
    if (error) {
      toast.error("Invalid credentials, please try again!");
      setFormData({ username: "", password: "" }); // Reset form after error
    }
  }, [error]);

  return (
    <>
      <NavigationBar />
      <Container
        fluid
        className="d-flex justify-content-center align-items-center"
        style={{
          height: "91vh",
          background:
            "linear-gradient(130deg, white,rgb(71, 136, 83)), url('https://www.transparenttextures.com/patterns/gplay.png')",
          // backgroundSize: "cover",
          backgroundBlendMode: "overlay",
          opacity: 0.9,
        }}
      >
        <Row className="w-100 justify-content-center align-items-center">
          <Col md={6} lg={4} className="d-md-block d-none">
            <SideInfo/>
          </Col>
          <Col md={6} lg={4}>
            <Card className="shadow-lg rounded-3 p-4">
              <div className="text-center mt-3">
                <FaChalkboardTeacher size={80} color="#146c43" />
                <h3 style={{ color: "#146c43" }} className="mt-3">
                  Contributor Login
                </h3>
              </div>
              <Card.Body>
                <Form onSubmit={handleSubmit}>
                  <Form.Group controlId="username" className="mb-3">
                    <Form.Label>Username</Form.Label>
                    <Form.Control
                      type="text"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      placeholder="Enter username"
                    />
                  </Form.Group>
                  <Form.Group controlId="password" className="mb-3">
                    <Form.Label>Password</Form.Label>
                    <Form.Control
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      placeholder="Enter password"
                    />
                  </Form.Group>
                  <Button
                    variant="outline-success"
                    type="submit"
                    className="w-100"
                    disabled={loading}
                  >
                    {loading ? "Logging in..." : "Login"}
                  </Button>
                </Form>
              </Card.Body>
            </Card>
          </Col>
        </Row>
        <Toaster />
      </Container>
      <Footer />
    </>
  );
};

export default ContributorLogin;
