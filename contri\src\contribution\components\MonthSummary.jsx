import React from "react";
import { Table, Card } from "react-bootstrap";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

const MonthSummary = ({ month, data, isLoading }) => {
  return (
    <Card className="mb-4">
      <Card.Body>
        <Card.Title>
          {isLoading ? (
            <Skeleton width={200} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
          ) : (
            `${month} Data Summary`
          )}
        </Card.Title>
        <div className="table-responsive">
          <Table striped bordered hover>
            <thead>
              <tr>
                {["Category", "Created", "Approved", "Pending", "Rejected"].map(
                  (heading, index) => (
                    <th key={index} style={{ fontSize: "0.9rem" }}>
                      {isLoading ? (
                        <Skeleton
                          width={80}
                          baseColor="#e6ffe6"
                          highlightColor="#c4f7c4"
                        />
                      ) : (
                        heading
                      )}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody>
              {isLoading
                ? // Show 5 rows of skeleton loading
                  [...Array(2)].map((_, rowIndex) => (
                    <tr key={rowIndex}>
                      {[...Array(5)].map((_, colIndex) => (
                        <td key={colIndex}>
                          <Skeleton
                            width={60}
                            baseColor="#e6ffe6"
                            highlightColor="#c4f7c4"
                          />
                        </td>
                      ))}
                    </tr>
                  ))
                : // Show actual data when not loading
                  Object.keys(data).map((category) => (
                    <tr key={category}>
                      <td>{category}</td>
                      <td>{data[category]?.created || 0}</td>
                      <td>{data[category]?.approved || 0}</td>
                      <td>{data[category]?.pending || 0}</td>
                      <td>{data[category]?.rejected || 0}</td>
                    </tr>
                  ))}
            </tbody>
          </Table>
        </div>
      </Card.Body>
    </Card>
  );
};

export default MonthSummary;
