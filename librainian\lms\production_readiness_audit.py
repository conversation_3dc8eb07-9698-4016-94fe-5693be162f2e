#!/usr/bin/env python3
"""
Production Readiness Audit for Membership Expiry Notification System
This script evaluates if the system is ready for production deployment
"""

import os
import sys
import django
from pathlib import Path
import subprocess

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def check_security():
    """Check security configurations"""
    print("🔒 SECURITY AUDIT")
    print("=" * 25)
    
    issues = []
    
    try:
        from django.conf import settings
        
        # Check DEBUG setting
        if getattr(settings, 'DEBUG', True):
            issues.append("❌ DEBUG=True in production (should be False)")
        else:
            print("✅ DEBUG=False (production ready)")
        
        # Check SECRET_KEY
        secret_key = getattr(settings, 'SECRET_KEY', '')
        if not secret_key or len(secret_key) < 50:
            issues.append("❌ SECRET_KEY is weak or missing")
        elif 'django-insecure' in secret_key:
            issues.append("❌ Using default Django SECRET_KEY")
        else:
            print("✅ SECRET_KEY looks secure")
        
        # Check ALLOWED_HOSTS
        allowed_hosts = getattr(settings, 'ALLOWED_HOSTS', [])
        if not allowed_hosts or allowed_hosts == ['*']:
            issues.append("❌ ALLOWED_HOSTS not properly configured")
        else:
            print(f"✅ ALLOWED_HOSTS configured: {allowed_hosts}")
        
        # Check HTTPS settings
        if not getattr(settings, 'SECURE_SSL_REDIRECT', False):
            issues.append("⚠️ SECURE_SSL_REDIRECT not enabled")
        
        if not getattr(settings, 'SECURE_HSTS_SECONDS', 0):
            issues.append("⚠️ SECURE_HSTS_SECONDS not configured")
        
    except Exception as e:
        issues.append(f"❌ Error checking security settings: {e}")
    
    return issues

def check_database():
    """Check database configuration"""
    print("\n💾 DATABASE AUDIT")
    print("=" * 25)
    
    issues = []
    
    try:
        from django.conf import settings
        from django.db import connection
        
        # Check database connection
        connection.ensure_connection()
        print("✅ Database connection working")
        
        # Check database engine
        db_engine = settings.DATABASES['default']['ENGINE']
        if 'sqlite' in db_engine.lower():
            issues.append("⚠️ Using SQLite (consider PostgreSQL/MySQL for production)")
        else:
            print(f"✅ Using production database: {db_engine}")
        
        # Check database backups
        issues.append("⚠️ Database backup strategy not implemented")
        
        # Check connection pooling
        issues.append("⚠️ Database connection pooling not configured")
        
    except Exception as e:
        issues.append(f"❌ Database connection failed: {e}")
    
    return issues

def check_logging():
    """Check logging configuration"""
    print("\n📝 LOGGING AUDIT")
    print("=" * 25)
    
    issues = []
    
    try:
        from django.conf import settings
        import logging
        
        # Check if logging is configured
        if hasattr(settings, 'LOGGING'):
            print("✅ Logging configuration found")
            
            # Check log levels
            logging_config = settings.LOGGING
            if 'handlers' in logging_config:
                print("✅ Log handlers configured")
            else:
                issues.append("⚠️ No log handlers configured")
        else:
            issues.append("❌ No logging configuration found")
        
        # Check log rotation
        issues.append("⚠️ Log rotation not implemented")
        
        # Check centralized logging
        issues.append("⚠️ Centralized logging not implemented")
        
    except Exception as e:
        issues.append(f"❌ Error checking logging: {e}")
    
    return issues

def check_error_handling():
    """Check error handling and monitoring"""
    print("\n🚨 ERROR HANDLING AUDIT")
    print("=" * 35)
    
    issues = []
    
    try:
        # Check if Sentry or similar is configured
        from django.conf import settings
        
        if not hasattr(settings, 'SENTRY_DSN') and 'sentry' not in str(settings.INSTALLED_APPS):
            issues.append("⚠️ No error monitoring (Sentry) configured")
        
        # Check email error reporting
        if not getattr(settings, 'ADMINS', []):
            issues.append("⚠️ No ADMINS configured for error emails")
        
        # Check notification error handling
        print("✅ Basic error handling in notification system")
        
    except Exception as e:
        issues.append(f"❌ Error checking error handling: {e}")
    
    return issues

def check_performance():
    """Check performance configurations"""
    print("\n⚡ PERFORMANCE AUDIT")
    print("=" * 30)
    
    issues = []
    
    try:
        from django.conf import settings
        
        # Check caching
        if not hasattr(settings, 'CACHES') or settings.CACHES.get('default', {}).get('BACKEND') == 'django.core.cache.backends.locmem.LocMemCache':
            issues.append("⚠️ No production caching configured (Redis/Memcached)")
        
        # Check static files
        if not getattr(settings, 'STATIC_ROOT', ''):
            issues.append("❌ STATIC_ROOT not configured")
        
        # Check media files
        if not getattr(settings, 'MEDIA_ROOT', ''):
            issues.append("⚠️ MEDIA_ROOT not configured")
        
        # Check database optimization
        issues.append("⚠️ Database query optimization not audited")
        
        # Check CDN
        issues.append("⚠️ CDN not configured for static files")
        
    except Exception as e:
        issues.append(f"❌ Error checking performance: {e}")
    
    return issues

def check_notification_system():
    """Check notification system production readiness"""
    print("\n🔔 NOTIFICATION SYSTEM AUDIT")
    print("=" * 40)
    
    issues = []
    
    try:
        # Check FCM configuration
        from librarian.models import DeviceToken
        
        token_count = DeviceToken.objects.filter(is_active=True).count()
        if token_count == 0:
            issues.append("❌ No active FCM tokens registered")
        else:
            print(f"✅ {token_count} active FCM tokens")
        
        # Check Firebase credentials
        if not os.path.exists('firebase-service-account.json'):
            issues.append("⚠️ Firebase service account file not found")
        
        # Check notification templates
        from librarian.notification_templates import NOTIFICATION_TEMPLATES
        required_templates = [
            'member_expiry_10_days',
            'member_expiry_5_days', 
            'member_expiry_1_day',
            'member_expired',
            'member_expired_4_days'
        ]
        
        missing_templates = []
        for template in required_templates:
            if template not in NOTIFICATION_TEMPLATES:
                missing_templates.append(template)
        
        if missing_templates:
            issues.append(f"❌ Missing notification templates: {missing_templates}")
        else:
            print("✅ All notification templates present")
        
        # Check rate limiting
        issues.append("⚠️ No rate limiting for notifications")
        
        # Check notification delivery tracking
        issues.append("⚠️ No notification delivery tracking")
        
    except Exception as e:
        issues.append(f"❌ Error checking notification system: {e}")
    
    return issues

def check_deployment():
    """Check deployment configuration"""
    print("\n🚀 DEPLOYMENT AUDIT")
    print("=" * 30)
    
    issues = []
    
    # Check web server
    issues.append("⚠️ No production web server configured (Nginx/Apache)")
    
    # Check WSGI server
    issues.append("⚠️ No WSGI server configured (Gunicorn/uWSGI)")
    
    # Check process management
    issues.append("⚠️ No process manager configured (Supervisor/systemd)")
    
    # Check SSL certificate
    issues.append("⚠️ SSL certificate not configured")
    
    # Check environment variables
    if not os.path.exists('.env'):
        issues.append("⚠️ Environment variables file (.env) not found")
    
    # Check requirements.txt
    if not os.path.exists('requirements.txt'):
        issues.append("⚠️ requirements.txt not found")
    
    return issues

def check_monitoring():
    """Check monitoring and health checks"""
    print("\n📊 MONITORING AUDIT")
    print("=" * 30)
    
    issues = []
    
    # Check health check endpoint
    issues.append("⚠️ No health check endpoint implemented")
    
    # Check metrics collection
    issues.append("⚠️ No metrics collection (Prometheus/Grafana)")
    
    # Check uptime monitoring
    issues.append("⚠️ No uptime monitoring configured")
    
    # Check notification success/failure tracking
    issues.append("⚠️ No notification analytics/tracking")
    
    return issues

def check_scalability():
    """Check scalability considerations"""
    print("\n📈 SCALABILITY AUDIT")
    print("=" * 35)
    
    issues = []
    
    # Check task queue
    issues.append("⚠️ No task queue for background processing (Celery/RQ)")
    
    # Check load balancing
    issues.append("⚠️ No load balancing configured")
    
    # Check horizontal scaling
    issues.append("⚠️ Not configured for horizontal scaling")
    
    # Check database scaling
    issues.append("⚠️ No database scaling strategy")
    
    return issues

def generate_production_checklist():
    """Generate a production deployment checklist"""
    print("\n📋 PRODUCTION DEPLOYMENT CHECKLIST")
    print("=" * 50)
    
    checklist = [
        "🔒 Security:",
        "  [ ] Set DEBUG=False",
        "  [ ] Generate strong SECRET_KEY",
        "  [ ] Configure ALLOWED_HOSTS",
        "  [ ] Enable HTTPS (SSL certificate)",
        "  [ ] Configure security headers",
        "",
        "💾 Database:",
        "  [ ] Use production database (PostgreSQL/MySQL)",
        "  [ ] Configure database backups",
        "  [ ] Set up connection pooling",
        "  [ ] Optimize database queries",
        "",
        "🔔 Notifications:",
        "  [ ] Configure Firebase service account",
        "  [ ] Set up notification rate limiting",
        "  [ ] Implement delivery tracking",
        "  [ ] Test notification templates",
        "",
        "🚀 Deployment:",
        "  [ ] Configure web server (Nginx/Apache)",
        "  [ ] Set up WSGI server (Gunicorn)",
        "  [ ] Configure process manager (Supervisor)",
        "  [ ] Set up environment variables",
        "  [ ] Create requirements.txt",
        "",
        "📊 Monitoring:",
        "  [ ] Implement health checks",
        "  [ ] Set up error monitoring (Sentry)",
        "  [ ] Configure logging and log rotation",
        "  [ ] Set up uptime monitoring",
        "",
        "⚡ Performance:",
        "  [ ] Configure caching (Redis/Memcached)",
        "  [ ] Set up CDN for static files",
        "  [ ] Optimize database queries",
        "  [ ] Configure static file serving",
        "",
        "📈 Scalability:",
        "  [ ] Set up task queue (Celery)",
        "  [ ] Configure load balancing",
        "  [ ] Plan for horizontal scaling",
        "  [ ] Database scaling strategy"
    ]
    
    for item in checklist:
        print(item)

def main():
    print("🔍 PRODUCTION READINESS AUDIT")
    print("=" * 50)
    print("Evaluating membership expiry notification system for production deployment")
    print()
    
    all_issues = []
    
    # Run all audits
    audits = [
        ("Security", check_security),
        ("Database", check_database),
        ("Logging", check_logging),
        ("Error Handling", check_error_handling),
        ("Performance", check_performance),
        ("Notification System", check_notification_system),
        ("Deployment", check_deployment),
        ("Monitoring", check_monitoring),
        ("Scalability", check_scalability),
    ]
    
    for audit_name, audit_func in audits:
        try:
            issues = audit_func()
            all_issues.extend(issues)
        except Exception as e:
            all_issues.append(f"❌ {audit_name} audit failed: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 PRODUCTION READINESS SUMMARY")
    print("=" * 50)
    
    critical_issues = [issue for issue in all_issues if issue.startswith("❌")]
    warning_issues = [issue for issue in all_issues if issue.startswith("⚠️")]
    
    print(f"❌ Critical Issues: {len(critical_issues)}")
    print(f"⚠️ Warnings: {len(warning_issues)}")
    print(f"📊 Total Issues: {len(all_issues)}")
    
    if len(critical_issues) == 0 and len(warning_issues) <= 5:
        print("\n🎉 PRODUCTION READY!")
        print("The system is ready for production deployment with minor improvements.")
    elif len(critical_issues) <= 2:
        print("\n⚠️ MOSTLY READY")
        print("The system needs some improvements before production deployment.")
    else:
        print("\n❌ NOT PRODUCTION READY")
        print("The system needs significant improvements before production deployment.")
    
    # Show all issues
    if all_issues:
        print("\n🔧 ISSUES TO ADDRESS:")
        for issue in all_issues:
            print(f"  {issue}")
    
    # Generate checklist
    generate_production_checklist()
    
    print("\n💡 RECOMMENDATION:")
    if len(critical_issues) == 0:
        print("✅ Core functionality is working well!")
        print("🔧 Focus on addressing warnings for better production deployment.")
        print("🚀 Consider implementing monitoring and scalability features.")
    else:
        print("🔧 Address critical issues first before production deployment.")
        print("⚠️ Test thoroughly in a staging environment.")
        print("📋 Follow the production checklist above.")

if __name__ == "__main__":
    main()
