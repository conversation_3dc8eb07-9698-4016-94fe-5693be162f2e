import React from 'react'
import { Card } from 'react-bootstrap'
import Skeleton from 'react-loading-skeleton'

const QuestionListSkeleton = ({number}) => {
  return (
    <>{
        [...Array(number)].map(
          (
            _,
            index // Show 5 skeletons while loading
          ) => (
            <Card
              key={index}
              className="shadow position-relative"
              style={{
                width: "100%",
                marginBottom: "1rem",
                // backgroundColor: "#e6ffe6",
              }}
            >
              <Card.Body>
                <Skeleton
                  height={20}
                  width="100%"
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                />
                <Skeleton
                  height={20}
                  width="50%"
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                  className="mb-5"
                />
                <Skeleton
                  height={100}
                  width="70%"
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                />
                <Skeleton
                  height={20}
                  width="10%"
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                />
                <Skeleton
                  height={20}
                  width="10%"
                  baseColor="#e6ffe6"
                  highlightColor="#c4f7c4"
                />
              </Card.Body>
            </Card>
          )
        )
    }
    </>
  )
}

export default QuestionListSkeleton
