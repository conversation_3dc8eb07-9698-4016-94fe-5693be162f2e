import { useState, useRef, useEffect } from "react";
import { Mo<PERSON>, <PERSON>, Button } from "react-bootstrap";
import { Toaster } from "react-hot-toast";
import RichTextEditor from "./RichTextEditor";
import MathEditor from "./MathEditor";
import MathTextRenderer from "./MathTextRenderer";

const QuestionEditModal = ({
  show,
  onHide,
  updatedQuestionData,
  handleSubmitQuestion,
  handleQuestionInputChange,
  handleImageChange,
  imagePreview,
  imageSizeText,
  imageError,
}) => {
  const imageInputRef = useRef(null);

  // Math editor state
  const [localMathContent, setLocalMathContent] = useState("");
  const [showMathEditor, setShowMathEditor] = useState(false);

  // Reset math content when modal closes
  useEffect(() => {
    if (!show) {
      setLocalMathContent("");
      setShowMathEditor(false);
    }
  }, [show]);

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit Question</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmitQuestion}>
          <Form.Group controlId="content">
            <RichTextEditor
              label="Question Content"
              name="content"
              value={updatedQuestionData.content}
              onChange={handleQuestionInputChange}
              placeholder="Enter question content"
              rows={3}
              required
            />
          </Form.Group>

          {/* Math Editor Section */}
          <Form.Group className="mb-3">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <Form.Label>Add Mathematical Expressions (Optional)</Form.Label>
              <Button
                variant="outline-primary"
                size="sm"
                onClick={() => setShowMathEditor(!showMathEditor)}
              >
                {showMathEditor ? 'Hide' : 'Show'} Math Editor
              </Button>
            </div>

            {showMathEditor && (
              <MathEditor
                value={localMathContent}
                onChange={setLocalMathContent}
                label="Mathematical Expression"
                placeholder="Enter mathematical expressions, formulas, equations..."
                showPreview={true}
                showRawLatex={false}
                displayMode={true}
                embeddedMode={true}
                textContent={updatedQuestionData.content}
                onTextContentChange={(newContent) => handleQuestionInputChange({
                  target: { name: 'content', value: newContent }
                })}
                className="mb-3"
              />
            )}

            {/* Preview of question content with embedded math */}
            {updatedQuestionData.content && (
              <div className="mt-2">
                <Form.Label>Question Preview:</Form.Label>
                <div className="border rounded p-3 bg-light">
                  <MathTextRenderer text={updatedQuestionData.content} />
                </div>
              </div>
            )}
          </Form.Group>

          {imagePreview && (
            <Form.Group controlId="questionImage" className="mt-3">
              {imageSizeText && <p className="text-success">{imageSizeText}</p>}
              {imageError && <p className="text-danger mb-2">{imageError}</p>}
              <Form.Label>Question Image</Form.Label>
              <Form.Control
                ref={imageInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageChange}
              />
            </Form.Group>
          )}

          {imagePreview && (
            <div className="mb-3">
              <img
                src={imagePreview}
                alt="Preview"
                style={{
                  width: "100%",
                  maxHeight: "200px",
                  objectFit: "cover",
                }}
              />
            </div>
          )}

          <Button variant="outline-success" type="submit" className="mt-3 w-100">
            Save Changes
          </Button>
        </Form>
      </Modal.Body>
      <Toaster />
    </Modal>
  );
};

export default QuestionEditModal;
