#!/usr/bin/env python3
"""
Firebase Development Setup Script
This script helps you set up Firebase for development quickly.
"""

import os
import json
import sys

def create_env_file():
    """Create or update .env file with Firebase configuration"""
    env_content = """
# Firebase Configuration for Development
FIREBASE_PROJECT_ID=librainian-app

# Add your Firebase service account JSON here (all in one line, no spaces)
# FIREBASE_SERVICE_ACCOUNT_JSON={"type":"service_account","project_id":"librainian-app",...}
"""
    
    env_file = ".env"
    if os.path.exists(env_file):
        print(f"✅ {env_file} already exists")
        with open(env_file, 'r') as f:
            content = f.read()
            if 'FIREBASE_PROJECT_ID' not in content:
                with open(env_file, 'a') as f:
                    f.write(env_content)
                print("✅ Added Firebase configuration to existing .env file")
            else:
                print("✅ Firebase configuration already exists in .env file")
    else:
        with open(env_file, 'w') as f:
            f.write(env_content.strip())
        print(f"✅ Created {env_file} with Firebase configuration")

def check_firebase_setup():
    """Check current Firebase setup status"""
    print("\n🔍 Checking Firebase Setup Status:")
    
    # Check environment variables
    project_id = os.getenv("FIREBASE_PROJECT_ID")
    service_account = os.getenv("FIREBASE_SERVICE_ACCOUNT_JSON")
    
    print(f"📋 Project ID: {project_id or '❌ Not set'}")
    print(f"🔑 Service Account: {'✅ Set' if service_account else '❌ Not set'}")
    
    # Check service account file
    service_account_file = "firebase-service-account.json"
    if os.path.exists(service_account_file):
        print(f"📄 Service Account File: ✅ Found ({service_account_file})")
    else:
        print(f"📄 Service Account File: ❌ Not found ({service_account_file})")
    
    # Check template file
    template_file = "firebase-service-account-template.json"
    if os.path.exists(template_file):
        print(f"📋 Template File: ✅ Found ({template_file})")
    else:
        print(f"📋 Template File: ❌ Not found ({template_file})")

def show_instructions():
    """Show setup instructions"""
    print("\n📋 Firebase Setup Instructions:")
    print("\n🔥 Option 1: Quick Setup (Recommended for Development)")
    print("1. Go to https://console.firebase.google.com/")
    print("2. Select your project: 'librainian-app'")
    print("3. Go to Project Settings (gear icon) → Service Accounts")
    print("4. Click 'Generate new private key'")
    print("5. Download the JSON file")
    print("6. Rename it to 'firebase-service-account.json'")
    print("7. Place it in your project root directory")
    print("8. Restart your Django server")
    
    print("\n🔥 Option 2: Environment Variable Setup")
    print("1. Follow steps 1-5 above")
    print("2. Open the downloaded JSON file")
    print("3. Copy the entire JSON content (minified, no line breaks)")
    print("4. Add to your .env file:")
    print("   FIREBASE_SERVICE_ACCOUNT_JSON='{\"type\":\"service_account\",...}'")
    print("5. Restart your Django server")
    
    print("\n🧪 Testing:")
    print("1. Visit: http://localhost:8000/fcm-test/")
    print("2. Complete steps 1-6 to save device token")
    print("3. Try sending a notification")
    print("4. Check server logs - should see 'Firebase initialized' instead of 'Firebase not available'")

def main():
    print("🚀 Firebase Development Setup")
    print("=" * 50)
    
    # Create .env file
    create_env_file()
    
    # Check current setup
    check_firebase_setup()
    
    # Show instructions
    show_instructions()
    
    print("\n" + "=" * 50)
    print("✅ Setup complete! Follow the instructions above to enable Firebase.")
    print("💡 After setup, restart your Django server to see the changes.")

if __name__ == "__main__":
    main()
