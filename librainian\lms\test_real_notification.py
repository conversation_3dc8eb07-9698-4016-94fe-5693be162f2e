#!/usr/bin/env python3
"""
Test Real FCM Notification
This script tests FCM notifications with real Firebase credentials.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def test_real_fcm_notification():
    """Test FCM notification with real credentials"""
    print("🚀 Testing Real FCM Notifications")
    print("=" * 50)
    
    try:
        from librarian.firebase_config import initialize_firebase, get_firebase_app
        from librarian.notification_utils import send_fcm_notification
        from django.contrib.auth.models import User
        from librarian.models import DeviceToken
        
        # Test 1: Firebase Initialization
        print("1️⃣ Initializing Firebase...")
        firebase_initialized = initialize_firebase()
        if firebase_initialized:
            print("   ✅ Firebase initialized with real credentials")
        else:
            print("   ❌ Firebase initialization failed")
            return False
        
        # Test 2: Find user with device token
        print("\n2️⃣ Finding user with device token...")
        device_tokens = DeviceToken.objects.filter(is_active=True)
        
        if not device_tokens.exists():
            print("   ❌ No active device tokens found")
            print("   💡 Please register a device token first:")
            print("      1. Visit: http://localhost:8000/fcm-test/")
            print("      2. Complete steps 1-6 to register token")
            return False
        
        # Get the first user with a device token
        device_token = device_tokens.first()
        user = device_token.user
        
        print(f"   ✅ Found user with device token:")
        print(f"      User: {user.username}")
        print(f"      Device: {device_token.device_name}")
        print(f"      Token: {device_token.token[:30]}...")
        
        # Test 3: Send foreground notification
        print(f"\n3️⃣ Sending FOREGROUND notification to {user.username}...")
        try:
            result = send_fcm_notification(
                user=user,
                title="🔥 FOREGROUND Test - Real Firebase!",
                body="This is a REAL FCM notification sent with your Firebase credentials! Keep this browser tab ACTIVE to see foreground notification.",
                data={
                    "type": "foreground_test",
                    "source": "real_firebase_test",
                    "timestamp": str(os.time.time() if hasattr(os, 'time') else '123456789'),
                    "url": "http://localhost:8000/fcm-test/"
                }
            )
            
            if result:
                print("   ✅ FOREGROUND notification sent successfully!")
                print(f"   📊 Result: {result}")
                print("   💡 If browser tab is ACTIVE, you should see a custom notification popup")
            else:
                print("   ❌ FOREGROUND notification failed")
                return False
                
        except Exception as e:
            print(f"   ❌ Error sending FOREGROUND notification: {e}")
            return False
        
        # Wait a moment
        print("\n⏳ Waiting 3 seconds before sending background notification...")
        import time
        time.sleep(3)
        
        # Test 4: Send background notification
        print(f"\n4️⃣ Sending BACKGROUND notification to {user.username}...")
        print("   💡 MINIMIZE your browser window NOW to test background notifications!")
        
        # Wait for user to minimize
        time.sleep(2)
        
        try:
            result = send_fcm_notification(
                user=user,
                title="🌙 BACKGROUND Test - Real Firebase!",
                body="SUCCESS! This background notification proves your Firebase setup is working perfectly. Click to return to the app.",
                data={
                    "type": "background_test",
                    "source": "real_firebase_test",
                    "timestamp": str(int(time.time())),
                    "url": "http://localhost:8000/fcm-test/"
                }
            )
            
            if result:
                print("   ✅ BACKGROUND notification sent successfully!")
                print(f"   📊 Result: {result}")
                print("   💡 You should see a SYSTEM notification even with browser minimized")
            else:
                print("   ❌ BACKGROUND notification failed")
                return False
                
        except Exception as e:
            print(f"   ❌ Error sending BACKGROUND notification: {e}")
            return False
        
        return True
    
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def show_results():
    """Show test results and next steps"""
    print("\n" + "=" * 50)
    print("🎉 REAL FCM NOTIFICATION TEST COMPLETE!")
    print("=" * 50)
    
    print("\n✅ What you should have seen:")
    print("   1. 🔥 FOREGROUND notification (custom popup on active page)")
    print("   2. 🌙 BACKGROUND notification (system notification when minimized)")
    
    print("\n🧪 Manual Testing:")
    print("   1. Visit: http://localhost:8000/fcm-test/")
    print("   2. Complete steps 1-6 (if not done already)")
    print("   3. Test step 7: Server notification (instant)")
    print("   4. Test step 8: Background notification (5s delay)")
    
    print("\n🎯 Production Ready:")
    print("   • Firebase is properly configured with real credentials")
    print("   • Both foreground and background notifications work")
    print("   • Your app can now send real FCM push notifications")
    
    print("\n📱 Next Steps:")
    print("   • Integrate notifications into your app workflows")
    print("   • Set up notification triggers (QR scans, visitor callbacks, etc.)")
    print("   • Configure user notification preferences")

def main():
    success = test_real_fcm_notification()
    show_results()
    
    if success:
        print("\n🚀 SUCCESS: Real FCM notifications are working!")
        sys.exit(0)
    else:
        print("\n❌ FAILED: Check the errors above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
