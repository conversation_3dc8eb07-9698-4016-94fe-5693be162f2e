#!/usr/bin/env python3
"""
Manual Testing Script for Course-Related Django Components
This script performs comprehensive testing of all course-related functionality.
"""

import os
import sys
import django
import traceback
from datetime import timed<PERSON><PERSON>

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Import models after Django setup
from django.contrib.auth.models import User
from questions.models import (
    Course, SubCourse, Tier, Paper, Section, Module, Subject, Topic, SubTopic,
    MasterQuestion, MasterOption, Question, Option, PreviousYearQuestion
)
from contributor.models import ContributorProfile
from students.models import Student


class TestResults:
    """Class to track test results."""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"✅ PASS: {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"❌ FAIL: {test_name} - {error}")
    
    def summary(self):
        total = self.passed + self.failed
        print(f"\n{'='*60}")
        print(f"TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success Rate: {(self.passed/total*100):.1f}%" if total > 0 else "No tests run")
        
        if self.errors:
            print(f"\n{'='*60}")
            print(f"FAILED TESTS:")
            print(f"{'='*60}")
            for error in self.errors:
                print(f"- {error}")


def test_course_model():
    """Test Course model functionality."""
    results = TestResults()
    
    try:
        # Test 1: Course creation
        course = Course.objects.create(name='Test Course', description='Test Description')
        if course.name == 'Test Course' and course.description == 'Test Description':
            results.add_pass("Course creation with valid data")
        else:
            results.add_fail("Course creation with valid data", "Fields not set correctly")
        
        # Test 2: Course slug generation
        if course.slug:
            results.add_pass("Course slug generation")
        else:
            results.add_fail("Course slug generation", "Slug not generated")
        
        # Test 3: Course __str__ method
        if str(course) == 'Test Course':
            results.add_pass("Course __str__ method")
        else:
            results.add_fail("Course __str__ method", f"Expected 'Test Course', got '{str(course)}'")
        
        # Test 4: Course with None name
        course_no_name = Course.objects.create(description='No name course')
        if str(course_no_name) == 'Unnamed Course':
            results.add_pass("Course __str__ method with None name")
        else:
            results.add_fail("Course __str__ method with None name", f"Expected 'Unnamed Course', got '{str(course_no_name)}'")
        
        # Test 5: Unique slug generation
        course2 = Course.objects.create(name='Test Course')
        if course.slug != course2.slug:
            results.add_pass("Unique slug generation")
        else:
            results.add_fail("Unique slug generation", "Slugs are not unique")
        
        # Cleanup
        Course.objects.filter(name__startswith='Test Course').delete()
        Course.objects.filter(description='No name course').delete()
        
    except Exception as e:
        results.add_fail("Course model tests", f"Exception: {str(e)}")
    
    return results


def test_subcourse_model():
    """Test SubCourse model functionality."""
    results = TestResults()
    
    try:
        # Setup
        course = Course.objects.create(name='Parent Course')
        
        # Test 1: SubCourse creation
        subcourse = SubCourse.objects.create(course=course, name='Test SubCourse')
        if subcourse.course == course and subcourse.name == 'Test SubCourse':
            results.add_pass("SubCourse creation with valid data")
        else:
            results.add_fail("SubCourse creation with valid data", "Fields not set correctly")
        
        # Test 2: SubCourse slug generation
        if subcourse.slug:
            results.add_pass("SubCourse slug generation")
        else:
            results.add_fail("SubCourse slug generation", "Slug not generated")
        
        # Test 3: SubCourse __str__ method
        expected_str = f"Test SubCourse (Parent Course)"
        if str(subcourse) == expected_str:
            results.add_pass("SubCourse __str__ method")
        else:
            results.add_fail("SubCourse __str__ method", f"Expected '{expected_str}', got '{str(subcourse)}'")
        
        # Test 4: SubCourse relationship
        if subcourse in course.sub_courses.all():
            results.add_pass("SubCourse relationship with Course")
        else:
            results.add_fail("SubCourse relationship with Course", "Relationship not established")
        
        # Test 5: SubCourse with None name
        subcourse_no_name = SubCourse.objects.create(course=course)
        if str(subcourse_no_name) == 'Unnamed SubCourse':
            results.add_pass("SubCourse __str__ method with None name")
        else:
            results.add_fail("SubCourse __str__ method with None name", f"Expected 'Unnamed SubCourse', got '{str(subcourse_no_name)}'")
        
        # Cleanup
        SubCourse.objects.filter(course=course).delete()
        course.delete()
        
    except Exception as e:
        results.add_fail("SubCourse model tests", f"Exception: {str(e)}")
    
    return results


def test_subject_model():
    """Test Subject model functionality."""
    results = TestResults()
    
    try:
        # Test 1: Subject creation
        subject = Subject.objects.create(name='Mathematics', description='Math subject', rank=1)
        if subject.name == 'Mathematics' and subject.rank == 1:
            results.add_pass("Subject creation with valid data")
        else:
            results.add_fail("Subject creation with valid data", "Fields not set correctly")
        
        # Test 2: Subject slug generation
        if subject.slug:
            results.add_pass("Subject slug generation")
        else:
            results.add_fail("Subject slug generation", "Slug not generated")
        
        # Test 3: Subject __str__ method
        if str(subject) == 'Mathematics':
            results.add_pass("Subject __str__ method")
        else:
            results.add_fail("Subject __str__ method", f"Expected 'Mathematics', got '{str(subject)}'")
        
        # Test 4: Subject rank default value
        subject_default = Subject.objects.create(name='Physics')
        if subject_default.rank == 0:
            results.add_pass("Subject rank default value")
        else:
            results.add_fail("Subject rank default value", f"Expected 0, got {subject_default.rank}")
        
        # Cleanup
        Subject.objects.filter(name__in=['Mathematics', 'Physics']).delete()
        
    except Exception as e:
        results.add_fail("Subject model tests", f"Exception: {str(e)}")
    
    return results


def test_topic_model():
    """Test Topic model functionality."""
    results = TestResults()
    
    try:
        # Setup
        subject = Subject.objects.create(name='Mathematics')
        
        # Test 1: Topic creation
        topic = Topic.objects.create(subject=subject, name='Algebra', description='Algebra topic')
        if topic.subject == subject and topic.name == 'Algebra':
            results.add_pass("Topic creation with valid data")
        else:
            results.add_fail("Topic creation with valid data", "Fields not set correctly")
        
        # Test 2: Topic slug generation
        if topic.slug:
            results.add_pass("Topic slug generation")
        else:
            results.add_fail("Topic slug generation", "Slug not generated")
        
        # Test 3: Topic __str__ method
        expected_str = f"Algebra (Mathematics)"
        if str(topic) == expected_str:
            results.add_pass("Topic __str__ method")
        else:
            results.add_fail("Topic __str__ method", f"Expected '{expected_str}', got '{str(topic)}'")
        
        # Test 4: Topic relationship with Subject
        if topic in subject.topics.all():
            results.add_pass("Topic relationship with Subject")
        else:
            results.add_fail("Topic relationship with Subject", "Relationship not established")
        
        # Cleanup
        topic.delete()
        subject.delete()
        
    except Exception as e:
        results.add_fail("Topic model tests", f"Exception: {str(e)}")
    
    return results


def test_subtopic_model():
    """Test SubTopic model functionality."""
    results = TestResults()
    
    try:
        # Setup
        subject = Subject.objects.create(name='Mathematics')
        topic = Topic.objects.create(subject=subject, name='Algebra')
        
        # Test 1: SubTopic creation
        subtopic = SubTopic.objects.create(topic=topic, name='Linear Equations', description='Linear equations subtopic')
        if subtopic.topic == topic and subtopic.name == 'Linear Equations':
            results.add_pass("SubTopic creation with valid data")
        else:
            results.add_fail("SubTopic creation with valid data", "Fields not set correctly")
        
        # Test 2: SubTopic slug generation
        if subtopic.slug:
            results.add_pass("SubTopic slug generation")
        else:
            results.add_fail("SubTopic slug generation", "Slug not generated")
        
        # Test 3: SubTopic __str__ method
        expected_str = f"Linear Equations (Algebra)"
        if str(subtopic) == expected_str:
            results.add_pass("SubTopic __str__ method")
        else:
            results.add_fail("SubTopic __str__ method", f"Expected '{expected_str}', got '{str(subtopic)}'")
        
        # Test 4: SubTopic relationship with Topic
        if subtopic in topic.subtopics.all():
            results.add_pass("SubTopic relationship with Topic")
        else:
            results.add_fail("SubTopic relationship with Topic", "Relationship not established")
        
        # Cleanup
        subtopic.delete()
        topic.delete()
        subject.delete()
        
    except Exception as e:
        results.add_fail("SubTopic model tests", f"Exception: {str(e)}")
    
    return results


def test_question_model():
    """Test Question model functionality."""
    results = TestResults()

    try:
        # Setup
        import uuid
        unique_username = f'testcontributor_{uuid.uuid4().hex[:8]}'
        user = User.objects.create_user(username=unique_username, password='test123')
        contributor = ContributorProfile.objects.create(user=user, role='contributor')
        
        # Test 1: Question creation
        question = Question.objects.create(
            content='What is 2 + 2?',
            difficulty=5,
            author=contributor,
            status='active'
        )
        if question.content == 'What is 2 + 2?' and question.difficulty == 5:
            results.add_pass("Question creation with valid data")
        else:
            results.add_fail("Question creation with valid data", "Fields not set correctly")
        
        # Test 2: Question slug generation
        if question.slug:
            results.add_pass("Question slug generation")
        else:
            results.add_fail("Question slug generation", "Slug not generated")
        
        # Test 3: Question difficulty levels
        test_cases = [
            (3, 'Easy'),
            (6, 'Medium'),
            (8, 'Hard'),
            (10, 'Extremely Hard'),
            (11, 'Invalid difficulty level')
        ]
        
        for difficulty, expected in test_cases:
            test_question = Question.objects.create(
                content=f'Test question {difficulty}',
                difficulty=difficulty,
                author=contributor
            )
            if test_question.get_difficulty_level() == expected:
                results.add_pass(f"Question difficulty level {difficulty} -> {expected}")
            else:
                results.add_fail(f"Question difficulty level {difficulty}", f"Expected '{expected}', got '{test_question.get_difficulty_level()}'")
            test_question.delete()
        
        # Cleanup
        question.delete()
        contributor.delete()
        user.delete()
        
    except Exception as e:
        results.add_fail("Question model tests", f"Exception: {str(e)}")
    
    return results


def test_option_model():
    """Test Option model functionality."""
    results = TestResults()

    try:
        # Setup
        import uuid
        unique_username = f'testcontributor_{uuid.uuid4().hex[:8]}'
        user = User.objects.create_user(username=unique_username, password='test123')
        contributor = ContributorProfile.objects.create(user=user, role='contributor')
        question = Question.objects.create(
            content='What is 2 + 2?',
            difficulty=5,
            author=contributor
        )

        # Test 1: Option creation
        option = Option.objects.create(
            question=question,
            option_text='4',
            is_correct=True
        )
        if option.question == question and option.option_text == '4' and option.is_correct:
            results.add_pass("Option creation with valid data")
        else:
            results.add_fail("Option creation with valid data", "Fields not set correctly")

        # Test 2: Option slug generation
        if option.slug:
            results.add_pass("Option slug generation")
        else:
            results.add_fail("Option slug generation", "Slug not generated")

        # Test 3: Option __str__ method
        if str(option) == '4':
            results.add_pass("Option __str__ method")
        else:
            results.add_fail("Option __str__ method", f"Expected '4', got '{str(option)}'")

        # Test 4: Option relationship with Question
        if option in question.options.all():
            results.add_pass("Option relationship with Question")
        else:
            results.add_fail("Option relationship with Question", "Relationship not established")

        # Test 5: Multiple options for same question
        option2 = Option.objects.create(
            question=question,
            option_text='5',
            is_correct=False
        )
        if question.options.count() == 2:
            results.add_pass("Multiple options for same question")
        else:
            results.add_fail("Multiple options for same question", f"Expected 2 options, got {question.options.count()}")

        # Cleanup
        Option.objects.filter(question=question).delete()
        question.delete()
        contributor.delete()
        user.delete()

    except Exception as e:
        results.add_fail("Option model tests", f"Exception: {str(e)}")

    return results


def test_master_question_model():
    """Test MasterQuestion model functionality."""
    results = TestResults()

    try:
        # Setup
        import uuid
        unique_username = f'testcontributor_{uuid.uuid4().hex[:8]}'
        user = User.objects.create_user(username=unique_username, password='test123')
        contributor = ContributorProfile.objects.create(user=user, role='contributor')

        # Test 1: MasterQuestion creation
        master_question = MasterQuestion.objects.create(
            author=contributor,
            title='Reading Comprehension',
            passage_content='This is a sample passage for reading comprehension.',
            approval_status='pending'
        )
        if (master_question.author == contributor and
            master_question.title == 'Reading Comprehension' and
            master_question.approval_status == 'pending'):
            results.add_pass("MasterQuestion creation with valid data")
        else:
            results.add_fail("MasterQuestion creation with valid data", "Fields not set correctly")

        # Test 2: MasterQuestion slug generation
        if master_question.slug:
            results.add_pass("MasterQuestion slug generation")
        else:
            results.add_fail("MasterQuestion slug generation", "Slug not generated")

        # Test 3: MasterQuestion __str__ method
        expected_str = f"MasterQuestion: Reading Comprehension"
        if str(master_question) == expected_str:
            results.add_pass("MasterQuestion __str__ method")
        else:
            results.add_fail("MasterQuestion __str__ method", f"Expected '{expected_str}', got '{str(master_question)}'")

        # Test 4: MasterQuestion with empty title (testing __str__ method)
        master_question_no_title = MasterQuestion.objects.create(
            author=contributor,
            title='',  # Empty string instead of None to avoid slug generation issues
            passage_content='Sample passage'
        )
        if str(master_question_no_title) == 'MasterQuestion: Untitled':
            results.add_pass("MasterQuestion __str__ method with empty title")
        else:
            results.add_fail("MasterQuestion __str__ method with empty title", f"Expected 'MasterQuestion: Untitled', got '{str(master_question_no_title)}'")

        # Cleanup
        MasterQuestion.objects.filter(author=contributor).delete()
        contributor.delete()
        user.delete()

    except Exception as e:
        results.add_fail("MasterQuestion model tests", f"Exception: {str(e)}")

    return results


def test_master_option_model():
    """Test MasterOption model functionality."""
    results = TestResults()

    try:
        # Setup
        import uuid
        unique_username = f'testcontributor_{uuid.uuid4().hex[:8]}'
        user = User.objects.create_user(username=unique_username, password='test123')
        contributor = ContributorProfile.objects.create(user=user, role='contributor')

        # Test 1: MasterOption creation
        master_option = MasterOption.objects.create(
            author=contributor,
            title='Common Options Set',
            option_content='Option A, Option B, Option C, Option D',
            approval_status='pending'
        )
        if (master_option.author == contributor and
            master_option.title == 'Common Options Set' and
            master_option.approval_status == 'pending'):
            results.add_pass("MasterOption creation with valid data")
        else:
            results.add_fail("MasterOption creation with valid data", "Fields not set correctly")

        # Test 2: MasterOption slug generation
        if master_option.slug:
            results.add_pass("MasterOption slug generation")
        else:
            results.add_fail("MasterOption slug generation", "Slug not generated")

        # Test 3: MasterOption __str__ method
        expected_str = f"MasterOption: Common Options Set"
        if str(master_option) == expected_str:
            results.add_pass("MasterOption __str__ method")
        else:
            results.add_fail("MasterOption __str__ method", f"Expected '{expected_str}', got '{str(master_option)}'")

        # Cleanup
        master_option.delete()
        contributor.delete()
        user.delete()

    except Exception as e:
        results.add_fail("MasterOption model tests", f"Exception: {str(e)}")

    return results


def test_previous_year_question_model():
    """Test PreviousYearQuestion model functionality."""
    results = TestResults()

    try:
        # Setup
        import uuid
        unique_username = f'testcontributor_{uuid.uuid4().hex[:8]}'
        user = User.objects.create_user(username=unique_username, password='test123')
        contributor = ContributorProfile.objects.create(user=user, role='contributor')
        course = Course.objects.create(name='Test Course PYQ')
        subcourse = SubCourse.objects.create(course=course, name='Test SubCourse PYQ')
        question = Question.objects.create(
            content='Previous year question?',
            difficulty=5,
            author=contributor
        )

        # Test 1: PreviousYearQuestion creation
        pyq = PreviousYearQuestion.objects.create(
            question=question,
            year=2023,
            month='January',
            course=course,
            exams=subcourse,
            status='active',
            approval_status='approved'
        )
        if (pyq.question == question and pyq.year == 2023 and
            pyq.month == 'January' and pyq.course == course):
            results.add_pass("PreviousYearQuestion creation with valid data")
        else:
            results.add_fail("PreviousYearQuestion creation with valid data", "Fields not set correctly")

        # Test 2: PreviousYearQuestion slug generation
        if pyq.slug:
            results.add_pass("PreviousYearQuestion slug generation")
        else:
            results.add_fail("PreviousYearQuestion slug generation", "Slug not generated")

        # Test 3: PreviousYearQuestion __str__ method
        expected_str = f"Previous year question?-2023"
        if str(pyq) == expected_str:
            results.add_pass("PreviousYearQuestion __str__ method")
        else:
            results.add_fail("PreviousYearQuestion __str__ method", f"Expected '{expected_str}', got '{str(pyq)}'")

        # Cleanup
        pyq.delete()
        question.delete()
        subcourse.delete()
        course.delete()
        contributor.delete()
        user.delete()

    except Exception as e:
        results.add_fail("PreviousYearQuestion model tests", f"Exception: {str(e)}")

    return results


def main():
    """Run all tests."""
    print("🚀 Starting Comprehensive Course Functionality Tests")
    print("="*60)

    all_results = TestResults()

    # Run all test functions
    test_functions = [
        test_course_model,
        test_subcourse_model,
        test_subject_model,
        test_topic_model,
        test_subtopic_model,
        test_question_model,
        test_option_model,
        test_master_question_model,
        test_master_option_model,
        test_previous_year_question_model
    ]
    
    for test_func in test_functions:
        print(f"\n📋 Running {test_func.__name__}...")
        try:
            result = test_func()
            all_results.passed += result.passed
            all_results.failed += result.failed
            all_results.errors.extend(result.errors)
        except Exception as e:
            all_results.add_fail(test_func.__name__, f"Test function failed: {str(e)}")
            print(f"💥 Error in {test_func.__name__}: {str(e)}")
            traceback.print_exc()
    
    # Print final summary
    all_results.summary()


if __name__ == '__main__':
    main()
