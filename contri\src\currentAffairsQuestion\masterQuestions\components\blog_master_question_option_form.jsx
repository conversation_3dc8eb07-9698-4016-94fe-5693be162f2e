import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createQuestion } from '../../../redux/slice/questionSlice';
import { createOption } from '../../../redux/slice/optionsSlice'; // Import the createOption function
import { getCourses } from '../../../redux/slice/courseSlice';
import toast, { Toaster } from 'react-hot-toast';
import {Link, useNavigate, useParams } from 'react-router-dom';
import { Form, Button, Row, Col, Card, Container, Modal } from 'react-bootstrap';
import Select from 'react-select';
import axios from 'axios';

import { FaPlus } from 'react-icons/fa';  


export default function BlogMasterQuestionOptionForm({onMasterQuestionContentChange, onMasterQuestionOptionAdd }) {

  // to get the master_question_id from the param

  const {blogId, id: master_question_id } = useParams();

//console.log("MasterQuestionID", master_question_id)

  const [questionContent, setQuestionContent] = useState('');
  const [questionId, setQuestionId] = useState(null);

  const handleContentChange = (e) => {
    const newContent = e.target.value;
    setQuestionContent(newContent);
    // onMasterQuestionContentChange(newContent); // Pass the new content to the parent
    setFormData({ ...formData, content: e.target.value })
  };

  const accessToken = useSelector((state) => state.contributor.accessToken || null);
  const contributorProfileId = useSelector((state) => state.contributor.contributorProfileId || null);
  const courses = useSelector((state) => state.course.courses || []); // Get courses from redux state
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // State to hold form data
  const [formData, setFormData] = useState({
    content: '',
    difficulty: 3,
    author: contributorProfileId,
    status: 'active',
    current_affairs: blogId,
    is_current_affairs: true,
    approval_status: 'pending',
    average_score: 0.0,
    times_attempted: 0,
    subject: '',
    subject_name: '',
    topic: '',
    topic_name: '',
    sub_topic: '',
    sub_topic_name: '',
    language: '',
    course: [],
    subcourse: [],
    master_question: master_question_id,
    is_master: true,
    master_option: null,
    is_master_option: false,
  });

  // for options 
  const [optionFormData, setOptionFormData] = useState({
    optionText: '',
    isCorrect: false,
  });


  const handleOptionInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'radio') {
      setOptionFormData((prev) => ({
        ...prev,
        isCorrect: checked ? value : prev.isCorrect,
      }));
    } else {
      setOptionFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // for getting the courses

  const [selectedCourses, setSelectedCourses] = useState([]);
  const [selectedSubcourses, setSelectedSubcourses] = useState({});
  const [subcourseOptions, setSubcourseOptions] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [optionText, setOptionText] = useState('');
  const [isCorrect, setIsCorrect] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  

  // for getting the Subject, Topic and Sub-topis

  const [subjects, setSubjects] = useState([]);
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [selectedSubtopic, setSelectedSubtopic] = useState(null);
  const [topics, setTopics] = useState([]);
  const [subtopics, setSubtopics] = useState([]);


  // Fetch subjects on component mount
  useEffect(() => {
    axios.get(`${import.meta.env.VITE_BASE_URL}api/questions/subjects/`)  // Replace with your API URL
      .then(response => {
        if (response.data.success) {
          setSubjects(response.data.data);  // Set the subjects from the response
        }
      })
      .catch(error => {
        console.error('Error fetching subjects:', error);
      });
  }, []);

  // Handle Subject Change
  const handleSubjectChange = (selectedOption) => {
    setSelectedSubject(selectedOption);
    const subject = subjects.find(subject => subject.subject_id === selectedOption.value);
    setTopics(subject.topics || []); // Set topics for the selected subject
    setSelectedTopic(null);  // Reset topic when subject changes
    setSubtopics([]);        // Reset subtopics when subject changes

    // Update formData with the selected subject's ID and name
    setFormData({
      ...formData,
      subject: selectedOption.value,
      subject_name: selectedOption.label,  // Set the subject name in formData
    });
  };


  // Handle Topic Change
  const handleTopicChange = (selectedOption) => {
    setSelectedTopic(selectedOption);
    const topic = topics.find(topic => topic.topic_id === selectedOption.value);
    setSubtopics(topic.subtopics || []); // Set subtopics for the selected topic
    setSelectedSubtopic(null); // Reset subtopic when topic changes

    // Update formData with the selected topic's ID and name
    setFormData({
      ...formData,
      topic: selectedOption.value,
      topic_name: selectedOption.label,  // Set the topic name in formData
    });
  };

  // Handle Subtopic Change
  const handleSubtopicChange = (selectedOption) => {
    setSelectedSubtopic(selectedOption);

    // Update formData with the selected subtopic's ID and name
    setFormData({
      ...formData,
      sub_topic: selectedOption.value,
      sub_topic_name: selectedOption.label,  // Set the subtopic name in formData
    });
  };

  // Options for subject dropdown
  const subjectOptions = subjects.map((subject) => ({
    value: subject.subject_id,
    label: subject.name,
  }));

  // Options for topic dropdown
  const topicOptions = topics.map((topic) => ({
    value: topic.topic_id,
    label: topic.name,
  }));

  // Options for subtopic dropdown
  const subtopicOptions = subtopics.map((subtopic) => ({
    value: subtopic.subtopic_id,
    label: subtopic.name,
  }));


  useEffect(() => {
    if (!accessToken) {
      toast.error('Please log in as a contributor!');
      const timer = setTimeout(() => {
        navigate('/contributor_login');
      }, 2000);
      return () => clearTimeout(timer);
    }
    dispatch(getCourses());
  }, [accessToken, dispatch, navigate]);

  const handleCourseChange = (selectedOptions) => {
    const courseIds = selectedOptions ? selectedOptions.map((option) => option.value) : [];
    setSelectedCourses(courseIds);
    setFormData({
      ...formData,
      course: courseIds,
      subcourse: [], // Reset subcourses when courses change
    });
  };

  const handleSelectAllCourses = (selectedOptions) => {
    if (selectedOptions.some((option) => option.value === 'selectAll')) {
      setSelectedCourses(courses.map((course) => course.course_id)); // Select all courses
    } else {
      setSelectedCourses(selectedOptions.map((option) => option.value)); // Keep other selections
    }
  };

  const handleSelectAllSubcourses = (courseId, selectedOptions) => {
    if (selectedOptions.some(option => option.value === 'selectAll')) {
      setSelectedSubcourses({
        ...selectedSubcourses,
        [courseId]: subcourseOptions[courseId]?.map(option => option.value),
      });
    } else {
      const subcourseIds = selectedOptions.map(option => option.value);
      setSelectedSubcourses({
        ...selectedSubcourses,
        [courseId]: subcourseIds,
      });
    }
  };

  const handleGetSubcourses = async () => {
    const payload = { course_ids: selectedCourses };
    try {
      const response = await axios.post(`${import.meta.env.VITE_BASE_URL}api/questions/courses-with-sub-courses/`, payload);
      const data = response.data;
      const newSubcourseOptions = {};

      data.forEach((course) => {
        newSubcourseOptions[course.course_id] = course.sub_courses.map((subcourse) => ({
          value: subcourse.subcourse_id,
          label: subcourse.name,
        }));
      });

      setSubcourseOptions(newSubcourseOptions);
    } catch (error) {
      toast.error('Please select at least one course');
      console.error(error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Check if subject, topic, and subtopic IDs are selected
    if (!formData.content || !formData.subject || !formData.topic || selectedCourses.length === 0 || formData.language === '') {
      toast.error('Please fill in all required fields');
      return;
    }

    // Preparing final payload for creating a question
    const payload = {
      ...formData,
      course: selectedCourses,  // Set selected course IDs
      subcourse: Object.values(selectedSubcourses).flat(),  // Flatten selected subcourse IDs
    };


    console.log("Form Data:", payload);  // For debugging

    try {
      const actionResult = await dispatch(createQuestion({ data: payload })).unwrap();
      const newQuestionId = actionResult.question_id;  // Capture question_id from the response
      setQuestionId(newQuestionId);  // Save the questionId in the state
      toast.success('Question created successfully!');
      setShowModal(true);  // Show modal for adding options

      console.log("Question ID:", newQuestionId);  // Log the questionId if needed
    } catch (error) {
      toast.error(error.message || 'Failed to create question');
    }
  };

  const languageOptions = [
    { value: 'english', label: 'English' },
    { value: 'hindi', label: 'Hindi' },
  ];

  const courseOptionsWithSelectAll = [
    { value: 'selectAll', label: 'Select All Courses' },
    ...courses.map((course) => ({
      value: course.course_id,
      label: course.name,
    })),
  ];

  // Now `questionId` is available and can be used for adding options
  const handleAddOption = async () => {
    // Disable the button to prevent multiple submissions
    setIsSubmitting(true);
  
    if (!questionId) {
      toast.error('Please create a question first');
      setIsSubmitting(false); // Re-enable the button if no questionId
      return;
    }
  
    const optionPayload = {
      option_text: optionFormData.optionText,
      is_correct: optionFormData.isCorrect,
    };
  
    try {
      await dispatch(createOption({ payload: optionPayload, questionId })).unwrap();
      toast.success('Option added successfully!');
      setOptionFormData({
        ...optionFormData,
        optionText: '',
        isCorrect: false,
      });
      setShowModal(true);  // Keep the modal open after adding an option
      onMasterQuestionOptionAdd();  // Optional callback for any additional behavior
    } catch (error) {
      toast.error(error.message || 'Failed to add option');
    } finally {
      // Re-enable the button after the request is finished
      setIsSubmitting(false);
    }
  };
  
 
  const handleCloseModal = () => {
    setShowConfirmation(true); // Show confirmation dialog
  };

  const handleConfirmClose = () => {
    setShowConfirmation(false);
    setShowModal(false); // Close modal
  
    // Reset option form data
    setOptionFormData({
      optionText: "",
      isCorrect: "false",
    });
  
    // Reset the main form data
    setFormData({
      content: "",
      difficulty: 3,
      author: contributorProfileId, // Assuming this remains constant
      status: "active",
      current_affairs: blogId,
      is_current_affairs: true,
      approval_status: "pending",
      average_score: 0.0,
      times_attempted: 0,
      subject: "",
      subject_name: "",
      topic: "",
      topic_name: "",
      sub_topic: "",
      sub_topic_name: "",
      language: "",
      course: [],
      subcourse: [],
      master_question: master_question_id,
      is_master: true,
      master_option: null,
      is_master_option: false,
    });
  
    // Optionally, reset selected states if necessary
    setSelectedCourses([]);
    setSelectedSubcourses({});
    setSelectedSubject(null);
    setSelectedTopic(null);
    setSelectedSubtopic(null);
  };
  

  const handleCancelClose = () => {
    setShowConfirmation(false); // Cancel close
  };

  return (
    <>
      <Container className="mt-4">
        <Row>
          <Col>
            <Card className="shadow-lg p-4">
              <Form onSubmit={handleSubmit}>
                <Form.Group controlId="content" className="mb-3">
                  <Form.Label>Question Content</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={2}
                    name="content"
                    value={formData.content}
                    onChange={handleContentChange}
                    // onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                    placeholder="Enter the question content"
                    required
                  />
                </Form.Group>

                <Row className="mb-3">
                  <Col>
                    <Form.Group controlId="difficulty">
                      <Form.Label className='pb-3 pt-1'>Difficulty Level</Form.Label>
                      <Form.Select
                        name="difficulty"
                        value={formData.difficulty}
                        onChange={(e) => setFormData({ ...formData, difficulty: e.target.value })}
                        required
                      >
                        {[...Array(11).keys()].map((level) => (
                          <option key={level} value={level}>
                            {level}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>

                  <Col>
                    {/* Subject Dropdown */}
                    <Form.Group controlId="subject">
                      <Form.Label> Subject
                         <Link to="/subjects_dashboard" > 
                            <Button variant="outline-success" style={{marginLeft: "1rem"}}>
                            <FaPlus/> {/* Add the Plus icon */}
                            </Button>
                          </Link>                  
                      </Form.Label>
                      <Select
                        options={subjectOptions}
                        onChange={handleSubjectChange}
                        value={selectedSubject}
                        placeholder="Select Subject"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col>
                    {/* Topic Dropdown */}
                    {selectedSubject && (
                      <Form.Group controlId="topic">
                        <Form.Label>Topic</Form.Label>
                        <Select
                          options={topicOptions}
                          onChange={handleTopicChange}
                          value={selectedTopic}
                          placeholder="Select Topic"
                        />
                      </Form.Group>
                    )}
                  </Col>

                  <Col>
                    {selectedTopic && (
                      <Form.Group controlId="subtopic">
                        <Form.Label>Subtopic</Form.Label>
                        <Select
                          options={subtopicOptions}
                          onChange={handleSubtopicChange}
                          value={selectedSubtopic}
                          placeholder="Select Subtopic"
                        />
                      </Form.Group>
                    )}
                  </Col>
                </Row>

                <Form.Group controlId="course" className="mb-3">
                  <Form.Label>Select Course
                  <Link to="/add_courses" > 
                    <Button variant="outline-success" style={{marginLeft: "1rem"}}>
                    <FaPlus/> {/* Add the Plus icon */}
                    </Button>
                  </Link>
                  </Form.Label>
                  <Select
                    options={courseOptionsWithSelectAll}
                    isMulti
                    value={courseOptionsWithSelectAll.filter((option) => selectedCourses.includes(option.value))}
                    onChange={handleSelectAllCourses}
                    placeholder="Select courses"
                  />
                </Form.Group>

                <div>
                  <Button variant="outline-primary w-100 text-center" onClick={handleGetSubcourses} className="mb-3">
                    Get Subcourses
                  </Button>
                </div>

                {selectedCourses.map((courseId) => (
                  <Form.Group key={courseId} controlId={`subcourse-${courseId}`} className="mb-3">
                    <Form.Label>Select Subcourses for {courses.find(course => course.course_id === courseId)?.name}</Form.Label>
                    <Select
                      options={[
                        { value: 'selectAll', label: 'Select All Subcourses' },
                        ...(subcourseOptions[courseId] || [])
                      ]}
                      isMulti
                      value={selectedSubcourses[courseId]?.map(id => ({ value: id, label: subcourseOptions[courseId]?.find(sub => sub.value === id)?.label })) || []}
                      onChange={(selectedOptions) => handleSelectAllSubcourses(courseId, selectedOptions)}
                      placeholder="Select subcourses"
                    />
                  </Form.Group>
                ))}

                <Row>
                  <Col>
                    <Form.Group controlId="language">
                      <Form.Label>Select Language</Form.Label>
                      <Select
                        options={languageOptions}
                        value={languageOptions.find((option) => option.value === formData.language)}
                        onChange={(selectedOption) => setFormData({ ...formData, language: selectedOption.value })}
                        placeholder="Select language"
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col className="d-flex align-items-end">
                    <Button variant="outline-success" type="submit" className="w-100">
                      Submit
                    </Button>
                  </Col>
                </Row>
              </Form>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Modal for Adding Options */}
      <Modal 
        show={showModal}
        onHide={handleCloseModal} // Trigger confirmation dialog on close
        backdrop="static" // Prevent closing on outside click
        keyboard={false} // Prevent closing on Esc key
                
        centered style={{
        position: 'fixed',
        top: '50%',
        left: '10%',
        transform: 'translateY(-50%)',
        margin: '0',
        width: '310px', // Optional: Set a specific width for the modal
      }}>
        <Modal.Header closeButton>
          <Modal.Title>Add Option</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Control
            type="text"
            name="optionText"
            value={optionFormData.optionText}
            onChange={handleOptionInputChange} // Use the handler for optionFormData
            placeholder="Enter option text"
          />

          <Form.Check
            type="radio"
            label="Yes"
            name="isCorrect"
            value="true"
            checked={optionFormData.isCorrect === 'true'}
            onChange={handleOptionInputChange} // Use the handler for optionFormData
          />
          <Form.Check
            type="radio"
            label="No"
            name="isCorrect"
            value="false"
            checked={optionFormData.isCorrect === 'false'}
            onChange={handleOptionInputChange}
          />


          <Button variant="success" onClick={handleAddOption} disabled={isSubmitting} className="w-100">
            Add Option
          </Button>
        </Modal.Body>
      </Modal>

       {/* Confirmation Modal */}
       <Modal
        show={showConfirmation}
        onHide={handleCancelClose}
        centered
        backdrop="static"
        keyboard={false}
      >
        <Modal.Header>
          <Modal.Title>Are you sure?</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Do you really want to close the form? All changes will be lost.
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCancelClose}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleConfirmClose}>
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>

    </>
  );
};

