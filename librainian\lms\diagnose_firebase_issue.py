#!/usr/bin/env python3
"""
Diagnose Firebase FCM Issue
This script diagnoses the 404 /batch error and provides solutions.
"""

import os
import sys
import django
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def check_firebase_project_config():
    """Check Firebase project configuration"""
    print("🔍 DIAGNOSING FIREBASE FCM ISSUE")
    print("=" * 50)
    
    # Check service account file
    service_account_file = "firebase-service-account.json"
    if not os.path.exists(service_account_file):
        print("❌ Service account file not found")
        return False
    
    try:
        with open(service_account_file, 'r') as f:
            service_account = json.load(f)
        
        project_id = service_account.get('project_id')
        client_email = service_account.get('client_email')
        
        print(f"✅ Service Account File Found:")
        print(f"   Project ID: {project_id}")
        print(f"   Client Email: {client_email}")
        
        # Check if it's the real credentials
        if 'firebase-adminsdk' not in client_email:
            print("⚠️  WARNING: This doesn't look like a real Firebase service account")
            return False
        
        return True, project_id, client_email
        
    except Exception as e:
        print(f"❌ Error reading service account file: {e}")
        return False

def show_fcm_setup_solution():
    """Show the solution for FCM setup"""
    print("\n🔧 SOLUTION: Enable FCM API in Firebase Project")
    print("=" * 50)
    
    print("\n📋 The 404 /batch error means FCM API is not enabled.")
    print("Here's how to fix it:")
    
    print("\n1️⃣ **Enable FCM API in Google Cloud Console**")
    print("   🌐 Visit: https://console.cloud.google.com/apis/library/fcm.googleapis.com")
    print("   🔘 Select project: 'librainian-app'")
    print("   🔘 Click 'ENABLE' button")
    
    print("\n2️⃣ **Enable Firebase Cloud Messaging API**")
    print("   🌐 Visit: https://console.cloud.google.com/apis/api/fcm.googleapis.com")
    print("   🔘 Make sure it shows 'API enabled'")
    
    print("\n3️⃣ **Check Firebase Project Settings**")
    print("   🌐 Visit: https://console.firebase.google.com/project/librainian-app/settings/cloudmessaging")
    print("   🔘 Verify 'Cloud Messaging' tab is accessible")
    print("   🔘 Check if Server Key is available")
    
    print("\n4️⃣ **Verify Service Account Permissions**")
    print("   🌐 Visit: https://console.cloud.google.com/iam-admin/iam")
    print("   🔘 Find your service account: <EMAIL>")
    print("   🔘 Ensure it has 'Firebase Admin SDK Administrator Service Agent' role")

def create_simple_test():
    """Create a simple FCM test without multicast"""
    print("\n🧪 TESTING WITH SIMPLE FCM MESSAGE")
    print("=" * 50)
    
    try:
        from firebase_admin import messaging
        from librarian.models import DeviceToken
        
        # Get a device token
        device_token = DeviceToken.objects.filter(is_active=True).first()
        if not device_token:
            print("❌ No device tokens found")
            return False
        
        print(f"📱 Testing with token: {device_token.token[:30]}...")
        
        # Create simple message
        message = messaging.Message(
            notification=messaging.Notification(
                title="🧪 Simple FCM Test",
                body="Testing FCM with basic message (no multicast)"
            ),
            token=device_token.token,
            data={
                "test": "simple_fcm",
                "timestamp": "123456789"
            }
        )
        
        # Send single message
        response = messaging.send(message)
        print(f"✅ Simple message sent successfully!")
        print(f"📊 Message ID: {response}")
        return True
        
    except Exception as e:
        print(f"❌ Simple FCM test failed: {e}")
        if "404" in str(e):
            print("🔍 Still getting 404 - FCM API is definitely not enabled")
        return False

def show_alternative_solution():
    """Show alternative solution using legacy FCM"""
    print("\n🔄 ALTERNATIVE: Use Legacy FCM HTTP API")
    print("=" * 50)
    
    print("\n💡 If enabling FCM API doesn't work, we can use the legacy HTTP API:")
    
    print("\n1️⃣ **Get Server Key from Firebase**")
    print("   🌐 Visit: https://console.firebase.google.com/project/librainian-app/settings/cloudmessaging")
    print("   🔑 Copy the 'Server key' (starts with 'AAAA...')")
    
    print("\n2️⃣ **Use HTTP API Instead**")
    print("   📡 Send notifications via HTTPS POST to:")
    print("   https://fcm.googleapis.com/fcm/send")
    
    print("\n3️⃣ **I can implement this fallback method**")
    print("   ✅ More reliable for some Firebase projects")
    print("   ✅ Works even if Admin SDK has issues")
    print("   ✅ Same functionality, different transport")

def main():
    # Check Firebase project config
    config_result = check_firebase_project_config()
    if not config_result:
        print("\n❌ Firebase configuration issue detected")
        return
    
    success, project_id, client_email = config_result
    
    # Show the main solution
    show_fcm_setup_solution()
    
    # Try simple FCM test
    print("\n" + "=" * 50)
    simple_test_result = create_simple_test()
    
    if not simple_test_result:
        show_alternative_solution()
    
    print("\n" + "=" * 50)
    print("🎯 SUMMARY:")
    print("The Firebase credentials are correct, but FCM API is not enabled.")
    print("Follow the steps above to enable FCM API in Google Cloud Console.")
    print("\n🚀 After enabling FCM API:")
    print("   1. Wait 2-3 minutes for propagation")
    print("   2. Restart Django server")
    print("   3. Test notifications again")
    print("   4. Should work without 404 errors!")

if __name__ == "__main__":
    main()
