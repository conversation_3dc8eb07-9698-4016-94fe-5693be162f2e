import React, { useContext } from 'react';
import { View, Text, ScrollView, StyleSheet, SafeAreaView } from 'react-native';
import BottomTabBar from '../../components/BottomTabBar';
import { ThemeContext } from '../../context/ThemeContext'; // Add this import

export default function AboutUsScreen() {
  const { isDarkMode } = useContext(ThemeContext); // Use ThemeContext

  // Dynamic styles based on theme
  const themedStyles = {
    container: {
      ...styles.container,
      backgroundColor: isDarkMode ? '#181818' : '#f5f5f5',
    },
    title: {
      ...styles.title,
      color: isDarkMode ? '#fff' : '#28a745',
    },
    subtitle: {
      ...styles.subtitle,
      color: isDarkMode ? '#aaa' : '#666',
    },
    card: {
      ...styles.card,
      backgroundColor: isDarkMode ? '#232323' : '#fff',
      shadowColor: isDarkMode ? '#000' : '#000',
    },
    paragraph: {
      ...styles.paragraph,
      color: isDarkMode ? '#eee' : '#333',
    },
  };

  return (
    <BottomTabBar>
      <SafeAreaView style={themedStyles.container}>
        <ScrollView>
          <View style={styles.headerSection}>
            <Text style={themedStyles.title}>About Us</Text>
            <Text style={themedStyles.subtitle}>Learn. Practice. Compete. Improve. That's the Shashtrath way.</Text>
          </View>

          <View style={themedStyles.card}>
            <View style={styles.cardContent}>
              <Text style={themedStyles.paragraph}>
                <Text style={styles.bold}>Shashtrath</Text> is an evolving digital platform built to empower students preparing for competitive exams across India. 
                We believe that success in examinations is not just about hard work—it's about smart preparation, personalized insights, and community support.
              </Text>
              
              <Text style={themedStyles.paragraph}>
                Our platform offers <Text style={styles.bold}>mock tests</Text>, <Text style={styles.bold}>performance analytics</Text>, {' '}
                <Text style={styles.bold}>rank comparisons</Text>, and <Text style={styles.bold}>strategy-based insights</Text> to help every learner 
                understand their strengths and improve where it matters most.
              </Text>

              <Text style={themedStyles.paragraph}>
                But we're more than just a testing platform. With an integrated <Text style={styles.bold}>blog</Text>, real-time {' '}
                <Text style={styles.bold}>referral rewards</Text>, and a student-first mobile app, we're creating an ecosystem that makes 
                exam prep engaging, efficient, and empowering.
              </Text>

              <Text style={[themedStyles.paragraph, styles.bold]}>Learn. Practice. Compete. Improve.</Text>
              <Text style={themedStyles.paragraph}>That's the Shashtrath way.</Text>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </BottomTabBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerSection: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#28a745',
    textTransform: 'uppercase',
    marginTop: 20,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 10,
    color: '#666',
  },
  card: {
    margin: 15,
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardContent: {
    padding: 15,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 15,
    color: '#333',
  },
  bold: {
    fontWeight: 'bold',
  },
});
