    {/* Current Month Card */}
    <Card className="my-2 shadow">
          <Card.Body>
            <Row className="d-flex align-items-center">
              <Col md={7}>
                <h5 className="mb-3" style={{ fontSize: '1.3rem', color: '#146c43' }}>Current Month Summary</h5>
                <MonthSummary
                  month="Current Month"
                  data={contributionData?.current_month_data?.questions || {}}
                />
              </Col>
              <Col md={5}>
                <h5 className="text-center mb-3" style={{ fontSize: '1.3rem', color: '#146c43' }}>Current Month Points</h5>
                {/* Pass individual props to SmallTiles */}
                <SmallTiles
                  normalQuestions={contributionData?.current_month_points?.normal_questions || 0}
                  masterQuestions={contributionData?.current_month_points?.master_questions || 0}
                  masterOptions={contributionData?.current_month_points?.master_options || 0}
                  blogs={contributionData?.current_month_points?.blogs || 0}
                  previousQuestions={contributionData?.current_month_points?.previous_questions || 0}
                  totalPoints={contributionData?.current_month_points?.total_points || 0}
                />
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Previous Month Card */}
        <Card className="my-2 shadow">
          <Card.Body>
            <Row>
              <Col md={7}>
                <h5 className="mb-3" style={{ fontSize: '1.3rem', color: '#146c43' }}>Previous Month Summary</h5>
                <MonthSummary
                  month="Previous Month"
                  data={contributionData?.previous_month_data?.questions || {}}
                />
              </Col>
              <Col md={5}>
                <h5 className="text-center mb-3" style={{ fontSize: '1.3rem', color: '#146c43' }}>Previous Month Points</h5>
                {/* Pass individual props to SmallTiles */}
                <SmallTiles
                  normalQuestions={contributionData?.previous_month_points?.normal_questions || 0}
                  masterQuestions={contributionData?.previous_month_points?.master_questions || 0}
                  masterOptions={contributionData?.previous_month_points?.master_options || 0}
                  blogs={contributionData?.previous_month_points?.blogs || 0}
                  previousQuestions={contributionData?.previous_month_points?.previous_questions || 0}
                  totalPoints={contributionData?.previous_month_points?.total_points || 0}
                />
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Third Month Card */}
        <Card className="my-2 shadow">
          <Card.Body>
            <Row>
              <Col md={7}>
                <h5 className="mb-3" style={{ fontSize: '1.3rem', color: '#146c43' }}>Third Month Summary</h5>
                <MonthSummary
                  month="Third Month"
                  data={contributionData?.third_month_data?.questions || {}}
                />
              </Col>
              <Col md={5}>
                <h5 className="text-center mb-3" style={{ fontSize: '1.3rem', color: '#146c43' }}>Third Month Points</h5>
                {/* Pass individual props to SmallTiles */}
                <SmallTiles
                  normalQuestions={contributionData?.third_month_points?.normal_questions || 0}
                  masterQuestions={contributionData?.third_month_points?.master_questions || 0}
                  masterOptions={contributionData?.third_month_points?.master_options || 0}
                  blogs={contributionData?.third_month_points?.blogs || 0}
                  previousQuestions={contributionData?.third_month_points?.previous_questions || 0}
                  totalPoints={contributionData?.third_month_points?.total_points || 0}
                />
              </Col>
            </Row>
          </Card.Body>
        </Card>
