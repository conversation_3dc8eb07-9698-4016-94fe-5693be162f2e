import React from 'react';
import { InlineMath, BlockMath } from 'react-katex';
import 'katex/dist/katex.min.css';

const MathPreview = ({ 
  latex = '', 
  displayMode = false, 
  className = '',
  showRaw = false,
  placeholder = 'Math preview will appear here...' 
}) => {
  // Clean up the LaTeX string
  const cleanLatex = latex.trim();

  if (!cleanLatex) {
    return (
      <div className={`math-preview-placeholder text-muted ${className}`}>
        <em>{placeholder}</em>
      </div>
    );
  }

  try {
    return (
      <div className={`math-preview ${className}`}>
        {displayMode ? (
          <BlockMath math={cleanLatex} />
        ) : (
          <InlineMath math={cleanLatex} />
        )}
        {showRaw && (
          <div className="mt-2">
            <small className="text-muted">
              <strong>LaTeX:</strong> <code>{cleanLatex}</code>
            </small>
          </div>
        )}
      </div>
    );
  } catch (error) {
    return (
      <div className={`math-preview-error text-danger ${className}`}>
        <small>
          <strong>Error rendering math:</strong> {error.message}
          {showRaw && (
            <div className="mt-1">
              <strong>LaTeX:</strong> <code>{cleanLatex}</code>
            </div>
          )}
        </small>
      </div>
    );
  }
};

export default MathPreview;
