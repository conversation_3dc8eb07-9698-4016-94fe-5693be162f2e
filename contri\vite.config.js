import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';

export default defineConfig({
  plugins: [react()],
  build: {
    minify: 'esbuild', // SWC handles JSX & transforms; Esbuild does final minification
    sourcemap: false,
    cssCodeSplit: true, // Splits CSS for better performance
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('node_modules')) {
            if (id.includes('react-bootstrap')) return 'react-bootstrap';
            if (id.includes('chart.js')) return 'chart-js';
            if (id.includes('firebase')) return 'firebase';
            return 'vendor'; // Let Vite decide chunking for other dependencies
          }
        },
      },
    },
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
  },
});
