"""
Comprehensive Notification Events System
Handles all notification triggers for the LMS
"""

from django.utils import timezone
from django.db.models import Sum
from django.contrib.auth.models import User
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class NotificationEventManager:
    """Manages all notification events and triggers"""
    
    def __init__(self):
        self.notification_service = None
        try:
            from .notification_service import notification_service
            self.notification_service = notification_service
        except ImportError:
            logger.warning("Notification service not available")
    
    def send_notification(self, event_type, recipients, **kwargs):
        """Send notification using the notification service"""
        if not self.notification_service:
            logger.warning(f"Cannot send notification: {event_type}")
            return False
            
        try:
            # Send to multiple recipients if needed
            if hasattr(recipients, '__iter__') and not isinstance(recipients, (str, User)):
                # Handle QuerySet or list of recipients
                recipients = list(recipients)
            else:
                recipients = [recipients]

            for recipient in recipients:
                self.notification_service.send_notification(
                    event_type, recipient, **kwargs
                )
            return True
        except Exception as e:
            logger.error(f"Error sending notification {event_type}: {e}")
            return False

    # 1. VISITOR CALLBACK NOTIFICATIONS
    def check_visitor_callbacks_today(self):
        """Check for visitor callbacks scheduled for today"""
        try:
            from visitorsData.models import Visitor

            today = timezone.now().date()
            callbacks_today = Visitor.objects.filter(
                callback=today,
                status__in=['pending', 'Checked In', 'Checked Out']  # Active visitors
            )

            if callbacks_today.exists():
                # Get librarians to notify
                librarians = User.objects.filter(
                    groups__name__in=['Librarian', 'SubLibrarian']
                )

                for callback in callbacks_today:
                    self.send_notification(
                        'visitor_callback_due',
                        librarians,
                        visitor_name=callback.name,
                        visitor_mobile=str(callback.contact),
                        callback_date=callback.callback.strftime('%Y-%m-%d'),
                        visitor_id=callback.id
                    )

                logger.info(f"Sent {callbacks_today.count()} visitor callback notifications")
                return callbacks_today.count()

        except Exception as e:
            logger.error(f"Error checking visitor callbacks: {e}")
        return 0

    # 2. ADMISSION PROCESSED NOTIFICATIONS
    def notify_admission_processed(self, student, processed_by):
        """Notify when admission is processed by sublibrarian"""
        try:
            # Notify main librarians
            librarians = User.objects.filter(
                groups__name='Librarian'
            ).exclude(id=processed_by.id)
            
            self.send_notification(
                'admission_processed',
                librarians,
                student_name=student.name,
                student_email=student.email,
                processed_by=processed_by.username,
                admission_date=timezone.now().strftime('%Y-%m-%d'),
                student_id=student.id
            )
            
            logger.info(f"Admission processed notification sent for {student.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending admission processed notification: {e}")
            return False

    # 3. MEMBER EXPIRY NOTIFICATIONS
    def check_member_expiry_notifications(self):
        """Check for member expiry notifications (10, 5, 1 days, and expired)"""
        try:
            from studentsData.models import StudentsData
            
            today = timezone.now().date()
            notifications_sent = 0
            
            # Get librarians to notify
            librarians = User.objects.filter(
                groups__name__in=['Librarian', 'SubLibrarian']
            )
            
            # Check for different expiry periods
            expiry_periods = [
                (10, 'member_expiry_10_days'),
                (5, 'member_expiry_5_days'), 
                (1, 'member_expiry_1_day'),
                (0, 'member_expired')
            ]
            
            for days, event_type in expiry_periods:
                if days == 0:
                    # Already expired
                    target_date = today
                    members = StudentsData.objects.filter(
                        membership_end_date=target_date,
                        is_active=True
                    )
                else:
                    # Expiring in X days
                    target_date = today + timedelta(days=days)
                    members = StudentsData.objects.filter(
                        membership_end_date=target_date,
                        is_active=True
                    )
                
                for member in members:
                    self.send_notification(
                        event_type,
                        librarians,
                        member_name=member.name,
                        member_email=member.email,
                        member_mobile=member.mobile,
                        expiry_date=member.membership_end_date.strftime('%Y-%m-%d'),
                        days_remaining=days,
                        member_id=member.id
                    )
                    notifications_sent += 1
            
            logger.info(f"Sent {notifications_sent} member expiry notifications")
            return notifications_sent
            
        except Exception as e:
            logger.error(f"Error checking member expiry: {e}")
            return 0

    # 4. INVOICE CREATED NOTIFICATIONS
    def notify_invoice_created(self, invoice, created_by):
        """Notify when invoice is created by sublibrarian"""
        try:
            # Notify main librarians
            librarians = User.objects.filter(
                groups__name='Librarian'
            ).exclude(id=created_by.id)
            
            self.send_notification(
                'invoice_created',
                librarians,
                invoice_number=invoice.invoice_number,
                student_name=invoice.student.name,
                amount=str(invoice.total_amount),
                created_by=created_by.username,
                creation_date=timezone.now().strftime('%Y-%m-%d'),
                invoice_id=invoice.id
            )
            
            logger.info(f"Invoice created notification sent for {invoice.invoice_number}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending invoice created notification: {e}")
            return False

    # 5. SALES MILESTONE NOTIFICATIONS
    def check_sales_milestones(self):
        """Check for sales milestones (50k, 100k, 150k, 200k)"""
        try:
            from studentsData.models import Invoice
            
            today = timezone.now().date()
            
            # Get total sales for current month
            month_start = today.replace(day=1)
            monthly_sales = Invoice.objects.filter(
                issue_date__gte=month_start,
                issue_date__lte=today,
                payment_status='Paid'
            ).aggregate(total=Sum('total_amount'))['total'] or 0
            
            # Check milestones
            milestones = [50000, 100000, 150000, 200000]
            librarians = User.objects.filter(
                groups__name__in=['Librarian', 'SubLibrarian']
            )
            
            for milestone in milestones:
                if monthly_sales >= milestone:
                    # Check if we've already notified for this milestone this month
                    milestone_key = f"sales_milestone_{milestone}_{month_start.strftime('%Y_%m')}"
                    
                    # You can store this in cache or a simple model to avoid duplicate notifications
                    # For now, we'll send the notification
                    self.send_notification(
                        'sales_milestone_reached',
                        librarians,
                        milestone_amount=f"₹{milestone:,}",
                        current_sales=f"₹{monthly_sales:,}",
                        month=month_start.strftime('%B %Y'),
                        milestone_value=milestone
                    )
                    
                    logger.info(f"Sales milestone notification sent: ₹{milestone:,}")
            
            return monthly_sales
            
        except Exception as e:
            logger.error(f"Error checking sales milestones: {e}")
            return 0

    # 6. MONTHLY SALES SUMMARY
    def send_monthly_sales_summary(self):
        """Send monthly sales summary on last day of month"""
        try:
            from studentsData.models import Invoice
            
            today = timezone.now().date()
            
            # Check if today is last day of month
            tomorrow = today + timedelta(days=1)
            if tomorrow.month != today.month:
                # It's the last day of the month
                month_start = today.replace(day=1)
                
                monthly_sales = Invoice.objects.filter(
                    issue_date__gte=month_start,
                    issue_date__lte=today,
                    payment_status='Paid'
                ).aggregate(total=Sum('total_amount'))['total'] or 0

                invoice_count = Invoice.objects.filter(
                    issue_date__gte=month_start,
                    issue_date__lte=today,
                    payment_status='Paid'
                ).count()
                
                librarians = User.objects.filter(
                    groups__name__in=['Librarian', 'SubLibrarian']
                )
                
                self.send_notification(
                    'monthly_sales_summary',
                    librarians,
                    month=month_start.strftime('%B %Y'),
                    total_sales=f"₹{monthly_sales:,}",
                    invoice_count=invoice_count,
                    average_invoice=f"₹{monthly_sales/invoice_count if invoice_count > 0 else 0:,.2f}"
                )
                
                logger.info(f"Monthly sales summary sent: ₹{monthly_sales:,}")
                return True
                
        except Exception as e:
            logger.error(f"Error sending monthly sales summary: {e}")
        return False

    # 7. VISITOR ADDED NOTIFICATIONS
    def notify_visitor_added(self, visitor, added_by):
        """Notify when visitor is added by sublibrarian"""
        try:
            # Notify main librarians
            librarians = User.objects.filter(
                groups__name='Librarian'
            ).exclude(id=added_by.id)

            self.send_notification(
                'visitor_added',
                librarians,
                visitor_name=visitor.name,
                visitor_mobile=str(visitor.contact),
                visitor_email=getattr(visitor, 'email', 'Not provided'),
                visitor_purpose=getattr(visitor, 'purpose', 'Not specified'),
                added_by=added_by.get_full_name() or added_by.username,
                visit_date=timezone.now().strftime('%Y-%m-%d'),
                visitor_id=visitor.id
            )

            logger.info(f"Visitor added notification sent for {visitor.name}")
            return True

        except Exception as e:
            logger.error(f"Error sending visitor added notification: {e}")
            return False

    # 8. QR REGISTRATION NOTIFICATIONS (Enhanced)
    def notify_qr_registration(self, temp_student):
        """Enhanced QR registration notification with complete data"""
        try:
            # Get the librarian to notify
            if temp_student.librarian:
                recipient = temp_student.librarian.user
            else:
                # Fallback to all librarians if no specific librarian
                librarians = User.objects.filter(groups__name='Librarian')
                if not librarians.exists():
                    logger.warning("No librarians found for QR registration notification")
                    return False
                recipient = librarians.first()

            # Prepare comprehensive data
            context_data = {
                'student_name': temp_student.name,
                'student_email': temp_student.email or 'Not provided',
                'student_mobile': str(temp_student.mobile) if temp_student.mobile else 'Not provided',
                'course': temp_student.course.name if temp_student.course else 'Not specified',
                'student_age': temp_student.age or 'Not provided',
                'student_gender': temp_student.gender or 'Not provided',
                'student_city': temp_student.city or 'Not provided',
                'student_state': temp_student.state.name if temp_student.state else 'Not provided',
                'library_name': temp_student.librarian.library_name if temp_student.librarian else 'Library',
                'registration_date': temp_student.registration_date.strftime('%Y-%m-%d'),
                'temp_student_id': temp_student.id,
                'identity_type': temp_student.identity_type or 'Not provided',
                'identity_number': temp_student.identity_number or 'Not provided'
            }

            self.send_notification(
                'qr_registration',
                [recipient],
                **context_data
            )

            logger.info(f"Enhanced QR registration notification sent for {temp_student.name}")
            return True

        except Exception as e:
            logger.error(f"Error sending QR registration notification: {e}")
            return False

    # 9. ADMISSION PROCESSED NOTIFICATIONS
    def notify_admission_processed(self, student, processed_by):
        """Notify when admission is processed by librarian"""
        try:
            # Notify other librarians and sublibrarians
            recipients = User.objects.filter(
                groups__name__in=['Librarian', 'SubLibrarian']
            ).exclude(id=processed_by.id)

            self.send_notification(
                'admission_processed',
                recipients,
                student_name=student.name,
                student_email=student.email,
                student_mobile=str(student.mobile),
                course=student.course.name if student.course else 'Not specified',
                processed_by=processed_by.get_full_name() or processed_by.username,
                admission_date=timezone.now().strftime('%Y-%m-%d'),
                student_id=student.id,
                unique_id=student.unique_id
            )

            logger.info(f"Admission processed notification sent for {student.name}")
            return True

        except Exception as e:
            logger.error(f"Error sending admission processed notification: {e}")
            return False

    # 10. INVOICE CREATED NOTIFICATIONS
    def notify_invoice_created(self, invoice, created_by):
        """Notify when invoice is created"""
        try:
            # Notify librarians
            recipients = User.objects.filter(
                groups__name__in=['Librarian', 'SubLibrarian']
            ).exclude(id=created_by.id)

            self.send_notification(
                'invoice_created',
                recipients,
                invoice_number=invoice.invoice_id,
                student_name=invoice.student.name,
                student_email=invoice.student.email,
                student_mobile=str(invoice.student.mobile),
                course=invoice.student.course.name if invoice.student.course else 'Not specified',
                amount=invoice.total_amount,
                due_date=invoice.due_date.strftime('%Y-%m-%d'),
                created_by=created_by.get_full_name() or created_by.username,
                invoice_id=invoice.id
            )

            logger.info(f"Invoice created notification sent for {invoice.invoice_id}")
            return True

        except Exception as e:
            logger.error(f"Error sending invoice created notification: {e}")
            return False

    # 11. PAYMENT RECEIVED NOTIFICATIONS
    def notify_payment_received(self, payment, received_by):
        """Notify when payment is received"""
        try:
            # Notify librarians
            recipients = User.objects.filter(
                groups__name__in=['Librarian', 'SubLibrarian']
            ).exclude(id=received_by.id)

            invoice = payment.invoice
            student = invoice.student

            self.send_notification(
                'payment_received',
                recipients,
                student_name=student.name,
                student_email=student.email,
                student_mobile=str(student.mobile),
                course=student.course.name if student.course else 'Not specified',
                invoice_number=invoice.invoice_id,
                amount_paid=payment.amount_paid,
                payment_mode=payment.payment_mode,
                remaining_due=invoice.remaining_due,
                payment_status=invoice.payment_status,
                received_by=received_by.get_full_name() or received_by.username,
                payment_date=payment.payment_date.strftime('%Y-%m-%d'),
                payment_id=payment.id
            )

            logger.info(f"Payment received notification sent for ₹{payment.amount_paid}")
            return True

        except Exception as e:
            logger.error(f"Error sending payment received notification: {e}")
            return False

    # 12. SEAT OCCUPANCY NOTIFICATIONS
    def check_seat_occupancy(self):
        """Check seat occupancy and notify when 80% threshold is reached"""
        try:
            from studentsData.models import Seat, Booking, Shift

            notifications_sent = 0

            # Check occupancy for each shift
            shifts = Shift.objects.all()

            for shift in shifts:
                # Get total seats for this shift
                total_seats = Seat.objects.filter(shift=shift).count()

                if total_seats == 0:
                    continue

                # Get occupied seats (active bookings)
                occupied_seats = Booking.objects.filter(
                    seat__shift=shift,
                    expire_date__gte=timezone.now().date()
                ).count()

                occupancy_percentage = (occupied_seats / total_seats) * 100

                # Check if 80% threshold is reached
                if occupancy_percentage >= 80:
                    # Get librarians to notify
                    librarians = User.objects.filter(
                        groups__name__in=['Librarian', 'SubLibrarian']
                    )

                    self.send_notification(
                        'seat_occupancy_alert',
                        librarians,
                        shift_name=shift.name,
                        occupancy_percentage=f"{occupancy_percentage:.1f}%",
                        occupied_seats=occupied_seats,
                        total_seats=total_seats,
                        available_seats=total_seats - occupied_seats,
                        alert_date=timezone.now().strftime('%Y-%m-%d'),
                        shift_id=shift.pk
                    )

                    notifications_sent += 1
                    logger.info(f"Seat occupancy alert sent for {shift.name}: {occupancy_percentage:.1f}%")

            return notifications_sent

        except Exception as e:
            logger.error(f"Error checking seat occupancy: {e}")
            return 0

    # 13. ENHANCED MEMBERSHIP EXPIRY NOTIFICATIONS (For Librarians)
    def check_membership_expiry_notifications(self):
        """Enhanced membership expiry notifications for librarians - checks Membership model"""
        try:
            from membership.models import Membership

            today = timezone.now().date()
            notifications_sent = 0

            # Get librarians to notify (all librarians get notified about any membership expiry)
            librarians = User.objects.filter(
                groups__name__in=['Librarian', 'SubLibrarian']
            )

            # Enhanced expiry periods including post-expiry
            expiry_periods = [
                (10, 'member_expiry_10_days', 'expires in 10 days'),
                (5, 'member_expiry_5_days', 'expires in 5 days'),
                (1, 'member_expiry_1_day', 'expires tomorrow'),
                (0, 'member_expired', 'expired today'),
                (-4, 'member_expired_4_days', 'expired 4 days ago')
            ]

            for days, event_type, description in expiry_periods:
                if days >= 0:
                    # Future or today expiry
                    target_date = today + timedelta(days=days)
                    memberships = Membership.objects.filter(
                        expiry_date=target_date
                    )
                else:
                    # Past expiry (4 days after)
                    target_date = today + timedelta(days=days)  # days is negative
                    memberships = Membership.objects.filter(
                        expiry_date=target_date
                    )

                for membership in memberships:
                    librarian_user = membership.librarian.user
                    library_name = membership.librarian.library_name if hasattr(membership.librarian, 'library_name') else 'Library'

                    self.send_notification(
                        event_type,
                        librarians,
                        member_name=librarian_user.get_full_name() or librarian_user.username,
                        member_email=librarian_user.email,
                        member_mobile='Not available',  # Librarian mobile not in user model
                        course='Librarian Membership',
                        expiry_date=membership.expiry_date.strftime('%Y-%m-%d'),
                        days_info=description,
                        member_id=membership.pk,
                        unique_id=f"LIB_{membership.pk}",
                        plan_name=membership.plan.name if membership.plan else 'Unknown Plan',
                        library_name=library_name
                    )
                    notifications_sent += 1

                    logger.info(f"Membership expiry notification sent for {library_name}: {description}")

            logger.info(f"Sent {notifications_sent} membership expiry notifications")
            return notifications_sent

        except Exception as e:
            logger.error(f"Error checking membership expiry notifications: {e}")
            return 0

    # 14. STUDENT SUBSCRIPTION EXPIRY NOTIFICATIONS (For Librarians about Students)
    def check_student_subscription_expiry_notifications(self):
        """Check student subscription expiry notifications for librarians"""
        try:
            from studentsData.models import Invoice

            today = timezone.now().date()
            notifications_sent = 0

            # Get librarians to notify
            librarians = User.objects.filter(
                groups__name__in=['Librarian', 'SubLibrarian']
            )

            # Enhanced expiry periods including post-expiry
            expiry_periods = [
                (10, 'student_subscription_expiry_10_days', 'expires in 10 days'),
                (5, 'student_subscription_expiry_5_days', 'expires in 5 days'),
                (1, 'student_subscription_expiry_1_day', 'expires tomorrow'),
                (0, 'student_subscription_expired', 'expired today'),
                (-4, 'student_subscription_expired_4_days', 'expired 4 days ago')
            ]

            for days, event_type, description in expiry_periods:
                if days >= 0:
                    # Future or today expiry
                    target_date = today + timedelta(days=days)
                    invoices = Invoice.objects.filter(
                        billing_end_date=target_date,
                        is_active=True,
                        payment_status='Paid'
                    )
                else:
                    # Past expiry (4 days after)
                    target_date = today + timedelta(days=days)  # days is negative
                    invoices = Invoice.objects.filter(
                        billing_end_date=target_date,
                        is_active=True
                    )

                for invoice in invoices:
                    student = invoice.student
                    self.send_notification(
                        event_type,
                        librarians,
                        student_name=student.name,
                        student_email=student.email,
                        student_mobile=str(student.mobile),
                        course=student.course.name if student.course else 'Not specified',
                        expiry_date=invoice.billing_end_date.strftime('%Y-%m-%d') if invoice.billing_end_date else 'Not set',
                        days_info=description,
                        student_id=student.pk,
                        unique_id=student.unique_id,
                        invoice_id=invoice.invoice_id,
                        library_name=student.librarian.library_name if student.librarian else 'Library'
                    )
                    notifications_sent += 1

            logger.info(f"Sent {notifications_sent} student subscription expiry notifications")
            return notifications_sent

        except Exception as e:
            logger.error(f"Error checking student subscription expiry notifications: {e}")
            return 0

    # 14. DAILY GALLA SUBMISSION REMINDER
    def send_daily_galla_reminder(self):
        """Send daily galla submission reminder"""
        try:
            # Send to sublibrarians who need to submit daily galla
            sublibrarians = User.objects.filter(
                groups__name='SubLibrarian',
                is_active=True
            )

            today = timezone.now().date()

            for sublibrarian in sublibrarians:
                self.send_notification(
                    'daily_galla_reminder',
                    [sublibrarian],
                    reminder_date=today.strftime('%Y-%m-%d'),
                    day_name=today.strftime('%A'),
                    user_name=sublibrarian.get_full_name() or sublibrarian.username
                )

            logger.info(f"Daily galla reminders sent to {sublibrarians.count()} sublibrarians")
            return sublibrarians.count()

        except Exception as e:
            logger.error(f"Error sending daily galla reminders: {e}")
            return 0

    # 9. CUSTOM ADMIN NOTIFICATIONS
    def send_custom_notification(self, title, message, recipients=None, priority='normal'):
        """Send custom notification from Django admin"""
        try:
            if recipients is None:
                # Send to all librarians and sublibrarians
                recipients = User.objects.filter(
                    groups__name__in=['Librarian', 'SubLibrarian'],
                    is_active=True
                )
            
            self.send_notification(
                'custom_admin_notification',
                recipients,
                custom_title=title,
                custom_message=message,
                priority=priority,
                sent_date=timezone.now().strftime('%Y-%m-%d %H:%M'),
                sender='Admin'
            )
            
            recipient_count = len(recipients) if isinstance(recipients, list) else recipients.count()
            logger.info(f"Custom admin notification sent to {recipient_count} users")
            return True
            
        except Exception as e:
            logger.error(f"Error sending custom admin notification: {e}")
            return False


# Global instance
notification_events = NotificationEventManager()
