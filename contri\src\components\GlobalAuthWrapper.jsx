import React, { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import toast from 'react-hot-toast';
import { clearContributorState } from '../Redux/slice/contributorSlice';
import { isTokenValid } from '../utils/authUtils';

const GlobalAuthWrapper = ({ children }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { accessToken } = useSelector((state) => state.contributor);
  const isRedirecting = useRef(false);

  useEffect(() => {
    // Set up axios response interceptor for global token expiration handling
    const responseInterceptor = axios.interceptors.response.use(
      (response) => {
        // If response is successful, return it as is
        return response;
      },
      (error) => {
        // Check if error is due to authentication issues
        if (error.response) {
          const { status, data } = error.response;
          
          // Handle 401 Unauthorized (token expired/invalid)
          if (status === 401) {
            handleTokenExpiration('Your session has expired. Please log in again.');
            return Promise.reject(error);
          }
          
          // Handle 403 Forbidden (insufficient permissions or invalid token)
          if (status === 403) {
            // Check if it's specifically about token expiration
            const errorMessage = data?.detail || data?.message || '';
            if (errorMessage.toLowerCase().includes('token') || 
                errorMessage.toLowerCase().includes('expired') ||
                errorMessage.toLowerCase().includes('invalid')) {
              handleTokenExpiration('Your session has expired. Please log in again.');
              return Promise.reject(error);
            }
          }
        }
        
        // Return the error for further handling by the calling code
        return Promise.reject(error);
      }
    );

    // Cleanup function to remove interceptor when component unmounts
    return () => {
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, []);

  // Function to handle token expiration
  const handleTokenExpiration = (message) => {
    // Prevent multiple simultaneous redirects
    if (isRedirecting.current) {
      return;
    }

    isRedirecting.current = true;

    // Clear the Redux state
    dispatch(clearContributorState());

    // Show error message
    toast.error(message);

    // Redirect to login page after a short delay
    setTimeout(() => {
      isRedirecting.current = false;
      navigate('/contributor_login');
    }, 1500);
  };

  // Use the utility function for token validation

  // Check token validity on app load and route changes
  useEffect(() => {
    // Skip validation for public routes
    const publicRoutes = ['/', '/contributor_login', '/contributor_signup'];
    const isPublicRoute = publicRoutes.includes(location.pathname);
    
    if (isPublicRoute) {
      return;
    }

    // If there's no token and user is trying to access protected route
    if (!accessToken) {
      toast.error('Please log in as a contributor!');
      setTimeout(() => {
        navigate('/contributor_login');
      }, 1500);
      return;
    }

    // If token exists, check if it's expired (for JWT tokens)
    if (!isTokenValid(accessToken)) {
      handleTokenExpiration('Your session has expired. Please log in again.');
      return;
    }

  }, [accessToken, location.pathname, navigate, dispatch]);

  return children;
};

export default GlobalAuthWrapper;
