import React, { useState, useEffect } from "react";
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button, Badge, <PERSON>, Modal, <PERSON><PERSON>, Spinner } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { 
  fetchPopupBanners, 
  approvePopupBanner, 
  rejectPopupBanner, 
  togglePopupBanner,
  clearError 
} from "../../redux/slice/popupApprovalSlice";
import NavigationBar from "../../commonComponents/NavigationBar";
import { toast } from "react-hot-toast";
import { FaCheck, FaTimes, FaEye, FaToggleOn, FaToggleOff, FaFilter } from "react-icons/fa";
import Swal from "sweetalert2";

const PopupApprovalDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { banners, isLoading, error } = useSelector((state) => state.popupApproval);
  const { access } = useSelector((state) => state.customerCare);

  // CSS styles for the preview modal
  const modalStyles = `
    .popup-preview-modal .modal-dialog {
      max-width: 90vw;
    }
    .popup-preview-modal .modal-content {
      border: none;
      border-radius: 16px;
      overflow: hidden;
    }
  `;

  // Local state
  const [filters, setFilters] = useState({
    approval_status: '',
    content_type: '',
    search: ''
  });
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewBanner, setPreviewBanner] = useState(null);

  // Check authentication
  useEffect(() => {
    if (!access) {
      toast.error("Please login first");
      navigate("/customer_care_login");
      return;
    }
    fetchBanners();
  }, [access, navigate, dispatch]);

  // Fetch banners
  const fetchBanners = () => {
    dispatch(fetchPopupBanners(filters));
  };

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // Apply filters
  const applyFilters = () => {
    fetchBanners();
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({ approval_status: '', content_type: '', search: '' });
    dispatch(fetchPopupBanners({}));
  };

  // Handle approve
  const handleApprove = async (id) => {
    const result = await Swal.fire({
      title: 'Approve Banner?',
      text: 'This will approve the banner for display.',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#28a745',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Approve!'
    });

    if (result.isConfirmed) {
      try {
        await dispatch(approvePopupBanner(id)).unwrap();
        toast.success('Banner approved successfully!');
        fetchBanners();
      } catch (error) {
        toast.error('Failed to approve banner');
      }
    }
  };

  // Handle reject
  const handleReject = (banner) => {
    setSelectedBanner(banner);
    setShowRejectModal(true);
  };

  // Submit rejection
  const submitRejection = async () => {
    if (!rejectionReason.trim()) {
      toast.error('Please provide a rejection reason');
      return;
    }

    try {
      await dispatch(rejectPopupBanner({ 
        id: selectedBanner.id, 
        rejection_reason: rejectionReason 
      })).unwrap();
      toast.success('Banner rejected successfully!');
      setShowRejectModal(false);
      setRejectionReason('');
      setSelectedBanner(null);
      fetchBanners();
    } catch (error) {
      toast.error('Failed to reject banner');
    }
  };

  // Handle toggle active status
  const handleToggleActive = async (id, currentStatus) => {
    const action = currentStatus ? 'deactivate' : 'activate';
    const result = await Swal.fire({
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Banner?`,
      text: `This will ${action} the banner.`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: currentStatus ? '#dc3545' : '#28a745',
      cancelButtonColor: '#6c757d',
      confirmButtonText: `Yes, ${action.charAt(0).toUpperCase() + action.slice(1)}!`
    });

    if (result.isConfirmed) {
      try {
        await dispatch(togglePopupBanner({ id, is_active: !currentStatus })).unwrap();
        toast.success(`Banner ${action}d successfully!`);
        fetchBanners();
      } catch (error) {
        toast.error(`Failed to ${action} banner`);
      }
    }
  };

  // Handle preview
  const handlePreview = (banner) => {
    setPreviewBanner(banner);
    setShowPreviewModal(true);
  };

  // Render popup preview content
  const renderPopupPreview = () => {
    if (!previewBanner) return null;

    const handleMainAction = () => {
      if (previewBanner.link_url) {
        window.open(previewBanner.link_url, '_blank');
      }
    };

    return (
      <div style={{
        background: 'white',
        borderRadius: '16px',
        padding: '2rem',
        maxWidth: '400px',
        width: '100%',
        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
        textAlign: 'center',
        margin: '0 auto'
      }}>
        {/* Title */}
        <h4 style={{
          marginBottom: '1rem',
          color: '#333',
          fontSize: '1.25rem',
          fontWeight: '600',
          lineHeight: '1.4'
        }}>
          {previewBanner.title}
        </h4>

        {/* Content based on type */}
        {previewBanner.content_type === "text_only" && (
          <div style={{
            color: '#666',
            marginBottom: '1.5rem',
            fontSize: '0.95rem',
            lineHeight: '1.5'
          }}>
            {previewBanner.text_content}
          </div>
        )}

        {previewBanner.content_type === "image_only" && previewBanner.image && (
          <div style={{ marginBottom: '1.5rem' }}>
            <img
              src={previewBanner.image}
              alt="Popup"
              style={{
                maxWidth: '100%',
                maxHeight: '200px',
                objectFit: 'contain',
                borderRadius: '8px'
              }}
            />
          </div>
        )}

        {previewBanner.content_type === "text_and_image" && (
          <>
            {previewBanner.image && (
              <div style={{ marginBottom: '1rem' }}>
                <img
                  src={previewBanner.image}
                  alt="Popup"
                  style={{
                    maxWidth: '100%',
                    maxHeight: '150px',
                    objectFit: 'contain',
                    borderRadius: '8px'
                  }}
                />
              </div>
            )}
            {previewBanner.text_content && (
              <div style={{
                color: '#666',
                marginBottom: '1.5rem',
                fontSize: '0.95rem',
                lineHeight: '1.5'
              }}>
                {previewBanner.text_content}
              </div>
            )}
          </>
        )}

        {/* Action buttons */}
        <div style={{ display: 'flex', gap: '0.75rem', justifyContent: 'center' }}>
          {previewBanner.link_url && (
            <button
              onClick={handleMainAction}
              style={{
                background: '#007bff',
                color: 'white',
                border: 'none',
                padding: '0.75rem 1.5rem',
                borderRadius: '8px',
                fontSize: '0.9rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'background-color 0.2s'
              }}
              onMouseOver={(e) => e.target.style.background = '#0056b3'}
              onMouseOut={(e) => e.target.style.background = '#007bff'}
            >
              {previewBanner.link_text || 'Learn More'}
            </button>
          )}
          <button
            onClick={() => setShowPreviewModal(false)}
            style={{
              background: '#6c757d',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '8px',
              fontSize: '0.9rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.target.style.background = '#545b62'}
            onMouseOut={(e) => e.target.style.background = '#6c757d'}
          >
            Close
          </button>
        </div>
      </div>
    );
  };

  // Get status badge variant
  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'approved_by_care': return 'success';
      case 'rejected_by_care': return 'danger';
      case 'approved_by_admin': return 'primary';
      case 'rejected_by_admin': return 'dark';
      default: return 'secondary';
    }
  };

  // Format status text
  const formatStatus = (status) => {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <>
      <style>{modalStyles}</style>
      <NavigationBar />
      <Container className="my-4">
        <Row>
          <Col>
            <h2 className="text-success mb-4">
              <FaEye className="me-2" />
              Popup Banner Approval Dashboard
            </h2>
          </Col>
        </Row>

        {/* Filters */}
        <Card className="mb-4">
          <Card.Header>
            <FaFilter className="me-2" />
            Filters
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Approval Status</Form.Label>
                  <Form.Select
                    name="approval_status"
                    value={filters.approval_status}
                    onChange={handleFilterChange}
                  >
                    <option value="">All Statuses</option>
                    <option value="pending">Pending</option>
                    <option value="approved_by_care">Approved by Care</option>
                    <option value="rejected_by_care">Rejected by Care</option>
                    <option value="approved_by_admin">Approved by Admin</option>
                    <option value="rejected_by_admin">Rejected by Admin</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Content Type</Form.Label>
                  <Form.Select
                    name="content_type"
                    value={filters.content_type}
                    onChange={handleFilterChange}
                  >
                    <option value="">All Types</option>
                    <option value="page_target">Page Target</option>
                    <option value="external_link">External Link</option>
                    <option value="modal_content">Modal Content</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Search</Form.Label>
                  <Form.Control
                    type="text"
                    name="search"
                    value={filters.search}
                    onChange={handleFilterChange}
                    placeholder="Search by title, creator..."
                  />
                </Form.Group>
              </Col>
              <Col md={2} className="d-flex align-items-end">
                <div className="d-flex gap-2 mb-3">
                  <Button variant="primary" onClick={applyFilters}>
                    Apply
                  </Button>
                  <Button variant="outline-secondary" onClick={clearFilters}>
                    Clear
                  </Button>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert variant="danger" dismissible onClose={() => dispatch(clearError())}>
            {typeof error === 'string' ? error : 'An error occurred'}
          </Alert>
        )}

        {/* Loading Spinner */}
        {isLoading && (
          <div className="text-center my-4">
            <Spinner animation="border" variant="primary" />
            <p className="mt-2">Loading banners...</p>
          </div>
        )}

        {/* Banners List */}
        <Row>
          {banners.length === 0 && !isLoading ? (
            <Col>
              <Alert variant="info" className="text-center">
                No banners found matching your criteria.
              </Alert>
            </Col>
          ) : (
            banners.map((banner) => (
              <Col md={6} lg={4} key={banner.id} className="mb-4">
                <Card className="h-100 shadow-sm">
                  <Card.Header className="d-flex justify-content-between align-items-center">
                    <Badge bg={getStatusBadge(banner.approval_status)}>
                      {formatStatus(banner.approval_status)}
                    </Badge>
                    <Badge bg={banner.is_active ? 'success' : 'secondary'}>
                      {banner.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </Card.Header>
                  <Card.Body>
                    <Card.Title className="h6">{banner.title}</Card.Title>
                    <Card.Text className="small text-muted mb-2">
                      {banner.description && (
                        <div className="mb-2">
                          <strong>Description:</strong> {banner.description}
                        </div>
                      )}
                      <strong>Type:</strong> {banner.content_type?.replace('_', ' ')}<br />
                      <strong>Priority:</strong> {banner.priority}<br />
                      <strong>Duration:</strong> {banner.display_duration || 'N/A'}ms<br />
                      <strong>Delay:</strong> {banner.delay_ms || 0}ms<br />
                      <strong>Target:</strong> {banner.page_target || 'All pages'}<br />
                      <strong>Created by:</strong> {banner.created_by_username}<br />
                      <strong>Created:</strong> {new Date(banner.created_at).toLocaleDateString()}
                    </Card.Text>

                    {/* Content Preview */}
                    {banner.content_type === 'text_only' && banner.text_content && (
                      <div className="mb-2">
                        <small className="text-muted">Text Content:</small>
                        <p className="small mb-0" style={{ maxHeight: '60px', overflow: 'hidden' }}>
                          {banner.text_content.length > 100
                            ? `${banner.text_content.substring(0, 100)}...`
                            : banner.text_content}
                        </p>
                      </div>
                    )}

                    {banner.content_type === 'image_only' && banner.image && (
                      <div className="mb-2">
                        <small className="text-muted">Image:</small>
                        <div className="mt-1">
                          <img
                            src={banner.image}
                            alt="Popup"
                            style={{ maxWidth: "100%", maxHeight: "80px", objectFit: "cover" }}
                            className="img-thumbnail"
                          />
                        </div>
                      </div>
                    )}

                    {banner.content_type === 'text_and_image' && (
                      <div className="mb-2">
                        {banner.image && (
                          <div className="mb-1">
                            <small className="text-muted">Image:</small>
                            <div className="mt-1">
                              <img
                                src={banner.image}
                                alt="Popup"
                                style={{ maxWidth: "100%", maxHeight: "60px", objectFit: "cover" }}
                                className="img-thumbnail"
                              />
                            </div>
                          </div>
                        )}
                        {banner.text_content && (
                          <div>
                            <small className="text-muted">Text:</small>
                            <p className="small mb-0" style={{ maxHeight: '40px', overflow: 'hidden' }}>
                              {banner.text_content.length > 80
                                ? `${banner.text_content.substring(0, 80)}...`
                                : banner.text_content}
                            </p>
                          </div>
                        )}
                      </div>
                    )}

                    {banner.link_url && (
                      <div className="mb-2">
                        <small className="text-muted">
                          <strong>Link:</strong> {banner.link_text || 'Learn More'} → {banner.link_url}
                        </small>
                      </div>
                    )}

                    {banner.rejection_reason && (
                      <Alert variant="danger" className="small">
                        <strong>Rejection Reason:</strong> {banner.rejection_reason}
                      </Alert>
                    )}
                  </Card.Body>
                  <Card.Footer>
                    <div className="d-flex gap-2 flex-wrap">
                      {banner.approval_status === 'pending' && (
                        <>
                          <Button
                            size="sm"
                            variant="success"
                            onClick={() => handleApprove(banner.id)}
                          >
                            <FaCheck className="me-1" />
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant="danger"
                            onClick={() => handleReject(banner)}
                          >
                            <FaTimes className="me-1" />
                            Reject
                          </Button>
                        </>
                      )}

                      {(banner.approval_status === 'approved_by_care' || banner.approval_status === 'approved_by_admin') && (
                        <Button
                          size="sm"
                          variant={banner.is_active ? 'warning' : 'success'}
                          onClick={() => handleToggleActive(banner.id, banner.is_active)}
                        >
                          {banner.is_active ? <FaToggleOff className="me-1" /> : <FaToggleOn className="me-1" />}
                          {banner.is_active ? 'Deactivate' : 'Activate'}
                        </Button>
                      )}

                      {/* Preview Button - Always available */}
                      <Button
                        size="sm"
                        variant="outline-info"
                        onClick={() => handlePreview(banner)}
                      >
                        <FaEye className="me-1" />
                        Preview
                      </Button>
                    </div>
                  </Card.Footer>
                </Card>
              </Col>
            ))
          )}
        </Row>

        {/* Rejection Modal */}
        <Modal show={showRejectModal} onHide={() => setShowRejectModal(false)}>
          <Modal.Header closeButton>
            <Modal.Title>Reject Banner</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p><strong>Banner:</strong> {selectedBanner?.title}</p>
            <Form.Group>
              <Form.Label>Rejection Reason *</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Please provide a reason for rejection..."
                required
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowRejectModal(false)}>
              Cancel
            </Button>
            <Button variant="danger" onClick={submitRejection}>
              Reject Banner
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Preview Modal */}
        <Modal
          show={showPreviewModal}
          onHide={() => setShowPreviewModal(false)}
          size="lg"
          centered
          backdrop="static"
          dialogClassName="popup-preview-modal"
        >
          <Modal.Header closeButton>
            <Modal.Title>Popup Preview - {previewBanner?.title}</Modal.Title>
          </Modal.Header>
          <Modal.Body style={{ padding: '2rem', background: '#f8f9fa', minHeight: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            {renderPopupPreview()}
          </Modal.Body>
          <Modal.Footer>
            <div className="w-100 d-flex justify-content-between align-items-center">
              <div className="text-muted small">
                <strong>Settings:</strong> Delay: {previewBanner?.delay_ms || 0}ms |
                Duration: {previewBanner?.display_duration || 'N/A'}ms |
                Target: {previewBanner?.page_target || 'All pages'}
              </div>
              <Button variant="secondary" onClick={() => setShowPreviewModal(false)}>
                Close Preview
              </Button>
            </div>
          </Modal.Footer>
        </Modal>
      </Container>
    </>
  );
};

export default PopupApprovalDashboard;
