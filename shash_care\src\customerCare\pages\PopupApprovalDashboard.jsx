import React, { useState, useEffect } from "react";
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button, Badge, <PERSON>, Modal, <PERSON><PERSON>, Spinner } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { 
  fetchPopupBanners, 
  approvePopupBanner, 
  rejectPopupBanner, 
  togglePopupBanner,
  clearError 
} from "../../redux/slice/popupApprovalSlice";
import NavigationBar from "../../commonComponents/NavigationBar";
import { toast } from "react-hot-toast";
import { FaCheck, FaTimes, FaEye, FaToggleOn, FaToggleOff, FaFilter } from "react-icons/fa";
import Swal from "sweetalert2";

const PopupApprovalDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { banners, isLoading, error } = useSelector((state) => state.popupApproval);
  const { access } = useSelector((state) => state.customerCare);

  // Local state
  const [filters, setFilters] = useState({
    approval_status: '',
    content_type: '',
    search: ''
  });
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [rejectionReason, setRejectionReason] = useState('');

  // Check authentication
  useEffect(() => {
    if (!access) {
      toast.error("Please login first");
      navigate("/customer_care_login");
      return;
    }
    fetchBanners();
  }, [access, navigate, dispatch]);

  // Fetch banners
  const fetchBanners = () => {
    dispatch(fetchPopupBanners(filters));
  };

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // Apply filters
  const applyFilters = () => {
    fetchBanners();
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({ approval_status: '', content_type: '', search: '' });
    dispatch(fetchPopupBanners({}));
  };

  // Handle approve
  const handleApprove = async (id) => {
    const result = await Swal.fire({
      title: 'Approve Banner?',
      text: 'This will approve the banner for display.',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#28a745',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Approve!'
    });

    if (result.isConfirmed) {
      try {
        await dispatch(approvePopupBanner(id)).unwrap();
        toast.success('Banner approved successfully!');
        fetchBanners();
      } catch (error) {
        toast.error('Failed to approve banner');
      }
    }
  };

  // Handle reject
  const handleReject = (banner) => {
    setSelectedBanner(banner);
    setShowRejectModal(true);
  };

  // Submit rejection
  const submitRejection = async () => {
    if (!rejectionReason.trim()) {
      toast.error('Please provide a rejection reason');
      return;
    }

    try {
      await dispatch(rejectPopupBanner({ 
        id: selectedBanner.id, 
        rejection_reason: rejectionReason 
      })).unwrap();
      toast.success('Banner rejected successfully!');
      setShowRejectModal(false);
      setRejectionReason('');
      setSelectedBanner(null);
      fetchBanners();
    } catch (error) {
      toast.error('Failed to reject banner');
    }
  };

  // Handle toggle active status
  const handleToggleActive = async (id, currentStatus) => {
    const action = currentStatus ? 'deactivate' : 'activate';
    const result = await Swal.fire({
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Banner?`,
      text: `This will ${action} the banner.`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: currentStatus ? '#dc3545' : '#28a745',
      cancelButtonColor: '#6c757d',
      confirmButtonText: `Yes, ${action.charAt(0).toUpperCase() + action.slice(1)}!`
    });

    if (result.isConfirmed) {
      try {
        await dispatch(togglePopupBanner({ id, is_active: !currentStatus })).unwrap();
        toast.success(`Banner ${action}d successfully!`);
        fetchBanners();
      } catch (error) {
        toast.error(`Failed to ${action} banner`);
      }
    }
  };

  // Get status badge variant
  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'approved_by_care': return 'success';
      case 'rejected_by_care': return 'danger';
      case 'approved_by_admin': return 'primary';
      case 'rejected_by_admin': return 'dark';
      default: return 'secondary';
    }
  };

  // Format status text
  const formatStatus = (status) => {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <>
      <NavigationBar />
      <Container className="my-4">
        <Row>
          <Col>
            <h2 className="text-success mb-4">
              <FaEye className="me-2" />
              Popup Banner Approval Dashboard
            </h2>
          </Col>
        </Row>

        {/* Filters */}
        <Card className="mb-4">
          <Card.Header>
            <FaFilter className="me-2" />
            Filters
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Approval Status</Form.Label>
                  <Form.Select
                    name="approval_status"
                    value={filters.approval_status}
                    onChange={handleFilterChange}
                  >
                    <option value="">All Statuses</option>
                    <option value="pending">Pending</option>
                    <option value="approved_by_care">Approved by Care</option>
                    <option value="rejected_by_care">Rejected by Care</option>
                    <option value="approved_by_admin">Approved by Admin</option>
                    <option value="rejected_by_admin">Rejected by Admin</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Content Type</Form.Label>
                  <Form.Select
                    name="content_type"
                    value={filters.content_type}
                    onChange={handleFilterChange}
                  >
                    <option value="">All Types</option>
                    <option value="page_target">Page Target</option>
                    <option value="external_link">External Link</option>
                    <option value="modal_content">Modal Content</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Search</Form.Label>
                  <Form.Control
                    type="text"
                    name="search"
                    value={filters.search}
                    onChange={handleFilterChange}
                    placeholder="Search by title, creator..."
                  />
                </Form.Group>
              </Col>
              <Col md={2} className="d-flex align-items-end">
                <div className="d-flex gap-2 mb-3">
                  <Button variant="primary" onClick={applyFilters}>
                    Apply
                  </Button>
                  <Button variant="outline-secondary" onClick={clearFilters}>
                    Clear
                  </Button>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert variant="danger" dismissible onClose={() => dispatch(clearError())}>
            {typeof error === 'string' ? error : 'An error occurred'}
          </Alert>
        )}

        {/* Loading Spinner */}
        {isLoading && (
          <div className="text-center my-4">
            <Spinner animation="border" variant="primary" />
            <p className="mt-2">Loading banners...</p>
          </div>
        )}

        {/* Banners List */}
        <Row>
          {banners.length === 0 && !isLoading ? (
            <Col>
              <Alert variant="info" className="text-center">
                No banners found matching your criteria.
              </Alert>
            </Col>
          ) : (
            banners.map((banner) => (
              <Col md={6} lg={4} key={banner.id} className="mb-4">
                <Card className="h-100 shadow-sm">
                  <Card.Header className="d-flex justify-content-between align-items-center">
                    <Badge bg={getStatusBadge(banner.approval_status)}>
                      {formatStatus(banner.approval_status)}
                    </Badge>
                    <Badge bg={banner.is_active ? 'success' : 'secondary'}>
                      {banner.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </Card.Header>
                  <Card.Body>
                    <Card.Title className="h6">{banner.title}</Card.Title>
                    <Card.Text className="small text-muted">
                      <strong>Type:</strong> {banner.content_type}<br />
                      <strong>Priority:</strong> {banner.priority}<br />
                      <strong>Created by:</strong> {banner.created_by_username}<br />
                      <strong>Created:</strong> {new Date(banner.created_at).toLocaleDateString()}
                    </Card.Text>
                    
                    {banner.rejection_reason && (
                      <Alert variant="danger" className="small">
                        <strong>Rejection Reason:</strong> {banner.rejection_reason}
                      </Alert>
                    )}
                  </Card.Body>
                  <Card.Footer>
                    <div className="d-flex gap-2 flex-wrap">
                      {banner.approval_status === 'pending' && (
                        <>
                          <Button
                            size="sm"
                            variant="success"
                            onClick={() => handleApprove(banner.id)}
                          >
                            <FaCheck className="me-1" />
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant="danger"
                            onClick={() => handleReject(banner)}
                          >
                            <FaTimes className="me-1" />
                            Reject
                          </Button>
                        </>
                      )}
                      
                      {(banner.approval_status === 'approved_by_care' || banner.approval_status === 'approved_by_admin') && (
                        <Button
                          size="sm"
                          variant={banner.is_active ? 'warning' : 'success'}
                          onClick={() => handleToggleActive(banner.id, banner.is_active)}
                        >
                          {banner.is_active ? <FaToggleOff className="me-1" /> : <FaToggleOn className="me-1" />}
                          {banner.is_active ? 'Deactivate' : 'Activate'}
                        </Button>
                      )}
                    </div>
                  </Card.Footer>
                </Card>
              </Col>
            ))
          )}
        </Row>

        {/* Rejection Modal */}
        <Modal show={showRejectModal} onHide={() => setShowRejectModal(false)}>
          <Modal.Header closeButton>
            <Modal.Title>Reject Banner</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p><strong>Banner:</strong> {selectedBanner?.title}</p>
            <Form.Group>
              <Form.Label>Rejection Reason *</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Please provide a reason for rejection..."
                required
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowRejectModal(false)}>
              Cancel
            </Button>
            <Button variant="danger" onClick={submitRejection}>
              Reject Banner
            </Button>
          </Modal.Footer>
        </Modal>
      </Container>
    </>
  );
};

export default PopupApprovalDashboard;
