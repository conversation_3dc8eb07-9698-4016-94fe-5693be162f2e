import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor; // Adjust this path to match your state structure
  return accessToken;
};

// Thunks

// Create a new previous year question
export const createPreviousYearQuestion = createAsyncThunk(
  'previousYearQuestion/createPreviousYearQuestion',
  async (questionData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // Retrieve the access token from Redux state
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PREVIOUS_YEAR_QUESTION}`, // API endpoint for creating a previous year question
        questionData, // Payload to be sent in the request body
        {
          headers: {
            Authorization: `Bearer ${token}`, // Add the token to the Authorization header
          },
        }
      );
      return response.data; // Return the response data upon success
    } catch (error) {
      // Handle API errors and return a custom error message
      return rejectWithValue(error.response?.data || 'Error creating previous year question');
    }
  }
);

// Fetch all previous year questions
export const getAllPreviousYearQuestions = createAsyncThunk(
  'previousYearQuestion/getAllPreviousYearQuestions',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // Retrieve the access token from Redux state
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PREVIOUS_YEAR_QUESTION}`, // API endpoint to fetch all previous year questions
        {
          headers: {
            Authorization: `Bearer ${token}`, // Add the token to the Authorization header
          },
        }
      );
      return response.data; // Return the response data upon success
    } catch (error) {
      // Handle API errors and return a custom error message
      return rejectWithValue(error.response?.data || 'Error fetching previous year questions');
    }
  }
);

// Fetch a specific previous year question by slug
export const getPreviousYearQuestion = createAsyncThunk(
  'previousYearQuestion/getPreviousYearQuestion',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // Retrieve the access token from Redux state
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PREVIOUS_YEAR_QUESTION}${slug}/`, // API endpoint with the question slug
        {
          headers: {
            Authorization: `Bearer ${token}`, // Add the token to the Authorization header
          },
        }
      );
      return response.data; // Return the response data upon success
    } catch (error) {
      // Handle API errors and return a custom error message
      return rejectWithValue(error.response?.data || 'Error fetching previous year question');
    }
  }
);

// Update a specific previous year question by slug
export const updatePreviousYearQuestion = createAsyncThunk(
  'previousYearQuestion/updatePreviousYearQuestion',
  async ({ slug, updatedData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // Retrieve the access token from Redux state
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PREVIOUS_YEAR_QUESTION}${slug}/`, // API endpoint with the question slug
        updatedData, // Updated data to be sent in the request body
        {
          headers: {
            Authorization: `Bearer ${token}`, // Add the token to the Authorization header
          },
        }
      );
      return response.data; // Return the response data upon success
    } catch (error) {
      // Handle API errors and return a custom error message
      return rejectWithValue(error.response?.data || 'Error updating previous year question');
    }
  }
);

// Delete a specific previous year question by slug
export const deletePreviousYearQuestion = createAsyncThunk(
  'previousYearQuestion/deletePreviousYearQuestion',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // Retrieve the access token from Redux state
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PREVIOUS_YEAR_QUESTION}${slug}/`, // API endpoint with the question slug
        {
          headers: {
            Authorization: `Bearer ${token}`, // Add the token to the Authorization header
          },
        }
      );
      return slug; // Return the slug of the deleted question
    } catch (error) {
      // Handle API errors and return a custom error message
      return rejectWithValue(error.response?.data || 'Error deleting previous year question');
    }
  }
);

// Slice
const previousYearQuestionSlice = createSlice({
  name: 'previousYearQuestion', // Slice name
  initialState: {
    isLoading: false, // Loading state
    error: null, // Error state
  },
  reducers: {}, // Reducers can be added here if needed
  extraReducers: (builder) => {
    // Handle createPreviousYearQuestion actions
    builder
      .addCase(createPreviousYearQuestion.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPreviousYearQuestion.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(createPreviousYearQuestion.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload; // Store the error message
      })

      // Handle getAllPreviousYearQuestions actions
      .addCase(getAllPreviousYearQuestions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getAllPreviousYearQuestions.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(getAllPreviousYearQuestions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload; // Store the error message
      })

      // Handle getPreviousYearQuestion actions
      .addCase(getPreviousYearQuestion.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getPreviousYearQuestion.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(getPreviousYearQuestion.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload; // Store the error message
      })

      // Handle updatePreviousYearQuestion actions
      .addCase(updatePreviousYearQuestion.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updatePreviousYearQuestion.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(updatePreviousYearQuestion.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload; // Store the error message
      })

      // Handle deletePreviousYearQuestion actions
      .addCase(deletePreviousYearQuestion.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deletePreviousYearQuestion.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(deletePreviousYearQuestion.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload; // Store the error message
      });
  },
});

export default previousYearQuestionSlice.reducer;
