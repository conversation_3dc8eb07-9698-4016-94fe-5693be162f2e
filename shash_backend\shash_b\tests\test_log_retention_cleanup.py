#!/usr/bin/env python3
"""
Comprehensive test script for log retention and cleanup functionality
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status

from log_admin.models import (
    LogConfig, PerformanceLog, ErrorLog, UserActivity,
    APIAccessLog, DatabaseQueryLog, AuthenticationLog,
    SecurityIncident, SystemHealthLog
)


class TestResults:
    """Class to track test results"""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"✅ PASS: {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"❌ FAIL: {test_name} - {error}")
    
    def print_summary(self):
        total = self.passed + self.failed
        print(f"\n{'='*60}")
        print(f"LOG RETENTION AND CLEANUP TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success Rate: {(self.passed/total*100):.1f}%" if total > 0 else "No tests run")
        
        if self.errors:
            print(f"\nFAILED TESTS:")
            for error in self.errors:
                print(f"  - {error}")


def test_enhanced_log_config_model():
    """Test the enhanced LogConfig model with retention policies"""
    results = TestResults()

    try:
        # Create admin user with unique username
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        admin_user = User.objects.create_superuser(
            username=f'retention_admin_{unique_id}',
            email=f'retention_admin_{unique_id}@example.com',
            password='adminpass123'
        )
        
        # Test creating enhanced log configuration
        config = LogConfig.objects.create(
            name="Test Retention Configuration",
            description="Test configuration for retention policies",
            is_active=True,
            level="INFO",
            log_retention_days=30,
            error_retention_days=90,
            security_retention_days=180,
            enable_email_alerts=True,
            alert_email_addresses="<EMAIL>, <EMAIL>",
            critical_error_threshold=5,
            alert_cooldown_minutes=60,
            created_by=admin_user,
            updated_by=admin_user
        )
        
        results.add_pass("Enhanced LogConfig model creation")
        
        # Test get_active_config method
        active_config = LogConfig.get_active_config()
        if active_config.id == config.id:
            results.add_pass("LogConfig get_active_config method")
        else:
            results.add_fail("LogConfig get_active_config method", "Wrong active config returned")
        
        # Test alert email parsing
        emails = config.get_alert_emails()
        if len(emails) == 2 and "<EMAIL>" in emails:
            results.add_pass("LogConfig alert email parsing")
        else:
            results.add_fail("LogConfig alert email parsing", f"Expected 2 emails, got {len(emails)}")
        
        # Test retention days for different types
        general_retention = config.get_retention_days_for_type('general')
        error_retention = config.get_retention_days_for_type('error')
        security_retention = config.get_retention_days_for_type('security')
        
        if general_retention == 30 and error_retention == 90 and security_retention == 180:
            results.add_pass("LogConfig retention days by type")
        else:
            results.add_fail("LogConfig retention days by type", f"Wrong retention values: {general_retention}, {error_retention}, {security_retention}")
        
        # Test should_log_level method
        if config.should_log_level('ERROR') and not config.should_log_level('DEBUG'):
            results.add_pass("LogConfig should_log_level method")
        else:
            results.add_fail("LogConfig should_log_level method", "Level filtering not working correctly")
        
    except Exception as e:
        results.add_fail("Enhanced LogConfig model tests", str(e))
    
    return results


def test_log_cleanup_api():
    """Test the enhanced log cleanup API endpoints"""
    results = TestResults()
    
    try:
        # Create admin user with unique username
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        admin_user = User.objects.create_superuser(
            username=f'cleanup_admin_{unique_id}',
            email=f'cleanup_admin_{unique_id}@example.com',
            password='adminpass123'
        )
        
        client = APIClient()
        client.force_authenticate(user=admin_user)
        
        # Create test log entries with different ages
        now = timezone.now()
        old_date = now - timedelta(days=35)  # Older than default retention (30 days)
        recent_date = now - timedelta(days=15)  # Within retention period
        
        # Create old logs that should be cleaned up
        PerformanceLog.objects.create(
            path='/test/old',
            method='GET',
            duration=1.5,
            status_code=200,
            user=admin_user,
            created_at=old_date
        )
        
        ErrorLog.objects.create(
            view_name='test_view',
            error_type='BUSINESS_LOGIC',
            error_message='Old test error',
            severity='MEDIUM',
            user=admin_user,
            timestamp=old_date
        )
        
        # Create recent logs that should be kept
        PerformanceLog.objects.create(
            path='/test/recent',
            method='GET',
            duration=0.5,
            status_code=200,
            user=admin_user,
            created_at=recent_date
        )
        
        # Test cleanup preview (GET request)
        response = client.get('/api/log-admin/cleanup/')
        
        if response.status_code == 200:
            results.add_pass("Log cleanup preview API")
            
            data = response.json()
            if 'cleanup_preview' in data and 'total_to_delete' in data:
                results.add_pass("Cleanup preview response format")
                
                # Check if old logs are identified for cleanup
                if data['total_to_delete'] > 0:
                    results.add_pass("Cleanup preview identifies old logs")
                else:
                    results.add_fail("Cleanup preview identifies old logs", "No logs identified for cleanup")
            else:
                results.add_fail("Cleanup preview response format", f"Missing fields in response: {data}")
        else:
            results.add_fail("Log cleanup preview API", f"Status: {response.status_code}")
        
        # Test dry run cleanup
        response = client.post('/api/log-admin/cleanup/', {
            'dry_run': True
        })
        
        if response.status_code == 200:
            results.add_pass("Dry run cleanup API")
            
            data = response.json()
            if data.get('cleanup_results', {}).get('dry_run') == True:
                results.add_pass("Dry run flag verification")
            else:
                results.add_fail("Dry run flag verification", "Dry run flag not set correctly")
        else:
            results.add_fail("Dry run cleanup API", f"Status: {response.status_code}")
        
        # Test actual cleanup
        response = client.post('/api/log-admin/cleanup/', {
            'dry_run': False,
            'archive': False
        })
        
        if response.status_code == 200:
            results.add_pass("Actual cleanup API")
            
            data = response.json()
            if data.get('total_deleted', 0) > 0:
                results.add_pass("Cleanup deletes old logs")
            else:
                results.add_fail("Cleanup deletes old logs", "No logs were deleted")
        else:
            results.add_fail("Actual cleanup API", f"Status: {response.status_code}")
        
    except Exception as e:
        results.add_fail("Log cleanup API tests", str(e))
    
    return results


def test_retention_policy_api():
    """Test the retention policy API endpoint"""
    results = TestResults()
    
    try:
        # Create admin user with unique username
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        admin_user = User.objects.create_superuser(
            username=f'retention_policy_admin_{unique_id}',
            email=f'retention_policy_admin_{unique_id}@example.com',
            password='adminpass123'
        )
        
        client = APIClient()
        client.force_authenticate(user=admin_user)
        
        # Create some test logs with different ages
        now = timezone.now()
        very_old_date = now - timedelta(days=400)  # Very old (should trigger cleanup recommendation)
        old_date = now - timedelta(days=35)        # Old (older than 30 day retention)
        recent_date = now - timedelta(days=10)     # Recent
        
        # Create logs of different ages
        for i, date in enumerate([very_old_date, old_date, recent_date]):
            PerformanceLog.objects.create(
                path=f'/test/{i}',
                method='GET',
                duration=1.0,
                status_code=200,
                user=admin_user,
                created_at=date
            )
        
        # Test retention policy endpoint
        response = client.get('/api/log-admin/retention/')
        
        if response.status_code == 200:
            results.add_pass("Retention policy API endpoint")
            
            data = response.json()
            expected_fields = ['config_name', 'retention_policies', 'log_age_statistics', 'recommendations']
            missing_fields = [field for field in expected_fields if field not in data]
            
            if not missing_fields:
                results.add_pass("Retention policy response format")
            else:
                results.add_fail("Retention policy response format", f"Missing fields: {missing_fields}")
            
            # Check if recommendations are generated
            recommendations = data.get('recommendations', [])
            if isinstance(recommendations, list):
                results.add_pass("Retention policy recommendations")
                
                # Check if cleanup recommendation is generated for very old logs
                cleanup_recommendations = [r for r in recommendations if r.get('type') == 'cleanup_needed']
                if cleanup_recommendations:
                    results.add_pass("Cleanup recommendations for old logs")
                else:
                    results.add_fail("Cleanup recommendations for old logs", "No cleanup recommendations generated")
            else:
                results.add_fail("Retention policy recommendations", "Recommendations not in correct format")
            
            # Check log age statistics
            log_stats = data.get('log_age_statistics', {})
            if 'performance_logs' in log_stats:
                perf_stats = log_stats['performance_logs']
                if perf_stats.get('total_count', 0) >= 3:  # We created 3 logs
                    results.add_pass("Log age statistics calculation")
                else:
                    results.add_fail("Log age statistics calculation", f"Expected at least 3 logs, got {perf_stats.get('total_count', 0)}")
            else:
                results.add_fail("Log age statistics calculation", "Performance logs statistics missing")
        else:
            results.add_fail("Retention policy API endpoint", f"Status: {response.status_code}")
        
    except Exception as e:
        results.add_fail("Retention policy API tests", str(e))
    
    return results


def test_management_command():
    """Test the cleanup management command functionality"""
    results = TestResults()
    
    try:
        from django.core.management import call_command
        from io import StringIO
        
        # Create test logs with unique username
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        admin_user = User.objects.create_superuser(
            username=f'command_admin_{unique_id}',
            email=f'command_admin_{unique_id}@example.com',
            password='adminpass123'
        )
        
        # Create old log that should be cleaned up
        old_date = timezone.now() - timedelta(days=35)  # Older than 30 day retention
        PerformanceLog.objects.create(
            path='/test/command',
            method='GET',
            duration=1.0,
            status_code=200,
            user=admin_user,
            created_at=old_date
        )
        
        # Test dry run command
        out = StringIO()
        call_command('cleanup_logs', '--dry-run', '--force', stdout=out)
        output = out.getvalue()
        
        if 'DRY RUN' in output and 'CLEANUP PREVIEW' in output:
            results.add_pass("Management command dry run")
        else:
            results.add_fail("Management command dry run", "Expected dry run output not found")
        
        # Test verbose output
        out = StringIO()
        call_command('cleanup_logs', '--dry-run', '--verbose', '--force', stdout=out)
        output = out.getvalue()
        
        if 'CLEANUP PREVIEW' in output:
            results.add_pass("Management command verbose output")
        else:
            results.add_fail("Management command verbose output", "Expected verbose output not found")
        
        results.add_pass("Management command functionality")
        
    except Exception as e:
        results.add_fail("Management command tests", str(e))
    
    return results


def main():
    """Run all log retention and cleanup tests"""
    print("🚀 Starting Log Retention and Cleanup Tests")
    print("="*60)
    
    all_results = TestResults()
    
    # Run all test functions
    test_functions = [
        test_enhanced_log_config_model,
        test_log_cleanup_api,
        test_retention_policy_api,
        test_management_command,
    ]
    
    for test_func in test_functions:
        print(f"\n📋 Running {test_func.__name__}...")
        try:
            result = test_func()
            all_results.passed += result.passed
            all_results.failed += result.failed
            all_results.errors.extend(result.errors)
        except Exception as e:
            all_results.add_fail(test_func.__name__, f"Test function failed: {str(e)}")
            print(f"💥 Error in {test_func.__name__}: {str(e)}")
    
    # Print final summary
    all_results.print_summary()
    
    # Return exit code
    return 0 if all_results.failed == 0 else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
