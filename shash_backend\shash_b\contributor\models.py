from django.contrib.auth.models import User
from django.db import models
from django.utils.text import slugify
from django.utils import timezone
from django.core.exceptions import ValidationError
from questions.utils import generate_unique_slug
from decimal import Decimal

class ContributorProfile(models.Model):
    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="contributor_profile"
    )
    role = models.CharField(max_length=30, default="contributor")
    security_question = models.CharField(max_length=255, blank=True, null=True)
    security_answer = models.CharField(max_length=255, blank=True, null=True)
    slug = models.SlugField(max_length=255, unique=True, blank=True)

    # Link to custom point configuration (optional - falls back to default if None)
    custom_points = models.ForeignKey(
        'ContributorPoints',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_contributors',
        help_text="Custom point configuration for this contributor. If not set, uses default points."
    )

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(f"{self.user.username}-{self.role}")
        super().save(*args, **kwargs)

    def get_points_config(self):
        """Get the points configuration for this contributor (custom or default)"""
        if self.custom_points:
            return self.custom_points
        # Return the latest/default points configuration
        return ContributorPoints.objects.last()

    def __str__(self):
        return f"{self.user.username} - {self.role}"


class ContributorPoints(models.Model):
    name = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True, blank=True)

    normal_questions = models.PositiveIntegerField(
        default=0, help_text="Points for normal questions"
    )
    master_questions = models.PositiveIntegerField(
        default=0, help_text="Points for master questions"
    )
    master_options = models.PositiveIntegerField(
        default=0, help_text="Points for creating master options"
    )
    blogs = models.PositiveIntegerField(default=0, help_text="Points for writing blogs")
    previous_questions = models.PositiveIntegerField(
        default=0, help_text="Points for answering previous questions"
    )

    last_updated = models.DateTimeField(
        auto_now=True, help_text="Last updated timestamp"
    )
    created_at = models.DateTimeField(auto_now_add=True, help_text="Created timestamp")

    class Meta:
        verbose_name = "Contributor Point"
        verbose_name_plural = "Contributor Points"
        ordering = ["-last_updated"]

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name}"


class ContributorEarning(models.Model):
    """Model to track individual contributor earnings"""

    contributor = models.ForeignKey(
        ContributorProfile,
        on_delete=models.CASCADE,
        related_name='earnings',
        help_text="The contributor who earned these points"
    )

    # Activity tracking
    normal_questions_count = models.PositiveIntegerField(
        default=0, help_text="Number of normal questions created"
    )
    master_questions_count = models.PositiveIntegerField(
        default=0, help_text="Number of master questions created"
    )
    master_options_count = models.PositiveIntegerField(
        default=0, help_text="Number of master options created"
    )
    blogs_count = models.PositiveIntegerField(
        default=0, help_text="Number of blogs written"
    )
    previous_questions_count = models.PositiveIntegerField(
        default=0, help_text="Number of previous questions answered"
    )

    # Points earned
    normal_questions_points = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        help_text="Points earned from normal questions"
    )
    master_questions_points = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        help_text="Points earned from master questions"
    )
    master_options_points = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        help_text="Points earned from master options"
    )
    blogs_points = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        help_text="Points earned from blogs"
    )
    previous_questions_points = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        help_text="Points earned from previous questions"
    )

    # Total earnings
    total_points = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        help_text="Total points earned"
    )
    total_earnings = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        help_text="Total earnings in currency (if applicable)"
    )

    # Period tracking
    period_type = models.CharField(
        max_length=20,
        choices=[
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly'),
            ('yearly', 'Yearly'),
            ('lifetime', 'Lifetime'),
        ],
        default='monthly',
        help_text="The period this earning record covers"
    )
    period_start = models.DateTimeField(help_text="Start of the earning period")
    period_end = models.DateTimeField(help_text="End of the earning period")

    # Metadata
    points_config_used = models.ForeignKey(
        ContributorPoints,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="The points configuration used for this calculation"
    )
    is_paid = models.BooleanField(
        default=False,
        help_text="Whether this earning has been paid out"
    )
    paid_at = models.DateTimeField(
        null=True, blank=True,
        help_text="When this earning was paid out"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Contributor Earning"
        verbose_name_plural = "Contributor Earnings"
        ordering = ["-period_start", "-created_at"]
        unique_together = ['contributor', 'period_type', 'period_start', 'period_end']
        indexes = [
            models.Index(fields=['contributor', 'period_type']),
            models.Index(fields=['period_start', 'period_end']),
            models.Index(fields=['is_paid']),
        ]

    def calculate_total_points(self):
        """Calculate and update total points"""
        self.total_points = (
            self.normal_questions_points +
            self.master_questions_points +
            self.master_options_points +
            self.blogs_points +
            self.previous_questions_points
        )
        return self.total_points

    def save(self, *args, **kwargs):
        # Auto-calculate total points before saving
        self.calculate_total_points()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.contributor.user.username} - {self.period_type} - {self.total_points} points"


class Banner(models.Model):
    banner_image = models.ImageField(upload_to="banner/")
    banner_name = models.CharField(max_length=255) 
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(Banner,self.banner_name)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.banner_name}" 

class PageCounter(models.Model):
    url = models.CharField(max_length=255)
    count = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    def __str__(self):
        return f"{self.url}: {self.count}"


class PopupBanner(models.Model):
    """
    PopupBanner model for managing popup banners with different content types
    and multi-level approval workflow (contributor -> customer care -> admin)
    """

    # Content type choices
    CONTENT_TYPE_CHOICES = [
        ('text_only', 'Text Only'),
        ('image_only', 'Image Only'),
        ('text_image', 'Text + Image'),
        ('page_target', 'Page Target'),
        ('text_link', 'Text + Link'),
        ('link_anchor', 'Link + Anchor Tag'),
    ]

    # Approval status choices
    APPROVAL_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved_by_care', 'Approved by Customer Care'),
        ('approved_by_admin', 'Approved by Admin'),
        ('rejected_by_care', 'Rejected by Customer Care'),
        ('rejected_by_admin', 'Rejected by Admin'),
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]

    # Priority choices
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    # Basic Information
    title = models.CharField(max_length=255, help_text="Banner title")
    description = models.TextField(blank=True, null=True, help_text="Banner description")
    content_type = models.CharField(
        max_length=20,
        choices=CONTENT_TYPE_CHOICES,
        help_text="Type of content in the banner"
    )

    # Content Fields
    image = models.ImageField(
        upload_to="popup_banners/images/",
        blank=True,
        null=True,
        help_text="Banner image (required for image-based content types)"
    )
    text_content = models.TextField(
        blank=True,
        null=True,
        help_text="Text content for the banner"
    )
    link_url = models.URLField(
        blank=True,
        null=True,
        help_text="URL for link-based content types"
    )
    link_text = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Display text for the link"
    )
    anchor_tag = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="HTML anchor tag attributes"
    )

    # Page Target (for page_target content type)
    page_target = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Target page where banner should be displayed (for page_target content type)"
    )

    # Display Settings
    display_duration = models.PositiveIntegerField(
        default=5000,
        help_text="Duration to display banner in milliseconds"
    )
    delay_ms = models.PositiveIntegerField(
        default=0,
        help_text="Delay before showing popup in milliseconds (default: 0ms = immediate)"
    )
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='medium',
        help_text="Banner display priority"
    )
    is_active = models.BooleanField(
        default=False,
        help_text="Whether the banner is currently active"
    )

    # Approval Workflow
    approval_status = models.CharField(
        max_length=20,
        choices=APPROVAL_STATUS_CHOICES,
        default='pending',
        help_text="Current approval status"
    )

    # User Tracking
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='created_popup_banners',
        help_text="Contributor who created the banner"
    )
    approved_by_care = models.ForeignKey(
        'customrcare.CustomrcareProfile',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_popup_banners',
        help_text="Customer care user who approved the banner"
    )
    approved_by_admin = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='admin_approved_popup_banners',
        help_text="Admin user who gave final approval"
    )

    # Rejection Tracking
    rejection_reason = models.TextField(
        blank=True,
        null=True,
        help_text="Reason for rejection"
    )
    rejected_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='rejected_popup_banners',
        help_text="User who rejected the banner"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    activated_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    slug = models.SlugField(max_length=255, unique=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Popup Banner"
        verbose_name_plural = "Popup Banners"
        indexes = [
            models.Index(fields=['approval_status']),
            models.Index(fields=['is_active']),
            models.Index(fields=['created_by']),
            models.Index(fields=['content_type']),
        ]

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(PopupBanner, self.title)
        super().save(*args, **kwargs)

    def clean(self):
        """Validate content based on content type"""
        super().clean()

        # Text only validation
        if self.content_type == 'text_only':
            if not self.text_content:
                raise ValidationError(f"Text content is required for content type '{self.get_content_type_display()}'")

        # Image validation
        if self.content_type in ['image_only', 'text_image']:
            if not self.image:
                raise ValidationError(f"Image is required for content type '{self.get_content_type_display()}'")

        # Text validation for combined types
        if self.content_type in ['text_image', 'text_link']:
            if not self.text_content:
                raise ValidationError(f"Text content is required for content type '{self.get_content_type_display()}'")

        # Page target validation
        if self.content_type == 'page_target':
            if not self.page_target:
                raise ValidationError("Page target is required for 'Page Target' content type")

        # Link validation
        if self.content_type in ['text_link', 'link_anchor']:
            if not self.link_url:
                raise ValidationError(f"Link URL is required for content type '{self.get_content_type_display()}'")
            if not self.link_text:
                raise ValidationError(f"Link text is required for content type '{self.get_content_type_display()}'")

        # Anchor tag validation
        if self.content_type == 'link_anchor':
            if not self.anchor_tag:
                raise ValidationError("Anchor tag is required for 'Link + Anchor Tag' content type")

    def can_be_approved_by_care(self):
        """Check if banner can be approved by customer care"""
        return self.approval_status == 'pending'

    def can_be_approved_by_admin(self):
        """Check if banner can be approved by admin"""
        return self.approval_status in ['pending', 'approved_by_care', 'rejected_by_care']

    def can_be_activated(self):
        """Check if banner can be activated"""
        return self.approval_status in ['approved_by_care', 'approved_by_admin']

    def approve_by_care(self, care_user):
        """Approve banner by customer care"""
        if self.can_be_approved_by_care():
            self.approval_status = 'approved_by_care'
            self.approved_by_care = care_user
            self.approved_at = timezone.now()
            self.rejection_reason = None
            self.rejected_by = None
            return True
        return False

    def approve_by_admin(self, admin_user):
        """Approve banner by admin (final approval)"""
        if self.can_be_approved_by_admin():
            self.approval_status = 'approved_by_admin'
            self.approved_by_admin = admin_user
            self.approved_at = timezone.now()
            self.rejection_reason = None
            self.rejected_by = None
            return True
        return False

    def reject(self, user, reason):
        """Reject banner with reason"""
        if hasattr(user, 'customrcare_profile'):
            self.approval_status = 'rejected_by_care'
        elif user.is_staff or user.is_superuser:
            self.approval_status = 'rejected_by_admin'

        self.rejection_reason = reason
        self.rejected_by = user
        self.is_active = False
        return True

    def activate(self):
        """Activate the banner"""
        if self.can_be_activated():
            self.is_active = True
            self.activated_at = timezone.now()
            return True
        return False

    def deactivate(self):
        """Deactivate the banner"""
        self.is_active = False
        return True

    def __str__(self):
        return f"{self.title} - {self.get_approval_status_display()}"