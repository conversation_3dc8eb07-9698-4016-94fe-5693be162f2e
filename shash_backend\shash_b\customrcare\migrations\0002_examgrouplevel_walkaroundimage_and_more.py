# Generated by Django 5.1.1 on 2025-07-23 09:36

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customrcare', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ExamGroupLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('difficulty_level', models.IntegerField(default=1)),
                ('passing_score', models.FloatField(default=60.0)),
            ],
        ),
        migrations.CreateModel(
            name='WalkAroundImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='walk_around_images/')),
                ('title', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive')], default='active', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AlterModelOptions(
            name='frontenderror',
            options={'verbose_name': 'Frontend Error', 'verbose_name_plural': 'Frontend Errors'},
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='browser_name',
            field=models.CharField(choices=[('CHROME', 'Chrome'), ('FIREFOX', 'Firefox'), ('SAFARI', 'Safari'), ('EDGE', 'Edge'), ('OPERA', 'Opera'), ('OTHER', 'Other')], default='OTHER', max_length=20),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='browser_version',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='column_number',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='component_name',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='console_logs',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='device_type',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='error_message',
            field=models.TextField(default=''),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='error_type',
            field=models.CharField(choices=[('JAVASCRIPT', 'JavaScript Error'), ('NETWORK', 'Network Error'), ('VALIDATION', 'Validation Error'), ('AUTHENTICATION', 'Authentication Error'), ('PERMISSION', 'Permission Error'), ('TIMEOUT', 'Timeout Error'), ('RESOURCE_LOAD', 'Resource Load Error'), ('API_ERROR', 'API Error'), ('RENDER_ERROR', 'Render Error'), ('USER_ACTION', 'User Action Error'), ('UNKNOWN', 'Unknown Error')], default='UNKNOWN', max_length=20),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='first_occurrence',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='function_name',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='last_occurrence',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='line_number',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='occurrence_count',
            field=models.IntegerField(default=1),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='page_title',
            field=models.CharField(blank=True, max_length=500),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='page_url',
            field=models.URLField(default='', max_length=1000),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='referrer_url',
            field=models.URLField(blank=True, max_length=1000),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='resolution_notes',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='resolved',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='resolved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='resolved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_frontend_errors', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='screen_resolution',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='session_id',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='severity',
            field=models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], default='MEDIUM', max_length=10),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='stack_trace',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='user_actions',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='frontenderror',
            name='user_agent',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='frontenderror',
            name='error_data',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AddIndex(
            model_name='frontenderror',
            index=models.Index(fields=['error_type', 'created_at'], name='customrcare_error_t_cafbd9_idx'),
        ),
        migrations.AddIndex(
            model_name='frontenderror',
            index=models.Index(fields=['severity', 'created_at'], name='customrcare_severit_101f51_idx'),
        ),
        migrations.AddIndex(
            model_name='frontenderror',
            index=models.Index(fields=['user', 'created_at'], name='customrcare_user_id_4b35f5_idx'),
        ),
        migrations.AddIndex(
            model_name='frontenderror',
            index=models.Index(fields=['page_url', 'created_at'], name='customrcare_page_ur_23cfd2_idx'),
        ),
        migrations.AddIndex(
            model_name='frontenderror',
            index=models.Index(fields=['resolved', 'created_at'], name='customrcare_resolve_0352b6_idx'),
        ),
        migrations.AddField(
            model_name='walkaroundimage',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='walk_around_images', to=settings.AUTH_USER_MODEL),
        ),
    ]
