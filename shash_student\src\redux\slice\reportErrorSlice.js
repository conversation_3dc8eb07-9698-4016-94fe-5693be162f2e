import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Define the async thunk for reporting errors
export const reportError = createAsyncThunk(
  "errorReport/report",
  async (pageData, { rejectWithValue }) => {
    try {
      const response = await axios.post( `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_REPORT_ERROR}`, pageData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error reporting error");
    }
  }
);

const reportErrorSlice = createSlice({
  name: "errorReport",
  initialState: {
    loading: false,
    error: null,
    success: false,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(reportError.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(reportError.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(reportError.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default reportErrorSlice.reducer;
