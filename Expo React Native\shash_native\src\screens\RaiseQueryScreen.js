import React, { useState, useRef, useContext, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
  Image,
  Platform,
  Alert,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { useDispatch, useSelector } from 'react-redux';
import { createTicket, getCustomerCareList } from '../redux/ticketSlice';
import { Picker } from '@react-native-picker/picker';
import { ThemeContext } from '../context/ThemeContext';
import * as Icons from '@expo/vector-icons';
import Toast from 'react-native-toast-message';
import BottomTabBar from '../components/BottomTabBar';

const RaiseQueryScreen = ({ navigation }) => {
  const [customercare, setCustomercare] = useState('');
  const [subject, setSubject] = useState('');
  const [description, setDescription] = useState('');
  const [attachment, setAttachment] = useState(null);
  const [tags, setTags] = useState('');
  const [loading, setLoading] = useState(false);
  const [preview, setPreview] = useState(null);
  const [customercareslist, setCustomercareslist] = useState([]);
  const { isDarkMode } = useContext(ThemeContext);

  const dispatch = useDispatch();
  const student = useSelector((state) => state?.auth?.user);

  useEffect(() => {
    const loadCustomerCares = async () => {
      try {
        const res = await dispatch(getCustomerCareList()).unwrap();
        setCustomercareslist(res);
      } catch (err) {
        console.error('Failed to load customer care list:', err);
      }
    };
    loadCustomerCares();
  }, []);

  const handleImagePick = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant camera roll permissions to upload images.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.5,
        maxWidth: 300,
        maxHeight: 300,
      });

      if (!result.canceled) {
        const selectedAsset = result.assets[0];
        setAttachment(selectedAsset);
        setPreview(selectedAsset.uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to pick image',
      });
    }
  };

  const handleSubmit = async () => {
    if (!subject || !description || !customercare || !tags) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please fill all required fields',
      });
      return;
    }

    const ticketData = {
      customer_id: Number(customercare),
      student_id: student?.id,
      ticket_status: 'open',
      priority: 'high',
      subject: subject,
      description: description,
      resolve_summary: '',
      tags: tags,
      ...(attachment && { attachments: attachment }),
    };

    try {
      setLoading(true);
      const res = await dispatch(createTicket({ data: ticketData })).unwrap();
      if (res) {
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'Query sent successfully!',
        });
        navigation.goBack();
      }
    } catch (err) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to send query',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <BottomTabBar>
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={[styles.formContainer, isDarkMode && styles.formContainerDark]}>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, isDarkMode && styles.labelDark]}>Customer Care *</Text>
              <View style={[styles.pickerWrapper, isDarkMode && styles.inputDark]}>
                <Picker
                  selectedValue={customercare}
                  onValueChange={(value) => setCustomercare(value)}
                  dropdownIconColor={isDarkMode ? '#fff' : '#000'}
                  style={{ color: isDarkMode ? '#fff' : '#000' }}
                >
                  <Picker.Item label="Select Customer Care" value="" />
                  {customercareslist.map((cc) => (
                    <Picker.Item
                      key={cc.id}
                      label={`${cc.user.first_name} ${cc.user.last_name}`}
                      value={cc.id}
                    />
                  ))}
                </Picker>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, isDarkMode && styles.labelDark]}>Subject *</Text>
              <TextInput
                style={[styles.input, isDarkMode && styles.inputDark]}
                placeholder="Enter subject"
                placeholderTextColor={isDarkMode ? '#999' : '#666'}
                value={subject}
                onChangeText={setSubject}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, isDarkMode && styles.labelDark]}>Description *</Text>
              <TextInput
                style={[styles.textArea, isDarkMode && styles.inputDark]}
                placeholder="Enter description"
                placeholderTextColor={isDarkMode ? '#999' : '#666'}
                value={description}
                onChangeText={setDescription}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, isDarkMode && styles.labelDark]}>Tags *</Text>
              <TextInput
                style={[styles.input, isDarkMode && styles.inputDark]}
                placeholder="Enter tags (comma separated)"
                placeholderTextColor={isDarkMode ? '#999' : '#666'}
                value={tags}
                onChangeText={setTags}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, isDarkMode && styles.labelDark]}>Attachment (Optional)</Text>
              <TouchableOpacity 
                style={[styles.uploadButton, isDarkMode && styles.uploadButtonDark]} 
                onPress={handleImagePick}
              >
                <Icons.Ionicons
                  name="cloud-upload-outline"
                  size={24}
                  color={isDarkMode ? '#fff' : '#000'}
                />
                <Text style={[styles.uploadText, isDarkMode && styles.uploadTextDark]}>
                  Choose Image
                </Text>
              </TouchableOpacity>
              {preview && (
                <Image 
                  source={{ uri: preview }} 
                  style={styles.imagePreview}
                  resizeMode="cover"
                />
              )}
            </View>

            <TouchableOpacity
              style={[styles.submitButton, loading && styles.submitButtonDisabled]}
              onPress={handleSubmit}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.submitButtonText}>Submit Query</Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SafeAreaView>
    </BottomTabBar>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  formContainerDark: {
    backgroundColor: '#1a1a1a',
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  labelDark: {
    color: '#fff',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#000',
  },
  inputDark: {
    borderColor: '#333',
    backgroundColor: '#2d2d2d',
    color: '#fff',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#000',
    minHeight: 100,
  },
  pickerWrapper: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 8,
    backgroundColor: '#fff',
    marginBottom: 10,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  uploadButtonDark: {
    backgroundColor: '#2d2d2d',
    borderColor: '#404040',
  },
  uploadText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#333',
  },
  uploadTextDark: {
    color: '#fff',
  },
  imagePreview: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginTop: 12,
  },
  submitButton: {
    backgroundColor: '#198754',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 20,
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default RaiseQueryScreen;
