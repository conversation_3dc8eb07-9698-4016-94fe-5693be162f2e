import React, { useContext } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/FontAwesome';
import { ThemeContext } from '../context/ThemeContext';

const CartIcon = ({ onPress, size = 24, color, showBadge = true }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const { itemCount } = useSelector((state) => state.cart);

  const iconColor = color || (isDarkMode ? '#fff' : '#333');

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <Icon
          name="shopping-cart"
          size={size}
          color={iconColor}
        />
        {showBadge && itemCount > 0 && (
          <View style={[
            styles.badge,
            isDarkMode && styles.badgeDark
          ]}>
            <Text style={[
              styles.badgeText,
              itemCount > 99 && styles.badgeTextSmall
            ]}>
              {itemCount > 99 ? '99+' : itemCount}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 8,
  },
  iconContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#dc3545',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  badgeDark: {
    borderColor: '#1e1e1e',
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '700',
    textAlign: 'center',
  },
  badgeTextSmall: {
    fontSize: 10,
  },
});

export default CartIcon;
