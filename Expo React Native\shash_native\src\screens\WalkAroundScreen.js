import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { completeWalkthrough } from '../redux/authSlice';
import { getWalkArounds } from '../redux/walkAroundSlice';

export default function WalkAroundScreen({ navigation }) {
  // Remove the header for this screen
  useEffect(() => {
    navigation.setOptions({ headerShown: false });
  }, [navigation]);
  const dispatch = useDispatch();
  const { walkArounds, isLoading, error } = useSelector((state) => state.walkAround);
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    dispatch(getWalkArounds());
  }, [dispatch]);
  const completeAndNavigate = () => {
    dispatch(completeWalkthrough());
    navigation.reset({
      index: 0,
      routes: [{ name: 'Main' }],
    });
  };

  const handleNext = () => {
    if (currentIndex < walkArounds.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else {
      completeAndNavigate();
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleSkip = () => {
    completeAndNavigate();
  };

  // Show loading indicator while fetching data
  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#28a745" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  // Show error message if failed to load
  if (error) {
    console.log('WalkAroundScreen error:', error);
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.errorText}>Failed to load walk around images</Text>
        <TouchableOpacity style={styles.button} onPress={() => dispatch(getWalkArounds())}>
          <Text style={styles.buttonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Show fallback if no walkArounds available
  if (!walkArounds || walkArounds.length === 0) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.errorText}>No walk around images available</Text>
        <TouchableOpacity style={styles.button} onPress={handleSkip}>
          <Text style={styles.buttonText}>Continue</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Image
        source={{ uri: walkArounds[currentIndex]?.image_url }}
        style={styles.image}
        resizeMode="stretch"
      />
      <View style={styles.overlay} />
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, { opacity: currentIndex === 0 ? 0.5 : 1 }]}
          onPress={handlePrevious}
          disabled={currentIndex === 0}
        >
          <Text style={styles.buttonText}>Previous</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={handleSkip}>
          <Text style={styles.buttonText}>Skip</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={handleNext}>
          <Text style={styles.buttonText}>
            {currentIndex === walkArounds.length - 1 ? 'Finish' : 'Next'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.pagination}>
        {walkArounds.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              index === currentIndex && styles.activeDot,
            ]}
          />
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 10,
  },
  errorText: {
    color: '#ffffff',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: 'rgba(40, 167, 69, 0.9)',
    borderRadius: 25,
    minWidth: 100,
    alignItems: 'center',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  pagination: {
    position: 'absolute',
    bottom: 120,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: '#28a745',
    width: 12,
    height: 12,
    borderRadius: 6,
  },
});
