import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  FlatList,
  RefreshControl,
} from 'react-native';
import { MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';
import { ThemeContext } from '../context/ThemeContext';
import EventDetailModal from '../components/EventDetailModal';
import BottomTabBar from '../components/BottomTabBar';

const EventsScreen = ({ navigation }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const [events, setEvents] = useState([]);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [refreshing, setRefreshing] = useState(false);

  // Dummy event data - exactly same as React JS version
  const dummyEvents = [
    {
      id: 1,
      title: "Annual Science Fair 2024",
      description: "Join us for an exciting showcase of innovative science projects and experiments from students across all departments.",
      date: "2024-03-15",
      time: "10:00 AM",
      location: "Main Auditorium",
      category: "academic",
      attendees: 250,
      image: "https://via.placeholder.com/400x200/007bff/ffffff?text=Science+Fair",
      status: "upcoming",
      organizer: "Science Department",
      registrationRequired: true,
      maxAttendees: 300,
      tags: ["science", "innovation", "students"]
    },
    {
      id: 2,
      title: "Cultural Festival - Rangmanch",
      description: "A vibrant celebration of arts, music, dance, and cultural diversity. Experience performances from various cultural groups.",
      date: "2024-03-22",
      time: "6:00 PM",
      location: "Open Ground",
      category: "cultural",
      attendees: 450,
      image: "https://via.placeholder.com/400x200/28a745/ffffff?text=Cultural+Festival",
      status: "upcoming",
      organizer: "Cultural Committee",
      registrationRequired: false,
      maxAttendees: 500,
      tags: ["culture", "music", "dance", "arts"]
    },
    {
      id: 3,
      title: "Tech Symposium 2024",
      description: "Explore the latest trends in technology with expert speakers, workshops, and networking opportunities.",
      date: "2024-02-28",
      time: "9:00 AM",
      location: "Conference Hall",
      category: "technology",
      attendees: 180,
      image: "https://via.placeholder.com/400x200/dc3545/ffffff?text=Tech+Symposium",
      status: "completed",
      organizer: "IT Department",
      registrationRequired: true,
      maxAttendees: 200,
      tags: ["technology", "innovation", "networking"]
    },
    {
      id: 4,
      title: "Sports Championship",
      description: "Annual inter-department sports competition featuring cricket, football, basketball, and athletics.",
      date: "2024-04-05",
      time: "8:00 AM",
      location: "Sports Complex",
      category: "sports",
      attendees: 320,
      image: "https://via.placeholder.com/400x200/ffc107/000000?text=Sports+Championship",
      status: "upcoming",
      organizer: "Sports Committee",
      registrationRequired: true,
      maxAttendees: 400,
      tags: ["sports", "competition", "athletics"]
    },
    {
      id: 5,
      title: "Career Guidance Workshop",
      description: "Professional development workshop with industry experts sharing insights on career opportunities and skill development.",
      date: "2024-03-10",
      time: "2:00 PM",
      location: "Seminar Hall",
      category: "professional",
      attendees: 95,
      image: "https://via.placeholder.com/400x200/6f42c1/ffffff?text=Career+Workshop",
      status: "upcoming",
      organizer: "Placement Cell",
      registrationRequired: true,
      maxAttendees: 100,
      tags: ["career", "professional", "skills"]
    },
    {
      id: 6,
      title: "Alumni Meet 2024",
      description: "Reconnect with fellow alumni, share experiences, and network with professionals from various industries.",
      date: "2024-02-15",
      time: "7:00 PM",
      location: "Grand Hall",
      category: "networking",
      attendees: 150,
      image: "https://via.placeholder.com/400x200/17a2b8/ffffff?text=Alumni+Meet",
      status: "completed",
      organizer: "Alumni Association",
      registrationRequired: true,
      maxAttendees: 200,
      tags: ["alumni", "networking", "reunion"]
    },
    {
      id: 7,
      title: "Mathematics Olympiad 2024",
      description: "Test your mathematical skills in this challenging competition featuring problems from various mathematical domains.",
      date: "2024-04-12",
      time: "9:00 AM",
      location: "Mathematics Department",
      category: "academic",
      attendees: 120,
      image: "https://via.placeholder.com/400x200/007bff/ffffff?text=Math+Olympiad",
      status: "upcoming",
      organizer: "Mathematics Department",
      registrationRequired: true,
      maxAttendees: 150,
      tags: ["mathematics", "competition", "academic"]
    },
    {
      id: 9,
      title: "Robotics Workshop",
      description: "Hands-on workshop on building and programming robots using Arduino and Raspberry Pi platforms.",
      date: "2024-01-20",
      time: "2:00 PM",
      location: "Engineering Lab",
      category: "technology",
      attendees: 80,
      image: "https://via.placeholder.com/400x200/dc3545/ffffff?text=Robotics+Workshop",
      status: "completed",
      organizer: "Engineering Department",
      registrationRequired: true,
      maxAttendees: 100,
      tags: ["robotics", "programming", "engineering"]
    },
    {
      id: 10,
      title: "Basketball Tournament",
      description: "Inter-college basketball championship with teams from various institutions competing for the trophy.",
      date: "2024-04-20",
      time: "4:00 PM",
      location: "Basketball Court",
      category: "sports",
      attendees: 500,
      image: "https://via.placeholder.com/400x200/ffc107/000000?text=Basketball+Tournament",
      status: "upcoming",
      organizer: "Sports Committee",
      registrationRequired: true,
      maxAttendees: 600,
      tags: ["basketball", "tournament", "sports"]
    },
    {
      id: 11,
      title: "Entrepreneurship Summit",
      description: "Learn from successful entrepreneurs and startup founders about building and scaling businesses.",
      date: "2024-03-05",
      time: "10:00 AM",
      location: "Business Center",
      category: "professional",
      attendees: 180,
      image: "https://via.placeholder.com/400x200/6f42c1/ffffff?text=Entrepreneurship+Summit",
      status: "upcoming",
      organizer: "Business Department",
      registrationRequired: true,
      maxAttendees: 200,
      tags: ["entrepreneurship", "business", "startup"]
    },
    {
      id: 12,
      title: "Photography Contest",
      description: "Capture the beauty around you in this photography competition with multiple categories and prizes.",
      date: "2024-01-15",
      time: "12:00 PM",
      location: "Campus Wide",
      category: "cultural",
      attendees: 150,
      image: "https://via.placeholder.com/400x200/28a745/ffffff?text=Photography+Contest",
      status: "completed",
      organizer: "Photography Club",
      registrationRequired: true,
      maxAttendees: 200,
      tags: ["photography", "contest", "creativity"]
    }
  ];

  useEffect(() => {
    // Simulate API call
    setEvents(dummyEvents);
  }, []);

  const handleShowModal = (event) => {
    setSelectedEvent(event);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedEvent(null);
  };

  const getStatusColor = (status) => {
    const colors = {
      upcoming: '#007bff',
      completed: '#6c757d',
      ongoing: '#28a745'
    };
    return colors[status] || '#6c757d';
  };

  const getCategoryColor = (category) => {
    const colors = {
      academic: '#007bff',
      cultural: '#28a745',
      technology: '#dc3545',
      sports: '#ffc107',
      professional: '#17a2b8',
      networking: '#343a40'
    };
    return colors[category] || '#6c757d';
  };

  // Apply filters
  const filteredEvents = events.filter(event => {
    const categoryMatch = categoryFilter === 'all' || event.category === categoryFilter;
    const statusMatch = statusFilter === 'all' || event.status === statusFilter;
    return categoryMatch && statusMatch;
  });

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const onRefresh = () => {
    setRefreshing(true);
    // Simulate refresh
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const renderFilterButton = (label, value, currentFilter, setFilter) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        currentFilter === value && styles.filterButtonActive,
        isDarkMode && styles.filterButtonDark,
        currentFilter === value && isDarkMode && styles.filterButtonActiveDark
      ]}
      onPress={() => setFilter(value)}
    >
      <Text style={[
        styles.filterButtonText,
        currentFilter === value && styles.filterButtonTextActive,
        isDarkMode && styles.filterButtonTextDark,
        currentFilter === value && isDarkMode && styles.filterButtonTextActiveDark
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <BottomTabBar>
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        {/* Header */}
        <View style={[styles.header, isDarkMode && styles.headerDark]}>
          <View style={styles.headerContent}>
            <MaterialIcons name="event" size={28} color="#28a745" />
            <Text style={[styles.headerTitle, isDarkMode && styles.headerTitleDark]}>
              Events & Activities
            </Text>
          </View>
          <Text style={[styles.headerSubtitle, isDarkMode && styles.headerSubtitleDark]}>
            Stay updated with all the exciting events, workshops, and activities happening in our institution.
          </Text>
        </View>

        <ScrollView 
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {/* Filter Buttons */}
          <View style={styles.filtersContainer}>
            <View style={styles.filterSection}>
              <View style={styles.filterHeader}>
                <MaterialIcons name="filter-list" size={16} color={isDarkMode ? '#ccc' : '#666'} />
                <Text style={[styles.filterTitle, isDarkMode && styles.filterTitleDark]}>
                  Filter by Status
                </Text>
              </View>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
                {renderFilterButton('All Status', 'all', statusFilter, setStatusFilter)}
                {renderFilterButton('Upcoming', 'upcoming', statusFilter, setStatusFilter)}
                {renderFilterButton('Completed', 'completed', statusFilter, setStatusFilter)}
              </ScrollView>
            </View>

            <View style={styles.filterSection}>
              <Text style={[styles.filterTitle, isDarkMode && styles.filterTitleDark]}>
                Filter by Category
              </Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
                {renderFilterButton('All Categories', 'all', categoryFilter, setCategoryFilter)}
                {renderFilterButton('Academic', 'academic', categoryFilter, setCategoryFilter)}
                {renderFilterButton('Cultural', 'cultural', categoryFilter, setCategoryFilter)}
                {renderFilterButton('Technology', 'technology', categoryFilter, setCategoryFilter)}
                {renderFilterButton('Sports', 'sports', categoryFilter, setCategoryFilter)}
                {renderFilterButton('Professional', 'professional', categoryFilter, setCategoryFilter)}
              </ScrollView>
            </View>
          </View>

          {/* Events List */}
          <FlatList
            data={filteredEvents}
            keyExtractor={(item) => item.id.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[styles.eventCard, isDarkMode && styles.eventCardDark]}
                onPress={() => handleShowModal(item)}
              >
                <Image source={{ uri: item.image }} style={styles.eventImage} />
                
                <View style={styles.eventContent}>
                  <View style={styles.eventHeader}>
                    <View style={[styles.categoryBadge, { backgroundColor: getCategoryColor(item.category) }]}>
                      <Text style={styles.categoryText}>{item.category.toUpperCase()}</Text>
                    </View>
                    <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
                      <Text style={styles.statusText}>{item.status.toUpperCase()}</Text>
                    </View>
                  </View>

                  <Text style={[styles.eventTitle, isDarkMode && styles.eventTitleDark]}>
                    {item.title}
                  </Text>
                  
                  <Text style={[styles.eventDescription, isDarkMode && styles.eventDescriptionDark]}>
                    {item.description.length > 100
                      ? `${item.description.substring(0, 100)}...`
                      : item.description}
                  </Text>

                  <View style={styles.eventDetails}>
                    <View style={styles.detailRow}>
                      <MaterialIcons name="event" size={16} color={isDarkMode ? '#ccc' : '#666'} />
                      <Text style={[styles.detailText, isDarkMode && styles.detailTextDark]}>
                        {formatDate(item.date)}
                      </Text>
                    </View>
                    
                    <View style={styles.detailRow}>
                      <MaterialIcons name="access-time" size={16} color={isDarkMode ? '#ccc' : '#666'} />
                      <Text style={[styles.detailText, isDarkMode && styles.detailTextDark]}>
                        {item.time}
                      </Text>
                    </View>
                    
                    <View style={styles.detailRow}>
                      <MaterialIcons name="location-on" size={16} color={isDarkMode ? '#ccc' : '#666'} />
                      <Text style={[styles.detailText, isDarkMode && styles.detailTextDark]}>
                        {item.location}
                      </Text>
                    </View>
                    
                    <View style={styles.detailRow}>
                      <MaterialIcons name="people" size={16} color={isDarkMode ? '#ccc' : '#666'} />
                      <Text style={[styles.detailText, isDarkMode && styles.detailTextDark]}>
                        {item.attendees}/{item.maxAttendees} attendees
                      </Text>
                    </View>
                  </View>

                  <TouchableOpacity
                    style={styles.viewButton}
                    onPress={() => handleShowModal(item)}
                  >
                    <MaterialIcons name="visibility" size={16} color="#28a745" />
                    <Text style={styles.viewButtonText}>View Details</Text>
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            )}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />

          {filteredEvents.length === 0 && (
            <View style={styles.noEventsContainer}>
              <MaterialCommunityIcons 
                name="calendar-remove" 
                size={64} 
                color={isDarkMode ? '#666' : '#ccc'} 
              />
              <Text style={[styles.noEventsTitle, isDarkMode && styles.noEventsTitleDark]}>
                No events found
              </Text>
              <Text style={[styles.noEventsText, isDarkMode && styles.noEventsTextDark]}>
                Try adjusting your filters or check back later for new events.
              </Text>
            </View>
          )}
        </ScrollView>

        {/* Event Detail Modal */}
        <EventDetailModal
          visible={showModal}
          onClose={handleCloseModal}
          event={selectedEvent}
          getCategoryColor={getCategoryColor}
          getStatusColor={getStatusColor}
          formatDate={formatDate}
        />
      </SafeAreaView>
    </BottomTabBar>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  containerDark: {
    backgroundColor: '#1a1a1a',
  },
  header: {
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#2a2a2a',
    borderBottomColor: '#444',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 12,
  },
  headerTitleDark: {
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  headerSubtitleDark: {
    color: '#ccc',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  filtersContainer: {
    marginBottom: 20,
  },
  filterSection: {
    marginBottom: 16,
  },
  filterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  filterTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginLeft: 8,
  },
  filterTitleDark: {
    color: '#ccc',
  },
  filterRow: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#28a745',
    marginRight: 8,
  },
  filterButtonDark: {
    backgroundColor: '#2a2a2a',
    borderColor: '#28a745',
  },
  filterButtonActive: {
    backgroundColor: '#28a745',
  },
  filterButtonActiveDark: {
    backgroundColor: '#28a745',
  },
  filterButtonText: {
    fontSize: 12,
    color: '#28a745',
    fontWeight: '500',
  },
  filterButtonTextDark: {
    color: '#28a745',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  filterButtonTextActiveDark: {
    color: '#fff',
  },
  eventCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  eventCardDark: {
    backgroundColor: '#2a2a2a',
  },
  eventImage: {
    width: '100%',
    height: 200,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  eventContent: {
    padding: 16,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  categoryText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#fff',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#fff',
  },
  eventTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  eventTitleDark: {
    color: '#fff',
  },
  eventDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 16,
  },
  eventDescriptionDark: {
    color: '#ccc',
  },
  eventDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  detailTextDark: {
    color: '#ccc',
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#28a745',
    borderRadius: 8,
  },
  viewButtonText: {
    fontSize: 14,
    color: '#28a745',
    fontWeight: '600',
    marginLeft: 8,
  },
  noEventsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  noEventsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  noEventsTitleDark: {
    color: '#ccc',
  },
  noEventsText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  noEventsTextDark: {
    color: '#ccc',
  },
});

export default EventsScreen;
