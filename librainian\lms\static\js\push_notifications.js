
        // Firebase configuration - using environment variables
        const firebaseConfig = {
            apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
            authDomain: "librainian-app.firebaseapp.com",
            projectId: "librainian-app",
            storageBucket: "librainian-app.firebasestorage.app",
            messagingSenderId: "623132670328",
            appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
            measurementId: "G-XNDKJL6JWH"
        };

        // VAPID Key for Web Push
        const vapidKey = "BFm8KEWYXyt703OsjQ4338IbyV72W3m6nndMoZhzRV9SlSj0UHMv4INixoql0AJLWh6LJKC1CrP3r_M8YqsGrAY";

       

        // Initialize Firebase
        const app = firebase.initializeApp(firebaseConfig);
        const messaging = firebase.messaging();

        // Automatically request notification permission and register device token
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔔 Push notifications script loaded');
            console.log('🔧 Firebase config:', firebaseConfig);
            console.log('🔑 VAPID key:', vapidKey);

            const notificationBtn = document.getElementById('notificationBtn');
            if (notificationBtn) {
                notificationBtn.style.display = 'none'; // Hide the button since we will auto-register
            }

            if ('Notification' in window) {
                console.log('✅ Browser supports notifications');
                requestAndShowNotification();
            } else {
                console.log('❌ This browser does not support notifications.');
            }
        });

        function requestAndShowNotification() {
            console.log('🔔 Requesting notification permission...');
            console.log('📊 Current permission status:', Notification.permission);

            Notification.requestPermission().then(function(permission) {
                console.log('📋 Permission result:', permission);
                if (permission === 'granted') {
                    console.log('✅ Notification permission granted');
                    showNotification();
                    registerDeviceToken();
                } else {
                    console.log('❌ Notification permission denied:', permission);
                }
            }).catch(function(error) {
                console.error('❌ Error requesting permission:', error);
            });
        }

        function showNotification() {
            const options = {
                body: 'Welcome to Librainian! Your CRM tool for libraries and study centers.',
                icon: '/static/img/librainian-logo-black-transparent.png'
            };

            new Notification('Librainian Notification', options);
        }

        function registerDeviceToken() {
            console.log('🎫 Starting device token registration...');
            console.log('🔧 Messaging object:', messaging);
            console.log('🔑 Using VAPID key:', vapidKey);

            messaging.getToken({ vapidKey: vapidKey }).then((currentToken) => {
                console.log('📱 FCM getToken response:', currentToken);
                if (currentToken) {
                    console.log('✅ FCM Token received:', currentToken);
                    console.log('📏 Token length:', currentToken.length);
                    const deviceType = 'web';
                    saveDeviceToken(currentToken, deviceType);
                } else {
                    console.log('❌ No registration token available. Request permission to generate one.');
                    console.log('🔍 Check if service worker is registered and VAPID key is correct');
                }
            }).catch((err) => {
                console.error('❌ Error occurred while retrieving token:', err);
                console.error('🔍 Error details:', err.message, err.stack);
            });
        }

        function saveDeviceToken(token, deviceType) {
            console.log('💾 Saving device token to server...');
            console.log('🎫 Token:', token);
            console.log('📱 Device Type:', deviceType);

            const csrfToken = getCookie('csrftoken');
            console.log('🔐 CSRF Token:', csrfToken ? 'Found' : 'Not found');

            fetch('/librarian/save-device-token/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken
                },
                body: `token=${encodeURIComponent(token)}&device_type=${encodeURIComponent(deviceType)}`
            })
            .then(response => {
                console.log('📡 Server response status:', response.status);
                console.log('📡 Server response ok:', response.ok);
                return response.json();
            })
            .then(data => {
                console.log('✅ Server response data:', data);
                console.log('💾 Device token saved successfully!');
                console.log('🎫 Final token:', token);

                // Show success message to user
                if (data.status === 'success' || data.success) {
                    console.log('🎉 Token registration successful!');
                } else {
                    console.log('⚠️ Token registration had issues:', data.message);
                }
            })
            .catch(error => {
                console.error('❌ Error saving device token:', error);
                console.error('🔍 Error details:', error.message, error.stack);
            });
        }

        // Function to get CSRF token from cookies
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Service Worker Registration with detailed logging
        if ('serviceWorker' in navigator) {
            console.log('🔧 Registering service worker...');
            navigator.serviceWorker
                .register('/firebase-messaging-sw.js')
                .then(function(registration) {
                    console.log('✅ Service Worker registered successfully!');
                    console.log('🔧 Registration scope:', registration.scope);
                    console.log('🔧 Registration state:', registration.installing ? 'installing' : registration.waiting ? 'waiting' : registration.active ? 'active' : 'unknown');

                    // Set the service worker for messaging
                    messaging.useServiceWorker(registration);
                    console.log('🔧 Messaging service worker set');
                })
                .catch(function(err) {
                    console.error('❌ Service Worker registration failed:', err);
                    console.error('🔍 Error details:', err.message, err.stack);
                });
        } else {
            console.log('❌ Service Worker not supported in this browser');
        }
        