import React, { useState, useEffect, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>, Row, Col, Form, Container } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { getSubjects } from "../../redux/slice/subjectSlice";
import { getCustomerCareQuestions, getAllCustomerCareQuestion } from "../../redux/slice/customerCareSlice";
import { FaFilter } from "react-icons/fa";

import { toast } from "react-hot-toast";
import ContributorQuestionCard from "./ContributorQuestionCard";

const AllSubjectButtons = ({ subjectAdded }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [questions, setQuestions] = useState([]);
  const [isQuestionsLoading, setIsQuestionsLoading] = useState(false);
  const [activeSubject, setActiveSubject] = useState("");
  const [showFilter, setShowFilter] = useState(true);
  const questionType = location.pathname.split("/").pop()

  const dispatch = useDispatch();
  const { subjects, isLoading, error } = useSelector((state) => state.subject);

  useEffect(() => {
    dispatch(getSubjects());
  }, [dispatch, subjectAdded]);

  // Call getAllCustomerCareQuestion on page load for all types of questions
  useEffect(() => {
    const fetchAllQuestions = async () => {
      setIsQuestionsLoading(true);
      try {
        const response = await dispatch(getAllCustomerCareQuestion());
        setQuestions(response.payload); // <-- Set questions for all subjects
      } catch (error) {
        console.error("Error fetching all questions:", error);
        toast.error("Failed to fetch questions. Please try again.");
      } finally {
        setIsQuestionsLoading(false);
      }
    };

    fetchAllQuestions();
  }, [dispatch]);
  ;

  const filteredSubjects = useMemo(() => {
    return subjects?.filter((subject) =>
      subject.name.toLowerCase().includes(searchQuery.toLowerCase())
    ) || [];
  }, [subjects, searchQuery]);

  const handleSearch = () => {

  };


  const handleSubjectClick = async (subjectName) => {
    setActiveSubject(subjectName);
    setShowFilter(false); // Hide filter section after selecting subject
    setIsQuestionsLoading(true); // Start loading
    // console.log("IS QUESTIONS LOADING (before fetching)", true);

    try {
      const response = await dispatch(getCustomerCareQuestions(subjectName));
      console.log(response);
      
      setQuestions(response.payload); 
      // console.log("Questions", response.payload);
    } catch (error) {
      console.error("Error fetching questions:", error);
      toast.error("Failed to fetch questions. Please try again.");
    } finally {
      setIsQuestionsLoading(false); // Stop loading after success or error
      // console.log("IS QUESTIONS LOADING (after fetching)", false);
    }
  };

  const refetchQuestions = async (subjectName) => {
    setIsQuestionsLoading(true); // Start loading

    try {
      const response = await dispatch(getCustomerCareQuestions(subjectName));
      setQuestions(response.payload);
    } catch (error) {
      console.error("Error fetching questions:", error);
      toast.error("Failed to fetch questions. Please try again.");
    } finally {
      setIsQuestionsLoading(false); // Stop loading after success or error
    }
  };


  

  return (
    <Container>
      <Row className="justify-content-center my-4" style={{ position: "relative" }}>
        {/* Filter Icon Button to show filter if hidden */}
        {!showFilter && (
          <div
            style={{
              position: "fixed",
              left: 0,
              top: 150,
              zIndex: 1050,
            }}
          >
            <Button
              variant="outline-success"
              onClick={() => setShowFilter(true)}
              style={{
                borderRadius: "50%",
                width: "48px",
                height: "48px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
              }}
            >
              <FaFilter size={22} />
            </Button>
          </div>
        )}

        {/* Left Filter Section */}
        {showFilter && (
          <Col md={4} xs={12}>
            <h4 className="text-center text-success">
              Click the subjects to see the {questionType}
            </h4>
            <Row className="my-4 justify-content-center">
              <Col className="d-flex justify-content-between">
                <Form.Control
                  type="text"
                  placeholder="Search subjects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Button onClick={handleSearch} variant="success" style={{ width: "5rem", fontSize: "0.9rem", marginLeft: "0.2rem" }}>
                  Search
                </Button>
              </Col>
            </Row>
            {isLoading && (
              <div className="text-center">
                <div className="spinner-border text-success" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              </div>
            )}
            {error && (
              <div className="text-center text-danger">
                <p>{error}</p>
              </div>
            )}
            <div className="d-flex flex-wrap">
              {filteredSubjects.map((subject) => (
                <div className="m-1 rounded-3" key={subject.slug}>
                  <Button
                    variant={`${activeSubject === subject.name ? "success" : "outline-success"}`}
                    className="w-100 p-3 text-center"
                    style={{ fontSize: "1rem" }}
                    onClick={() => handleSubjectClick(subject.name)} // Dispatch action on click
                  >
                    {subject.name}
                  </Button>
                </div>
              ))}
            </div>
          </Col>
        )}
        {/* Right Section */}
        <Col md={showFilter ? 8 : 12} xs={12}>
          <ContributorQuestionCard allQuestions={questions} refetchQuestions={refetchQuestions} subjectName={activeSubject} questionLoading={isQuestionsLoading}/>
        </Col>
      </Row>
    </Container>
  );
};

export default AllSubjectButtons;
