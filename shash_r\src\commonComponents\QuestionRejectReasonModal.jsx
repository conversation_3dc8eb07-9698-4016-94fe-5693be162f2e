import { Modal, Button } from "react-bootstrap";

const QuestionRejectReasonModal = ({ show, onHide, reason, reasonDocument }) => {
  return (
    <Modal show={show} onHide={onHide} size="lg" centered >
      <Modal.Header closeButton>
        <Modal.Title>Rejection Reason</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <h6>Reason</h6>
        <p>{reason || "No reason provided"}</p>

        {reasonDocument && (
          <>
            <h6 className="mt-3">Supporting Document</h6>
            <div className="mt-2">
              <img
                src={`${import.meta.env.VITE_BASE_URL}/${reasonDocument}`}
                alt="Supporting Document"
                className="img-fluid rounded-3"
              />
            </div>
          </>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default QuestionRejectReasonModal;