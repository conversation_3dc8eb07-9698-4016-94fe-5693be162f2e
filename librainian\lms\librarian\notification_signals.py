"""
Enhanced Signal handlers for all notification events
"""

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from .notification_events import notification_events
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender='studentsData.TempStudentData')
def qr_registration_notification(sender, instance, created, **kwargs):
    """Send enhanced notification when someone submits registration via QR code"""
    if created:
        try:
            # Send enhanced notification with complete data
            notification_events.notify_qr_registration(instance)
            logger.info(f"Enhanced QR registration notification sent for {instance.name}")
        except Exception as e:
            logger.error(f"Failed to send QR registration notification: {str(e)}")


@receiver(post_save, sender='studentsData.StudentData')
def admission_processed_notification(sender, instance, created, **kwargs):
    """Send notification when admission is processed (TempStudentData -> StudentData)"""
    if created:
        try:
            # Get the user who processed the admission (from request context if available)
            # For now, we'll use the sublibrarian or librarian
            processed_by = instance.sublibrarian.user if instance.sublibrarian else instance.librarian.user

            notification_events.notify_admission_processed(instance, processed_by)
            logger.info(f"Admission processed notification sent for {instance.name}")
        except Exception as e:
            logger.error(f"Failed to send admission processed notification: {str(e)}")


@receiver(post_save, sender='studentsData.Invoice')
def invoice_created_notification(sender, instance, created, **kwargs):
    """Send notification when invoice is created"""
    if created:
        try:
            # Get the user who created the invoice
            created_by = instance.student.sublibrarian.user if instance.student.sublibrarian else instance.student.librarian.user

            notification_events.notify_invoice_created(instance, created_by)
            logger.info(f"Invoice created notification sent for {instance.invoice_id}")
        except Exception as e:
            logger.error(f"Failed to send invoice created notification: {str(e)}")


@receiver(post_save, sender='studentsData.Payment')
def payment_received_notification(sender, instance, created, **kwargs):
    """Send notification when payment is received"""
    if created:
        try:
            # Get the user who received the payment
            received_by = instance.invoice.student.sublibrarian.user if instance.invoice.student.sublibrarian else instance.invoice.student.librarian.user

            notification_events.notify_payment_received(instance, received_by)
            logger.info(f"Payment received notification sent for ₹{instance.amount_paid}")
        except Exception as e:
            logger.error(f"Failed to send payment received notification: {str(e)}")


@receiver(post_save, sender='visitorsData.Visitor')
def visitor_added_notification(sender, instance, created, **kwargs):
    """Send notification when visitor is added"""
    if created:
        try:
            # Get the user who added the visitor
            added_by = instance.sublibrarian.user if instance.sublibrarian else instance.librarian.user

            notification_events.notify_visitor_added(instance, added_by)
            logger.info(f"Visitor added notification sent for {instance.name}")
        except Exception as e:
            logger.error(f"Failed to send visitor added notification: {str(e)}")
