import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the authToken from Redux state
const getAuthToken = (getState) => {
  const { access } = getState().customerCare; // Adjust state path as needed
  return access;
};

// Create a ticket
export const createTicket = createAsyncThunk(
  'tickets/create',
  async ({data}, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TICKET}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to create ticket');
    }
  }
);

// Get all tickets
export const getAllTickets = createAsyncThunk(
  'tickets/getAll',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TICKET}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch tickets');
    }
  }
);

// Get a specific ticket
export const getTicket = createAsyncThunk(
  'tickets/get',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TICKET}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch ticket');
    }
  }
);

// Update a ticket
export const updateTicket = createAsyncThunk(
  'tickets/update',
  async ({ slug, data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TICKET}${slug}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to update ticket');
    }
  }
);

// Delete a ticket
export const deleteTicket = createAsyncThunk(
  'tickets/delete',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TICKET}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return slug; // Return the id to confirm deletion
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to delete ticket');
    }
  }
);

const ticketSlice = createSlice({
  name: 'tickets',
  initialState: { error: null },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Create ticket
      .addCase(createTicket.rejected, (state, action) => {
        state.error = action.payload;
      })

      // Get all tickets
      .addCase(getAllTickets.rejected, (state, action) => {
        state.error = action.payload;
      })

      // Get a specific ticket
      .addCase(getTicket.rejected, (state, action) => {
        state.error = action.payload;
      })

      // Update a ticket
      .addCase(updateTicket.rejected, (state, action) => {
        state.error = action.payload;
      })

      // Delete a ticket
      .addCase(deleteTicket.rejected, (state, action) => {
        state.error = action.payload;
      });
  },
});

export default ticketSlice.reducer;
