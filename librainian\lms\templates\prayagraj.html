<!DOCTYPE html>
<html lang="en">

<head>
<!-- Required Meta -->
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="icon" type="image/png" href="/static/img/figma_files/logoCircle.png">


<!-- 🔹 Title Tag (60–70 characters, SEO + Clickable) -->
<title>Librainian App Launch in Prayagraj – Smart Library Management SaaS</title>

<!-- 🔹 Meta Description (Under 160 characters, Hook + CTA + Keywords) -->
<meta name="description" content="Join the digital revolution! Librainian launches in Prayagraj on June 9, 2025. Transform your library with India's #1 real-time management SaaS.">

<!-- 🔹 Keywords (Enhanced for local SEO) -->
<meta name="keywords" content="Librainian Prayagraj, library management software, digital library Prayagraj, library app launch 2025, smart library system, SaaS for libraries, student management, library automation">

<!-- 🔹 Author & Publisher -->
<meta name="author" content="Librainian Team">
<meta name="publisher" content="Librainian">

<!-- 🔹 Enhanced Open Graph for Social Sharing -->
<meta property="og:title" content="🚀 Librainian Launches in Prayagraj - June 9, 2025">
<meta property="og:description" content="Revolutionary library management is coming to Prayagraj! Join India's fastest-growing library SaaS platform. Watch our demo & be part of the digital transformation.">
<meta property="og:type" content="website">
<meta property="og:url" content="https://www.librainian.com/prayagraj/"> 
<meta property="og:image" content="https://www.librainian.com/static/img/figma_files/prayag.jpg">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:site_name" content="Librainian">
<meta property="og:locale" content="en_IN">

<!-- 🔹 Twitter Card -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="🚀 Librainian Launches in Prayagraj - June 9, 2025">
<meta name="twitter:description" content="Revolutionary library management coming to Prayagraj! Join India's fastest-growing library SaaS platform.">
<meta name="twitter:image" content="https://www.librainian.com/static/img/figma_files/prayag.jpg">

<!-- 🔹 Canonical -->
<link rel="canonical" href="https://www.librainian.com/prayagraj/">

<!-- 🔹 JSON-LD Schema (Upgraded to include App and Event) -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "MobileApplication",
  "name": "Librainian",
  "operatingSystem": "Android, iOS",
  "applicationCategory": "ProductivityApplication",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "INR"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8",
    "reviewCount": "120"
  },
  "publisher": {
    "@type": "Organization",
    "name": "Librainian",
    "logo": {
      "@type": "ImageObject",
      "url": "https://librainian.com/static/img/librainian-logo-black-transparent.png"
    }
  },
  "url": "https://librainian.com/prayagraj/",
  "description": "Revolutionize library operations in Prayagraj with Librainian – India’s first real-time library SaaS platform. Download the app and go paperless."
}
</script>

<!-- Event Schema for Prayagraj Launch -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Event",
  "name": "Librainian App Launch in Prayagraj",
  "description": "Join the digital revolution! Librainian launches in Prayagraj on June 9, 2025. Transform your library with India's #1 real-time management SaaS.",
  "startDate": "2025-06-09T10:00:00+05:30",
  "endDate": "2025-06-09T18:00:00+05:30",
  "eventStatus": "https://schema.org/EventScheduled",
  "eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode",
  "location": {
    "@type": "Place",
    "name": "Prayagraj",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Prayagraj",
      "addressRegion": "Uttar Pradesh",
      "addressCountry": "IN"
    }
  },
  "organizer": {
    "@type": "Organization",
    "name": "Librainian",
    "url": "https://librainian.com/"
  },
  "url": "https://librainian.com/prayagraj/",
  "image": "https://librainian.com/static/img/figma_files/prayag.jpg"
}
</script>

<!-- LocalBusiness Schema for Prayagraj -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "name": "Librainian Prayagraj",
  "description": "Smart library management SaaS platform launching in Prayagraj. Transform your library operations with digital solutions.",
  "url": "https://librainian.com/prayagraj/",
  "areaServed": {
    "@type": "City",
    "name": "Prayagraj",
    "containedInPlace": {
      "@type": "State",
      "name": "Uttar Pradesh",
      "containedInPlace": {
        "@type": "Country",
        "name": "India"
      }
    }
  },
  "serviceType": "Library Management Software",
  "provider": {
    "@type": "Organization",
    "name": "Librainian",
    "url": "https://librainian.com/"
  }
}
</script>

<!-- BreadcrumbList Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "https://librainian.com/"
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "Prayagraj Launch",
      "item": "https://librainian.com/prayagraj/"
    }
  ]
}
</script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4f8cff;
            --secondary-color: #6ee7b7;
            --accent-color: #ff6b6b;
            --text-dark: #2d3748;
            --text-light: #718096;
            --bg-light: #f7fafc;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comfortaa', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-light);
            color: var(--text-dark);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Enhanced Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: #fff;
            padding: 80px 20px 60px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 1s ease-out;
        }

        .hero p {
            font-size: clamp(1.1rem, 2.5vw, 1.4rem);
            margin-bottom: 2rem;
            opacity: 0.95;
            animation: fadeInUp 1s ease-out 0.2s both;
        }

        .countdown-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            padding: 8px 20px;
            border-radius: 50px;
            margin-bottom: 1rem;
            font-weight: 600;
            animation: fadeInUp 1s ease-out 0.1s both;
        }

        .cta-btn {
            background: #fff;
            color: var(--primary-color);
            border: none;
            padding: 16px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            animation: fadeInUp 1s ease-out 0.4s both;
        }

        .cta-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            background: #f8f9ff;
            color: var(--primary-color);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Section Styling */
        .section {
            padding: 80px 0;
        }

        .section-title {
            text-align: center;
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 1rem;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: var(--text-light);
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Video Section */
        .video-section {
            padding: 60px 0;
            background: #fff;
        }

        .video-container {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            transition: transform 0.3s ease;
        }

        .video-container:hover {
            transform: scale(1.02);
        }

        .video-container iframe {
            width: 100%;
            height: 450px;
            border: none;
        }

        /* Instagram-Style Gallery */
        .gallery-section {
            background: var(--bg-light);
        }

        .instagram-carousel {
            max-width: 500px;
            margin: 0 auto;
            background: #fff;
            border-radius: 20px;
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            position: relative;
        }

        .carousel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #fff;
            border-bottom: 1px solid #e2e8f0;
        }

        .carousel-counter {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 0.9rem;
        }

        .carousel-dots {
            display: flex;
            gap: 8px;
        }

        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #cbd5e0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dot.active {
            background: var(--primary-color);
            transform: scale(1.2);
        }

        .carousel-container {
            position: relative;
            overflow: hidden;
            height: 400px;
        }

        .carousel-track {
            display: flex;
            transition: transform 0.3s ease;
            height: 100%;
        }

        .carousel-slide {
            min-width: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .carousel-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .slide-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            color: white;
            padding: 20px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .carousel-slide:hover .slide-overlay {
            opacity: 1;
        }

        .slide-content h4 {
            margin: 0 0 8px 0;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .slide-content p {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .carousel-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
            box-shadow: var(--shadow-md);
        }

        .carousel-btn:hover {
            background: white;
            transform: translateY(-50%) scale(1.1);
        }

        .carousel-btn-prev {
            left: 15px;
        }

        .carousel-btn-next {
            right: 15px;
        }

        .carousel-btn i {
            color: var(--text-dark);
            font-size: 0.9rem;
        }

        .swipe-indicator {
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 8px;
            opacity: 0.8;
            animation: fadeInOut 3s infinite;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 0.4; }
        }

        .swipe-indicator i {
            font-size: 0.9rem;
            animation: wiggle 2s infinite;
        }

        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(-10deg); }
            75% { transform: rotate(10deg); }
        }

        /* Features Section */
        .features-section {
            background: #fff;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .feature-card {
            text-align: center;
            padding: 40px 30px;
            border-radius: 20px;
            background: var(--bg-light);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-lg);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: white;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-dark);
        }

        .feature-description {
            color: var(--text-light);
            line-height: 1.6;
        }

        /* Testimonials */
        .testimonials-section {
            background: var(--bg-light);
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            max-width: 1000px;
            margin: 0 auto;
        }

        .testimonial-card {
            background: #fff;
            padding: 40px 30px;
            border-radius: 20px;
            box-shadow: var(--shadow-md);
            text-align: center;
            position: relative;
            transition: transform 0.3s ease;
        }

        .testimonial-card:hover {
            transform: translateY(-5px);
        }

        .testimonial-quote {
            font-size: 1.1rem;
            font-style: italic;
            color: var(--text-dark);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .testimonial-author {
            font-weight: 600;
            color: var(--primary-color);
        }

        .testimonial-role {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        /* CTA Section */
        .cta-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            text-align: center;
            padding: 80px 20px;
        }

        .cta-content {
            max-width: 600px;
            margin: 0 auto;
        }

        .cta-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            margin-bottom: 20px;
        }

        .cta-description {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: #fff;
            color: var(--primary-color);
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: var(--primary-color);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: white;
            color: var(--primary-color);
        }

        /* Sticky WhatsApp Button */
        .sticky-cta {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            background: #25d366;
            color: white;
            padding: 15px 20px;
            border-radius: 50px;
            text-decoration: none;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }

        .sticky-cta:hover {
            transform: scale(1.05);
            color: white;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(37, 211, 102, 0); }
            100% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0); }
        }

        /* Offers Section */
        .offers-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            position: relative;
            overflow: hidden;
        }

        .offers-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="offers-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(79,140,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23offers-pattern)"/></svg>');
        }

        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .offer-card {
            background: #fff;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            overflow: hidden;
        }

        .offer-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(79,140,255,0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .offer-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .offer-card:hover::before {
            opacity: 1;
        }

        .special-offer {
            border-color: var(--accent-color);
            background: linear-gradient(135deg, #fff 0%, #fff5f5 100%);
        }

        .popular-offer {
            border-color: var(--secondary-color);
            background: linear-gradient(135deg, #fff 0%, #f0fff4 100%);
            transform: scale(1.05);
        }

        .premium-offer {
            border-color: #ffd700;
            background: linear-gradient(135deg, #fff 0%, #fffbf0 100%);
        }

        .offer-badge {
            position: absolute;
            top: -10px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: var(--shadow-sm);
        }

        .special-offer .offer-badge {
            background: var(--accent-color);
        }

        .popular-offer .offer-badge {
            background: var(--secondary-color);
        }

        .premium-offer .offer-badge {
            background: #ffd700;
            color: #333;
        }

        .offer-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 2rem;
            color: white;
            position: relative;
            z-index: 2;
        }

        .special-offer .offer-icon {
            background: linear-gradient(135deg, var(--accent-color), #ff8a80);
        }

        .premium-offer .offer-icon {
            background: linear-gradient(135deg, #ffd700, #ffb300);
        }

        .offer-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: var(--text-dark);
            position: relative;
            z-index: 2;
        }

        .offer-description {
            color: var(--text-light);
            margin-bottom: 20px;
            line-height: 1.6;
            position: relative;
            z-index: 2;
        }

        .offer-highlight {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            margin: 20px 0;
            display: inline-block;
            font-size: 0.9rem;
            position: relative;
            z-index: 2;
        }

        .offer-benefits {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin: 20px 0;
            position: relative;
            z-index: 2;
        }

        .benefit-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            color: var(--text-dark);
            font-weight: 500;
        }

        .benefit-item i {
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .offer-btn {
            background: var(--primary-color);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
            box-shadow: var(--shadow-md);
            position: relative;
            z-index: 2;
        }

        .offer-btn:hover {
            background: #3d7bff;
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: white;
        }

        .special-offer .offer-btn {
            background: var(--accent-color);
        }

        .special-offer .offer-btn:hover {
            background: #ff5252;
        }

        .premium-offer .offer-btn {
            background: #ffd700;
            color: #333;
        }

        .premium-offer .offer-btn:hover {
            background: #ffb300;
            color: #333;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero {
                padding: 60px 20px 40px;
            }

            .section {
                padding: 60px 0;
            }

            .video-container iframe {
                height: 250px;
            }

            .instagram-carousel {
                max-width: 300px;
                margin: 0 20px;
            }

            .carousel-container {
                height: 100%;
            }

            .carousel-header {
                padding: 12px 15px;
            }

            .carousel-btn {
                width: 35px;
                height: 35px;
            }

            .carousel-btn-prev {
                left: 10px;
            }

            .carousel-btn-next {
                right: 10px;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .testimonials-grid {
                grid-template-columns: 1fr;
            }

            .offers-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .popular-offer {
                transform: none;
            }

            .offer-card {
                padding: 30px 20px;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .sticky-cta {
                bottom: 15px;
                right: 15px;
                padding: 12px 16px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .hero {
                padding: 40px 15px 30px;
            }

            .section {
                padding: 40px 0;
            }

            .feature-card,
            .testimonial-card {
                padding: 30px 20px;
            }

            .cta-section {
                padding: 60px 15px;
            }
        }

        /* Loading Animation */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Header Enhancement */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            box-shadow: var(--shadow-sm);
        }

        .logo {
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }
    </style>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5L8WZWCC');</script>
<!-- End Google Tag Manager -->
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">



    <!-- Preload critical resources -->
    <link rel="preload" href="/static/img/link_cover.jpg" as="image">
    <link rel="preload" href="/static/img/librainian-logo-black-transparent-med.png" as="image">

</head>

<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5L8WZWCC"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

    <!-- Header -->
    <header class="header text-center">
        <div class="container">
            <img src="/static/img/link_cover.jpg" alt="Librainian Logo" class="logo" style="height:100px;">
            <p class="mt-2" style="font-size: 1rem; color: var(--text-light);">Empowering Modern Libraries</p>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <div class="countdown-badge">
                <i class="fas fa-rocket"></i> Launching June 9, 2025
            </div>
            <h1>Transform Your Library with Librainian</h1>
            <p>Join the digital revolution in Prayagraj! Experience India's most advanced library management SaaS platform that's changing how libraries operate.</p>
            <a href="#features" class="cta-btn">
                <i class="fas fa-play-circle"></i> Discover the Future
            </a>
        </div>
    </section>

    <!-- Video Section -->
    <section class="video-section section">
        <div class="container">
            <h2 class="section-title fade-in">See Librainian in Action</h2>
            <p class="section-subtitle fade-in">Watch how Librainian is revolutionizing library management across India</p>
            <div class="video-container fade-in">
                <iframe
                    src="https://www.youtube.com/embed/S_FOYXtHUwo?si=CnMtr6ScsD50gcyt"
                    title="Librainian App Overview - Transform Your Library Management"
                    allowfullscreen
                    loading="lazy">
                </iframe>
            </div>
        </div>
    </section>

    <!-- Instagram-Style Gallery -->
    <section class="gallery-section section">
        <div class="container">
            <h2 class="section-title fade-in">📸 See Librainian in Action</h2>
            <p class="section-subtitle fade-in">Swipe through real library transformations and success stories</p>

            <div class="instagram-carousel fade-in">
                <!-- Carousel Header -->
                <div class="carousel-header">
                    <div class="carousel-counter">
                        <span class="current-slide">1</span>/<span class="total-slides">6</span>
                    </div>
                    <div class="carousel-dots">
                        <span class="dot active" data-slide="0"></span>
                        <span class="dot" data-slide="1"></span>
                        <span class="dot" data-slide="2"></span>
                        <span class="dot" data-slide="3"></span>
                        <span class="dot" data-slide="4"></span>
                        <span class="dot" data-slide="5"></span>
                    </div>
                </div>

                <!-- Carousel Container -->
                <div class="carousel-container">
                    <div class="carousel-track">
                        <div class="carousel-slide active">
                            <img src="/static/img/portfolio/WhatsApp Image 2025-05-28 at 17.26.12.jpeg" alt="Library Success Story 1" loading="lazy">
                            <div class="slide-overlay">
                                <div class="slide-content">
                                    <h4>📚 Digital Transformation</h4>
                                    <p>See how libraries are embracing digital solutions</p>
                                </div>
                            </div>
                        </div>

                        <div class="carousel-slide">
                            <img src="/static/img/portfolio/WhatsApp Image 2025-05-28 at 17.26.13 (1).jpeg" alt="Library Success Story 2" loading="lazy">
                            <div class="slide-overlay">
                                <div class="slide-content">
                                    <h4>👥 Student Engagement</h4>
                                    <p>Modern students love our interactive features</p>
                                </div>
                            </div>
                        </div>

                        <div class="carousel-slide">
                            <img src="/static/img/portfolio/WhatsApp Image 2025-05-28 at 17.26.13.jpeg" alt="Library Success Story 3" loading="lazy">
                            <div class="slide-overlay">
                                <div class="slide-content">
                                    <h4>📱 Mobile Management</h4>
                                    <p>Manage your library from anywhere, anytime</p>
                                </div>
                            </div>
                        </div>

                        <div class="carousel-slide">
                            <img src="/static/img/portfolio/WhatsApp Image 2025-05-28 at 17.26.14 (1).jpeg" alt="Library Success Story 4" loading="lazy">
                            <div class="slide-overlay">
                                <div class="slide-content">
                                    <h4>💰 Revenue Growth</h4>
                                    <p>Libraries report 300% efficiency improvement</p>
                                </div>
                            </div>
                        </div>

                        <div class="carousel-slide">
                            <img src="/static/img/portfolio/WhatsApp Image 2025-05-28 at 17.26.14.jpeg" alt="Library Success Story 5" loading="lazy">
                            <div class="slide-overlay">
                                <div class="slide-content">
                                    <h4>🏆 Success Stories</h4>
                                    <p>Join hundreds of satisfied library owners</p>
                                </div>
                            </div>
                        </div>

                        <div class="carousel-slide">
                            <img src="/static/img/portfolio/WhatsApp Image 2025-05-29 at 18.08.08.jpeg" alt="Library Success Story 6" loading="lazy">
                            <div class="slide-overlay">
                                <div class="slide-content">
                                    <h4>🚀 Future Ready</h4>
                                    <p>Be part of the library revolution in Prayagraj</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Arrows -->
                <button class="carousel-btn carousel-btn-prev" aria-label="Previous slide">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="carousel-btn carousel-btn-next" aria-label="Next slide">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <!-- Swipe Indicator -->
                <div class="swipe-indicator">
                    <i class="fas fa-hand-pointer"></i>
                    <span>Swipe to explore</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section section" id="features">
        <div class="container">
            <h2 class="section-title fade-in">Why Choose Librainian?</h2>
            <p class="section-subtitle fade-in">Discover the powerful features that make Librainian the #1 choice for modern libraries</p>
            <div class="features-grid">
                <div class="feature-card fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">Mobile-First Design</h3>
                    <p class="feature-description">Access your library management system from anywhere, anytime with our responsive mobile app and web interface.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="feature-title">Student Management</h3>
                    <p class="feature-description">Streamlined registration, attendance tracking, and communication tools to keep students engaged.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">Real-Time Analytics</h3>
                    <p class="feature-description">Get insights into library usage, student performance, and revenue with comprehensive analytics dashboard.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">Secure & Reliable</h3>
                    <p class="feature-description">Bank-level security with automated backups ensures your data is always safe and accessible.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <h3 class="feature-title">Smart Notifications</h3>
                    <p class="feature-description">Automated SMS and push notifications keep students and staff informed about important updates.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <h3 class="feature-title">Payment Integration</h3>
                    <p class="feature-description">Seamless purchase experience with zero human intervention, fully automated from click to confirmation</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section section">
        <div class="container">
            <h2 class="section-title fade-in">What Library Owners Say</h2>
            <p class="section-subtitle fade-in">Join hundreds of satisfied library owners who have transformed their operations with Librainian</p>
            <div class="testimonials-grid">
                <div class="testimonial-card fade-in">
                    <div class="testimonial-quote">
                        "Librainian completely transformed our library operations! The mobile app makes it so easy to manage everything from student registrations to fee collection. Our efficiency has increased by 300%."
                    </div>
                    <div class="testimonial-author">Amit Tiwari</div>
                    <div class="testimonial-role">Old Katra, Prayagraj</div>
                </div>
                <div class="testimonial-card fade-in">
                    <div class="testimonial-quote">
                        "The real-time analytics and automated notifications have been game-changers. We can now track student attendance and send updates instantly. Highly recommended!"
                    </div>
                    <div class="testimonial-author">Nihal Shukla</div>
                    <div class="testimonial-role">Saroli, Prayagraj</div>
                </div>
                <div class="testimonial-card fade-in">
                    <div class="testimonial-quote">
                        "Best investment we made for our library. The flexible payment options—monthly, yearly, semi-annual, with support for cards, coupons, and cashback offers—along with automated invoicing, have saved us countless hours. Customer support is excellent too!"
                    </div>
                    <div class="testimonial-author">Aatish Sharma</div>
                    <div class="testimonial-role">Chota Baghara, Prayagraj</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Exclusive Offers Section -->
    <section class="offers-section section">
        <div class="container">
            <h2 class="section-title fade-in">🎉 Exclusive Launch Offers for Prayagraj!</h2>
            <p class="section-subtitle fade-in">Limited time offers to celebrate our launch in Prayagraj - Don't miss out!</p>

            <div class="offers-grid">
                <!-- Coupon Offer -->
                <div class="offer-card special-offer fade-in">
                    <div class="offer-badge">EXCLUSIVE</div>
                    <div class="offer-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <h3 class="offer-title">Get Special Coupon</h3>
                    <p class="offer-description">Ask your Librainian distributor for exclusive discount coupons or call us directly!</p>
                    <div class="offer-cta">
                        <a href="tel:+916207628282" class="offer-btn">
                            <i class="fas fa-phone"></i> Call Now
                        </a>
                    </div>
                </div>

                <!-- Refer and Earn -->
                <div class="offer-card fade-in">
                    <div class="offer-badge">REFER & EARN</div>
                    <div class="offer-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="offer-title">15% Off Next Month</h3>
                    <p class="offer-description">Refer one library and get 15% discount on your next month subscription!</p>
                    <div class="offer-highlight">1 Library = 15% OFF</div>
                    <div class="offer-cta">
                        <a href="https://wa.me/916207628282?text=I%20want%20to%20refer%20a%20library%20for%2015%25%20discount!" class="offer-btn">
                            <i class="fas fa-share-alt"></i> Start Referring
                        </a>
                    </div>
                </div>

                <!-- Instagram Share -->
                <div class="offer-card fade-in">
                    <div class="offer-badge">SOCIAL MEDIA</div>
                    <div class="offer-icon">
                        <i class="fab fa-instagram"></i>
                    </div>
                    <h3 class="offer-title">Share & Save 25%</h3>
                    <p class="offer-description">Share your Librainian experience on Instagram and tag @Librainian.app</p>
                    <div class="offer-highlight">Get 25% OFF Next Month!</div>
                    <div class="offer-cta">
                        <a href="https://instagram.com/librainian.app" target="_blank" class="offer-btn">
                            <i class="fab fa-instagram"></i> Follow & Share
                        </a>
                    </div>
                </div>

                <!-- Yearly Package -->
                <div class="offer-card popular-offer fade-in">
                    <div class="offer-badge">MOST POPULAR</div>
                    <div class="offer-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h3 class="offer-title">Yearly Package Deal</h3>
                    <p class="offer-description">Subscribe to our yearly package and get 25% discount on your next month!</p>
                    <div class="offer-highlight">25% OFF + Premium Support</div>
                    <div class="offer-cta">
                        <a href="/membership/" class="offer-btn">
                            <i class="fas fa-crown"></i> Get Yearly Plan
                        </a>
                    </div>
                </div>

                <!-- Achievement Rewards -->
                <div class="offer-card premium-offer fade-in">
                    <div class="offer-badge">PREMIUM REWARDS</div>
                    <div class="offer-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 class="offer-title">Achievement Rewards</h3>
                    <p class="offer-description">Complete 500 invoices + 250 registrations and unlock amazing rewards!</p>
                    <div class="offer-benefits">
                        <div class="benefit-item">
                            <i class="fas fa-gift"></i> 20% Discount
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-box"></i> Gift Hamper
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-star"></i> Brand Sponsorship Chance
                        </div>
                    </div>
                    <div class="offer-cta">
                        <a href="/download-app/" class="offer-btn">
                            <i class="fas fa-rocket"></i> Start Journey
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content fade-in">
                <h2 class="cta-title">Ready to Transform Your Library?</h2>
                <p class="cta-description">Join the digital revolution on June 9, 2025! Be among the first libraries in Prayagraj to experience the future of library management. <strong>Don't miss our exclusive launch offers!</strong></p>
                <div class="cta-buttons">
                    <a href="https://play.google.com/store/apps/details?id=com.librainian.twa&pcampaignid=web_share" class="btn-primary">
                        <i class="fas fa-download"></i> Download App
                    </a>
                    <a href="https://calendly.com/pinakventure/30min" class="btn-secondary">
                        <i class="fas fa-calendar-alt"></i> Book Demo
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Sticky WhatsApp Button -->
    <a href="https://wa.me/916207628282?text=Hi!%20I'm%20interested%20in%20Librainian%20for%20my%20library%20in%20Prayagraj.%20Can%20you%20tell%20me%20more%20about%20the%20June%209%20launch%20and%20exclusive%20offers?"
       class="sticky-cta"
       target="_blank"
       rel="noopener">
        <i class="fab fa-whatsapp"></i> Chat with Us
    </a>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe all fade-in elements
        document.addEventListener('DOMContentLoaded', () => {
            const fadeElements = document.querySelectorAll('.fade-in');
            fadeElements.forEach(el => observer.observe(el));
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Track button clicks for analytics
        document.querySelectorAll('.cta-btn, .btn-primary, .btn-secondary, .sticky-cta, .offer-btn').forEach(button => {
            button.addEventListener('click', function() {
                if (typeof gtag !== 'undefined') {
                    const offerCard = this.closest('.offer-card');
                    const offerType = offerCard ? offerCard.querySelector('.offer-title')?.textContent : 'Unknown';

                    gtag('event', 'click', {
                        event_category: offerCard ? 'Offer_CTA' : 'CTA',
                        event_label: offerType || this.textContent.trim(),
                        value: 1
                    });
                }
            });
        });

        // Page view tracking for existing system
        function sendPageData() {
            const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

            if (Object.keys(pageData).length > 0) {
                fetch(location.origin + "/librarian/track-page-view/", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRFToken": document.querySelector('[name=csrfmiddlewaretoken]')?.value || '',
                    },
                    body: JSON.stringify(pageData),
                })
                .then(() => {
                    localStorage.removeItem("page_data");
                })
                .catch((error) => console.error("Error sending page data:", error));
            }
        }

        // Send data every 10 seconds
        setInterval(sendPageData, 10000);

        // Send data when page is about to unload
        window.addEventListener('beforeunload', sendPageData);

        // Instagram-style Carousel Functionality
        class InstagramCarousel {
            constructor(carouselElement) {
                this.carousel = carouselElement;
                this.track = this.carousel.querySelector('.carousel-track');
                this.slides = this.carousel.querySelectorAll('.carousel-slide');
                this.dots = this.carousel.querySelectorAll('.dot');
                this.prevBtn = this.carousel.querySelector('.carousel-btn-prev');
                this.nextBtn = this.carousel.querySelector('.carousel-btn-next');
                this.currentSlideSpan = this.carousel.querySelector('.current-slide');
                this.totalSlidesSpan = this.carousel.querySelector('.total-slides');

                this.currentSlide = 0;
                this.totalSlides = this.slides.length;

                this.init();
            }

            init() {
                // Set total slides
                this.totalSlidesSpan.textContent = this.totalSlides;

                // Add event listeners
                this.prevBtn.addEventListener('click', () => this.prevSlide());
                this.nextBtn.addEventListener('click', () => this.nextSlide());

                // Dot navigation
                this.dots.forEach((dot, index) => {
                    dot.addEventListener('click', () => this.goToSlide(index));
                });

                // Touch/swipe support
                this.addTouchSupport();

                // Auto-play (optional)
                this.startAutoPlay();

                // Keyboard navigation
                this.addKeyboardSupport();
            }

            updateCarousel() {
                // Move track
                const translateX = -this.currentSlide * 100;
                this.track.style.transform = `translateX(${translateX}%)`;

                // Update counter
                this.currentSlideSpan.textContent = this.currentSlide + 1;

                // Update dots
                this.dots.forEach((dot, index) => {
                    dot.classList.toggle('active', index === this.currentSlide);
                });

                // Update slide states
                this.slides.forEach((slide, index) => {
                    slide.classList.toggle('active', index === this.currentSlide);
                });
            }

            nextSlide() {
                this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
                this.updateCarousel();
                this.resetAutoPlay();
            }

            prevSlide() {
                this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
                this.updateCarousel();
                this.resetAutoPlay();
            }

            goToSlide(index) {
                this.currentSlide = index;
                this.updateCarousel();
                this.resetAutoPlay();
            }

            addTouchSupport() {
                let startX = 0;
                let currentX = 0;
                let isDragging = false;

                this.track.addEventListener('touchstart', (e) => {
                    startX = e.touches[0].clientX;
                    isDragging = true;
                });

                this.track.addEventListener('touchmove', (e) => {
                    if (!isDragging) return;
                    currentX = e.touches[0].clientX;
                });

                this.track.addEventListener('touchend', () => {
                    if (!isDragging) return;
                    isDragging = false;

                    const diffX = startX - currentX;
                    const threshold = 50;

                    if (Math.abs(diffX) > threshold) {
                        if (diffX > 0) {
                            this.nextSlide();
                        } else {
                            this.prevSlide();
                        }
                    }
                });

                // Mouse drag support for desktop
                let mouseDown = false;

                this.track.addEventListener('mousedown', (e) => {
                    startX = e.clientX;
                    mouseDown = true;
                    this.track.style.cursor = 'grabbing';
                });

                this.track.addEventListener('mousemove', (e) => {
                    if (!mouseDown) return;
                    currentX = e.clientX;
                });

                this.track.addEventListener('mouseup', () => {
                    if (!mouseDown) return;
                    mouseDown = false;
                    this.track.style.cursor = 'grab';

                    const diffX = startX - currentX;
                    const threshold = 50;

                    if (Math.abs(diffX) > threshold) {
                        if (diffX > 0) {
                            this.nextSlide();
                        } else {
                            this.prevSlide();
                        }
                    }
                });

                this.track.addEventListener('mouseleave', () => {
                    mouseDown = false;
                    this.track.style.cursor = 'grab';
                });
            }

            addKeyboardSupport() {
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowLeft') {
                        this.prevSlide();
                    } else if (e.key === 'ArrowRight') {
                        this.nextSlide();
                    }
                });
            }

            startAutoPlay() {
                this.autoPlayInterval = setInterval(() => {
                    this.nextSlide();
                }, 5000); // Change slide every 5 seconds
            }

            resetAutoPlay() {
                clearInterval(this.autoPlayInterval);
                this.startAutoPlay();
            }

            stopAutoPlay() {
                clearInterval(this.autoPlayInterval);
            }
        }

        // Initialize carousel when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            const carouselElement = document.querySelector('.instagram-carousel');
            if (carouselElement) {
                new InstagramCarousel(carouselElement);
            }
        });
    </script>
</body>

</html>