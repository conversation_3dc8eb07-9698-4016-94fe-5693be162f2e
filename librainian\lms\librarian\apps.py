from django.apps import AppConfig


class LibrarianConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'librarian'

    def ready(self):
        """Import signals when Django starts"""
        try:
            from . import membership_signals
            membership_signals.register_membership_signals()
        except ImportError:
            pass

    def ready(self):
        import librarian.notification_signals
        import librarian.signals  # Import analytics cache signals
