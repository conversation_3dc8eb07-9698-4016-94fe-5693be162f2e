"""
Django signals for immediate membership expiry notifications
These signals trigger immediately when membership data is saved in Django admin
No continuous monitoring - only triggers on admin save actions
"""

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender='membership.Membership')
def check_membership_expiry_on_save(sender, instance, created, **kwargs):
    """
    Trigger immediate notification check when membership is saved in admin
    This runs every time you save a membership in Django admin
    """
    try:
        from django.contrib.auth.models import User
        from librarian.notification_events import notification_events
        
        today = timezone.now().date()
        expiry_date = instance.expiry_date
        
        # Calculate days until/since expiry
        days_diff = (expiry_date - today).days
        
        logger.info(f"Membership saved: {instance.librarian.user.username}, expires {expiry_date}, days_diff: {days_diff}")
        
        # Get librarians to notify
        librarians = User.objects.filter(
            groups__name__in=['Librarian', 'SubLibrarian']
        )
        
        # If no librarians in groups, use superusers
        if not librarians.exists():
            librarians = User.objects.filter(is_superuser=True)
        
        # Check if this membership falls into any notification period
        notification_triggered = False
        
        # Define notification periods and their event types
        notification_periods = {
            10: 'member_expiry_10_days',
            5: 'member_expiry_5_days', 
            1: 'member_expiry_1_day',
            0: 'member_expired',
            -4: 'member_expired_4_days'
        }
        
        # Check if current membership matches any notification period
        for period_days, event_type in notification_periods.items():
            if days_diff == period_days:
                # Send immediate notification
                result = notification_events.send_notification(
                    event_type,
                    librarians,
                    member_name=instance.librarian.user.get_full_name() or instance.librarian.user.username,
                    member_email=instance.librarian.user.email,
                    member_mobile='Not available',
                    course='Librarian Membership',
                    expiry_date=instance.expiry_date.strftime('%Y-%m-%d'),
                    days_info=f"{'expires' if period_days >= 0 else 'expired'} {abs(period_days)} days {'from now' if period_days > 0 else ('today' if period_days == 0 else 'ago')}",
                    member_id=instance.pk,
                    unique_id=f"LIB_{instance.pk}",
                    plan_name=instance.plan.name,
                    library_name=getattr(instance.librarian, 'library_name', 'Library'),
                    start_date=instance.start_date.strftime('%Y-%m-%d'),
                    membership_duration=(instance.expiry_date - instance.start_date).days,
                    trigger_source='admin_save'
                )
                
                notification_triggered = True
                logger.info(f"Immediate notification sent for membership {instance.pk}: {event_type}")
                break
        
        # If no exact match but within notification range, send a general alert
        if not notification_triggered and -10 <= days_diff <= 15:
            # Send a general membership status notification
            if days_diff > 0:
                status = f"expires in {days_diff} days"
                priority = "normal" if days_diff > 5 else "high"
            elif days_diff == 0:
                status = "expires today"
                priority = "urgent"
            else:
                status = f"expired {abs(days_diff)} days ago"
                priority = "urgent"
            
            # Send custom notification
            from librarian.notification_utils import send_notification_to_all_users
            
            result = send_notification_to_all_users(
                title=f"🏛️ Membership Status Update",
                body=f"Membership updated in admin:\n\nMember: {instance.librarian.user.get_full_name() or instance.librarian.user.username}\nPlan: {instance.plan.name}\nStatus: {status}\nExpiry Date: {instance.expiry_date.strftime('%Y-%m-%d')}\nLibrary: {getattr(instance.librarian, 'library_name', 'Library')}\n\nUpdated via Django Admin",
                data={
                    "type": "membership_admin_update",
                    "member_id": instance.pk,
                    "expiry_date": instance.expiry_date.isoformat(),
                    "days_diff": days_diff,
                    "status": status,
                    "priority": priority,
                    "trigger_source": "admin_save"
                }
            )
            
            logger.info(f"General membership update notification sent for {instance.librarian.user.username}: {status}")
        
    except Exception as e:
        logger.error(f"Error in membership expiry signal: {e}")


@receiver(pre_save, sender='membership.Membership')
def log_membership_changes(sender, instance, **kwargs):
    """Log membership changes for debugging"""
    try:
        if instance.pk:  # Existing membership being updated
            old_instance = sender.objects.get(pk=instance.pk)
            if old_instance.expiry_date != instance.expiry_date:
                logger.info(f"Membership expiry changed: {instance.librarian.user.username} from {old_instance.expiry_date} to {instance.expiry_date}")
        else:  # New membership
            logger.info(f"New membership created: {instance.librarian.user.username}, expires {instance.expiry_date}")
    except Exception as e:
        logger.error(f"Error logging membership changes: {e}")


# Auto-register signals when this module is imported
def register_membership_signals():
    """Ensure signals are registered"""
    logger.info("Membership expiry signals registered")
