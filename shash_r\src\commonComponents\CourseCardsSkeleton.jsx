import React from 'react'
import { Card, Col, Row } from 'react-bootstrap'
import Skeleton from 'react-loading-skeleton'

const CourseCardsSkeleton = ({number}) => {
  return (
    <>
        <Row>
    {[...Array(number)].map((_, index) => ( // Show 6 skeletons while loading
      <Col key={index} xs={12} sm={6} md={6} lg={4}>
        <Card className="shadow-lg rounded-3 mb-4">
          <Card.Body>
            <div className="d-flex align-items-center">
              <Skeleton height={40} width={40} baseColor="#e6ffe6" highlightColor="#c4f7c4" className="me-3" />
              <Skeleton height={20} width={70} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
            </div>
            <Skeleton height={15} width="90%" baseColor="#e6ffe6" highlightColor="#c4f7c4" className="mt-2" />
            <div className="d-flex flex-wrap justify-content-center mt-3">
              <Skeleton height={35} width={60} baseColor="#e6ffe6" highlightColor="#c4f7c4" className="m-1" />
              <Skeleton height={35} width={60} baseColor="#e6ffe6" highlightColor="#c4f7c4" className="m-1" />
              <Skeleton height={35} width={40} baseColor="#e6ffe6" highlightColor="#c4f7c4" className="m-1" />
              <Skeleton height={35} width={40} baseColor="#e6ffe6" highlightColor="#c4f7c4" className="m-1" />
            </div>
          </Card.Body>
        </Card>
      </Col>
    ))}
  </Row>
    </>
  )
}

export default CourseCardsSkeleton
