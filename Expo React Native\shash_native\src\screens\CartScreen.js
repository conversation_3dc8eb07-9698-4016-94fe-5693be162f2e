import React, { useContext, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/FontAwesome';
import { ThemeContext } from '../context/ThemeContext';
import { clearCart, loadCartFromStorage, saveCartToStorage, removeFromCart } from '../redux/cartSlice';
import CartSummary from '../components/CartSummary';
import { LoadingSpinner } from '../components/LoadingSpinner';
import {
  trackScreenView,
  trackButtonPress,
  trackNavigation,
  trackContentView
} from '../utils/screenAnalytics';
import BottomTabBar from '../components/BottomTabBar';

const CartScreen = ({ navigation }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const { items, total, itemCount, loading, error } = useSelector((state) => state.cart);
  const { isAuthenticated } = useSelector((state) => state.auth);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedItems, setSelectedItems] = useState(items.map(item => item.id));

  useEffect(() => {
    // Track screen view
    trackScreenView('Cart', {
      item_count: itemCount,
      cart_total: total,
      is_authenticated: isAuthenticated,
    });

    // Load cart from storage when component mounts
    dispatch(loadCartFromStorage());
  }, [dispatch]);

  useEffect(() => {
    // Save cart to storage whenever cart changes
    if (items.length > 0 || total > 0) {
      dispatch(saveCartToStorage({ items, total, itemCount }));
    }
  }, [items, total, itemCount, dispatch]);

  useEffect(() => {
    setSelectedItems(items.map(item => item.id)); // Reset selection when cart changes
  }, [items]);

  const handleRefresh = async () => {
    trackButtonPress('refresh_cart', 'Cart');
    setRefreshing(true);
    await dispatch(loadCartFromStorage());
    setRefreshing(false);
  };

  const handleClearCart = () => {
    trackButtonPress('clear_cart_prompt', 'Cart', {
      item_count: itemCount,
      cart_total: total,
    });

    Alert.alert(
      'Clear Cart',
      'Are you sure you want to remove all items from your cart?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => {
            trackButtonPress('clear_cart_cancelled', 'Cart');
          },
        },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: () => {
            trackButtonPress('clear_cart_confirmed', 'Cart', {
              item_count: itemCount,
              cart_total: total,
            });
            dispatch(clearCart());
          },
        },
      ]
    );
  };

  // Calculate selected items, total, and count at the top level so they are available for rendering and checkout
  const selectedCartItems = items.filter(item => selectedItems.includes(item.id));
  const selectedTotal = selectedCartItems.reduce((sum, item) => sum + Number(item.price) * (item.quantity || 1), 0);
  const selectedCount = selectedCartItems.length;

  const handleCheckout = () => {
    trackButtonPress('checkout_initiated', 'Cart', {
      item_count: itemCount,
      cart_total: total,
      is_authenticated: isAuthenticated,
    });

    if (!isAuthenticated) {
      trackNavigation('Cart', 'Login', 'checkout_login_required');
      Alert.alert(
        'Login Required',
        'Please login to proceed with checkout.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => {
              trackButtonPress('checkout_login_cancelled', 'Cart');
            },
          },
          {
            text: 'Login',
            onPress: () => {
              trackButtonPress('checkout_login_confirmed', 'Cart');
              navigation.navigate('Auth', { screen: 'Login' });
            },
          },
        ]
      );
      return;
    }

    if (selectedItems.length === 0) {
      trackButtonPress('checkout_empty_cart', 'Cart');
      Alert.alert('No Items Selected', 'Please select at least one item to proceed.');
      return;
    }

    trackNavigation('Cart', 'PaymentScreen', 'checkout', {
      item_count: selectedCartItems.length,
      cart_total: selectedTotal,
    });

    // Navigate via parent to ensure correct stack
    const parent = navigation.getParent();
    if (parent) {
      parent.navigate('MainTabs', {
        screen: 'HomeTab', // Change this to the correct tab if needed
        params: {
          screen: 'PaymentScreen',
          params: {
            cartItems: selectedCartItems,
            cartTotal: selectedTotal,
            cartItemCount: selectedCount,
          },
        },
      });
    } else {
      navigation.navigate('PaymentScreen', {
        cartItems: selectedCartItems,
        cartTotal: selectedTotal,
        cartItemCount: selectedCount,
      });
    }
  };

  const handleItemPress = (item) => {
    // Navigate to item details if needed
    if (item.type === 'package') {
      // Navigate through the proper navigation hierarchy
      const parent = navigation.getParent();
      if (parent) {
        parent.navigate('MainTabs', {
          screen: 'HomeTab',
          params: {
            screen: 'PackagesScreen'
          }
        });
      } else {
        navigation.navigate('PackagesScreen');
      }
    }
    // Add more navigation logic for other item types
  };

  const handleRemoveItem = (item) => {
    Alert.alert(
      'Remove Item',
      `Are you sure you want to remove "${item.name}" from your cart?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            dispatch(removeFromCart({
              id: item.id,
              type: item.type
            }));
          },
        },
      ]
    );
  };

  const handleToggleSelect = (itemId) => {
    setSelectedItems((prev) =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const formatPrice = (price) => {
    return `₹${price.toLocaleString('en-IN')}`;
  };

  const renderCartItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.cartItem,
        isDarkMode && styles.cartItemDark
      ]}
      onPress={() => handleItemPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.itemContent}>
        {/* Select/Unselect Checkbox */}
        <TouchableOpacity
          onPress={() => handleToggleSelect(item.id)}
          style={{ marginRight: 10 }}
        >
          <Icon
            name={selectedItems.includes(item.id) ? 'check-square' : 'square-o'}
            size={22}
            color={isDarkMode ? '#4CAF50' : '#198754'}
          />
        </TouchableOpacity>

        {/* Item Image Placeholder */}
        <View style={[
          styles.itemImagePlaceholder,
          isDarkMode && styles.itemImagePlaceholderDark
        ]}>
          <Icon
            name={item.type === 'package' ? 'cube' : 'shopping-bag'}
            size={24}
            color={isDarkMode ? '#666' : '#999'}
          />
        </View>

        {/* Item Details */}
        <View style={styles.itemDetails}>
          <Text style={[
            styles.itemName,
            isDarkMode && styles.textDark
          ]} numberOfLines={2}>
            {item.name}
          </Text>

          {item.description && (
            <Text style={[
              styles.itemDescription,
              isDarkMode && styles.itemDescriptionDark
            ]} numberOfLines={2}>
              {item.description}
            </Text>
          )}

          <Text style={[
            styles.itemPrice,
            isDarkMode && styles.textDark
          ]}>
            {formatPrice(item.price)}
          </Text>
        </View>

        {/* Remove Button */}
        <TouchableOpacity
          style={[
            styles.removeButton,
            isDarkMode && styles.removeButtonDark
          ]}
          onPress={() => handleRemoveItem(item)}
          activeOpacity={0.7}
        >
          <Icon
            name="trash"
            size={16}
            color={isDarkMode ? '#ff6b6b' : '#dc3545'}
          />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyCart = () => (
    <View style={[
      styles.emptyContainer,
      isDarkMode && styles.emptyContainerDark
    ]}>
      <Icon
        name="shopping-cart"
        size={80}
        color={isDarkMode ? '#555' : '#ccc'}
      />
      <Text style={[
        styles.emptyTitle,
        isDarkMode && styles.emptyTextDark
      ]}>
        Your cart is empty
      </Text>
      <Text style={[
        styles.emptySubtitle,
        isDarkMode && styles.emptySubtitleDark
      ]}>
        Add some items to get started
      </Text>
      <TouchableOpacity
        style={[
          styles.shopButton,
          isDarkMode && styles.shopButtonDark
        ]}
        onPress={() => {
          // Navigate through the proper navigation hierarchy
          const parent = navigation.getParent();
          if (parent) {
            parent.navigate('MainTabs', {
              screen: 'HomeTab',
              params: {
                screen: 'PackagesScreen'
              }
            });
          } else {
            navigation.navigate('PackagesScreen');
          }
        }}
        activeOpacity={0.8}
      >
        <Icon name="cube" size={16} color="#fff" style={styles.shopIcon} />
        <Text style={styles.shopButtonText}>Browse Packages</Text>
      </TouchableOpacity>
    </View>
  );

  const renderHeader = () => (
    <View style={[
      styles.header,
      isDarkMode && styles.headerDark
    ]}>
      <View style={styles.headerLeft}>
        {/* <Text style={[
          styles.headerTitle,
          isDarkMode && styles.headerTitleDark
        ]}>
          Total Items on your Cart
        </Text> */}
        {itemCount > 0 && (
          <Text style={[
            styles.headerSubtitle,
            isDarkMode && styles.headerSubtitleDark
          ]}>
            {itemCount} {itemCount === 1 ? 'item' : 'items'}
          </Text>
        )}
      </View>
      {itemCount > 0 && (
        <TouchableOpacity
          style={[
            styles.clearButton,
            isDarkMode && styles.clearButtonDark
          ]}
          onPress={handleClearCart}
          activeOpacity={0.7}
        >
          <Icon name="trash" size={16} color="#dc3545" />
          <Text style={styles.clearButtonText}>Clear</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={[
        styles.loadingContainer,
        isDarkMode && styles.loadingContainerDark
      ]}>
        <LoadingSpinner />
      </View>
    );
  }

  return (
    <BottomTabBar>
      <View style={[
        styles.container,
        isDarkMode && styles.containerDark
      ]}>
        {renderHeader()}
        {itemCount === 0 ? (
          renderEmptyCart()
        ) : (
          <>
            <FlatList
              data={items}
              renderItem={renderCartItem}
              keyExtractor={(item) => `${item.id}-${item.type}`}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={handleRefresh}
                  colors={['#198754']}
                  tintColor={isDarkMode ? '#4CAF50' : '#198754'}
                />
              }
              contentContainerStyle={styles.listContent}
            />
            <CartSummary
              onCheckout={handleCheckout}
              checkoutButtonText="Proceed to Payment"
              items={selectedCartItems}
              total={selectedTotal}
              itemCount={selectedCount}
            />
          </>
        )}
      </View>
    </BottomTabBar>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingContainerDark: {
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#1e1e1e',
    borderBottomColor: '#333',
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
  },
  headerTitleDark: {
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  headerSubtitleDark: {
    color: '#999',
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#f8f9fa',
  },
  clearButtonDark: {
    backgroundColor: '#333',
  },
  clearButtonText: {
    color: '#dc3545',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  listContent: {
    paddingBottom: 20,
  },
  cartItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginVertical: 6,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cartItemDark: {
    backgroundColor: '#1e1e1e',
  },
  itemContent: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  itemImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  itemImagePlaceholderDark: {
    backgroundColor: '#333',
  },
  itemDetails: {
    flex: 1,
    marginRight: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  itemDescriptionDark: {
    color: '#999',
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: '700',
    color: '#198754',
  },
  removeButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: '#f8f9fa',
  },
  removeButtonDark: {
    backgroundColor: '#333',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyContainerDark: {
    backgroundColor: '#121212',
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333',
    marginTop: 20,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  emptyTextDark: {
    color: '#fff',
  },
  emptySubtitleDark: {
    color: '#999',
  },
  shopButton: {
    backgroundColor: '#198754',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  shopButtonDark: {
    backgroundColor: '#4CAF50',
  },
  shopIcon: {
    marginRight: 8,
  },
  shopButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CartScreen;
