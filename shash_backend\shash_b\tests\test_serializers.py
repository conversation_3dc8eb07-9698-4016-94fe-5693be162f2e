#!/usr/bin/env python3
"""
Serializer Testing Script for Course-Related Django Components
This script performs comprehensive testing of all course-related serializers.
"""

import os
import sys
import django
import traceback
from datetime import timed<PERSON><PERSON>

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Import models and serializers after Django setup
from django.contrib.auth.models import User
from questions.models import (
    Course, SubCourse, Tier, Paper, Section, Module, Subject, Topic, SubTopic,
    MasterQuestion, MasterOption, Question, Option, PreviousYearQuestion
)
from questions.serializers import (
    CourseSerializer, SubCourseSerializer, TierSerializer, PaperSerializer,
    SectionSerializer, ModuleSerializer, SubjectSerializer, TopicSerializer,
    SubTopicSerializer, QuestionSerializer, OptionSerializer,
    MasterQuestionSerializer, MasterOptionSerializer, PreviousYearQuestionSerializer
)
from contributor.models import ContributorProfile
from students.models import Student


class TestResults:
    """Class to track test results."""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"✅ PASS: {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"❌ FAIL: {test_name} - {error}")
    
    def summary(self):
        total = self.passed + self.failed
        print(f"\n{'='*60}")
        print(f"SERIALIZER TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success Rate: {(self.passed/total*100):.1f}%" if total > 0 else "No tests run")
        
        if self.errors:
            print(f"\n{'='*60}")
            print(f"FAILED TESTS:")
            print(f"{'='*60}")
            for error in self.errors:
                print(f"- {error}")


def test_course_serializer():
    """Test CourseSerializer functionality."""
    results = TestResults()
    
    try:
        # Test 1: Course serialization
        course = Course.objects.create(name='Test Course', description='Test Description')
        serializer = CourseSerializer(course)
        data = serializer.data
        
        if (data['name'] == 'Test Course' and 
            data['description'] == 'Test Description' and
            'course_id' in data and 'slug' in data):
            results.add_pass("Course serialization")
        else:
            results.add_fail("Course serialization", "Missing or incorrect fields")
        
        # Test 2: Course deserialization
        course_data = {
            'name': 'New Course',
            'description': 'New Description'
        }
        serializer = CourseSerializer(data=course_data)
        if serializer.is_valid():
            new_course = serializer.save()
            if new_course.name == 'New Course':
                results.add_pass("Course deserialization")
            else:
                results.add_fail("Course deserialization", "Course not created correctly")
        else:
            results.add_fail("Course deserialization", f"Validation errors: {serializer.errors}")
        
        # Test 3: Course with SubCourses
        subcourse = SubCourse.objects.create(course=course, name='Test SubCourse')
        serializer = CourseSerializer(course)
        data = serializer.data
        
        if 'sub_courses' in data and len(data['sub_courses']) > 0:
            results.add_pass("Course with SubCourses serialization")
        else:
            results.add_fail("Course with SubCourses serialization", "SubCourses not included")
        
        # Cleanup
        Course.objects.filter(name__in=['Test Course', 'New Course']).delete()
        
    except Exception as e:
        results.add_fail("Course serializer tests", f"Exception: {str(e)}")
    
    return results


def test_subject_serializer():
    """Test SubjectSerializer functionality."""
    results = TestResults()
    
    try:
        # Test 1: Subject serialization
        subject = Subject.objects.create(name='Mathematics', description='Math subject', rank=1)
        serializer = SubjectSerializer(subject)
        data = serializer.data
        
        if (data['name'] == 'Mathematics' and 
            data['description'] == 'Math subject' and
            data['rank'] == 1 and
            'subject_id' in data and 'slug' in data):
            results.add_pass("Subject serialization")
        else:
            results.add_fail("Subject serialization", "Missing or incorrect fields")
        
        # Test 2: Subject deserialization
        subject_data = {
            'name': 'Physics',
            'description': 'Physics subject',
            'rank': 2
        }
        serializer = SubjectSerializer(data=subject_data)
        if serializer.is_valid():
            new_subject = serializer.save()
            if new_subject.name == 'Physics' and new_subject.rank == 2:
                results.add_pass("Subject deserialization")
            else:
                results.add_fail("Subject deserialization", "Subject not created correctly")
        else:
            results.add_fail("Subject deserialization", f"Validation errors: {serializer.errors}")
        
        # Test 3: Subject with Topics
        topic = Topic.objects.create(subject=subject, name='Algebra')
        serializer = SubjectSerializer(subject)
        data = serializer.data
        
        if 'topics' in data and len(data['topics']) > 0:
            results.add_pass("Subject with Topics serialization")
        else:
            results.add_fail("Subject with Topics serialization", "Topics not included")
        
        # Cleanup
        Subject.objects.filter(name__in=['Mathematics', 'Physics']).delete()
        
    except Exception as e:
        results.add_fail("Subject serializer tests", f"Exception: {str(e)}")
    
    return results


def test_topic_serializer():
    """Test TopicSerializer functionality."""
    results = TestResults()
    
    try:
        # Setup
        subject = Subject.objects.create(name='Mathematics')
        
        # Test 1: Topic serialization
        topic = Topic.objects.create(subject=subject, name='Algebra', description='Algebra topic')
        serializer = TopicSerializer(topic)
        data = serializer.data
        
        if (data['name'] == 'Algebra' and 
            data['description'] == 'Algebra topic' and
            'topic_id' in data and 'slug' in data and 'subject' in data):
            results.add_pass("Topic serialization")
        else:
            results.add_fail("Topic serialization", "Missing or incorrect fields")
        
        # Test 2: Topic with SubTopics
        subtopic = SubTopic.objects.create(topic=topic, name='Linear Equations')
        serializer = TopicSerializer(topic)
        data = serializer.data
        
        if 'subtopics' in data and len(data['subtopics']) > 0:
            results.add_pass("Topic with SubTopics serialization")
        else:
            results.add_fail("Topic with SubTopics serialization", "SubTopics not included")
        
        # Cleanup
        topic.delete()
        subject.delete()
        
    except Exception as e:
        results.add_fail("Topic serializer tests", f"Exception: {str(e)}")
    
    return results


def test_question_serializer():
    """Test QuestionSerializer functionality."""
    results = TestResults()
    
    try:
        # Setup
        import uuid
        unique_username = f'testcontributor_{uuid.uuid4().hex[:8]}'
        user = User.objects.create_user(username=unique_username, password='test123')
        contributor = ContributorProfile.objects.create(user=user, role='contributor')
        subject = Subject.objects.create(name='Mathematics')
        topic = Topic.objects.create(subject=subject, name='Algebra')
        course = Course.objects.create(name='Test Course')
        
        # Test 1: Question serialization
        question = Question.objects.create(
            content='What is 2 + 2?',
            difficulty=5,
            author=contributor,
            status='active'
        )
        question.subject.add(subject)
        question.topic.add(topic)
        question.course.add(course)
        
        serializer = QuestionSerializer(question)
        data = serializer.data
        
        if (data['content'] == 'What is 2 + 2?' and 
            data['difficulty'] == 5 and
            data['status'] == 'active' and
            'question_id' in data and 'slug' in data):
            results.add_pass("Question serialization")
        else:
            results.add_fail("Question serialization", "Missing or incorrect fields")
        
        # Test 2: Question with Options
        option1 = Option.objects.create(question=question, option_text='4', is_correct=True)
        option2 = Option.objects.create(question=question, option_text='5', is_correct=False)
        
        serializer = QuestionSerializer(question)
        data = serializer.data
        
        if 'options' in data and len(data['options']) == 2:
            results.add_pass("Question with Options serialization")
        else:
            results.add_fail("Question with Options serialization", "Options not included correctly")
        
        # Test 3: Question difficulty level method
        if 'subject_name' in data and 'topic_name' in data:
            results.add_pass("Question many-to-many field serialization")
        else:
            results.add_fail("Question many-to-many field serialization", "M2M fields not serialized")
        
        # Cleanup
        question.delete()
        course.delete()
        topic.delete()
        subject.delete()
        contributor.delete()
        user.delete()
        
    except Exception as e:
        results.add_fail("Question serializer tests", f"Exception: {str(e)}")
    
    return results


def test_option_serializer():
    """Test OptionSerializer functionality."""
    results = TestResults()
    
    try:
        # Setup
        import uuid
        unique_username = f'testcontributor_{uuid.uuid4().hex[:8]}'
        user = User.objects.create_user(username=unique_username, password='test123')
        contributor = ContributorProfile.objects.create(user=user, role='contributor')
        question = Question.objects.create(
            content='What is 2 + 2?',
            difficulty=5,
            author=contributor
        )
        
        # Test 1: Option serialization
        option = Option.objects.create(
            question=question,
            option_text='4',
            is_correct=True
        )
        serializer = OptionSerializer(option)
        data = serializer.data
        
        if (data['option_text'] == '4' and 
            data['is_correct'] == True and
            'option_id' in data and 'slug' in data and 'question' in data):
            results.add_pass("Option serialization")
        else:
            results.add_fail("Option serialization", "Missing or incorrect fields")
        
        # Test 2: Option deserialization
        option_data = {
            'question': question.question_id,
            'option_text': '5',
            'is_correct': False
        }
        serializer = OptionSerializer(data=option_data)
        if serializer.is_valid():
            new_option = serializer.save()
            if new_option.option_text == '5' and not new_option.is_correct:
                results.add_pass("Option deserialization")
            else:
                results.add_fail("Option deserialization", "Option not created correctly")
        else:
            results.add_fail("Option deserialization", f"Validation errors: {serializer.errors}")
        
        # Cleanup
        Option.objects.filter(question=question).delete()
        question.delete()
        contributor.delete()
        user.delete()
        
    except Exception as e:
        results.add_fail("Option serializer tests", f"Exception: {str(e)}")
    
    return results


def main():
    """Run all serializer tests."""
    print("🚀 Starting Comprehensive Serializer Tests")
    print("="*60)
    
    all_results = TestResults()
    
    # Run all test functions
    test_functions = [
        test_course_serializer,
        test_subject_serializer,
        test_topic_serializer,
        test_question_serializer,
        test_option_serializer
    ]
    
    for test_func in test_functions:
        print(f"\n📋 Running {test_func.__name__}...")
        try:
            result = test_func()
            all_results.passed += result.passed
            all_results.failed += result.failed
            all_results.errors.extend(result.errors)
        except Exception as e:
            all_results.add_fail(test_func.__name__, f"Test function failed: {str(e)}")
            print(f"💥 Error in {test_func.__name__}: {str(e)}")
            traceback.print_exc()
    
    # Print final summary
    all_results.summary()


if __name__ == '__main__':
    main()
