"""
Helper function to trigger QR registration notifications
"""

from .notification_service import notification_service
import logging

logger = logging.getLogger(__name__)


def send_qr_registration_notification(temp_student):
    """
    Send notification when someone submits registration via QR code
    
    This function should be called from your registration form view
    after a TempStudentData instance is created.
    
    Args:
        temp_student: TempStudentData instance
        
    Returns:
        NotificationHistory instance or None
    """
    try:
        result = notification_service.qr_registration_submitted(temp_student)
        if result:
            logger.info(f"QR registration notification sent successfully for {temp_student.name}")
            return result
        else:
            logger.warning(f"Failed to send QR registration notification for {temp_student.name}")
            return None
    except Exception as e:
        logger.error(f"Error sending QR registration notification: {str(e)}")
        return None


def test_qr_notification(user_id=None):
    """
    Test function to send a sample QR registration notification
    
    Args:
        user_id: ID of the user to send notification to (optional)
        
    Returns:
        NotificationHistory instance or None
    """
    from django.contrib.auth.models import User
    
    try:
        if user_id:
            user = User.objects.get(id=user_id)
        else:
            # Get first available user
            user = User.objects.filter(is_active=True).first()
            
        if not user:
            logger.error("No user found for test notification")
            return None
        
        # Create sample context data
        context_data = {
            'student_name': 'Test Student',
            'student_email': '<EMAIL>',
            'student_mobile': '9876543210',
            'course': 'Computer Science',
            'library_name': 'Test Library',
            'registration_date': '2024-01-10',
            'temp_student_id': 999,
        }
        
        result = notification_service.send_notification(
            'qr_registration', 
            user, 
            **context_data
        )
        
        if result:
            logger.info(f"Test QR registration notification sent to {user.username}")
            return result
        else:
            logger.warning(f"Failed to send test notification to {user.username}")
            return None
            
    except Exception as e:
        logger.error(f"Error sending test QR registration notification: {str(e)}")
        return None
