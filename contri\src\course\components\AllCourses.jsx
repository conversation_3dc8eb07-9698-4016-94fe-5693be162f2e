import React, { useState, useEffect } from "react";
import ReactPaginate from "react-paginate";
import { Card, Button, Row, Col, Form, Container, Modal, Dropdown } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { getCourses, getCourse, updateCourse, deleteCourse } from "../../redux/slice/courseSlice";
import ViewModal from "../../commonComponents/ViewModal";
import { FaPlusCircle } from "react-icons/fa";
import { BsPencilSquare, BsTrash } from "react-icons/bs";
import { Link } from "react-router-dom";
import Swal from 'sweetalert2';
import toast, { Toaster } from 'react-hot-toast';
import imageCompression from 'browser-image-compression';
import Skeleton from "react-loading-skeleton";
import CourseCardsSkeleton from "../../commonComponents/CourseCardsSkeleton";
import PaginationComponent from "../../commonComponents/PaginationComponent";

const AllCourses = ({ courseAdded }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(0);
  const [showViewModal, setShowViewModal] = useState(false);  
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [courseData, setCourseData] = useState({ name: "", description: "", image: null });
  const [imageError, setImageError] = useState("");
  const [imagePreview, setImagePreview] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCheckingImage, setIsCheckingImage] = useState(false);
  const [image, setImage] = useState(null);
  
  const [coursesPerPage, setCoursesPerPage] = useState(6); // Default items per page
  const [dropdownTitle, setDropdownTitle] = useState("5 per page"); // Default dropdown title

  const dispatch = useDispatch();

  const [courses, setCourses] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchCourses = async () => {
    setIsLoading(true);
    try {
      const response = await dispatch(getCourses()); // Dispatch the action to fetch courses
      if (response && response.payload) {
        const coursesData = response.payload; // Assuming the data is in the payload
        setCourses(coursesData); // Set the courses data in the state
      }
    } catch (error) {
      console.error("Error fetching courses:", error);
      toast.error("Failed to load courses"); // Show error notification if fetching fails
    } finally {
      setIsLoading(false); // Set loading to false when the fetch process is complete
    }
  };

  // Fetch courses when the component mounts or when `courseAdded` changes
  useEffect(() => {
    fetchCourses();
  }, [dispatch, courseAdded]);

  // for view modal

  const handleViewCourse = (course) => {
    setSelectedCourse(course);
    setShowViewModal(true);
  };

  const handleEditCourse = (slug) => {
    dispatch(getCourse(slug)).then((action) => {
      if (action.type === 'course/getCourse/fulfilled') {
        const course = action.payload;
        setCourseData({ name: course.name, description: course.description, image: course.attachments || null });
        setSelectedCourse(course);
        setImagePreview(course.attachments ? `${import.meta.env.VITE_BASE_URL}/${course.attachments}` : "");
        setShowEditModal(true);
      }
    });
  };

  const handleImageChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setIsCheckingImage(true);
    setImageError("");
    setImage(null);
    setImagePreview("");

    const originalSizeKB = (file.size / 1024).toFixed(2);

    if (file.size <= 20 * 1024) {
      setImage(file);
      const reader = new FileReader();
      reader.onload = () => setImagePreview(reader.result);
      reader.readAsDataURL(file);
      setIsCheckingImage(false);
    } else {
      try {
        const options = {
          maxSizeMB: 0.02,
          maxWidthOrHeight: 300,
          useWebWorker: true,
        };
        const compressedFile = await imageCompression(file, options);
        const compressedSizeKB = (compressedFile.size / 1024).toFixed(2);

        if (compressedFile.size <= 20 * 1024) {
            // Convert Blob to File (required for form data)
            const fileName = "compressed_" + file.name;
            const compressedFileAsFile = new File([compressedFile], fileName, {
              type: compressedFile.type,
            });

            console.log("Setting compressed image:", compressedFileAsFile);

            // Set the image as File object
            setImage(compressedFileAsFile);
          const reader = new FileReader();
          reader.onload = () => setImagePreview(reader.result);
          reader.readAsDataURL(compressedFile);
        } else {
          setImageError(`Image exceeds 20KB even after compression. Original: ${originalSizeKB} KB, Compressed: ${compressedSizeKB} KB.`);
        }
      } catch (error) {
        console.error("Image compression failed:", error);
        setImageError("An error occurred while compressing the image.");
      } finally {
        setIsCheckingImage(false);
      }
    }
  };

  const handleDeleteCourse = async (slug) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        await dispatch(deleteCourse(slug));
        fetchCourses();
        toast.success("Course deleted successfully!");
      } catch (error) {
        console.error("Error deleting course", error);
        toast.error("Failed to delete the course. Please try again.");
      }
    }
  };

  const handleUpdateCourse = () => {
    // If no image was selected, use the existing image or set it to null.
    const updatedCourse = {
      name: courseData.name,
      description: courseData.description,
      attachments: image || selectedCourse.attachments,  // Use existing image if no new image is selected
    };
  
    // If the image is selected, it should pass the validation.
    if (image && image.size > 20 * 1024) {
      setImageError("Image exceeds the 20KB limit. Please upload a smaller image.");
      return;
    }
  
    setIsSubmitting(true);
  
    // Proceed with the update, dispatch the action to update the course
    dispatch(updateCourse({ slug: selectedCourse.slug, updatedData: updatedCourse }))
      .then(() => {
        setShowEditModal(false);
        fetchCourses();
        toast.success("Course updated successfully!");
        setIsSubmitting(false);  // Re-enable the button after successful update
      })
      .catch((error) => {
        console.error("Error updating course", error);
        toast.error("Failed to update the course. Please try again.");
        setIsSubmitting(false);
      });
  };
  
  const handleSearch = () => {
    setCurrentPage(0);
  };

  const handleItemsPerPageChange = (items, title) => {
    setCoursesPerPage(items);
    setDropdownTitle(title);
    setCurrentPage(0); // Reset to the first page
  };

  // for filtering the data
  const filteredCourses = courses.filter((course) =>
    (course.name && course.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (course.description && course.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );
  

  const handlePageChange = (page) => {
    setCurrentPage(page - 1); // Adjust for zero-based index
  };

  const indexOfLastCourse = (currentPage + 1) * coursesPerPage;
  const indexOfFirstCourse = indexOfLastCourse - coursesPerPage;
  const currentCourses = filteredCourses.slice(indexOfFirstCourse, indexOfLastCourse);

  return (
    <Container>
      <h2 className="text-center text-success mt-4 mb-2" style={{ fontSize: "1.9rem" }}>
        Dashboard / Courses, <small className="h4"> See live updated here. </small>
      </h2>

      {/* Search Bar */}
      <Row className="mb-4 justify-content-center">
        <Col xs={12} sm={8} md={6} lg={5} className="d-flex justify-content-between">
          <Form.Control
            type="text"
            placeholder="Search courses..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Dropdown>
            <Dropdown.Toggle variant="success" style={{ width: "8rem", fontSize: "0.9rem", marginLeft: "0.2rem" }}>
              {dropdownTitle}
            </Dropdown.Toggle>
            <Dropdown.Menu>
              <Dropdown.Item onClick={() => handleItemsPerPageChange(5, "5 per page")}>5 per page</Dropdown.Item>
              <Dropdown.Item onClick={() => handleItemsPerPageChange(25, "25 per page")}>25 per page</Dropdown.Item>
              <Dropdown.Item onClick={() => handleItemsPerPageChange(50, "50 per page")}>50 per page</Dropdown.Item>
              <Dropdown.Item onClick={() => handleItemsPerPageChange(100, "100 per page")}>100 per page</Dropdown.Item>
              <Dropdown.Item onClick={() => handleItemsPerPageChange(filteredCourses.length, "All")}>All</Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </Col>
      </Row>

      {/* Courses Cards */}

{isLoading ? (
  <CourseCardsSkeleton number={6} />
) : (
  <Row>
    {currentCourses.map((course) => (
      <Col key={course.slug} xs={12} sm={6} md={6} lg={4}>
        <Card
          className="shadow-lg rounded-3 mb-4"
          style={{
            transform: "scale(1)",
            transition: "transform 0.3s ease",
            width: "auto",
            display: "block",
          }}
        >
          <Card.Body style={{ height: "auto", width: "auto" }}>
            <div className="d-flex align-items-center">
              {course.attachments && (
                <Card.Img
                  variant="top"
                  src={`${import.meta.env.VITE_BASE_URL}/${course.attachments}`}
                  alt={course.name || 'Course Image'}
                  style={{ objectFit: 'cover', height: '3rem', width: '3rem', marginRight: '1rem' }}
                />
              )}
              <Card.Title className="text-success text-truncate w-100" style={{ fontSize: '1rem' }}>
                {course.name}
              </Card.Title>
            </div>
            <Card.Text className="text-truncate w-100">
              {course.description}
            </Card.Text>
            <div className="d-flex flex-wrap justify-content-center align-items-end">
              <Link to={`/course/${course.slug}`}>
                <Button variant="outline-primary" className="m-1 fs-6">
                  Sub.
                </Button>
              </Link>
              <Button variant="outline-info" className="m-1 fs-6" onClick={() => handleViewCourse(course)}>
                View 
              </Button>
              <Button variant="outline-success" className="m-1 fs-6" onClick={() => handleEditCourse(course.slug)}>
                <BsPencilSquare/>
              </Button>
              <Button variant="outline-danger" className="m-1 fs-6" onClick={() => handleDeleteCourse(course.slug)}>
                <BsTrash/>
              </Button>
            </div>
          </Card.Body>
        </Card>
      </Col>
    ))}
  </Row>
)}


      {/* Edit Course Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit Course</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group>
              <Form.Label>Course Name</Form.Label>
              <Form.Control
                type="text"
                value={courseData.name}
                onChange={(e) => setCourseData({ ...courseData, name: e.target.value })}
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={courseData.description}
                onChange={(e) => setCourseData({ ...courseData, description: e.target.value })}
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Course Image</Form.Label>
              <Form.Control
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                disabled={isCheckingImage}
              />
              {imageError && <p className="text-danger">{imageError}</p>}
              {imagePreview && <img src={imagePreview} alt="preview" style={{ maxWidth: "100%" }} />}
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>

        <Button
          variant="success"
          type="submit"
          className="w-100"
          onClick={handleUpdateCourse}
          disabled={isSubmitting || isCheckingImage || !!imageError || !courseData.name || !courseData.description}
          >
          {isSubmitting
            ? "Submitting..."
            : isCheckingImage
            ? "Checking image size..."
            : imageError
            ? "Image size error"
            : "Update Course"}
          </Button>

        </Modal.Footer>

      </Modal>

        {/* Pagination */}
        <Row className="mt-4 mb-5 justify-content-center">
        <Col xs={12} className="text-center">
          <PaginationComponent
            totalPages={Math.ceil(filteredCourses.length / coursesPerPage)}
            currentPage={currentPage + 1} // Adjust for one-based index
            handlePageChange={handlePageChange}
          />
        </Col>
      </Row>

      <ViewModal show={showViewModal} onHide={() => setShowViewModal(false)} content={selectedCourse} />


      {/* Toaster notifications */}
    </Container>
  );
};

export default AllCourses;
