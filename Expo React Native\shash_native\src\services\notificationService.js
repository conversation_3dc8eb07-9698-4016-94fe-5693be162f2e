import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform, Alert } from 'react-native';

/**
 * Expo Push Notifications Service
 * 
 * This service handles:
 * - Permission requests
 * - Token generation
 * - Foreground notification handling
 * - Rich media notifications (images)
 */

class NotificationService {
  constructor() {
    this.expoPushToken = null;
    this.notificationListener = null;
    this.responseListener = null;
    
    // Configure notification behavior
    this.configureNotifications();
  }

  /**
   * Configure how notifications are handled
   */
  configureNotifications() {
    // Set notification handler for foreground notifications
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });
  }

  /**
   * Request notification permissions and get push token
   */
  async initialize() {
    try {
      console.log('🔔 Initializing push notifications...');

      // Always try to get token first (for testing purposes)
      const token = await this.getExpoPushToken();

      // Check if running on physical device
      if (!Device.isDevice) {
        console.warn('⚠️ Running on simulator/emulator - notifications won\'t work properly');
        console.log('📱 Token generated for testing:', token);
        return token; // Return token anyway for testing
      }

      // Request permissions
      const permission = await this.requestPermissions();
      if (!permission) {
        console.warn('⚠️ Notification permissions denied');
        return token; // Still return token for testing
      }

      if (token) {
        console.log('✅ Push notifications initialized successfully');
        console.log('📱 Expo Push Token:', token);

        // Set up listeners only on physical device
        this.setupNotificationListeners();

        // Check for notification that launched the app
        this.checkLaunchNotification();

        return token;
      }

      return null;
    } catch (error) {
      console.error('❌ Failed to initialize push notifications:', error);
      return null;
    }
  }

  /**
   * Request notification permissions
   */
  async requestPermissions() {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please enable notifications in your device settings to receive important updates.',
          [{ text: 'OK' }]
        );
        return false;
      }

      console.log('✅ Notification permissions granted');
      return true;
    } catch (error) {
      console.error('❌ Error requesting permissions:', error);
      return false;
    }
  }

  /**
   * Get Expo Push Token
   */
  async getExpoPushToken() {
    try {
      const token = await Notifications.getExpoPushTokenAsync({
        // projectId will be automatically detected from app.json/app.config.js
      });

      this.expoPushToken = token.data;

      // Enhanced console logging for easy copy-paste
      console.log('\n' + '='.repeat(80));
      console.log('🔔 EXPO PUSH TOKEN READY FOR TESTING');
      console.log('='.repeat(80));
      console.log('📱 Token:', this.expoPushToken);
      console.log('\n📋 Ready-to-use cURL command:');
      console.log(`curl -X POST https://exp.host/--/api/v2/push/send \\
-H "Content-Type: application/json" \\
-d '{
  "to": "${this.expoPushToken}",
  "title": "🔔 Test Notification",
  "body": "This is a test sent from cURL!",
  "data": {
    "image": "https://placekitten.com/300/300"
  }
}'`);
      console.log('='.repeat(80) + '\n');

      // You can send this token to your backend here
      // await this.sendTokenToBackend(this.expoPushToken);

      return this.expoPushToken;
    } catch (error) {
      console.error('❌ Error getting push token:', error);
      return null;
    }
  }

  /**
   * Set up notification listeners
   */
  setupNotificationListeners() {
    // Listener for notifications received while app is in foreground
    this.notificationListener = Notifications.addNotificationReceivedListener(
      this.handleForegroundNotification.bind(this)
    );

    // Listener for when user taps on notification
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      this.handleNotificationResponse.bind(this)
    );

    console.log('👂 Notification listeners set up');
  }

  /**
   * Check if app was launched by tapping a notification
   */
  async checkLaunchNotification() {
    try {
      const response = await Notifications.getLastNotificationResponseAsync();

      if (response) {
        const { title, body, data } = response.notification.request.content;
        const imageUrl = data?.image || null;

        console.log('\n' + '🚀 APP LAUNCHED FROM NOTIFICATION');
        console.log('─'.repeat(50));
        console.log('📋 Title:', title || 'No title');
        console.log('📋 Body:', body || 'No body');
        console.log('📋 Image URL:', imageUrl || 'No image');
        console.log('📋 Data:', JSON.stringify(data, null, 2));
        console.log('─'.repeat(50) + '\n');

        // Handle the launch notification
        this.handleNotificationAction(response.notification.request.content);
      } else {
        console.log('📱 App launched normally (not from notification)');
      }
    } catch (error) {
      console.error('❌ Error checking launch notification:', error);
    }
  }

  /**
   * Handle notifications received while app is in foreground
   */
  handleForegroundNotification(notification) {
    const { title, body, data } = notification.request.content;
    const imageUrl = data?.image || null;

    console.log('\n' + '📨 FOREGROUND NOTIFICATION RECEIVED');
    console.log('─'.repeat(50));
    console.log('📋 Title:', title || 'No title');
    console.log('📋 Body:', body || 'No body');
    console.log('📋 Image URL:', imageUrl || 'No image');
    console.log('📋 Data:', JSON.stringify(data, null, 2));
    console.log('─'.repeat(50) + '\n');

    // Show alert for foreground notifications
    Alert.alert(
      title || 'New Notification',
      body || 'You have a new message',
      [
        {
          text: 'Dismiss',
          style: 'cancel',
          onPress: () => {
            console.log('👆 User dismissed foreground notification');
          }
        },
        {
          text: 'View',
          onPress: () => {
            console.log('👆 User tapped View on foreground notification');
            this.handleNotificationAction(notification.request.content);
          },
        },
      ]
    );
  }

  /**
   * Handle notification response (when user taps notification)
   * This handles both background and killed app scenarios
   */
  handleNotificationResponse(response) {
    const { title, body, data } = response.notification.request.content;
    const imageUrl = data?.image || null;

    console.log('\n' + '👆 NOTIFICATION TAPPED (Background/Killed App)');
    console.log('─'.repeat(50));
    console.log('📋 Title:', title || 'No title');
    console.log('📋 Body:', body || 'No body');
    console.log('📋 Image URL:', imageUrl || 'No image');
    console.log('📋 Data:', JSON.stringify(data, null, 2));
    console.log('📋 Action Origin:', response.actionIdentifier || 'default');
    console.log('─'.repeat(50) + '\n');

    // Handle the notification tap
    this.handleNotificationAction(response.notification.request.content);
  }

  /**
   * Handle notification actions (view, navigate, etc.)
   */
  handleNotificationAction(content) {
    const { data, title, body } = content;

    console.log('🎯 Processing notification action...');

    // Handle different notification types based on data
    if (data?.type) {
      console.log(`📱 Notification type: ${data.type}`);

      switch (data.type) {
        case 'exam_reminder':
          console.log('📚 Action: Navigate to exam screen');
          // TODO: Navigate to exam screen
          // Example: NavigationService.navigate('ExamScreen', { examId: data.examId });
          break;

        case 'new_package':
        case 'package_promotion':
          console.log('📦 Action: Navigate to packages screen');
          // TODO: Navigate to packages screen
          // Example: NavigationService.navigate('PackagesScreen', { packageId: data.packageId });
          break;

        case 'friend_progress':
          console.log('👥 Action: Navigate to progress screen');
          // TODO: Navigate to progress screen
          // Example: NavigationService.navigate('ProgressScreen', { friendId: data.friendId });
          break;

        case 'study_reminder':
          console.log('📖 Action: Navigate to practice screen');
          // TODO: Navigate to practice screen
          // Example: NavigationService.navigate('PracticeScreen');
          break;

        default:
          console.log('📱 Action: General notification handling');
          // TODO: Default action (e.g., navigate to home screen)
          break;
      }
    } else {
      console.log('📱 No specific type, handling as general notification');
    }

    // Handle image if present
    if (data?.image) {
      console.log('🖼️ Notification contains image:', data.image);
      // TODO: You can show the image in a modal or navigate to a screen that displays it
    }

    // Handle action URL if present
    if (data?.actionUrl) {
      console.log('🔗 Action URL provided:', data.actionUrl);
      // TODO: Handle deep linking or external URLs
    }

    // Log for analytics
    console.log('📊 Notification interaction logged for analytics');
    // TODO: Send analytics event
    // Example: Analytics.track('notification_opened', { type: data?.type, title, body });
  }

  /**
   * Send token to backend (placeholder)
   */
  async sendTokenToBackend(token) {
    try {
      // TODO: Replace with your actual backend endpoint
      console.log('📤 Sending token to backend:', token);
      
      // Example API call:
      // const response = await fetch('YOUR_BACKEND_URL/api/push-tokens', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({
      //     token: token,
      //     userId: 'current-user-id',
      //     platform: Platform.OS,
      //   }),
      // });
      
    } catch (error) {
      console.error('❌ Error sending token to backend:', error);
    }
  }

  /**
   * Get current push token
   */
  getCurrentToken() {
    return this.expoPushToken;
  }

  /**
   * Get token status for display
   */
  getTokenStatus() {
    return {
      token: this.expoPushToken,
      isPhysicalDevice: Device.isDevice,
      hasToken: !!this.expoPushToken,
      curlCommand: this.expoPushToken ? `curl -X POST https://exp.host/--/api/v2/push/send \\
-H "Content-Type: application/json" \\
-d '{
  "to": "${this.expoPushToken}",
  "title": "🔔 Test Notification",
  "body": "This is a test sent from cURL!",
  "data": {
    "image": "https://placekitten.com/300/300"
  }
}'` : null
    };
  }

  /**
   * Clean up listeners
   */
  cleanup() {
    if (this.notificationListener) {
      this.notificationListener.remove();
    }
    if (this.responseListener) {
      this.responseListener.remove();
    }
    console.log('🧹 Notification listeners cleaned up');
  }
}

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
