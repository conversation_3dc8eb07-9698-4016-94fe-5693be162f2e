#!/bin/bash

# Walk-Around Images API Manual Testing Script
# This script tests the API endpoints using curl commands

echo "🚀 Testing Walk-Around Images API with curl"
echo "============================================="

BASE_URL="http://localhost:8000"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ $2${NC}"
    else
        echo -e "${RED}✗ $2${NC}"
    fi
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

# Test 1: Test public access without authentication
echo ""
print_info "Test 1: Testing public access (no authentication required)"
response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/api/customrcare/walk-around-images/")
if [ "$response" = "200" ]; then
    print_status 0 "Public access working correctly (HTTP $response)"
else
    print_status 1 "Public access not working (HTTP $response)"
fi

# Test 2: Login with customer care credentials
echo ""
print_info "Test 2: Logging in with customer care credentials"

# First, let's try to find an existing customer care user or create one
# We'll use the test user from our previous tests
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/customrcare/login/" \
    -H "Content-Type: application/json" \
    -d '{"username": "testcare_rxjc46", "password": "testpass123"}')

echo "Login response: $LOGIN_RESPONSE"

# Extract token from response
TOKEN=$(echo "$LOGIN_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('access', ''))
except:
    print('')
")

if [ -n "$TOKEN" ]; then
    print_status 0 "Successfully logged in and obtained token"
    echo "Token: ${TOKEN:0:50}..."
else
    print_status 1 "Failed to obtain authentication token"
    echo "Trying with a different approach..."
    
    # Try to create a new user for testing
    print_info "Creating a new test user..."
    
    REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/customrcare/register/" \
        -H "Content-Type: application/json" \
        -d '{
            "username": "walktest123",
            "email": "<EMAIL>",
            "password": "testpass123",
            "contact": 9876543210
        }')
    
    echo "Register response: $REGISTER_RESPONSE"
    
    # Extract token from registration response
    TOKEN=$(echo "$REGISTER_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('access', ''))
except:
    print('')
")
    
    if [ -n "$TOKEN" ]; then
        print_status 0 "Successfully registered and obtained token"
    else
        print_status 1 "Failed to register and obtain token"
        echo "Exiting test..."
        exit 1
    fi
fi

# Test 3: Create a walk-around image
echo ""
print_info "Test 3: Creating a walk-around image"

# Create a simple test image file
echo "Creating test image..."
python3 -c "
from PIL import Image
import io

# Create a simple test image
img = Image.new('RGB', (200, 200), color='red')
img.save('test_walk_image.jpg', 'JPEG')
print('Test image created: test_walk_image.jpg')
"

if [ -f "test_walk_image.jpg" ]; then
    CREATE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/customrcare/walk-around-images/" \
        -H "Authorization: Bearer $TOKEN" \
        -F "image=@test_walk_image.jpg" \
        -F "title=Test Walk Around Image" \
        -F "description=This is a test image created via curl" \
        -F "status=active")
    
    echo "Create response: $CREATE_RESPONSE"
    
    # Extract image ID
    IMAGE_ID=$(echo "$CREATE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('id', ''))
except:
    print('')
")
    
    if [ -n "$IMAGE_ID" ]; then
        print_status 0 "Successfully created walk-around image (ID: $IMAGE_ID)"
    else
        print_status 1 "Failed to create walk-around image"
    fi
else
    print_status 1 "Failed to create test image file"
fi

# Test 4: List walk-around images
echo ""
print_info "Test 4: Listing walk-around images"

LIST_RESPONSE=$(curl -s -X GET "$BASE_URL/api/customrcare/walk-around-images/" \
    -H "Authorization: Bearer $TOKEN")

echo "List response: $LIST_RESPONSE"

IMAGE_COUNT=$(echo "$LIST_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if isinstance(data, list):
        print(len(data))
    elif 'results' in data:
        print(len(data['results']))
    else:
        print(0)
except:
    print(0)
")

if [ "$IMAGE_COUNT" -gt 0 ]; then
    print_status 0 "Successfully listed $IMAGE_COUNT walk-around images"
else
    print_status 1 "No images found or failed to list images"
fi

# Test 5: Update image status
if [ -n "$IMAGE_ID" ]; then
    echo ""
    print_info "Test 5: Updating image status"
    
    UPDATE_RESPONSE=$(curl -s -X PATCH "$BASE_URL/api/customrcare/walk-around-images/$IMAGE_ID/status/" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"status": "inactive"}')
    
    echo "Update response: $UPDATE_RESPONSE"
    
    UPDATED_STATUS=$(echo "$UPDATE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('status', ''))
except:
    print('')
")
    
    if [ "$UPDATED_STATUS" = "inactive" ]; then
        print_status 0 "Successfully updated image status to inactive"
    else
        print_status 1 "Failed to update image status"
    fi
fi

# Test 6: Delete an image
if [ -n "$IMAGE_ID" ]; then
    echo ""
    print_info "Test 6: Deleting an image"

    DELETE_RESPONSE=$(curl -s -X DELETE "$BASE_URL/api/customrcare/walk-around-images/$IMAGE_ID/" \
        -H "Authorization: Bearer $TOKEN")

    echo "Delete response: $DELETE_RESPONSE"

    DELETE_MESSAGE=$(echo "$DELETE_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('message', ''))
except:
    print('')
")

    if [ -n "$DELETE_MESSAGE" ]; then
        print_status 0 "Successfully deleted image with message: $DELETE_MESSAGE"
    else
        print_status 1 "Failed to delete image or no message returned"
    fi
fi

# Test 7: Get statistics
echo ""
print_info "Test 7: Getting statistics"

STATS_RESPONSE=$(curl -s -X GET "$BASE_URL/api/customrcare/walk-around-images/stats/" \
    -H "Authorization: Bearer $TOKEN")

echo "Stats response: $STATS_RESPONSE"

TOTAL_IMAGES=$(echo "$STATS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('total_images', 0))
except:
    print(0)
")

if [ "$TOTAL_IMAGES" -gt 0 ]; then
    print_status 0 "Successfully retrieved statistics (Total images: $TOTAL_IMAGES)"
else
    print_status 1 "Failed to retrieve statistics or no images found"
fi

# Cleanup
echo ""
print_info "Cleaning up test files..."
rm -f test_walk_image.jpg
print_status 0 "Test files cleaned up"

echo ""
echo "============================================="
echo "🎉 Walk-Around Images API testing completed!"
echo "============================================="
