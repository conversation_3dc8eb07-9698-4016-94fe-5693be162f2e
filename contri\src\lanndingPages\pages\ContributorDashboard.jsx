import React, { useEffect } from 'react';
import { Card, Col, Row, Button, Container } from 'react-bootstrap';
import { FaBook, FaQuestionCircle, FaClipboardList, FaChalkboardTeacher, FaShapes , FaRegFileAlt, FaBookOpen, FaBlogger, FaComments, FaGlobeAmericas, FaBullhorn     } from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {toast, Toaster } from 'react-hot-toast';
import NavigationBar from '../../commonComponents/NavigationBar';

const tilesData = [
  {
    id: 0,
    name: 'Normal Question',
    icon: <FaBook size={30} color="#146c43" />,
    route: '/questions_dashboard',
  },
  {
    id: 1,
    name: 'Master Question',
    icon: <FaQuestionCircle size={30} color="#146c43" />,
    route: '/master_questions_dashboard',
  },
  {
    id: 2,
    name: 'Master Options',
    icon: <FaClipboardList size={30} color="#146c43" />,
    route: '/master_options_dashboard',
  },
  {
    id: 3,
    name: 'Prev. Year Ques.',
    icon: <FaComments size={30} color="#146c43" />, 
    route: '/previous_year_questions_dashboard',
  },
  {
    id: 4,
    name: 'Current Affairs',
    icon: <FaGlobeAmericas size={30} color="#146c43" />, 
    route: '/all_blogs',
  },
  {
    id: 5,
    name: 'Courses',
    icon: <FaChalkboardTeacher size={30} color="#146c43" />,
    route: '/add_courses',
  },
  {
    id: 6,
    name: 'Test Patterns',
    icon: <FaShapes  size={30} color="#146c43" />,
    route: '/test_patterns_dashboard',
  },
  // {
  //   id: 7,
  //   name: 'Papers',
  //   icon: <FaRegFileAlt  size={30} color="#146c43" />,
  //   route: '/papers_dashboard',
  // },
  {
    id: 8,
    name: 'Subjects',
    icon: <FaBookOpen size={30} color="#146c43" />,
    route: '/subjects_dashboard',
  },
  {
    id: 9,
    name: 'Blogs',
    icon: <FaBlogger size={30} color="#146c43" />,
    route: '/blogs_dashboard',
  }, 
   {
    id: 10,
    name: 'POP UP',
    icon: <FaBullhorn  size={30} color="#146c43" />,
    route: '/popup_dashboard',
  }, 
    
];

const ContributorDashboard = () => {
  const navigate = useNavigate();
  const accessToken = useSelector((state) => state.contributor.accessToken);

  useEffect(() => {
    if (!accessToken || accessToken === null) { // Check for both null and undefined
      toast.error('Please log in as a contributor!');
      const timer = setTimeout(() => {
        navigate('/contributor_login'); // Redirect to login after 2 seconds
      }, 2000);
      return () => clearTimeout(timer); // Clean up the timer
    }
  }, [accessToken, navigate]);
  

  return (
    <>
      <NavigationBar />
      <Container
        className="mt-5 mb-5 d-flex flex-column justify-content-center "    
      >
        <h1 className="text-center mb-4" style={{ fontSize: '1.9rem', color: '#146c43' }}>
          Contributor Dashboard
        </h1>
        <Row xs={1} sm={2} md={4} lg={5} className="g-2 justify-content-start">
          {tilesData.map((tile) => (
            <Col key={tile.id} className="d-flex justify-content-center">
              <Card className="h-100 shadow-lg w-100">
                <Card.Body className="d-flex flex-column align-items-center justify-content-center text-center">
                  <div className="mb-3">{tile.icon}</div>
                  <Card.Title style={{fontSize:"1rem"}}>{tile.name}</Card.Title>
                  <Link to={tile.route} className="mt-auto">
                    <Button variant="outline-success" style={{fontSize:"0.9rem"}} >Go to {tile.name}</Button>
                  </Link>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      </Container>
      <Toaster/>
    </>
  );
};

export default ContributorDashboard;
