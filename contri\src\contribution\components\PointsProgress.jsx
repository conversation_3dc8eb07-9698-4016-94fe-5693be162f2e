import React from 'react';
import { <PERSON><PERSON><PERSON>, Card, Row, Col } from 'react-bootstrap';

const PointsProgress = ({ data }) => {
  return (
    <Card className="mb-4">
      <Card.Body>
        <Card.Title>Points Progress</Card.Title>
        <Row>
          {['current_month_points', 'previous_month_points', 'third_month_points'].map((monthKey) => (
            <Col key={monthKey} md={4} className="mb-4">
              <strong>{monthKey.replace('_', ' ').toUpperCase()}</strong>
              <ProgressBar
                now={data[monthKey]?.total_points || 0}
                max={100}
                label={`${data[monthKey]?.total_points || 0} Points`}
              />
            </Col>
          ))}
        </Row>
      </Card.Body>
    </Card>
  );
};

export default PointsProgress;
