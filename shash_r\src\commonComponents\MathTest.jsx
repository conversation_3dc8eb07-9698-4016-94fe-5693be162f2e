import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Alert } from 'react-bootstrap';
import MathTextRenderer from './MathTextRenderer';

/**
 * Simple test component to verify math functionality works
 * This can be temporarily added to a route to test math rendering
 */
const MathTest = () => {
  const [showAlert, setShowAlert] = useState(false);

  const testTexts = [
    "Simple inline math: $x^2 + y^2 = z^2$",
    "Display math: $$\\int_0^1 x^2 dx = \\frac{1}{3}$$",
    "Mixed content: The quadratic formula $x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}$ is very useful.",
    "Multiple expressions: $a^2$ and $b^2$ and $$c^2 = a^2 + b^2$$",
    "Plain text without math should work fine too.",
    "Greek letters: $\\alpha$, $\\beta$, $\\gamma$, $\\pi$, $\\theta$",
    "Fractions and roots: $\\frac{1}{2}$, $\\sqrt{x}$, $\\sqrt[3]{8}$",
    "Calculus: $\\lim_{x \\to \\infty} f(x)$, $\\frac{d}{dx}f(x)$, $\\int f(x) dx$"
  ];

  const handleTest = () => {
    console.log('Math rendering test completed');
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  return (
    <Container className="mt-4">
      <Card>
        <Card.Header>
          <h4>Math Rendering Test</h4>
        </Card.Header>
        <Card.Body>
          {showAlert && (
            <Alert variant="success" className="mb-3">
              Math rendering test completed! Check console for details.
            </Alert>
          )}
          
          <p className="text-muted mb-4">
            This component tests if math rendering is working correctly. 
            Each example below should show properly formatted mathematical expressions.
          </p>

          {testTexts.map((text, index) => (
            <Card key={index} className="mb-3">
              <Card.Body>
                <h6>Test {index + 1}:</h6>
                <div className="mb-2">
                  <strong>Input:</strong> <code>{text}</code>
                </div>
                <div>
                  <strong>Rendered:</strong> <MathTextRenderer text={text} />
                </div>
              </Card.Body>
            </Card>
          ))}

          <div className="text-center mt-4">
            <Button variant="primary" onClick={handleTest}>
              Run Test
            </Button>
          </div>

          <div className="mt-4 p-3 bg-light rounded">
            <h6>Expected Results:</h6>
            <ul className="small">
              <li>Inline math ($...$) should render within the text flow</li>
              <li>Display math ($$...$$) should render as centered blocks</li>
              <li>Plain text should render normally</li>
              <li>Mixed content should show both text and math correctly</li>
              <li>Greek letters and symbols should display properly</li>
              <li>If math doesn't render, check browser console for errors</li>
            </ul>
          </div>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default MathTest;
