# Contributor Earning System API Documentation

## Overview
This document provides comprehensive documentation for the Contributor Earning System APIs. The system allows contributors to earn points based on their activities and track their earnings over different time periods.

## Authentication
All APIs require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Base URL
```
http://localhost:8000/api/contributor/
```

## API Endpoints

### 1. Dashboard API
**Endpoint:** `GET /dashboard/`
**Description:** Get contributor dashboard with comprehensive earnings information

**Response Example:**
```json
{
  "contributor": "test_contributor_1",
  "earnings": {
    "current_month_earnings": {
      "total_points": 150.00,
      "activity_breakdown": {
        "normal_questions": {"count": 10, "points": 50.00},
        "master_questions": {"count": 5, "points": 100.00},
        "blogs": {"count": 2, "points": 30.00}
      }
    },
    "total_lifetime_earnings": {
      "total_points": 500.00,
      "earning_records_count": 3
    },
    "points_configuration": {
      "name": "Premium Points",
      "is_custom": true,
      "normal_questions": 10,
      "master_questions": 20
    }
  }
}
```

### 2. Earnings List API
**Endpoint:** `GET /earnings/`
**Description:** Get paginated list of contributor earnings

**Query Parameters:**
- `period_type` (optional): Filter by period type (daily, weekly, monthly, yearly)
- `is_paid` (optional): Filter by payment status (true/false)

**Response Example:**
```json
[
  {
    "id": 1,
    "contributor_username": "test_contributor_1",
    "period_type": "monthly",
    "period_start": "2024-01-01T00:00:00Z",
    "period_end": "2024-01-31T23:59:59Z",
    "total_points": 150.00,
    "is_paid": false,
    "activity_breakdown": {
      "normal_questions": {"count": 10, "points": 50.00},
      "master_questions": {"count": 5, "points": 100.00}
    }
  }
]
```

### 3. Earnings Summary API
**Endpoint:** `GET /earnings/summary/`
**Description:** Get earnings summary for a specific period

**Query Parameters:**
- `period_type` (required): Period type (daily, weekly, monthly, yearly, lifetime)

**Response Example:**
```json
{
  "period_type": "monthly",
  "period_start": "2024-01-01T00:00:00Z",
  "period_end": "2024-01-31T23:59:59Z",
  "total_points": 150.00,
  "total_earnings": 150.00,
  "activity_breakdown": {
    "normal_questions": {"count": 10, "points": 50.00},
    "master_questions": {"count": 5, "points": 100.00},
    "master_options": {"count": 8, "points": 40.00},
    "blogs": {"count": 2, "points": 30.00},
    "previous_questions": {"count": 3, "points": 24.00}
  },
  "is_paid": false,
  "paid_at": null
}
```

### 4. Total Earnings API
**Endpoint:** `GET /earnings/total/`
**Description:** Get total lifetime earnings for the contributor

**Response Example:**
```json
{
  "total_points": 500.00,
  "total_earnings": 500.00,
  "earning_records_count": 5
}
```

### 5. Points Configuration API
**Endpoint:** `GET /points-config/`
**Description:** Get the points configuration for the contributor

**Response Example:**
```json
{
  "points_configuration": {
    "id": 2,
    "name": "Premium Points",
    "normal_questions": 10,
    "master_questions": 20,
    "master_options": 5,
    "blogs": 15,
    "previous_questions": 8,
    "assigned_contributors_count": 3
  },
  "is_custom": true,
  "contributor": "test_contributor_1"
}
```

### 6. Recalculate Earnings API
**Endpoint:** `POST /earnings/recalculate/`
**Description:** Manually recalculate earnings for a specific period

**Request Body:**
```json
{
  "period_type": "monthly"
}
```

**Response Example:**
```json
{
  "message": "Earnings recalculated successfully",
  "earning": {
    "id": 1,
    "total_points": 150.00,
    "period_type": "monthly",
    "activity_breakdown": {
      "normal_questions": {"count": 10, "points": 50.00}
    }
  }
}
```

## Data Isolation
- Each contributor can only access their own earning data
- API responses are filtered by the authenticated user's contributor profile
- Cross-contributor data access is prevented at the API level

## Point Calculation
Points are calculated based on the contributor's assigned point configuration:
- **Normal Questions:** Configurable points per question
- **Master Questions:** Higher points for complex questions
- **Master Options:** Points for creating question options
- **Blogs:** Points for writing blog posts
- **Previous Questions:** Points for historical question data

## Error Responses
All APIs return appropriate HTTP status codes:
- `200`: Success
- `400`: Bad Request (invalid parameters)
- `401`: Unauthorized (invalid/missing token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `500`: Internal Server Error

## Testing with Postman
1. Import the generated Postman collection
2. Set the `base_url` variable to your server URL
3. Obtain a JWT token through the login API
4. Set the `auth_token` variable with your JWT token
5. Run the collection to test all endpoints

## Rate Limiting
APIs may be rate-limited to prevent abuse. Check response headers for rate limit information.

## Support
For technical support or questions about the earning system, contact the development team.
