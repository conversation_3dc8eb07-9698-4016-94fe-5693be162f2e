from django.urls import path
from .views import (
    CustomrcareProfileCreateView,
    CustomrcareProfileRetrieveUpdateDeleteView,
    DashboardAPIView,
    LoginView,
    LogoutView,
    TicketListCreateView,
    TicketDetailView,
    CustomrcareQuestionListView,
    CustomrcareQuestionSearchView,
    CustomrcareQuestionStatusUpdateView,
    TokenRefreshView,
    CustomerCareDashboardAPIView,
    FrontendErrorView,
    FrontendErrorAnalyticsView,
    FrontendErrorResolutionView,
    FrontendErrorBulkActionsView,
    cleanup_expired_images,
    list_images,
    upload_image,
    # Walk-around images views
    WalkAroundImageListCreateView,
    WalkAroundImageDetailView,
    WalkAroundImageStatusUpdateView,
    walk_around_images_stats,
    # PopupBanner views
    CustomerCarePopupBannerListView,
    CustomerCarePopupBannerDetailView,
    AdminPopupBannerListView,
    AdminPopupBannerDetailView,
    # SOP views
    SOPListCreateView,
    SOPDetailView,

)


urlpatterns = [
    path(
        "register/",
        CustomrcareProfileCreateView.as_view(),
        name="customrcare-profile-create",
    ),
    path(
        "profile/<slug:slug>/",
        CustomrcareProfileRetrieveUpdateDeleteView.as_view(),
        name="customrcare-profile-detail",
    ),
    path("login/", LoginView.as_view(), name="login"),
    path("logout/", LogoutView.as_view(), name="logout"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("tickets/", TicketListCreateView.as_view(), name="ticket-list-create"),
    path("tickets/<slug:slug>/", TicketDetailView.as_view(), name="ticket-detail"),
    path(
        "questions/",
        CustomrcareQuestionListView.as_view(),
        name="customrcare-question-list",
    ),
    path(
        "questions/search/",
        CustomrcareQuestionSearchView.as_view(),
        name="customrcare-question-search",
    ),
    path(
        "questions/status-update/",
        CustomrcareQuestionStatusUpdateView.as_view(),
        name="customrcare-question-status-update",
    ),
    path(
        "dashboard-api/",
        DashboardAPIView.as_view(),
        name="dashboard",
    ),
    path("customecare-card-view/", CustomerCareDashboardAPIView.as_view(), name="customecare_card_view"),
    # Enhanced frontend error logging endpoints
    path('log-error/', FrontendErrorView.as_view(), name='log_error'),
    path('get-errors/', FrontendErrorView.as_view(), name='get_errors'),
    path('del-errors/<int:pk>/', FrontendErrorView.as_view(), name='del_errors'),
    path('error-analytics/', FrontendErrorAnalyticsView.as_view(), name='frontend_error_analytics'),
    path('error-resolution/<int:pk>/', FrontendErrorResolutionView.as_view(), name='frontend_error_resolution'),
    path('error-bulk-actions/', FrontendErrorBulkActionsView.as_view(), name='frontend_error_bulk_actions'),

    # Image upload endpoints
    path('upload/', upload_image, name='upload_image'),
    path('images/', list_images, name='list_images'),

    # Walk-around images API endpoints
    path('walk-around-images/', WalkAroundImageListCreateView.as_view(), name='walk_around_images_list_create'),
    path('walk-around-images/<int:pk>/', WalkAroundImageDetailView.as_view(), name='walk_around_images_detail'),
    path('walk-around-images/<int:pk>/status/', WalkAroundImageStatusUpdateView.as_view(), name='walk_around_images_status_update'),
    path('walk-around-images/stats/', walk_around_images_stats, name='walk_around_images_stats'),

    # PopupBanner URLs for customer care and admin
    path('popup-banners/', CustomerCarePopupBannerListView.as_view(), name='customercare-popup-banner-list'),
    path('popup-banners/<int:pk>/', CustomerCarePopupBannerDetailView.as_view(), name='customercare-popup-banner-detail'),
    path('admin/popup-banners/', AdminPopupBannerListView.as_view(), name='admin-popup-banner-list'),
    path('admin/popup-banners/<int:pk>/', AdminPopupBannerDetailView.as_view(), name='admin-popup-banner-detail'),

    # SOP URLs
    path('sops/', SOPListCreateView.as_view(), name='sop-list-create'),
    path('sops/<int:pk>/', SOPDetailView.as_view(), name='sop-detail'),
]

