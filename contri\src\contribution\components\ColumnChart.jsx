import React from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import Skeleton from 'react-loading-skeleton';

// Register chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const ColumnChart = ({ currentMonthData, previousMonthData, thirdMonthData, isLoading }) => {
  const chartData = {
    labels: ['Current Month', 'Previous Month', 'Third Month'],
    datasets: [
      {
        label: 'Normal Questions - Created',
        data: [
          currentMonthData?.questions?.monthly?.created || 0,
          previousMonthData?.questions?.monthly?.created || 0,
          thirdMonthData?.questions?.monthly?.created || 0,
        ],
        backgroundColor: '#99ccff',
        stack: 'normalQuestions',
      },
      {
        label: 'Normal Questions - Approved',
        data: [
          currentMonthData?.questions?.monthly?.approved || 0,
          previousMonthData?.questions?.monthly?.approved || 0,
          thirdMonthData?.questions?.monthly?.approved || 0,
        ],
        backgroundColor: '#66b3ff',
        stack: 'normalQuestions',
      },
      {
        label: 'Normal Questions - Pending',
        data: [
          currentMonthData?.questions?.monthly?.pending || 0,
          previousMonthData?.questions?.monthly?.pending || 0,
          thirdMonthData?.questions?.monthly?.pending || 0,
        ],
        backgroundColor: '#ffeb3b',
        stack: 'normalQuestions',
      },
      {
        label: 'Normal Questions - Rejected',
        data: [
          currentMonthData?.questions?.monthly?.rejected || 0,
          previousMonthData?.questions?.monthly?.rejected || 0,
          thirdMonthData?.questions?.monthly?.rejected || 0,
        ],
        backgroundColor: '#f44336',
        stack: 'normalQuestions',
      },

      // Master Questions
      {
        label: 'Master Questions - Created',
        data: [
          currentMonthData?.master_questions?.monthly?.created || 0,
          previousMonthData?.master_questions?.monthly?.created || 0,
          thirdMonthData?.master_questions?.monthly?.created || 0,
        ],
        backgroundColor: '#80e27e',
        stack: 'masterQuestions',
      },
      {
        label: 'Master Questions - Approved',
        data: [
          currentMonthData?.master_questions?.monthly?.approved || 0,
          previousMonthData?.master_questions?.monthly?.approved || 0,
          thirdMonthData?.master_questions?.monthly?.approved || 0,
        ],
        backgroundColor: '#66c466',
        stack: 'masterQuestions',
      },
      {
        label: 'Master Questions - Pending',
        data: [
          currentMonthData?.master_questions?.monthly?.pending || 0,
          previousMonthData?.master_questions?.monthly?.pending || 0,
          thirdMonthData?.master_questions?.monthly?.pending || 0,
        ],
        backgroundColor: '#cddc39',
        stack: 'masterQuestions',
      },
      {
        label: 'Master Questions - Rejected',
        data: [
          currentMonthData?.master_questions?.monthly?.rejected || 0,
          previousMonthData?.master_questions?.monthly?.rejected || 0,
          thirdMonthData?.master_questions?.monthly?.rejected || 0,
        ],
        backgroundColor: '#f44336',
        stack: 'masterQuestions',
      },

      // Master Options
      {
        label: 'Master Options - Created',
        data: [
          currentMonthData?.master_options?.monthly?.created || 0,
          previousMonthData?.master_options?.monthly?.created || 0,
          thirdMonthData?.master_options?.monthly?.created || 0,
        ],
        backgroundColor: '#ff7043',
        stack: 'masterOptions',
      },
      {
        label: 'Master Options - Approved',
        data: [
          currentMonthData?.master_options?.monthly?.approved || 0,
          previousMonthData?.master_options?.monthly?.approved || 0,
          thirdMonthData?.master_options?.monthly?.approved || 0,
        ],
        backgroundColor: '#ff5722',
        stack: 'masterOptions',
      },
      {
        label: 'Master Options - Pending',
        data: [
          currentMonthData?.master_options?.monthly?.pending || 0,
          previousMonthData?.master_options?.monthly?.pending || 0,
          thirdMonthData?.master_options?.monthly?.pending || 0,
        ],
        backgroundColor: '#ffc107',
        stack: 'masterOptions',
      },
      {
        label: 'Master Options - Rejected',
        data: [
          currentMonthData?.master_options?.monthly?.rejected || 0,
          previousMonthData?.master_options?.monthly?.rejected || 0,
          thirdMonthData?.master_options?.monthly?.rejected || 0,
        ],
        backgroundColor: '#f44336',
        stack: 'masterOptions',
      },

      // Previous Questions
      {
        label: 'Previous Questions - Created',
        data: [
          currentMonthData?.previous_questions?.monthly?.created || 0,
          previousMonthData?.previous_questions?.monthly?.created || 0,
          thirdMonthData?.previous_questions?.monthly?.created || 0,
        ],
        backgroundColor: '#ab47bc',
        stack: 'previousQuestions',
      },
      {
        label: 'Previous Questions - Approved',
        data: [
          currentMonthData?.previous_questions?.monthly?.approved || 0,
          previousMonthData?.previous_questions?.monthly?.approved || 0,
          thirdMonthData?.previous_questions?.monthly?.approved || 0,
        ],
        backgroundColor: '#9c27b0',
        stack: 'previousQuestions',
      },
      {
        label: 'Previous Questions - Pending',
        data: [
          currentMonthData?.previous_questions?.monthly?.pending || 0,
          previousMonthData?.previous_questions?.monthly?.pending || 0,
          thirdMonthData?.previous_questions?.monthly?.pending || 0,
        ],
        backgroundColor: '#fbc02d',
        stack: 'previousQuestions',
      },
      {
        label: 'Previous Questions - Rejected',
        data: [
          currentMonthData?.previous_questions?.monthly?.rejected || 0,
          previousMonthData?.previous_questions?.monthly?.rejected || 0,
          thirdMonthData?.previous_questions?.monthly?.rejected || 0,
        ],
        backgroundColor: '#e64a19',
        stack: 'previousQuestions',
      },
    ],
  };

  return (
    <div style={{ width: '100%', height: '400px' }}>
      {isLoading ? (
        <Skeleton
          height={400}
          baseColor="#e6ffe6"
          highlightColor="#c4f7c4"
          style={{ borderRadius: "8px" }}
        />
      ) : (
        <Bar
          data={chartData}
          options={{
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: { position: "top" },
              title: {
                display: true,
                text: "Data Summary Comparison",
              },
            },
            scales: {
              x: { stacked: true },
              y: { stacked: true },
            },
          }}
        />
      )}
    </div>
  );
};

export default ColumnChart;
