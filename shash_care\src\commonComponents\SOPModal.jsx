import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Dropdown } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { fetchSOPs } from "../redux/slice/sopSlice";

const SOPModal = ({ isOpen, onRequestClose }) => {
  const dispatch = useDispatch();
  const { sops, isLoading, error } = useSelector((state) => state.sops);
  const [selectedSop, setSelectedSop] = useState(null);

  // Fetch SOPs when modal opens
  useEffect(() => {
    if (isOpen) {
      dispatch(fetchSOPs());
    }
  }, [isOpen, dispatch]);

  // Set the first SOP as selected when SOPs are loaded
  useEffect(() => {
    if (sops && sops.length > 0 && !selectedSop) {
      setSelectedSop(sops[0]);
    }
  }, [sops, selectedSop]);

  // Reset selected SOP when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedSop(null);
    }
  }, [isOpen]);

  const handleSopSelect = (sop) => {
    setSelectedSop(sop);
  };

  const handleDownloadPDF = async () => {
    if (selectedSop && selectedSop.pdf) {
      try {
        const response = await fetch(selectedSop.pdf);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${selectedSop.name || selectedSop.title}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (err) {
        alert('Failed to download PDF.');
      }
    }
  };

  const handleOpenInNewTab = () => {
    if (selectedSop && selectedSop.pdf) {
      window.open(selectedSop.pdf, '_blank');
    }
  };

  return (
    <Modal show={isOpen} onHide={onRequestClose} size="xl" centered>
      <Modal.Header closeButton>
        <Modal.Title className="d-flex align-items-center justify-content-between w-100">
          <span>Statement of Purpose (SOP)</span>
          <div className="d-flex align-items-center gap-2">
            {/* SOP Selection Dropdown */}
            {sops && sops.length > 1 && (
              <Dropdown>
                <Dropdown.Toggle variant="outline-primary" size="sm">
                  {selectedSop ? (selectedSop.name || selectedSop.title) : 'Select SOP'}
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  {sops.map((sop) => (
                    <Dropdown.Item
                      key={sop.id}
                      onClick={() => handleSopSelect(sop)}
                      active={selectedSop?.id === sop.id}
                    >
                      {sop.name || sop.title}
                    </Dropdown.Item>
                  ))}
                </Dropdown.Menu>
              </Dropdown>
            )}

            {/* Action Buttons */}
            {selectedSop && selectedSop.pdf && (
              <>
                <Button
                  variant="outline-success"
                  size="sm"
                  onClick={handleDownloadPDF}
                  title="Download PDF"
                >
                  📥 Download
                </Button>
                <Button
                  variant="outline-info"
                  size="sm"
                  onClick={handleOpenInNewTab}
                  title="Open in New Tab"
                >
                  🔗 New Tab
                </Button>
              </>
            )}
          </div>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ height: "80vh", padding: 0 }}>
        {isLoading ? (
          <div className="d-flex justify-content-center align-items-center h-100">
            <div className="text-center">
              <Spinner animation="border" role="status" />
              <div className="mt-2">Loading SOP...</div>
            </div>
          </div>
        ) : error ? (
          <div className="d-flex justify-content-center align-items-center h-100">
            <Alert variant="danger" className="text-center">
              <Alert.Heading>Error Loading SOP</Alert.Heading>
              <p>{typeof error === 'string' ? error : 'Failed to load SOP. Please try again.'}</p>
            </Alert>
          </div>
        ) : selectedSop && selectedSop.pdf ? (
          <iframe
            src={selectedSop.pdf}
            title={`SOP PDF - ${selectedSop.name || selectedSop.title}`}
            style={{ width: "100%", height: "100%", border: "none", borderRadius: "8px" }}
          />
        ) : sops && sops.length === 0 ? (
          <div className="d-flex justify-content-center align-items-center h-100">
            <Alert variant="info" className="text-center">
              <Alert.Heading>No SOP Available</Alert.Heading>
              <p>No Statement of Purpose document is currently available.</p>
            </Alert>
          </div>
        ) : (
          <div className="d-flex justify-content-center align-items-center h-100">
            <Alert variant="warning" className="text-center">
              <Alert.Heading>PDF Not Available</Alert.Heading>
              <p>The selected SOP document could not be loaded.</p>
            </Alert>
          </div>
        )}
      </Modal.Body>
    </Modal>
  );
};

export default SOPModal;
