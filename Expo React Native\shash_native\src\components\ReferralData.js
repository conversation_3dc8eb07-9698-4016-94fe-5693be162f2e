import React, { useEffect, useState, useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Share, Alert } from 'react-native';
import { Card } from 'react-native-paper';
import { useDispatch } from 'react-redux';
import { getReferralData } from '../redux/studentSlice';
import Icon from 'react-native-vector-icons/Ionicons';
import Constants from 'expo-constants';
import { ThemeContext } from '../context/ThemeContext';

const ReferralData = ({ profile = false }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const [referData, setReferData] = useState(null);
  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();

  useEffect(() => {
    const getReferralDataAsync = async () => {
      setLoading(true);
      try {
        const response = await dispatch(getReferralData()).unwrap();
        if (response) {
          setReferData(response);
        }
      } catch (error) {
        console.error(error);
        Alert.alert('Error', 'Error occurred while fetching referral data');
      } finally {
        setLoading(false);
      }
    };

    getReferralDataAsync();
  }, [dispatch]);

  const handleShare = async () => {
    try {
      await Share.share({
        title: 'Referral Code',
        message: `Use my referral code: ${referData?.referral_code}\nAnd get the discount bonus on memberships\n\nClick here to Register\nhttps://shashtrarth.com/students/signup-request/${referData?.referral_code}`,
      });
    } catch (error) {
      Alert.alert('Error', 'Error sharing referral code');
    }
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, isDarkMode && styles.loadingContainerDark]}>
        <Text style={isDarkMode && styles.textDark}>Loading...</Text>
      </View>
    );
  }

  return (
    <Card style={[
      styles.card,
      profile && styles.profileCard,
      isDarkMode && styles.cardDark,
      profile && isDarkMode && styles.profileCardDark
    ]}>
      <Card.Content>
        {!profile && (
          <View style={styles.statsRow}>
            <View style={styles.statsItem}>
              <Text style={[styles.label, isDarkMode && styles.textDark]}>Referral Code</Text>
              <Text style={[styles.value, isDarkMode && styles.valueDark]}>{referData?.referral_code}</Text>
            </View>
          </View>
        )}

        <View style={styles.qrCodeContainer}>
          {!profile && <Text style={[styles.qrLabel, isDarkMode && styles.textDark]}>QR Code</Text>}
          <Image
            source={{ uri: `${Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL}${referData?.qr_code_url}` }}
            style={[styles.qrCode, profile && styles.profileQrCode]}
            resizeMode="contain"
          />
          
          {profile ? (
            <View style={styles.referralRow}>
              <Text style={[styles.referralText, isDarkMode && styles.textDark]}>{referData?.referral_code}</Text>
              <TouchableOpacity
                style={styles.shareButton}
                onPress={handleShare}
              >
                <Icon name="share-social" size={24} color={isDarkMode ? "#2ecc71" : "#198754"} />
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.shareButtonLarge}
              onPress={handleShare}
            >
              <Text style={styles.shareButtonText}>Share</Text>
            </TouchableOpacity>
          )}
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    padding: 16,
    alignItems: 'center',
  },
  loadingContainerDark: {
    backgroundColor: '#121212',
  },
  card: {
    backgroundColor: '#ffffff',
  },
  cardDark: {
    backgroundColor: '#1e1e1e',
  },
  profileCard: {
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
  profileCardDark: {
    backgroundColor: 'transparent',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  statsItem: {
    alignItems: 'center',
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  textDark: {
    color: '#ffffff',
  },
  value: {
    fontSize: 18,
    color: '#198754',
    fontWeight: 'bold',
  },
  valueDark: {
    color: '#2ecc71',
  },
  qrCodeContainer: {
    alignItems: 'center',
  },
  qrLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  qrCode: {
    width: 200,
    height: 200,
  },
  profileQrCode: {
    width: 100,
    height: 100,
  },
  referralRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  referralText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
  shareButton: {
    padding: 4,
  },
  shareButtonLarge: {
    backgroundColor: '#198754',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
    marginTop: 16,
  },
  shareButtonText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
});

export default ReferralData;
