#!/usr/bin/env python3
"""
Debug Cron Job Notifications
This script helps debug why cron job notifications are not working
"""

import os
import sys
import django
import subprocess
from pathlib import Path
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def check_cron_service():
    """Check if cron service is running"""
    print("🔧 CHECKING CRON SERVICE")
    print("=" * 40)
    
    try:
        # Check if we're on Windows (cron doesn't exist on Windows)
        if os.name == 'nt':
            print("⚠️ You're on Windows - cron is not available!")
            print("💡 Windows alternatives:")
            print("   • Task Scheduler (schtasks)")
            print("   • PowerShell scheduled jobs")
            print("   • Manual testing via Django admin")
            return False
        
        # Check cron service status (Linux/Mac)
        result = subprocess.run(['systemctl', 'status', 'cron'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Cron service is running")
            return True
        else:
            print("❌ Cron service is not running")
            print("💡 Start it with: sudo systemctl start cron")
            return False
            
    except FileNotFoundError:
        print("❌ systemctl not found - trying alternative methods")
        
        # Try alternative methods
        try:
            result = subprocess.run(['service', 'cron', 'status'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Cron service is running (via service command)")
                return True
        except FileNotFoundError:
            pass
        
        print("❌ Could not determine cron service status")
        return False
    
    except Exception as e:
        print(f"❌ Error checking cron service: {e}")
        return False

def check_crontab():
    """Check current crontab entries"""
    print("\n📋 CHECKING CRONTAB ENTRIES")
    print("=" * 40)
    
    try:
        result = subprocess.run(['crontab', '-l'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            crontab_content = result.stdout.strip()
            if crontab_content:
                print("✅ Crontab entries found:")
                print(crontab_content)
                
                # Check if our membership expiry job is there
                if 'check_membership_expiry' in crontab_content:
                    print("✅ Membership expiry cron job found!")
                    return True
                else:
                    print("❌ Membership expiry cron job NOT found in crontab")
                    return False
            else:
                print("❌ No crontab entries found")
                return False
        else:
            print("❌ Could not read crontab")
            print(f"Error: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ crontab command not found")
        return False
    except Exception as e:
        print(f"❌ Error checking crontab: {e}")
        return False

def check_cron_logs():
    """Check cron logs for our job"""
    print("\n📊 CHECKING CRON LOGS")
    print("=" * 30)
    
    log_files = [
        '/var/log/cron',
        '/var/log/cron.log',
        '/var/log/syslog',
        '/var/log/membership_expiry.log'
    ]
    
    found_logs = False
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"📄 Checking {log_file}...")
            try:
                # Get last 20 lines
                result = subprocess.run(['tail', '-20', log_file], 
                                      capture_output=True, text=True)
                
                if 'check_membership_expiry' in result.stdout:
                    print(f"✅ Found membership expiry entries in {log_file}:")
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'check_membership_expiry' in line:
                            print(f"   {line}")
                    found_logs = True
                else:
                    print(f"   No membership expiry entries found")
                    
            except Exception as e:
                print(f"   Error reading {log_file}: {e}")
        else:
            print(f"❌ {log_file} does not exist")
    
    if not found_logs:
        print("❌ No cron job execution logs found")
        print("💡 This suggests the cron job may not be running")
    
    return found_logs

def test_command_manually():
    """Test the command manually to see if it works"""
    print("\n🧪 TESTING COMMAND MANUALLY")
    print("=" * 40)
    
    try:
        project_path = Path(__file__).parent.absolute()
        python_path = sys.executable
        
        print(f"📁 Project path: {project_path}")
        print(f"🐍 Python path: {python_path}")
        
        # Change to project directory
        os.chdir(project_path)
        print(f"📂 Changed to: {os.getcwd()}")
        
        # Run the command
        print("🚀 Running: python manage.py check_membership_expiry --verbose")
        
        result = subprocess.run([
            python_path, 'manage.py', 'check_membership_expiry', '--verbose'
        ], capture_output=True, text=True, timeout=60)
        
        print(f"📊 Return code: {result.returncode}")
        
        if result.stdout:
            print("📤 STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("📥 STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ Command executed successfully!")
            
            # Check if notifications were mentioned
            if 'notifications sent' in result.stdout.lower():
                print("✅ Notifications were sent!")
                return True
            else:
                print("⚠️ Command ran but no notifications mentioned")
                return False
        else:
            print("❌ Command failed!")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Command timed out after 60 seconds")
        return False
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("\n📦 CHECKING DEPENDENCIES")
    print("=" * 35)
    
    dependencies = [
        'django',
        'firebase-admin',
        'requests',
    ]
    
    all_good = True
    
    for dep in dependencies:
        try:
            __import__(dep.replace('-', '_'))
            print(f"✅ {dep} is installed")
        except ImportError:
            print(f"❌ {dep} is NOT installed")
            all_good = False
    
    # Check Django apps
    try:
        from django.conf import settings
        required_apps = [
            'librarian',
            'membership',
        ]
        
        for app in required_apps:
            if app in settings.INSTALLED_APPS:
                print(f"✅ Django app '{app}' is installed")
            else:
                print(f"❌ Django app '{app}' is NOT in INSTALLED_APPS")
                all_good = False
                
    except Exception as e:
        print(f"❌ Error checking Django apps: {e}")
        all_good = False
    
    return all_good

def check_fcm_tokens():
    """Check if there are active FCM tokens"""
    print("\n🔔 CHECKING FCM TOKENS")
    print("=" * 30)
    
    try:
        from librarian.models import DeviceToken
        
        total_tokens = DeviceToken.objects.count()
        active_tokens = DeviceToken.objects.filter(is_active=True).count()
        
        print(f"📊 Total FCM tokens: {total_tokens}")
        print(f"📊 Active FCM tokens: {active_tokens}")
        
        if active_tokens == 0:
            print("❌ No active FCM tokens found!")
            print("💡 You need to register an FCM token first:")
            print("   1. Visit: http://localhost:8000/fcm-test/")
            print("   2. Allow notifications when prompted")
            print("   3. Complete the FCM token registration")
            return False
        else:
            print("✅ Active FCM tokens found!")
            
            # Show token details
            tokens = DeviceToken.objects.filter(is_active=True)[:3]
            for token in tokens:
                print(f"   Token: {token.token[:20]}... (User: {token.user.username if token.user else 'Anonymous'})")
            
            return True
            
    except Exception as e:
        print(f"❌ Error checking FCM tokens: {e}")
        return False

def check_memberships():
    """Check if there are memberships that should trigger notifications"""
    print("\n👥 CHECKING MEMBERSHIPS")
    print("=" * 30)
    
    try:
        from membership.models import Membership
        from django.utils import timezone
        from datetime import timedelta
        
        today = timezone.now().date()
        
        # Check different expiry periods
        periods = [
            (10, "10 days before"),
            (5, "5 days before"),
            (1, "1 day before"),
            (0, "today"),
            (-4, "4 days after")
        ]
        
        total_found = 0
        
        for days, description in periods:
            target_date = today + timedelta(days=days)
            count = Membership.objects.filter(expiry_date=target_date).count()
            
            if count > 0:
                print(f"✅ {count} memberships expire {description} ({target_date})")
                total_found += count
            else:
                print(f"   0 memberships expire {description} ({target_date})")
        
        if total_found == 0:
            print("❌ No memberships found in notification periods!")
            print("💡 Create test memberships with expiry dates:")
            print(f"   • Today: {today}")
            print(f"   • Tomorrow: {today + timedelta(days=1)}")
            print(f"   • 5 days: {today + timedelta(days=5)}")
            return False
        else:
            print(f"✅ Found {total_found} memberships that should trigger notifications!")
            return True
            
    except Exception as e:
        print(f"❌ Error checking memberships: {e}")
        return False

def provide_solutions():
    """Provide solutions based on the issues found"""
    print("\n🔧 SOLUTIONS AND NEXT STEPS")
    print("=" * 40)
    
    print("Based on the checks above, here are the solutions:")
    print()
    
    print("1. 🪟 IF YOU'RE ON WINDOWS:")
    print("   • Cron doesn't work on Windows")
    print("   • Use Task Scheduler instead:")
    print("   • Or test manually via Django admin")
    print()
    
    print("2. 📋 IF CRON JOB IS NOT IN CRONTAB:")
    print("   • Run: crontab -e")
    print("   • Add: 51 16 * * * cd /path/to/project && python manage.py check_membership_expiry")
    print("   • Save and exit")
    print()
    
    print("3. 🔔 IF NO FCM TOKENS:")
    print("   • Visit: http://localhost:8000/fcm-test/")
    print("   • Allow notifications")
    print("   • Register FCM token")
    print()
    
    print("4. 👥 IF NO MEMBERSHIPS TO NOTIFY:")
    print("   • Go to Django admin")
    print("   • Create/edit memberships with expiry dates")
    print("   • Use admin actions to test")
    print()
    
    print("5. 🧪 FOR IMMEDIATE TESTING:")
    print("   • Use Django admin actions")
    print("   • Run: python manage.py check_membership_expiry --verbose")
    print("   • Check browser for notifications")

def main():
    print("🔍 CRON JOB NOTIFICATION DEBUGGER")
    print("=" * 50)
    print("Checking why cron job notifications are not working...")
    print()
    
    # Run all checks
    checks = [
        ("Cron Service", check_cron_service),
        ("Crontab Entries", check_crontab),
        ("Dependencies", check_dependencies),
        ("FCM Tokens", check_fcm_tokens),
        ("Memberships", check_memberships),
        ("Manual Command Test", test_command_manually),
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"❌ {check_name} check failed: {e}")
            results[check_name] = False
    
    # Check logs (separate as it might not be available on all systems)
    try:
        check_cron_logs()
    except Exception as e:
        print(f"⚠️ Could not check cron logs: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 50)
    
    for check_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{check_name}: {status}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n🎯 Overall: {passed_count}/{total_count} checks passed")
    
    # Provide solutions
    provide_solutions()
    
    print(f"\n⏰ Current time: {datetime.now()}")
    print("🕐 Your cron job is set to run at: 4:51 PM daily (51 16 * * *)")

if __name__ == "__main__":
    main()
