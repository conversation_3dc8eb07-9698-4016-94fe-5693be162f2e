"""
Comprehensive Test Suite for Course-Related Django Components
Testing all views, models, serializers, URLs, admin.py, and class functions
for course-related functionality to ensure everything works properly.
"""

"""
This file contains comprehensive tests for course-related Django components.
Run using: python3 manage.py test comprehensive_course_tests
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
import json
from datetime import timedelta

# Import models
from questions.models import (
    Course, SubCourse, Tier, Paper, Section, Module, Subject, Topic, SubTopic,
    MasterQuestion, MasterOption, Question, Option, PreviousYearQuestion
)
from contributor.models import ContributorProfile
from customrcare.models import CustomrcareProfile
from students.models import Student
from paper_engine.models import TestPattern


class CourseModelTests(TestCase):
    """Test Course model functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.course_data = {
            'name': 'Test Course',
            'description': 'Test course description'
        }
    
    def test_course_creation(self):
        """Test Course model creation with valid data."""
        course = Course.objects.create(**self.course_data)
        self.assertEqual(course.name, 'Test Course')
        self.assertEqual(course.description, 'Test course description')
        self.assertIsNotNone(course.slug)
        self.assertIsNotNone(course.created_date)
        self.assertIsNotNone(course.updated_date)
    
    def test_course_str_method(self):
        """Test Course __str__ method."""
        course = Course.objects.create(**self.course_data)
        self.assertEqual(str(course), 'Test Course')
        
        # Test with None name
        course_no_name = Course.objects.create(description='No name course')
        self.assertEqual(str(course_no_name), 'Unnamed Course')
    
    def test_course_slug_generation(self):
        """Test Course slug is generated automatically."""
        course = Course.objects.create(name='Test Course Name')
        self.assertIsNotNone(course.slug)
        self.assertTrue(course.slug)
        
        # Test unique slug generation
        course2 = Course.objects.create(name='Test Course Name')
        self.assertNotEqual(course.slug, course2.slug)
    
    def test_course_fields_validation(self):
        """Test Course field validations."""
        # Test with null/blank fields
        course = Course.objects.create()
        self.assertIsNone(course.name)
        self.assertIsNone(course.description)
        self.assertIsNone(course.attachments)


class SubCourseModelTests(TestCase):
    """Test SubCourse model functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.course = Course.objects.create(name='Parent Course')
        self.subcourse_data = {
            'course': self.course,
            'name': 'Test SubCourse',
            'description': 'Test subcourse description'
        }
    
    def test_subcourse_creation(self):
        """Test SubCourse model creation."""
        subcourse = SubCourse.objects.create(**self.subcourse_data)
        self.assertEqual(subcourse.course, self.course)
        self.assertEqual(subcourse.name, 'Test SubCourse')
        self.assertIsNotNone(subcourse.slug)
    
    def test_subcourse_str_method(self):
        """Test SubCourse __str__ method."""
        subcourse = SubCourse.objects.create(**self.subcourse_data)
        expected_str = f"Test SubCourse (Parent Course)"
        self.assertEqual(str(subcourse), expected_str)
        
        # Test with None values
        subcourse_no_name = SubCourse.objects.create(course=self.course)
        self.assertEqual(str(subcourse_no_name), 'Unnamed SubCourse')
    
    def test_subcourse_relationship(self):
        """Test SubCourse relationship with Course."""
        subcourse = SubCourse.objects.create(**self.subcourse_data)
        self.assertIn(subcourse, self.course.sub_courses.all())


class SubjectModelTests(TestCase):
    """Test Subject model functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.subject_data = {
            'name': 'Mathematics',
            'description': 'Mathematics subject',
            'rank': 1
        }
    
    def test_subject_creation(self):
        """Test Subject model creation."""
        subject = Subject.objects.create(**self.subject_data)
        self.assertEqual(subject.name, 'Mathematics')
        self.assertEqual(subject.rank, 1)
        self.assertIsNotNone(subject.slug)
    
    def test_subject_str_method(self):
        """Test Subject __str__ method."""
        subject = Subject.objects.create(**self.subject_data)
        self.assertEqual(str(subject), 'Mathematics')
    
    def test_subject_rank_default(self):
        """Test Subject rank default value."""
        subject = Subject.objects.create(name='Physics')
        self.assertEqual(subject.rank, 0)


class TopicModelTests(TestCase):
    """Test Topic model functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.subject = Subject.objects.create(name='Mathematics')
        self.topic_data = {
            'subject': self.subject,
            'name': 'Algebra',
            'description': 'Algebra topic'
        }
    
    def test_topic_creation(self):
        """Test Topic model creation."""
        topic = Topic.objects.create(**self.topic_data)
        self.assertEqual(topic.subject, self.subject)
        self.assertEqual(topic.name, 'Algebra')
        self.assertIsNotNone(topic.slug)
    
    def test_topic_str_method(self):
        """Test Topic __str__ method."""
        topic = Topic.objects.create(**self.topic_data)
        expected_str = f"Algebra (Mathematics)"
        self.assertEqual(str(topic), expected_str)
    
    def test_topic_relationship(self):
        """Test Topic relationship with Subject."""
        topic = Topic.objects.create(**self.topic_data)
        self.assertIn(topic, self.subject.topics.all())


class SubTopicModelTests(TestCase):
    """Test SubTopic model functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.subject = Subject.objects.create(name='Mathematics')
        self.topic = Topic.objects.create(subject=self.subject, name='Algebra')
        self.subtopic_data = {
            'topic': self.topic,
            'name': 'Linear Equations',
            'description': 'Linear equations subtopic'
        }
    
    def test_subtopic_creation(self):
        """Test SubTopic model creation."""
        subtopic = SubTopic.objects.create(**self.subtopic_data)
        self.assertEqual(subtopic.topic, self.topic)
        self.assertEqual(subtopic.name, 'Linear Equations')
        self.assertIsNotNone(subtopic.slug)
    
    def test_subtopic_str_method(self):
        """Test SubTopic __str__ method."""
        subtopic = SubTopic.objects.create(**self.subtopic_data)
        expected_str = f"Linear Equations (Algebra)"
        self.assertEqual(str(subtopic), expected_str)
    
    def test_subtopic_relationship(self):
        """Test SubTopic relationship with Topic."""
        subtopic = SubTopic.objects.create(**self.subtopic_data)
        self.assertIn(subtopic, self.topic.subtopics.all())


class TierModelTests(TestCase):
    """Test Tier model functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.course = Course.objects.create(name='Test Course')
        self.subcourse = SubCourse.objects.create(course=self.course, name='Test SubCourse')
        self.tier_data = {
            'subcourse': self.subcourse,
            'name': 'Tier 1',
            'description': 'First tier'
        }
    
    def test_tier_creation(self):
        """Test Tier model creation."""
        tier = Tier.objects.create(**self.tier_data)
        self.assertEqual(tier.subcourse, self.subcourse)
        self.assertEqual(tier.name, 'Tier 1')
        self.assertIsNotNone(tier.slug)
    
    def test_tier_str_method(self):
        """Test Tier __str__ method."""
        tier = Tier.objects.create(**self.tier_data)
        expected_str = f"Tier 1 (Test SubCourse)"
        self.assertEqual(str(tier), expected_str)


class PaperModelTests(TestCase):
    """Test Paper model functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.course = Course.objects.create(name='Test Course')
        self.subcourse = SubCourse.objects.create(course=self.course, name='Test SubCourse')
        self.tier = Tier.objects.create(subcourse=self.subcourse, name='Tier 1')
        self.user = User.objects.create_user(username='student', password='test123')
        self.student = Student.objects.create(user=self.user, phone='1234567890')
        
        self.paper_data = {
            'tier': self.tier,
            'name': 'Test Paper',
            'description': 'Test paper description',
            'max_marks': 100,
            'duration': timedelta(hours=2),
            'student': self.student
        }
    
    def test_paper_creation(self):
        """Test Paper model creation."""
        paper = Paper.objects.create(**self.paper_data)
        self.assertEqual(paper.tier, self.tier)
        self.assertEqual(paper.name, 'Test Paper')
        self.assertEqual(paper.max_marks, 100)
        self.assertEqual(paper.duration, timedelta(hours=2))
        self.assertIsNotNone(paper.slug)
    
    def test_paper_str_method(self):
        """Test Paper __str__ method."""
        paper = Paper.objects.create(**self.paper_data)
        expected_str = f"Test Paper (Tier 1)"
        self.assertEqual(str(paper), expected_str)


class SectionModelTests(TestCase):
    """Test Section model functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.course = Course.objects.create(name='Test Course')
        self.subcourse = SubCourse.objects.create(course=self.course, name='Test SubCourse')
        self.tier = Tier.objects.create(subcourse=self.subcourse, name='Tier 1')
        self.paper = Paper.objects.create(tier=self.tier, name='Test Paper')
        
        self.section_data = {
            'paper': self.paper,
            'name': 'Section A',
            'description': 'First section',
            'max_marks': 50,
            'number_of_questions': 25
        }
    
    def test_section_creation(self):
        """Test Section model creation."""
        section = Section.objects.create(**self.section_data)
        self.assertEqual(section.paper, self.paper)
        self.assertEqual(section.name, 'Section A')
        self.assertEqual(section.max_marks, 50)
        self.assertEqual(section.number_of_questions, 25)
        self.assertIsNotNone(section.slug)
    
    def test_section_str_method(self):
        """Test Section __str__ method."""
        section = Section.objects.create(**self.section_data)
        expected_str = f"Section A (Test Paper)"
        self.assertEqual(str(section), expected_str)


class ModuleModelTests(TestCase):
    """Test Module model functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.course = Course.objects.create(name='Test Course')
        self.subcourse = SubCourse.objects.create(course=self.course, name='Test SubCourse')
        self.tier = Tier.objects.create(subcourse=self.subcourse, name='Tier 1')
        self.paper = Paper.objects.create(tier=self.tier, name='Test Paper')
        self.section = Section.objects.create(paper=self.paper, name='Section A')
        
        self.module_data = {
            'section': self.section,
            'name': 'Module 1',
            'description': 'First module'
        }
    
    def test_module_creation(self):
        """Test Module model creation."""
        module = Module.objects.create(**self.module_data)
        self.assertEqual(module.section, self.section)
        self.assertEqual(module.name, 'Module 1')
        self.assertIsNotNone(module.slug)
    
    def test_module_str_method(self):
        """Test Module __str__ method."""
        module = Module.objects.create(**self.module_data)
        expected_str = f"Module 1 (Section A)"
        self.assertEqual(str(module), expected_str)


class QuestionModelTests(TestCase):
    """Test Question model functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(username='contributor', password='test123')
        self.contributor = ContributorProfile.objects.create(user=self.user, role='contributor')
        self.subject = Subject.objects.create(name='Mathematics')
        self.topic = Topic.objects.create(subject=self.subject, name='Algebra')
        self.subtopic = SubTopic.objects.create(topic=self.topic, name='Linear Equations')
        self.course = Course.objects.create(name='Test Course')
        self.subcourse = SubCourse.objects.create(course=self.course, name='Test SubCourse')

        self.question_data = {
            'content': 'What is 2 + 2?',
            'difficulty': 5,
            'author': self.contributor,
            'status': 'active',
            'approval_status': 'approved',
            'language': 'english'
        }

    def test_question_creation(self):
        """Test Question model creation."""
        question = Question.objects.create(**self.question_data)
        self.assertEqual(question.content, 'What is 2 + 2?')
        self.assertEqual(question.difficulty, 5)
        self.assertEqual(question.author, self.contributor)
        self.assertIsNotNone(question.slug)

    def test_question_str_method(self):
        """Test Question __str__ method."""
        question = Question.objects.create(**self.question_data)
        expected_str = f"{question.question_id} | active"
        self.assertEqual(str(question), expected_str)

    def test_question_difficulty_level(self):
        """Test Question get_difficulty_level method."""
        # Test Easy level (1-4)
        question_easy = Question.objects.create(**{**self.question_data, 'difficulty': 3})
        self.assertEqual(question_easy.get_difficulty_level(), 'Easy')

        # Test Medium level (5-7)
        question_medium = Question.objects.create(**{**self.question_data, 'difficulty': 6})
        self.assertEqual(question_medium.get_difficulty_level(), 'Medium')

        # Test Hard level (8-9)
        question_hard = Question.objects.create(**{**self.question_data, 'difficulty': 8})
        self.assertEqual(question_hard.get_difficulty_level(), 'Hard')

        # Test Extremely Hard level (10)
        question_extreme = Question.objects.create(**{**self.question_data, 'difficulty': 10})
        self.assertEqual(question_extreme.get_difficulty_level(), 'Extremely Hard')

        # Test Invalid level
        question_invalid = Question.objects.create(**{**self.question_data, 'difficulty': 11})
        self.assertEqual(question_invalid.get_difficulty_level(), 'Invalid difficulty level')

    def test_question_many_to_many_relationships(self):
        """Test Question many-to-many relationships."""
        question = Question.objects.create(**self.question_data)

        # Test subject relationship
        question.subject.add(self.subject)
        self.assertIn(self.subject, question.subject.all())

        # Test topic relationship
        question.topic.add(self.topic)
        self.assertIn(self.topic, question.topic.all())

        # Test subtopic relationship
        question.sub_topic.add(self.subtopic)
        self.assertIn(self.subtopic, question.sub_topic.all())

        # Test course relationship
        question.course.add(self.course)
        self.assertIn(self.course, question.course.all())

        # Test subcourse relationship
        question.subcourse.add(self.subcourse)
        self.assertIn(self.subcourse, question.subcourse.all())


class OptionModelTests(TestCase):
    """Test Option model functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(username='contributor', password='test123')
        self.contributor = ContributorProfile.objects.create(user=self.user, role='contributor')
        self.question = Question.objects.create(
            content='What is 2 + 2?',
            difficulty=5,
            author=self.contributor
        )

        self.option_data = {
            'question': self.question,
            'option_text': '4',
            'is_correct': True
        }

    def test_option_creation(self):
        """Test Option model creation."""
        option = Option.objects.create(**self.option_data)
        self.assertEqual(option.question, self.question)
        self.assertEqual(option.option_text, '4')
        self.assertTrue(option.is_correct)
        self.assertIsNotNone(option.slug)

    def test_option_str_method(self):
        """Test Option __str__ method."""
        option = Option.objects.create(**self.option_data)
        self.assertEqual(str(option), '4')

    def test_option_relationship(self):
        """Test Option relationship with Question."""
        option = Option.objects.create(**self.option_data)
        self.assertIn(option, self.question.options.all())


class MasterQuestionModelTests(TestCase):
    """Test MasterQuestion model functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(username='contributor', password='test123')
        self.contributor = ContributorProfile.objects.create(user=self.user, role='contributor')

        self.master_question_data = {
            'author': self.contributor,
            'title': 'Reading Comprehension',
            'passage_content': 'This is a sample passage for reading comprehension.',
            'approval_status': 'pending'
        }

    def test_master_question_creation(self):
        """Test MasterQuestion model creation."""
        master_question = MasterQuestion.objects.create(**self.master_question_data)
        self.assertEqual(master_question.author, self.contributor)
        self.assertEqual(master_question.title, 'Reading Comprehension')
        self.assertEqual(master_question.approval_status, 'pending')
        self.assertIsNotNone(master_question.slug)

    def test_master_question_str_method(self):
        """Test MasterQuestion __str__ method."""
        master_question = MasterQuestion.objects.create(**self.master_question_data)
        expected_str = f"MasterQuestion: Reading Comprehension"
        self.assertEqual(str(master_question), expected_str)

        # Test with None title
        master_question_no_title = MasterQuestion.objects.create(
            author=self.contributor,
            passage_content='Sample passage'
        )
        self.assertEqual(str(master_question_no_title), 'MasterQuestion: Untitled')


class MasterOptionModelTests(TestCase):
    """Test MasterOption model functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(username='contributor', password='test123')
        self.contributor = ContributorProfile.objects.create(user=self.user, role='contributor')

        self.master_option_data = {
            'author': self.contributor,
            'title': 'Common Options Set',
            'option_content': 'Option A, Option B, Option C, Option D',
            'approval_status': 'pending'
        }

    def test_master_option_creation(self):
        """Test MasterOption model creation."""
        master_option = MasterOption.objects.create(**self.master_option_data)
        self.assertEqual(master_option.author, self.contributor)
        self.assertEqual(master_option.title, 'Common Options Set')
        self.assertEqual(master_option.approval_status, 'pending')
        self.assertIsNotNone(master_option.slug)

    def test_master_option_str_method(self):
        """Test MasterOption __str__ method."""
        master_option = MasterOption.objects.create(**self.master_option_data)
        expected_str = f"MasterOption: Common Options Set"
        self.assertEqual(str(master_option), expected_str)


class PreviousYearQuestionModelTests(TestCase):
    """Test PreviousYearQuestion model functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(username='contributor', password='test123')
        self.contributor = ContributorProfile.objects.create(user=self.user, role='contributor')
        self.course = Course.objects.create(name='Test Course')
        self.subcourse = SubCourse.objects.create(course=self.course, name='Test SubCourse')
        self.question = Question.objects.create(
            content='Previous year question?',
            difficulty=5,
            author=self.contributor
        )

        self.pyq_data = {
            'question': self.question,
            'year': 2023,
            'month': 'January',
            'course': self.course,
            'exams': self.subcourse,
            'status': 'active',
            'approval_status': 'approved'
        }

    def test_previous_year_question_creation(self):
        """Test PreviousYearQuestion model creation."""
        pyq = PreviousYearQuestion.objects.create(**self.pyq_data)
        self.assertEqual(pyq.question, self.question)
        self.assertEqual(pyq.year, 2023)
        self.assertEqual(pyq.month, 'January')
        self.assertEqual(pyq.course, self.course)
        self.assertEqual(pyq.exams, self.subcourse)
        self.assertIsNotNone(pyq.slug)

    def test_previous_year_question_str_method(self):
        """Test PreviousYearQuestion __str__ method."""
        pyq = PreviousYearQuestion.objects.create(**self.pyq_data)
        expected_str = f"Previous year question?-2023"
        self.assertEqual(str(pyq), expected_str)


if __name__ == '__main__':
    import unittest
    unittest.main()
