import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { <PERSON>, But<PERSON>, Row, Col, Form, Container, Modal } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { getTestPatterns, editTestPattern, deleteTestPattern } from "../../redux/slice/paperEngineSlice";
import { FaPlusCircle } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import NavigationBar from "../../commonComponents/NavigationBar";

const ViewTestPatterns = ({ patternAdded }) => {
  const navigate = useNavigate();
  const accessToken = useSelector((state) => state?.contributor?.accessToken);

  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPattern, setSelectedPattern] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [patternData, setPatternData] = useState({
    name: "",
    description: "",
    sections: [],
    negativeMarking: -0.33,
    positiveMark: 1,
    subject: "Science",
    randomTopic: true,
  });

  const dispatch = useDispatch();
  const { testPatterns, isLoading, error } = useSelector((state) => state.paperEngine);

  useEffect(() => {
    if (!accessToken) {
      toast.error("Please log in as a contributor!");
      const timer = setTimeout(() => {
        navigate("/contributor_login");
      }, 2000);
      return () => clearTimeout(timer);
    }

    dispatch(getTestPatterns());
    setLoading(false);
  }, [accessToken, navigate, dispatch, patternAdded]);

  const handleSearch = () => {
    setCurrentPage(0); // Reset to first page after search
  };

  const filteredPatterns = testPatterns.filter((pattern) =>
    pattern.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const reversedPatterns = [...filteredPatterns].reverse(); // Reverse the filtered patterns

  if (loading || !accessToken) {
    return null; // or you can show a loading spinner or a message like 'Loading...'
  }

  const handleView = (patternId) => {
    const pattern = testPatterns.find((p) => p.pattern_id === patternId);
    if (pattern) {
      setSelectedPattern(pattern);
      setShowViewModal(true);
    }
  };

  const handleDelete = (patternId) => {
    setSelectedPattern(testPatterns.find((p) => p.pattern_id === patternId));
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (selectedPattern) {
      dispatch(deleteTestPattern(selectedPattern.pattern_id))
        .then(() => {
          toast.success("Pattern deleted successfully.");
          setShowDeleteModal(false);
          dispatch(getTestPatterns()); // Re-fetch patterns after delete
        })
        .catch((error) => {
          toast.error(error.message || "Failed to delete pattern.");
        });
    }
  };

  const handleEdit = (patternId) => {
    const pattern = testPatterns.find((p) => p.pattern_id === patternId);
    if (pattern) {
      setPatternData({
        name: pattern.name,
        description: pattern.description,
        sections: pattern.sections || [],
        negativeMarking: pattern.negative_marking,
        positiveMark: pattern.positive_mark,
        subject: pattern.subject,
        randomTopic: pattern.random_topic,
      });
      setShowEditModal(true);
    }
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setPatternData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleSubmitEdit = (e) => {
    e.preventDefault();
    const updatedPattern = { ...patternData, pattern_id: selectedPattern.pattern_id };

    try {
      dispatch(editTestPattern(updatedPattern))
        .then(() => {
          toast.success("Pattern updated successfully.");
          setShowEditModal(false);
          dispatch(getTestPatterns()); // Re-fetch patterns after update
        })
        .catch((error) => {
          toast.error(error.message || "Failed to update pattern.");
        });
    } catch (error) {
      toast.error("An error occurred while updating the pattern.");
    }
  };

  return (
    <>
      <NavigationBar />
      <Container style={{ height: "90vh" }}>
        <h2 className="text-center text-success" style={{ fontSize: "1.5rem", margin: "1rem" }}>
          Test Patterns / View All
        </h2>

        {/* Search Bar */}
        <Row className="mb-4 justify-content-center">
          <Col xs={12} sm={8} md={6} lg={5} className="d-flex justify-content-between">
            <Form.Control
              type="text"
              placeholder="Search patterns..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-100"
            />
            <Link to="/view_test_patterns">
              <Button onClick={handleSearch} variant="success" style={{ marginLeft: "0.2rem" }}>
                Search
              </Button>
            </Link>
          </Col>
        </Row>

        {/* Loading State */}
        {isLoading && (
          <div className="text-center">
            <div className="spinner-border text-success" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center text-danger">
            <p>{error}</p>
          </div>
        )}

        {/* Patterns Cards */}
        <Row xs={1} md={3} lg={3} className="g-4">
          {reversedPatterns?.map((pattern) => (
            <Col key={pattern?.pattern_id} className="d-flex justify-content-center">
              <Card
                className="shadow-lg rounded-3"
                style={{
                  width: "95%",
                  maxWidth: "600px",
                  transform: "scale(0.95)",
                  transition: "transform 0.2s ease-in-out",
                  position: "relative"
                }}
                onMouseEnter={(e) => (e.currentTarget.style.transform = "scale(1)")}
                onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(0.95)")}
              >
                <Card.Header className="bg-primary text-white text-center">
                  <h5>{pattern.name}</h5>
                  <small>Pattern ID: {pattern?.pattern_id}</small>
                </Card.Header>
                <Card.Body>
                  <div className="row mb-3">
                    <div className="col-6">
                      {/* <p>
                        <strong>Subject:</strong> {pattern?.sections.flatMap(section => section.subject_ids).join(", ")}
                      </p> */}
                      <p>
                        <strong>Version:</strong> {pattern?.version}
                      </p>
                    </div>
                    <div className="col-6">
                      <p>
                        <strong>Updated At:</strong> {new Date(pattern?.updated_at).toLocaleString()}
                      </p>
                      <p>
                        <strong>Random Topic:</strong> {pattern?.random_topic ? "Yes" : "No"}
                      </p>
                    </div>
                  </div>

                  <div className="row mb-3">
                  {pattern.sections.map((section, index) => (
                    <div
                      key={index}
                      className="col-12 col-md-6"
                      style={{
                        border: "1px solid #ddd",
                        borderRadius: "8px",
                        padding: "10px",
                        marginBottom: "10px",
                        background: "#f9f9f9",
                      }}
                    >
                      <h6 className="text-success">{section?.section_name}</h6>
                      <p>
                        <strong>Time Limit:</strong> {section?.time_limit} min
                      </p>
                      <p>
                        <strong>Questions:</strong> {section?.number_of_questions}
                      </p>
                    </div>
                  ))}
                </div>

                  <div className="row " style={{marginBottom: "4rem"}}>
                    <div className="col-6">
                      <p>
                        <strong>Positive Mark:</strong> {pattern?.positive_mark}
                      </p>
                      <p>
                        <strong>Negative Mark:</strong> {pattern?.negative_marking}
                      </p>
                    </div>
                  </div>

                  <div style={{display: "flex", justifyContent: "space_between" }} >
                  <div style={{ position: 'absolute', bottom: "1rem" }}>

                    <Button
                      variant="outline-primary"
                      onClick={() => handleView(pattern?.pattern_id)}
                      className="w-30 mx-1"
                    >
                      View
                    </Button>
                    <Button
                      variant="outline-success"
                      onClick={() => handleEdit(pattern?.pattern_id)}
                      className="w-30 mx-1"
                    >
                      Edit
                    </Button>
                    <Button
                      variant="outline-danger"
                      onClick={() => handleDelete(pattern?.pattern_id)}
                      className="w-30 mx-1"
                    >
                      Delete
                    </Button>
                  </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      </Container>
    </>
  );
};

export default ViewTestPatterns;
