import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

export const lightTheme = {
  ...MD3LightTheme,  colors: {
    ...MD3LightTheme.colors,
    primary: '#4CAF50',
    secondary: '#6c757d',
    error: '#dc3545',
    background: '#f5f5f5',
    surface: '#ffffff',
    text: '#000000',
    onSurface: '#000000',
    disabled: '#c7c7c7',
    placeholder: '#666666',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    notification: '#198754',
  },
};

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: '#198754',
    secondary: '#6c757d',
    error: '#dc3545',
    background: '#121212',
    surface: '#1e1e1e',
    text: '#ffffff',
    onSurface: '#ffffff',
    disabled: '#666666',
    placeholder: '#999999',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    notification: '#198754',
  },
};
