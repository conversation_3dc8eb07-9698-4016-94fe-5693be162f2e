"""
Comprehensive test suite for PopupBanner functionality
"""
import os
import tempfile
from django.test import TestCase, override_settings
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.core.exceptions import ValidationError
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.urls import reverse

# Import models
from contributor.models import PopupBanner, ContributorProfile
from customrcare.models import CustomrcareProfile
from students.models import Student

# Import serializers
from contributor.serializers import (
    PopupBannerContributorSerializer,
    PopupBannerCustomerCareSerializer,
    PopupBannerAdminSerializer,
    PopupBannerPublicSerializer
)


class PopupBannerModelTest(TestCase):
    """Test PopupBanner model functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.contributor_user = User.objects.create_user(
            username='contributor1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.contributor_profile = ContributorProfile.objects.create(
            user=self.contributor_user
        )
        
        self.care_user = User.objects.create_user(
            username='care1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.care_profile = CustomrcareProfile.objects.create(
            user=self.care_user,
            contact=1234567890
        )
        
        self.admin_user = User.objects.create_superuser(
            username='admin1',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_popup_banner_creation(self):
        """Test basic PopupBanner creation"""
        banner = PopupBanner.objects.create(
            title="Test Banner",
            description="Test Description",
            content_type="text_only",
            text_content="Test content",
            created_by=self.contributor_user
        )
        
        self.assertEqual(banner.title, "Test Banner")
        self.assertEqual(banner.approval_status, "pending")
        self.assertFalse(banner.is_active)
        self.assertIsNotNone(banner.slug)
        self.assertEqual(banner.delay_ms, 0)  # Default value

    def test_popup_banner_delay_ms(self):
        """Test delay_ms field functionality"""
        banner = PopupBanner.objects.create(
            title="Delayed Banner",
            content_type="text_only",
            text_content="Test content",
            delay_ms=3000,
            created_by=self.contributor_user
        )

        self.assertEqual(banner.delay_ms, 3000)

    def test_popup_banner_validation_image_required(self):
        """Test validation for image-required content types"""
        banner = PopupBanner(
            title="Test Banner",
            content_type="image_only",
            created_by=self.contributor_user
        )
        
        with self.assertRaises(ValidationError):
            banner.full_clean()

    def test_popup_banner_validation_page_target_required(self):
        """Test validation for page_target content type"""
        banner = PopupBanner(
            title="Test Banner",
            content_type="page_target",
            created_by=self.contributor_user
        )

        with self.assertRaises(ValidationError):
            banner.full_clean()

        # Test with page_target provided
        banner.page_target = "/home"
        banner.full_clean()  # Should not raise

    def test_popup_banner_validation_text_required(self):
        """Test validation for text-required content types"""
        banner = PopupBanner(
            title="Test Banner",
            content_type="text_image",
            created_by=self.contributor_user
        )
        
        with self.assertRaises(ValidationError):
            banner.full_clean()
    
    def test_popup_banner_validation_link_required(self):
        """Test validation for link-required content types"""
        banner = PopupBanner(
            title="Test Banner",
            content_type="text_link",
            text_content="Test content",
            created_by=self.contributor_user
        )
        
        with self.assertRaises(ValidationError):
            banner.full_clean()
    
    def test_approval_workflow_care(self):
        """Test customer care approval workflow"""
        banner = PopupBanner.objects.create(
            title="Test Banner",
            content_type="text_only",
            text_content="Test content",
            created_by=self.contributor_user
        )
        
        # Test approval by care
        self.assertTrue(banner.can_be_approved_by_care())
        self.assertTrue(banner.approve_by_care(self.care_profile))
        self.assertEqual(banner.approval_status, "approved_by_care")
        self.assertEqual(banner.approved_by_care, self.care_profile)
        self.assertIsNotNone(banner.approved_at)
    
    def test_approval_workflow_admin(self):
        """Test admin approval workflow"""
        banner = PopupBanner.objects.create(
            title="Test Banner",
            content_type="text_only",
            text_content="Test content",
            created_by=self.contributor_user
        )
        
        # Test direct admin approval
        self.assertTrue(banner.can_be_approved_by_admin())
        self.assertTrue(banner.approve_by_admin(self.admin_user))
        self.assertEqual(banner.approval_status, "approved_by_admin")
        self.assertEqual(banner.approved_by_admin, self.admin_user)
    
    def test_rejection_workflow(self):
        """Test banner rejection workflow"""
        banner = PopupBanner.objects.create(
            title="Test Banner",
            content_type="text_only",
            text_content="Test content",
            created_by=self.contributor_user
        )
        
        # Test rejection by care
        self.assertTrue(banner.reject(self.care_user, "Not suitable"))
        self.assertEqual(banner.approval_status, "rejected_by_care")
        self.assertEqual(banner.rejection_reason, "Not suitable")
        self.assertEqual(banner.rejected_by, self.care_user)
        self.assertFalse(banner.is_active)
    
    def test_activation_workflow(self):
        """Test banner activation workflow"""
        banner = PopupBanner.objects.create(
            title="Test Banner",
            content_type="text_only",
            text_content="Test content",
            created_by=self.contributor_user
        )
        
        # Cannot activate without approval
        self.assertFalse(banner.activate())
        
        # Approve and then activate
        banner.approve_by_care(self.care_profile)
        self.assertTrue(banner.activate())
        self.assertTrue(banner.is_active)
        self.assertIsNotNone(banner.activated_at)


class PopupBannerSerializerTest(TestCase):
    """Test PopupBanner serializers"""
    
    def setUp(self):
        """Set up test data"""
        self.contributor_user = User.objects.create_user(
            username='contributor1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.contributor_profile = ContributorProfile.objects.create(
            user=self.contributor_user
        )
    
    def test_contributor_serializer_validation(self):
        """Test contributor serializer validation"""
        # Test valid data
        data = {
            'title': 'Test Banner',
            'description': 'Test Description',
            'content_type': 'text_only',
            'text_content': 'Test content',
            'priority': 'medium'
        }
        
        serializer = PopupBannerContributorSerializer(data=data)
        self.assertTrue(serializer.is_valid())
    
    def test_contributor_serializer_image_validation(self):
        """Test contributor serializer image validation"""
        # Test image_only without image
        data = {
            'title': 'Test Banner',
            'content_type': 'image_only',
        }
        
        serializer = PopupBannerContributorSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('image', serializer.errors)
    
    def test_contributor_serializer_link_validation(self):
        """Test contributor serializer link validation"""
        # Test text_link without link_url
        data = {
            'title': 'Test Banner',
            'content_type': 'text_link',
            'text_content': 'Test content'
        }
        
        serializer = PopupBannerContributorSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('link_url', serializer.errors)


@override_settings(MEDIA_ROOT=tempfile.mkdtemp())
class PopupBannerAPITest(APITestCase):
    """Test PopupBanner API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create users
        self.contributor_user = User.objects.create_user(
            username='contributor1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.contributor_profile = ContributorProfile.objects.create(
            user=self.contributor_user
        )
        
        self.care_user = User.objects.create_user(
            username='care1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.care_profile = CustomrcareProfile.objects.create(
            user=self.care_user,
            contact=1234567890
        )
        
        self.admin_user = User.objects.create_superuser(
            username='admin1',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test banner
        self.banner = PopupBanner.objects.create(
            title="Test Banner",
            content_type="text_only",
            text_content="Test content",
            created_by=self.contributor_user
        )
    
    def test_contributor_create_banner(self):
        """Test contributor can create banner"""
        self.client.force_authenticate(user=self.contributor_user)
        
        data = {
            'title': 'New Banner',
            'description': 'New Description',
            'content_type': 'text_only',
            'text_content': 'New content',
            'priority': 'high'
        }
        
        url = reverse('contributor-popup-banner-list-create')
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['title'], 'New Banner')
        self.assertEqual(response.data['created_by'], self.contributor_user.id)

    def test_contributor_create_page_target_banner(self):
        """Test contributor can create page_target banner"""
        self.client.force_authenticate(user=self.contributor_user)

        data = {
            'title': 'Homepage Banner',
            'description': 'Banner for homepage',
            'content_type': 'page_target',
            'page_target': '/home',
            'priority': 'medium',
            'delay_ms': 2000
        }

        url = reverse('contributor-popup-banner-list-create')
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['title'], 'Homepage Banner')
        self.assertEqual(response.data['content_type'], 'page_target')
        self.assertEqual(response.data['page_target'], '/home')
        self.assertEqual(response.data['delay_ms'], 2000)
    
    def test_contributor_list_own_banners(self):
        """Test contributor can only see own banners"""
        self.client.force_authenticate(user=self.contributor_user)
        
        url = reverse('contributor-popup-banner-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], self.banner.id)
    
    def test_customer_care_list_all_banners(self):
        """Test customer care can see all banners"""
        self.client.force_authenticate(user=self.care_user)
        
        url = reverse('customercare-popup-banner-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
    
    def test_customer_care_approve_banner(self):
        """Test customer care can approve banner"""
        self.client.force_authenticate(user=self.care_user)
        
        data = {
            'approval_status': 'approved_by_care'
        }
        
        url = reverse('customercare-popup-banner-detail', kwargs={'pk': self.banner.pk})
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.banner.refresh_from_db()
        self.assertEqual(self.banner.approval_status, 'approved_by_care')
    
    def test_public_api_active_banners_only(self):
        """Test public API returns only active banners"""
        # Make banner active
        self.banner.approve_by_care(self.care_profile)
        self.banner.activate()
        self.banner.save()
        
        url = reverse('popup-banner-public-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        
        # Deactivate banner
        self.banner.deactivate()
        self.banner.save()
        
        response = self.client.get(url)
        self.assertEqual(len(response.data), 0)
    
    def test_unauthorized_access(self):
        """Test unauthorized access is denied"""
        url = reverse('contributor-popup-banner-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
