import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get auth token
const getAuthToken = (getState) => {
  const { access } = getState().customerCare;
  return access;
};

// Base API URL for SOPs
const API_URL = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SOP_ENDPOINT}`;

const initialState = {
  isLoading: false,
  error: null,
  sops: [],
  totalCount: 0,
};

// Fetch all SOPs (role-based access)
export const fetchSOPs = createAsyncThunk(
  "sop/fetchAll",
  async (filters = {}, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const queryParams = new URLSearchParams();
      
      // Add filters to query params
      if (filters.access) queryParams.append('access', filters.access);
      if (filters.search) queryParams.append('search', filters.search);
      
      const url = queryParams.toString() ? `${API_URL}?${queryParams}` : API_URL;
      
      const response = await axios.get(url, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching SOPs");
    }
  }
);

// Create new SOP (Admin only)
export const createSOP = createAsyncThunk(
  "sop/create",
  async (formData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(API_URL, formData, {
        headers: { 
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        },
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error creating SOP");
    }
  }
);

// Get SOP details
export const getSOPDetails = createAsyncThunk(
  "sop/getDetails",
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(`${API_URL}${id}/`, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching SOP details");
    }
  }
);

// Update SOP (Admin only)
export const updateSOP = createAsyncThunk(
  "sop/update",
  async ({ id, formData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.patch(`${API_URL}${id}/`, formData, {
        headers: { 
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        },
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error updating SOP");
    }
  }
);

// Delete SOP (Admin only)
export const deleteSOP = createAsyncThunk(
  "sop/delete",
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(`${API_URL}${id}/`, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return { id };
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error deleting SOP");
    }
  }
);

const sopSlice = createSlice({
  name: "sop",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSOPs: (state) => {
      state.sops = [];
      state.totalCount = 0;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch SOPs
      .addCase(fetchSOPs.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSOPs.fulfilled, (state, action) => {
        state.isLoading = false;
        state.sops = Array.isArray(action.payload) ? action.payload : action.payload.results || [];
        state.totalCount = action.payload.count || state.sops.length;
      })
      .addCase(fetchSOPs.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Create SOP
      .addCase(createSOP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createSOP.fulfilled, (state, action) => {
        state.isLoading = false;
        state.sops.unshift(action.payload);
        state.totalCount += 1;
      })
      .addCase(createSOP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Get SOP details
      .addCase(getSOPDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSOPDetails.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(getSOPDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Update SOP
      .addCase(updateSOP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateSOP.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.sops.findIndex(sop => sop.id === action.payload.id);
        if (index !== -1) {
          state.sops[index] = action.payload;
        }
      })
      .addCase(updateSOP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Delete SOP
      .addCase(deleteSOP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteSOP.fulfilled, (state, action) => {
        state.isLoading = false;
        state.sops = state.sops.filter(sop => sop.id !== action.payload.id);
        state.totalCount -= 1;
      })
      .addCase(deleteSOP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, clearSOPs } = sopSlice.actions;
export default sopSlice.reducer;
