from django.contrib import admin
from django.utils.html import format_html
from django.contrib import messages
from django.utils import timezone
from .models import *
from .models_notifications import (
    CustomNotification,
    NotificationSchedule,
    NotificationLog,
    NotificationPreference
)

# Register your models here.


@admin.register(NotificationCategory)
class NotificationCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'icon', 'color', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    list_display = ['event_type', 'category', 'priority', 'is_active', 'created_at']
    list_filter = ['category', 'priority', 'is_active', 'created_at']
    search_fields = ['event_type', 'title_template', 'body_template']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(NotificationHistory)
class NotificationHistoryAdmin(admin.ModelAdmin):
    list_display = ['title', 'recipient', 'template', 'status', 'sent_at']
    list_filter = ['status', 'template__category', 'sent_at']
    search_fields = ['title', 'recipient__username', 'recipient__email']
    readonly_fields = ['sent_at', 'delivered_at', 'clicked_at']
    date_hierarchy = 'sent_at'


@admin.register(UserNotificationPreference)
class UserNotificationPreferenceAdmin(admin.ModelAdmin):
    list_display = ['user', 'email_notifications', 'push_notifications', 'sms_notifications']
    list_filter = ['email_notifications', 'push_notifications', 'sms_notifications']
    search_fields = ['user__username', 'user__email']
    filter_horizontal = ['categories']


admin.site.register(Librarian_param)
admin.site.register(DailyTransaction)
admin.site.register(MonthlyTransaction)
admin.site.register(ContactMessage)
admin.site.register(ComplaintTicket)
admin.site.register(QRCode)
admin.site.register(DeviceToken)


# Enhanced Notification System Admin
@admin.register(CustomNotification)
class CustomNotificationAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'priority', 'is_sent', 'sent_at', 'recipient_count', 'created_at'
    ]
    list_filter = ['priority', 'is_sent', 'send_to_all_librarians', 'send_to_all_sublibrarians']
    search_fields = ['title', 'message']
    readonly_fields = ['is_sent', 'sent_at', 'sent_by']
    filter_horizontal = ['specific_recipients']

    fieldsets = (
        ('Notification Content', {
            'fields': ('title', 'message', 'priority')
        }),
        ('Recipients', {
            'fields': (
                'send_to_all_librarians',
                'send_to_all_sublibrarians',
                'specific_recipients'
            )
        }),
        ('Scheduling', {
            'fields': ('send_immediately', 'scheduled_time')
        }),
        ('Status', {
            'fields': ('is_sent', 'sent_at', 'sent_by'),
            'classes': ('collapse',)
        }),
    )

    actions = ['send_selected_notifications']

    def recipient_count(self, obj):
        return len(obj.get_recipients())
    recipient_count.short_description = 'Recipients'

    def send_selected_notifications(self, request, queryset):
        """Send selected notifications"""
        sent_count = 0
        for notification in queryset.filter(is_sent=False):
            success, message = notification.send_notification(sent_by_user=request.user)
            if success:
                sent_count += 1
            else:
                self.message_user(
                    request,
                    f"Failed to send '{notification.title}': {message}",
                    level=messages.ERROR
                )

        if sent_count > 0:
            self.message_user(
                request,
                f"Successfully sent {sent_count} notifications.",
                level=messages.SUCCESS
            )

    send_selected_notifications.short_description = "Send selected notifications"

    def save_model(self, request, obj, form, change):
        """Auto-send notification if send_immediately is True"""
        super().save_model(request, obj, form, change)

        if obj.send_immediately and not obj.is_sent:
            success, message = obj.send_notification(sent_by_user=request.user)
            if success:
                self.message_user(
                    request,
                    f"Notification '{obj.title}' sent successfully!",
                    level=messages.SUCCESS
                )
            else:
                self.message_user(
                    request,
                    f"Failed to send notification: {message}",
                    level=messages.ERROR
                )


@admin.register(NotificationLog)
class NotificationLogAdmin(admin.ModelAdmin):
    list_display = [
        'event_type', 'recipient_count', 'successful_deliveries',
        'failed_deliveries', 'triggered_at', 'duration_seconds', 'triggered_by'
    ]
    list_filter = ['event_type', 'triggered_by', 'triggered_at']
    search_fields = ['event_type', 'triggered_by']
    readonly_fields = [
        'event_type', 'recipient_count', 'successful_deliveries',
        'failed_deliveries', 'event_data', 'error_details',
        'triggered_at', 'completed_at', 'duration_seconds', 'triggered_by'
    ]
    date_hierarchy = 'triggered_at'

    def has_add_permission(self, request):
        return False  # Logs are created automatically

    def has_change_permission(self, request, obj=None):
        return False  # Logs should not be modified

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser  # Only superusers can delete logs


@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'push_notifications', 'email_notifications',
        'weekend_notifications', 'updated_at'
    ]
    list_filter = [
        'push_notifications', 'email_notifications', 'weekend_notifications'
    ]
    search_fields = ['user__username', 'user__email']

    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('General Preferences', {
            'fields': (
                'email_notifications',
                'push_notifications',
                'sms_notifications'
            )
        }),
        ('Event Preferences', {
            'fields': (
                'qr_registration_notifications',
                'visitor_notifications',
                'financial_notifications',
                'member_expiry_notifications',
                'sales_milestone_notifications',
                'galla_reminder_notifications',
                'custom_admin_notifications',
            )
        }),
        ('Timing Preferences', {
            'fields': (
                'quiet_hours_start',
                'quiet_hours_end',
                'weekend_notifications',
            )
        }),
    )


@admin.register(AnalyticsCache)
class AnalyticsCacheAdmin(admin.ModelAdmin):
    list_display = [
        'librarian_name',
        'students_this_month',
        'new_registrations_this_month',
        'todays_collection',
        'last_calculated',
        'needs_refresh',
        'cache_actions'
    ]
    list_filter = [
        'last_calculated',
        'force_recalculate',
        'last_student_activity',
        'last_invoice_activity'
    ]
    search_fields = ['librarian__library_name', 'librarian__user__username']
    readonly_fields = [
        'last_calculated',
        'last_student_activity',
        'last_invoice_activity',
        'last_visitor_activity'
    ]

    fieldsets = (
        ('Library Information', {
            'fields': ('librarian',)
        }),
        ('Current Analytics', {
            'fields': (
                'students_this_month',
                'students_growth_percent',
                'students_growth_positive',
                'new_registrations_this_month',
                'registrations_growth_percent',
                'registrations_growth_positive',
                'todays_collection',
            )
        }),
        ('Chart Data', {
            'fields': (
                'growth_months',
                'growth_counts',
                'revenue_months',
                'revenue_amounts',
                'visitor_days',
                'visitor_counts',
            ),
            'classes': ('collapse',)
        }),
        ('Cache Management', {
            'fields': (
                'force_recalculate',
                'last_calculated',
                'last_student_activity',
                'last_invoice_activity',
                'last_visitor_activity',
            )
        }),
    )

    def librarian_name(self, obj):
        return obj.librarian.library_name
    librarian_name.short_description = 'Library'
    librarian_name.admin_order_field = 'librarian__library_name'

    def needs_refresh(self, obj):
        needs = obj.needs_recalculation()
        if needs:
            return format_html('<span style="color: orange;">⚠️ Yes</span>')
        return format_html('<span style="color: green;">✅ No</span>')
    needs_refresh.short_description = 'Needs Refresh'

    def cache_actions(self, obj):
        return format_html(
            '<a class="button" href="#" onclick="'
            'if(confirm(\'Force refresh cache for {}?\')) {{'
            'fetch(\'/admin/refresh-cache/{}/\', {{method: \'POST\', '
            'headers: {{\'X-CSRFToken\': document.querySelector(\'[name=csrfmiddlewaretoken]\').value}}}});'
            'location.reload();'
            '}}">'
            '🔄 Refresh</a>',
            obj.librarian.library_name,
            obj.id
        )
    cache_actions.short_description = 'Actions'
    cache_actions.allow_tags = True

    actions = ['force_refresh_selected']

    def force_refresh_selected(self, request, queryset):
        """Admin action to force refresh selected caches"""
        count = 0
        for cache in queryset:
            cache.force_refresh()
            count += 1

        self.message_user(
            request,
            f'Successfully marked {count} analytics caches for refresh.',
            messages.SUCCESS
        )
    force_refresh_selected.short_description = "Force refresh selected analytics caches"
