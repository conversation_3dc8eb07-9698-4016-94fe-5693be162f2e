import { useState } from "react";
import { <PERSON>, But<PERSON>, Accordion } from "react-bootstrap";
import {
  FaQuestionCircle,
  FaRegCircle,
  FaEdit,
  FaTrashAlt,
  FaClipboardList,
  FaInfoCircle,
  FaPlus,
} from "react-icons/fa";
import QuestionRejectReasonModal from "./QuestionRejectReasonModal";
import MathTextRenderer from "./MathTextRenderer";

const NormalQuestionCard = ({ question, handleEditQuestionOption, handleDeleteQuestionOption, handleMarkAsPrevYear, handleEditQuestion, handleDeleteQuestion, handleAddOption }) => {
  const [showReasonModal, setShowReasonModal] = useState(false);

  const handleShowReason = () => setShowReasonModal(true);
  const handleCloseReason = () => setShowReasonModal(false);

  return (
    <>
      <Card
        key={question?.question_id}
        className="shadow position-relative"
        style={{
          width: "100%",
          marginBottom: "1rem",
          backgroundColor:
            question?.approval_status === "approved"
              ? "#e6ffee"
              : question?.approval_status === "rejected"
              ? "#ffe6e6"
              : "#ffffb3",
        }}
      >
        <Card.Body>
          <div className="d-flex justify-content-between">
            <div>
              <Card.Title>
                <h6 style={{ fontSize: "0.9rem" }}>
                  Subject: {question?.subject_name} | Topic:{" "}
                  {question?.topic_name} | Sub Topic: {question?.sub_topic_name}
                </h6>
              </Card.Title>
              <Card.Text
                style={{
                  marginRight: "0.7rem",
                  textAlign: "justify",
                  fontSize: "1.1rem",
                }}
              >
                <FaQuestionCircle /> <MathTextRenderer text={question?.content} />
              </Card.Text>

              {/* Question Image - on next line */}
              {question?.attachments && (
                <div className="question-image mt-3 mb-3">
                  <img
                    src={`${import.meta.env.VITE_BASE_URL}/${question?.attachments}`}
                    alt="Question attachment"
                    className="img-fluid rounded-3 shadow-sm"
                    style={{
                      maxWidth: "100%",
                      height: "auto",
                      border: "1px solid #e9ecef"
                    }}
                  />
                </div>
              )}

              {/* Add rejection reason link */}
              {question?.approval_status === "rejected" && (
                <Button
                  variant="link"
                  className="text-danger p-0 mb-2"
                  onClick={handleShowReason}
                >
                  <FaInfoCircle className="me-1" />
                  See why it rejected
                </Button>
              )}

              {question?.options && question?.options?.length > 0 ? (
                <div className="mt-2">
                  <ol
                    style={{ listStyleType: "decimal", padding: "0rem 2rem" }}
                  >
                    {question?.options.map((option) => (
                      <li
                        key={option.option_id}
                        style={{
                          color: option?.is_correct ? "#198754" : "#dc3545",
                          padding: "8px",
                          borderRadius: "4px",
                          fontSize: "1rem",
                          margin: "8px 0",
                          display: "flex",
                          alignItems: "flex-start",
                          gap: "8px",
                        }}
                      >
                        <FaRegCircle style={{ marginTop: "4px", flexShrink: 0 }} />
                        <div style={{ flex: 1, minWidth: 0 }}>
                          <div className="option-text">
                            <MathTextRenderer text={option?.option_text} />
                          </div>
                          {/* Option Image - on next line */}
                          {option?.attachments && (
                            <div className="option-image mt-3">
                              <img
                                src={`${import.meta.env.VITE_BASE_URL}/${option?.attachments}`}
                                alt="Option attachment"
                                className="img-fluid rounded-3 shadow-sm"
                                style={{
                                  maxWidth: "250px",
                                  height: "auto",
                                  border: "1px solid #e9ecef"
                                }}
                              />
                            </div>
                          )}
                        </div>
                        <div style={{ display: "flex", gap: "4px", flexShrink: 0 }}>
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() =>
                              handleEditQuestionOption(option, question?.slug)
                            }
                            title="Edit Option"
                          >
                            <FaEdit size={12} />
                          </Button>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() =>
                              handleDeleteQuestionOption(
                                option?.slug,
                                question?.slug
                              )
                            }
                            title="Delete Option"
                          >
                            <FaTrashAlt size={12} />
                          </Button>
                        </div>
                      </li>
                    ))}
                  </ol>
                </div>
              ) : (
                <div className="no-options text-center py-3 mt-3">
                  <p className="text-muted mb-2">No options available for this question.</p>
                  <Button
                    variant="outline-primary"
                    size="sm"
                    onClick={() => handleAddOption && handleAddOption(question)}
                    className="d-flex align-items-center gap-2 mx-auto"
                  >
                    <FaPlus size={12} />
                    Add Options
                  </Button>
                </div>
              )}

             
            </div>
            <div className="d-flex flex-column align-items-center justify-content-center">
              <Button
                variant="outline-info"
                className="action-buttons m-1"
                onClick={() => handleAddOption && handleAddOption(question)}
                title="Add Options"
              >
                <FaPlus size={15} />
              </Button>
              <Button
                variant="outline-success"
                className="action-buttons m-1"
                onClick={() => handleMarkAsPrevYear(question)}
                title="Mark as Previous Year Question"
              >
                <FaClipboardList size={15} />
              </Button>
              <Button
                variant="outline-primary"
                className="action-buttons m-1"
                onClick={() => handleEditQuestion(question)}
                title="Edit Question"
              >
                <FaEdit size={15} />
              </Button>
              <Button
                variant="outline-danger"
                onClick={() => handleDeleteQuestion(question?.slug)}
                className="m-1"
                title="Delete Question"
              >
                <FaTrashAlt size={15} />
              </Button>
            </div>
          </div>
           {/* Move explanation accordion here */}
           {question?.explanation && (
                <Accordion className="mt-3">
                  <Accordion.Item eventKey="0">
                    <Accordion.Header>Explanation</Accordion.Header>
                    <Accordion.Body>
                      <div className="explanation-text">
                        <MathTextRenderer text={question?.explanation} />
                      </div>
                      {/* Explanation Image - on next line */}
                      {question?.explanation_attachment && (
                        <div className="explanation-image mt-3">
                          <img
                            src={`${import.meta.env.VITE_BASE_URL}/${question?.explanation_attachment}`}
                            alt="Explanation attachment"
                            className="img-fluid rounded-3 shadow-sm"
                            style={{
                              maxWidth: "100%",
                              height: "auto",
                              border: "1px solid #e9ecef"
                            }}
                          />
                        </div>
                      )}
                    </Accordion.Body>
                  </Accordion.Item>
                </Accordion>
              )}
          
        </Card.Body>
      </Card>

      <QuestionRejectReasonModal
        show={showReasonModal}
        onHide={handleCloseReason}
        reason={question?.reason}
        reasonDocument={question?.reason_document}
      />
    </>
  );
};

export default NormalQuestionCard;
