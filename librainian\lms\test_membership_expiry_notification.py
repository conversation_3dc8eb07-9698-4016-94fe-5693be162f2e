#!/usr/bin/env python3
"""
Test Membership Expiry Notification
This script tests the membership expiry notification for the membership you changed in Django admin.
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Library.settings')
django.setup()

def test_membership_expiry_notification():
    """Test membership expiry notification for today's date"""
    print("🏛️ Testing Membership Expiry Notification")
    print("=" * 50)
    
    try:
        from membership.models import Membership
        from librarian.notification_events import notification_events
        from librarian.models import DeviceToken
        from django.utils import timezone
        
        today = timezone.now().date()
        print(f"📅 Today's date: {today}")
        
        # Check if we have device tokens for librarians
        device_tokens = DeviceToken.objects.filter(is_active=True)
        if not device_tokens.exists():
            print("❌ No active device tokens found!")
            print("💡 Please visit http://localhost:8000/fcm-test/ and register a token first")
            return False
        
        print(f"✅ Found {device_tokens.count()} active device tokens")
        
        # Check memberships expiring today
        memberships_today = Membership.objects.filter(expiry_date=today)
        print(f"\n🔍 Memberships expiring today ({today}): {memberships_today.count()}")
        
        for membership in memberships_today:
            print(f"   📋 Membership ID: {membership.pk}")
            print(f"   👤 Librarian: {membership.librarian.user.username}")
            print(f"   📦 Plan: {membership.plan.name}")
            print(f"   📅 Start Date: {membership.start_date}")
            print(f"   ⏰ Expiry Date: {membership.expiry_date}")
            print(f"   🏛️ Library: {getattr(membership.librarian, 'library_name', 'N/A')}")
        
        if not memberships_today.exists():
            print("   ⚠️ No memberships found expiring today")
            
            # Check if there are any memberships at all
            all_memberships = Membership.objects.all()
            print(f"\n📊 Total memberships in system: {all_memberships.count()}")
            
            if all_memberships.exists():
                print("\n📋 All memberships:")
                for membership in all_memberships:
                    print(f"   ID: {membership.pk} | Librarian: {membership.librarian.user.username} | Expiry: {membership.expiry_date}")
            
            return False
        
        # Test the notification system
        print(f"\n🔔 Testing membership expiry notifications...")
        count = notification_events.check_membership_expiry_notifications()
        
        print(f"✅ Membership expiry check completed!")
        print(f"📊 Notifications sent: {count}")
        
        if count > 0:
            print("🎉 SUCCESS: Membership expiry notifications sent!")
            print("💡 Check your browser for the notification popup")
            return True
        else:
            print("⚠️ No notifications sent - this might be normal if no memberships are expiring")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_expiry_periods():
    """Test all expiry periods (10, 5, 1 days, today, 4 days after)"""
    print("\n🔍 Testing All Expiry Periods")
    print("=" * 40)
    
    try:
        from membership.models import Membership
        from django.utils import timezone
        
        today = timezone.now().date()
        
        # Check different expiry periods
        expiry_periods = [
            (10, 'expires in 10 days'),
            (5, 'expires in 5 days'),
            (1, 'expires tomorrow'),
            (0, 'expires today'),
            (-4, 'expired 4 days ago')
        ]
        
        for days, description in expiry_periods:
            target_date = today + timedelta(days=days)
            memberships = Membership.objects.filter(expiry_date=target_date)
            
            print(f"📅 {target_date} ({description}): {memberships.count()} memberships")
            
            for membership in memberships:
                print(f"   👤 {membership.librarian.user.username} - {membership.plan.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking expiry periods: {e}")
        return False

def send_manual_test_notification():
    """Send a manual test notification to verify the system works"""
    print("\n🧪 Sending Manual Test Notification")
    print("=" * 40)
    
    try:
        from librarian.notification_utils import send_notification_to_all_users
        
        result = send_notification_to_all_users(
            title="🏛️ Membership Expiry Test",
            body="This is a test notification for membership expiry. Your membership expires today! Please renew to continue using the library management system.",
            data={
                "type": "membership_expiry_test",
                "expiry_date": "2025-07-22",
                "test": True
            }
        )
        
        if result and result.get('successful_count', 0) > 0:
            print("✅ Manual test notification sent successfully!")
            print(f"📊 Success: {result['successful_count']}, Failed: {result['failed_count']}")
            return True
        else:
            print("❌ Manual test notification failed")
            return False
            
    except Exception as e:
        print(f"❌ Error sending manual test: {e}")
        return False

def main():
    print("🧪 MEMBERSHIP EXPIRY NOTIFICATION TEST")
    print("=" * 60)
    print("This test checks the membership you changed in Django admin")
    
    # Test membership expiry notification
    membership_test = test_membership_expiry_notification()
    
    # Test all expiry periods
    periods_test = test_all_expiry_periods()
    
    # Send manual test notification
    manual_test = send_manual_test_notification()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    print(f"🏛️ Membership Expiry Test: {'✅ PASSED' if membership_test else '⚠️ NO DATA/FAILED'}")
    print(f"📅 Expiry Periods Check: {'✅ PASSED' if periods_test else '❌ FAILED'}")
    print(f"🧪 Manual Test Notification: {'✅ PASSED' if manual_test else '❌ FAILED'}")
    
    if manual_test:
        print("\n🎉 SUCCESS: Notification system is working!")
        print("💡 If you didn't get the membership expiry notification, it might be because:")
        print("   • The membership expiry date doesn't match today's date exactly")
        print("   • The notification was already sent for this membership")
        print("   • The membership model structure is different than expected")
    
    print("\n🌐 Next Steps:")
    print("   1. Check the browser for the manual test notification")
    print("   2. Verify the membership expiry date in Django admin")
    print("   3. Run the notification check command manually")

if __name__ == "__main__":
    main()
