#!/usr/bin/env python3
"""
Database Operations Testing Script for Course-Related Django Components
This script performs comprehensive testing of database queries, filtering, ordering, and performance.
"""

import os
import sys
import django
import traceback
import time
from datetime import timedelta

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Import after Django setup
from django.contrib.auth.models import User
from django.db import connection
from django.db.models import Count, Q, Prefetch
from questions.models import (
    Course, SubCourse, Tier, Paper, Section, Module, Subject, Topic, SubTopic,
    MasterQuestion, MasterOption, Question, Option, PreviousYearQuestion
)
from contributor.models import ContributorProfile
from students.models import Student


class TestResults:
    """Class to track test results."""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"✅ PASS: {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"❌ FAIL: {test_name} - {error}")
    
    def summary(self):
        total = self.passed + self.failed
        print(f"\n{'='*60}")
        print(f"DATABASE OPERATIONS TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success Rate: {(self.passed/total*100):.1f}%" if total > 0 else "No tests run")
        
        if self.errors:
            print(f"\n{'='*60}")
            print(f"FAILED TESTS:")
            print(f"{'='*60}")
            for error in self.errors:
                print(f"- {error}")


def test_basic_crud_operations():
    """Test basic Create, Read, Update, Delete operations."""
    results = TestResults()
    
    try:
        # Test 1: Create operations
        course = Course.objects.create(name='CRUD Test Course', description='Test Description')
        if course.course_id:
            results.add_pass("Course creation")
        else:
            results.add_fail("Course creation", "Course ID not generated")
        
        subject = Subject.objects.create(name='CRUD Test Subject', rank=1)
        if subject.subject_id:
            results.add_pass("Subject creation")
        else:
            results.add_fail("Subject creation", "Subject ID not generated")
        
        # Test 2: Read operations
        retrieved_course = Course.objects.get(course_id=course.course_id)
        if retrieved_course.name == 'CRUD Test Course':
            results.add_pass("Course retrieval by ID")
        else:
            results.add_fail("Course retrieval by ID", "Retrieved course name doesn't match")
        
        retrieved_subject = Subject.objects.get(name='CRUD Test Subject')
        if retrieved_subject.rank == 1:
            results.add_pass("Subject retrieval by name")
        else:
            results.add_fail("Subject retrieval by name", "Retrieved subject rank doesn't match")
        
        # Test 3: Update operations
        course.description = 'Updated Description'
        course.save()
        updated_course = Course.objects.get(course_id=course.course_id)
        if updated_course.description == 'Updated Description':
            results.add_pass("Course update")
        else:
            results.add_fail("Course update", "Course description not updated")
        
        Subject.objects.filter(name='CRUD Test Subject').update(rank=2)
        updated_subject = Subject.objects.get(name='CRUD Test Subject')
        if updated_subject.rank == 2:
            results.add_pass("Subject bulk update")
        else:
            results.add_fail("Subject bulk update", "Subject rank not updated")
        
        # Test 4: Delete operations
        course_id = course.course_id
        course.delete()
        if not Course.objects.filter(course_id=course_id).exists():
            results.add_pass("Course deletion")
        else:
            results.add_fail("Course deletion", "Course still exists after deletion")
        
        Subject.objects.filter(name='CRUD Test Subject').delete()
        if not Subject.objects.filter(name='CRUD Test Subject').exists():
            results.add_pass("Subject bulk deletion")
        else:
            results.add_fail("Subject bulk deletion", "Subject still exists after deletion")
        
    except Exception as e:
        results.add_fail("Basic CRUD operations tests", f"Exception: {str(e)}")
    
    return results


def test_relationship_queries():
    """Test queries involving model relationships."""
    results = TestResults()
    
    try:
        # Setup test data
        course = Course.objects.create(name='Relationship Test Course')
        subcourse = SubCourse.objects.create(course=course, name='Test SubCourse')
        subject = Subject.objects.create(name='Relationship Test Subject')
        topic = Topic.objects.create(subject=subject, name='Test Topic')
        subtopic = SubTopic.objects.create(topic=topic, name='Test SubTopic')
        
        # Test 1: Forward relationship queries
        subcourses = SubCourse.objects.filter(course=course)
        if subcourses.count() == 1 and subcourses.first().name == 'Test SubCourse':
            results.add_pass("Forward relationship query (Course -> SubCourse)")
        else:
            results.add_fail("Forward relationship query (Course -> SubCourse)", "Incorrect subcourse count or data")
        
        topics = Topic.objects.filter(subject=subject)
        if topics.count() == 1 and topics.first().name == 'Test Topic':
            results.add_pass("Forward relationship query (Subject -> Topic)")
        else:
            results.add_fail("Forward relationship query (Subject -> Topic)", "Incorrect topic count or data")
        
        # Test 2: Reverse relationship queries
        course_from_subcourse = subcourse.course
        if course_from_subcourse.name == 'Relationship Test Course':
            results.add_pass("Reverse relationship query (SubCourse -> Course)")
        else:
            results.add_fail("Reverse relationship query (SubCourse -> Course)", "Incorrect course data")
        
        subject_from_topic = topic.subject
        if subject_from_topic.name == 'Relationship Test Subject':
            results.add_pass("Reverse relationship query (Topic -> Subject)")
        else:
            results.add_fail("Reverse relationship query (Topic -> Subject)", "Incorrect subject data")
        
        # Test 3: Related manager queries
        course_subcourses = course.sub_courses.all()
        if course_subcourses.count() == 1:
            results.add_pass("Related manager query (Course.sub_courses)")
        else:
            results.add_fail("Related manager query (Course.sub_courses)", f"Expected 1, got {course_subcourses.count()}")
        
        subject_topics = subject.topics.all()
        if subject_topics.count() == 1:
            results.add_pass("Related manager query (Subject.topics)")
        else:
            results.add_fail("Related manager query (Subject.topics)", f"Expected 1, got {subject_topics.count()}")
        
        # Test 4: Nested relationship queries
        subtopics_by_subject = SubTopic.objects.filter(topic__subject=subject)
        if subtopics_by_subject.count() == 1:
            results.add_pass("Nested relationship query (SubTopic -> Topic -> Subject)")
        else:
            results.add_fail("Nested relationship query (SubTopic -> Topic -> Subject)", f"Expected 1, got {subtopics_by_subject.count()}")
        
        # Cleanup
        subtopic.delete()
        topic.delete()
        subject.delete()
        subcourse.delete()
        course.delete()
        
    except Exception as e:
        results.add_fail("Relationship queries tests", f"Exception: {str(e)}")
    
    return results


def test_filtering_and_searching():
    """Test various filtering and searching operations."""
    results = TestResults()
    
    try:
        # Setup test data
        course1 = Course.objects.create(name='Python Programming', description='Learn Python')
        course2 = Course.objects.create(name='Java Programming', description='Learn Java')
        course3 = Course.objects.create(name='Web Development', description='Learn Web Development')
        
        subject1 = Subject.objects.create(name='Mathematics', rank=1)
        subject2 = Subject.objects.create(name='Physics', rank=2)
        subject3 = Subject.objects.create(name='Chemistry', rank=3)
        
        # Test 1: Exact filtering
        python_courses = Course.objects.filter(name='Python Programming')
        if python_courses.count() == 1:
            results.add_pass("Exact filtering")
        else:
            results.add_fail("Exact filtering", f"Expected 1, got {python_courses.count()}")
        
        # Test 2: Contains filtering
        programming_courses = Course.objects.filter(name__icontains='programming')
        if programming_courses.count() == 2:
            results.add_pass("Contains filtering (case-insensitive)")
        else:
            results.add_fail("Contains filtering (case-insensitive)", f"Expected 2, got {programming_courses.count()}")
        
        # Test 3: Startswith filtering
        learn_courses = Course.objects.filter(description__startswith='Learn')
        if learn_courses.count() == 3:
            results.add_pass("Startswith filtering")
        else:
            results.add_fail("Startswith filtering", f"Expected 3, got {learn_courses.count()}")
        
        # Test 4: Range filtering
        mid_rank_subjects = Subject.objects.filter(rank__range=[1, 2])
        if mid_rank_subjects.count() == 2:
            results.add_pass("Range filtering")
        else:
            results.add_fail("Range filtering", f"Expected 2, got {mid_rank_subjects.count()}")
        
        # Test 5: Q object filtering (OR conditions)
        math_or_physics = Subject.objects.filter(Q(name='Mathematics') | Q(name='Physics'))
        if math_or_physics.count() == 2:
            results.add_pass("Q object OR filtering")
        else:
            results.add_fail("Q object OR filtering", f"Expected 2, got {math_or_physics.count()}")
        
        # Test 6: Exclude filtering
        non_chemistry = Subject.objects.exclude(name='Chemistry')
        if non_chemistry.count() == 2:
            results.add_pass("Exclude filtering")
        else:
            results.add_fail("Exclude filtering", f"Expected 2, got {non_chemistry.count()}")
        
        # Cleanup
        Course.objects.filter(name__in=['Python Programming', 'Java Programming', 'Web Development']).delete()
        Subject.objects.filter(name__in=['Mathematics', 'Physics', 'Chemistry']).delete()
        
    except Exception as e:
        results.add_fail("Filtering and searching tests", f"Exception: {str(e)}")
    
    return results


def test_ordering_and_aggregation():
    """Test ordering and aggregation operations."""
    results = TestResults()
    
    try:
        # Setup test data
        subject1 = Subject.objects.create(name='Mathematics', rank=3)
        subject2 = Subject.objects.create(name='Physics', rank=1)
        subject3 = Subject.objects.create(name='Chemistry', rank=2)
        
        course = Course.objects.create(name='Test Course')
        topic1 = Topic.objects.create(subject=subject1, name='Algebra')
        topic2 = Topic.objects.create(subject=subject1, name='Geometry')
        topic3 = Topic.objects.create(subject=subject2, name='Mechanics')
        
        # Test 1: Ordering by single field
        subjects_by_rank = Subject.objects.order_by('rank')
        if (subjects_by_rank[0].name == 'Physics' and 
            subjects_by_rank[1].name == 'Chemistry' and 
            subjects_by_rank[2].name == 'Mathematics'):
            results.add_pass("Ordering by single field (ascending)")
        else:
            results.add_fail("Ordering by single field (ascending)", "Incorrect order")
        
        # Test 2: Reverse ordering
        subjects_by_rank_desc = Subject.objects.order_by('-rank')
        if subjects_by_rank_desc[0].name == 'Mathematics':
            results.add_pass("Ordering by single field (descending)")
        else:
            results.add_fail("Ordering by single field (descending)", "Incorrect order")
        
        # Test 3: Multiple field ordering
        subjects_by_name_rank = Subject.objects.order_by('name', 'rank')
        if subjects_by_name_rank[0].name == 'Chemistry':
            results.add_pass("Multiple field ordering")
        else:
            results.add_fail("Multiple field ordering", "Incorrect order")
        
        # Test 4: Count aggregation
        subject_topic_counts = Subject.objects.annotate(topic_count=Count('topics'))
        math_subject = subject_topic_counts.get(name='Mathematics')
        if math_subject.topic_count == 2:
            results.add_pass("Count aggregation")
        else:
            results.add_fail("Count aggregation", f"Expected 2, got {math_subject.topic_count}")
        
        # Test 5: Filtering with aggregation
        subjects_with_topics = Subject.objects.annotate(topic_count=Count('topics')).filter(topic_count__gt=0)
        if subjects_with_topics.count() == 2:  # Mathematics and Physics have topics
            results.add_pass("Filtering with aggregation")
        else:
            results.add_fail("Filtering with aggregation", f"Expected 2, got {subjects_with_topics.count()}")
        
        # Cleanup
        Topic.objects.filter(subject__in=[subject1, subject2, subject3]).delete()
        Subject.objects.filter(name__in=['Mathematics', 'Physics', 'Chemistry']).delete()
        course.delete()
        
    except Exception as e:
        results.add_fail("Ordering and aggregation tests", f"Exception: {str(e)}")
    
    return results


def test_query_optimization():
    """Test query optimization techniques."""
    results = TestResults()
    
    try:
        # Setup test data
        import uuid
        unique_username = f'testcontributor_{uuid.uuid4().hex[:8]}'
        user = User.objects.create_user(username=unique_username, password='test123')
        contributor = ContributorProfile.objects.create(user=user, role='contributor')
        
        subject = Subject.objects.create(name='Optimization Test Subject')
        topic = Topic.objects.create(subject=subject, name='Optimization Test Topic')
        course = Course.objects.create(name='Optimization Test Course')
        
        # Create questions with relationships
        questions = []
        for i in range(5):
            question = Question.objects.create(
                content=f'Test question {i}',
                difficulty=5,
                author=contributor
            )
            question.subject.add(subject)
            question.topic.add(topic)
            question.course.add(course)
            questions.append(question)
            
            # Add options
            for j in range(4):
                Option.objects.create(
                    question=question,
                    option_text=f'Option {j}',
                    is_correct=(j == 0)
                )
        
        # Test 1: Select related optimization
        start_time = time.time()
        questions_with_author = Question.objects.select_related('author__user').all()
        for question in questions_with_author:
            _ = question.author.user.username  # Access related field
        select_related_time = time.time() - start_time
        
        if select_related_time < 1.0:  # Should be fast
            results.add_pass("Select related optimization")
        else:
            results.add_fail("Select related optimization", f"Query took {select_related_time:.2f} seconds")
        
        # Test 2: Prefetch related optimization
        start_time = time.time()
        questions_with_options = Question.objects.prefetch_related('options').all()
        for question in questions_with_options:
            _ = list(question.options.all())  # Access related objects
        prefetch_related_time = time.time() - start_time
        
        if prefetch_related_time < 1.0:  # Should be fast
            results.add_pass("Prefetch related optimization")
        else:
            results.add_fail("Prefetch related optimization", f"Query took {prefetch_related_time:.2f} seconds")
        
        # Test 3: Only/defer optimization
        start_time = time.time()
        questions_only_content = Question.objects.only('content', 'difficulty').all()
        for question in questions_only_content:
            _ = question.content  # Access only specified fields
        only_time = time.time() - start_time
        
        if only_time < 1.0:  # Should be fast
            results.add_pass("Only fields optimization")
        else:
            results.add_fail("Only fields optimization", f"Query took {only_time:.2f} seconds")
        
        # Test 4: Bulk operations
        start_time = time.time()
        Question.objects.filter(author=contributor).update(difficulty=7)
        bulk_update_time = time.time() - start_time
        
        if bulk_update_time < 1.0:  # Should be fast
            results.add_pass("Bulk update optimization")
        else:
            results.add_fail("Bulk update optimization", f"Query took {bulk_update_time:.2f} seconds")
        
        # Cleanup
        Option.objects.filter(question__in=questions).delete()
        for question in questions:
            question.delete()
        course.delete()
        topic.delete()
        subject.delete()
        contributor.delete()
        user.delete()
        
    except Exception as e:
        results.add_fail("Query optimization tests", f"Exception: {str(e)}")
    
    return results


def test_database_constraints():
    """Test database constraints and validations."""
    results = TestResults()
    
    try:
        # Test 1: Unique constraint (slug fields)
        course1 = Course.objects.create(name='Unique Test Course')
        course2 = Course.objects.create(name='Unique Test Course')  # Should generate different slug
        
        if course1.slug != course2.slug:
            results.add_pass("Unique slug generation")
        else:
            results.add_fail("Unique slug generation", "Slugs are not unique")
        
        # Test 2: Foreign key constraint
        try:
            # This should work
            subject = Subject.objects.create(name='FK Test Subject')
            topic = Topic.objects.create(subject=subject, name='FK Test Topic')
            results.add_pass("Valid foreign key constraint")
            
            # Cleanup
            topic.delete()
            subject.delete()
        except Exception as e:
            results.add_fail("Valid foreign key constraint", f"Exception: {str(e)}")
        
        # Test 3: Many-to-many relationships
        try:
            import uuid
            unique_username = f'testcontributor_{uuid.uuid4().hex[:8]}'
            user = User.objects.create_user(username=unique_username, password='test123')
            contributor = ContributorProfile.objects.create(user=user, role='contributor')
            
            question = Question.objects.create(
                content='M2M Test Question',
                difficulty=5,
                author=contributor
            )
            subject1 = Subject.objects.create(name='M2M Subject 1')
            subject2 = Subject.objects.create(name='M2M Subject 2')
            
            question.subject.add(subject1, subject2)
            
            if question.subject.count() == 2:
                results.add_pass("Many-to-many relationship")
            else:
                results.add_fail("Many-to-many relationship", f"Expected 2, got {question.subject.count()}")
            
            # Cleanup
            question.delete()
            subject1.delete()
            subject2.delete()
            contributor.delete()
            user.delete()
            
        except Exception as e:
            results.add_fail("Many-to-many relationship", f"Exception: {str(e)}")
        
        # Cleanup
        course1.delete()
        course2.delete()
        
    except Exception as e:
        results.add_fail("Database constraints tests", f"Exception: {str(e)}")
    
    return results


def main():
    """Run all database operations tests."""
    print("🚀 Starting Comprehensive Database Operations Tests")
    print("="*60)
    
    all_results = TestResults()
    
    # Run all test functions
    test_functions = [
        test_basic_crud_operations,
        test_relationship_queries,
        test_filtering_and_searching,
        test_ordering_and_aggregation,
        test_query_optimization,
        test_database_constraints
    ]
    
    for test_func in test_functions:
        print(f"\n📋 Running {test_func.__name__}...")
        try:
            result = test_func()
            all_results.passed += result.passed
            all_results.failed += result.failed
            all_results.errors.extend(result.errors)
        except Exception as e:
            all_results.add_fail(test_func.__name__, f"Test function failed: {str(e)}")
            print(f"💥 Error in {test_func.__name__}: {str(e)}")
            traceback.print_exc()
    
    # Print final summary
    all_results.summary()


if __name__ == '__main__':
    main()
