"""
Enhanced JWT Token Management Utilities
Provides centralized token refresh and validation logic for all apps.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from django.contrib.auth import get_user_model
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class EnhancedTokenRefreshView(APIView):
    """
    Enhanced token refresh view with proper error handling and token rotation.
    This view should be used across all apps for consistent token refresh behavior.
    """
    
    def post(self, request):
        refresh_token = request.data.get("refresh")
        
        if not refresh_token:
            return Response(
                {
                    "error": "refresh_token_required",
                    "message": "Refresh token is required",
                    "code": "MISSING_REFRESH_TOKEN"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        try:
            # Validate and refresh the token
            refresh = RefreshToken(refresh_token)
            
            # Get user information for logging
            user_id = refresh.payload.get('user_id')
            user = None
            if user_id:
                try:
                    user = User.objects.get(id=user_id)
                except User.DoesNotExist:
                    logger.warning(f"Token refresh attempted for non-existent user ID: {user_id}")
            
            # Generate new access token
            new_access_token = str(refresh.access_token)
            
            response_data = {
                "access": new_access_token,
                "token_type": "Bearer",
                "expires_in": 900,  # 15 minutes in seconds
            }
            
            # If token rotation is enabled, provide new refresh token
            if hasattr(refresh, 'set_jti'):
                # Token rotation is enabled, provide new refresh token
                new_refresh_token = str(refresh)
                response_data["refresh"] = new_refresh_token
                
                logger.info(f"Token refreshed successfully for user {user.username if user else user_id} with rotation")
            else:
                logger.info(f"Token refreshed successfully for user {user.username if user else user_id}")
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except TokenError as e:
            logger.warning(f"Token refresh failed: {str(e)}")
            return Response(
                {
                    "error": "invalid_token",
                    "message": "The provided refresh token is invalid or expired",
                    "code": "INVALID_REFRESH_TOKEN",
                    "details": str(e)
                },
                status=status.HTTP_401_UNAUTHORIZED
            )
        except Exception as e:
            logger.error(f"Unexpected error during token refresh: {str(e)}")
            return Response(
                {
                    "error": "token_refresh_failed",
                    "message": "An unexpected error occurred during token refresh",
                    "code": "REFRESH_ERROR"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


def generate_tokens_for_user(user):
    """
    Generate both access and refresh tokens for a user.
    Returns a dictionary with token information.
    """
    try:
        refresh = RefreshToken.for_user(user)
        
        return {
            "refresh": str(refresh),
            "access": str(refresh.access_token),
            "token_type": "Bearer",
            "expires_in": 900,  # 15 minutes in seconds
            "refresh_expires_in": 604800,  # 7 days in seconds
        }
    except Exception as e:
        logger.error(f"Failed to generate tokens for user {user.username}: {str(e)}")
        raise


def validate_access_token(token):
    """
    Validate an access token and return user information.
    Returns tuple (is_valid, user, error_message)
    """
    try:
        from rest_framework_simplejwt.tokens import AccessToken
        
        access_token = AccessToken(token)
        user_id = access_token.payload.get('user_id')
        
        if not user_id:
            return False, None, "Invalid token payload"
        
        try:
            user = User.objects.get(id=user_id)
            return True, user, None
        except User.DoesNotExist:
            return False, None, "User not found"
            
    except TokenError as e:
        return False, None, str(e)
    except Exception as e:
        logger.error(f"Unexpected error validating access token: {str(e)}")
        return False, None, "Token validation failed"


def is_token_expired(token):
    """
    Check if a token is expired.
    Returns True if expired, False otherwise.
    """
    try:
        from rest_framework_simplejwt.tokens import AccessToken
        
        access_token = AccessToken(token)
        current_time = timezone.now().timestamp()
        token_exp = access_token.payload.get('exp', 0)
        
        return current_time >= token_exp
    except:
        return True  # Consider invalid tokens as expired


class TokenValidationMixin:
    """
    Mixin to add token validation capabilities to views.
    """
    
    def get_user_from_token(self, request):
        """
        Extract and validate user from Authorization header.
        Returns tuple (user, error_response)
        """
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        
        if not auth_header:
            return None, Response(
                {"error": "authorization_required", "message": "Authorization header is required"},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        try:
            token_type, token = auth_header.split(' ', 1)
            if token_type.lower() != 'bearer':
                return None, Response(
                    {"error": "invalid_token_type", "message": "Token type must be Bearer"},
                    status=status.HTTP_401_UNAUTHORIZED
                )
        except ValueError:
            return None, Response(
                {"error": "invalid_auth_header", "message": "Invalid authorization header format"},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        is_valid, user, error_msg = validate_access_token(token)
        
        if not is_valid:
            return None, Response(
                {"error": "invalid_token", "message": error_msg},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        return user, None


# Error codes for consistent error handling across frontend
JWT_ERROR_CODES = {
    'MISSING_REFRESH_TOKEN': 'refresh_token_required',
    'INVALID_REFRESH_TOKEN': 'invalid_refresh_token',
    'REFRESH_ERROR': 'token_refresh_failed',
    'TOKEN_EXPIRED': 'access_token_expired',
    'INVALID_TOKEN': 'invalid_access_token',
    'AUTHORIZATION_REQUIRED': 'authorization_required',
    'INVALID_TOKEN_TYPE': 'invalid_token_type',
    'INVALID_AUTH_HEADER': 'invalid_auth_header',
}
