# Postman API Testing Guide - Subscription API v2

## 📋 Overview
This guide provides comprehensive testing instructions for the Subscription API v2 endpoints using Postman and curl commands.

## 🔧 Base Configuration

### Environment Variables
Set up these variables in Postman:
```
BASE_URL: http://127.0.0.1:8000
API_BASE: {{BASE_URL}}/api/packages
```

### Headers (Global)
```
Content-Type: application/json
Accept: application/json
```

---

## 🧪 API Endpoints Testing

### 1. Package Listing API

**Endpoint:** `GET /api/packages/`  
**Purpose:** Retrieve all available packages

#### Postman Setup:
- **Method:** GET
- **URL:** `{{API_BASE}}/`
- **Headers:** Default

#### Curl Command:
```bash
curl -X GET "http://127.0.0.1:8000/api/packages/" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json"
```

#### Expected Response (200 OK):
```json
[
  {
    "id": 1,
    "name": "Updated Package Name",
    "package_type": "validity",
    "price": 500.00,
    "discount_price": 298.00,
    "duration_months": 7,
    "is_active": true,
    "description": "Premium validity package"
  },
  {
    "id": 3,
    "name": "Graiden George",
    "package_type": "validity",
    "price": 1200.00,
    "discount_price": 915.00,
    "duration_months": 12,
    "is_active": true
  }
]
```

---

### 2. Razorpay Configuration API

**Endpoint:** `GET /api/packages/razorpay-config/`  
**Purpose:** Get Razorpay configuration for frontend

#### Postman Setup:
- **Method:** GET
- **URL:** `{{API_BASE}}/razorpay-config/`
- **Headers:** Default

#### Curl Command:
```bash
curl -X GET "http://127.0.0.1:8000/api/packages/razorpay-config/" \
  -H "Content-Type: application/json"
```

#### Expected Response (200 OK):
```json
{
  "razorpay_key": "rzp_test_lQx7mOhOhSX6FC",
  "currency": "INR",
  "company_name": "Shashtrarth Platform"
}
```

---

### 3. Create Subscription API v2 (Main Endpoint)

**Endpoint:** `POST /api/packages/v2/create-subscription/`  
**Purpose:** Create a new subscription with Razorpay order

#### Postman Setup:
- **Method:** POST
- **URL:** `{{API_BASE}}/v2/create-subscription/`
- **Headers:** Default
- **Body:** Raw JSON

#### Test Cases:

##### Test Case 1: Basic Subscription Creation
```json
{
  "student": 11,
  "package": 1
}
```

**Curl Command:**
```bash
curl -X POST "http://127.0.0.1:8000/api/packages/v2/create-subscription/" \
  -H "Content-Type: application/json" \
  -d '{
    "student": 11,
    "package": 1
  }'
```

**Expected Response (201 Created):**
```json
{
  "success": true,
  "subscription_id": 7,
  "invoice_id": 5,
  "final_price": 298.0,
  "currency": "INR",
  "is_free": false,
  "package_type": "validity",
  "package_name": "Updated Package Name",
  "razorpay_order_id": "order_QyS0J51dkl5RY2",
  "razorpay_key": "rzp_test_lQx7mOhOhSX6FC",
  "amount_in_paise": 29800
}
```

##### Test Case 2: Subscription with Coupon
```json
{
  "student": 11,
  "package": 3,
  "coupon": "SAVE20"
}
```

**Curl Command:**
```bash
curl -X POST "http://127.0.0.1:8000/api/packages/v2/create-subscription/" \
  -H "Content-Type: application/json" \
  -d '{
    "student": 11,
    "package": 3,
    "coupon": "SAVE20"
  }'
```

##### Test Case 3: Subscription with Gift Card
```json
{
  "student": 11,
  "package": 1,
  "gift_card_code": "GIFT123",
  "gift_card_pin": "1234"
}
```

**Curl Command:**
```bash
curl -X POST "http://127.0.0.1:8000/api/packages/v2/create-subscription/" \
  -H "Content-Type: application/json" \
  -d '{
    "student": 11,
    "package": 1,
    "gift_card_code": "GIFT123",
    "gift_card_pin": "1234"
  }'
```

##### Test Case 4: Event Package Subscription
```json
{
  "student": 11,
  "package": 4
}
```

**Curl Command:**
```bash
curl -X POST "http://127.0.0.1:8000/api/packages/v2/create-subscription/" \
  -H "Content-Type: application/json" \
  -d '{
    "student": 11,
    "package": 4
  }'
```

---

### 4. Payment Verification API v2

**Endpoint:** `POST /api/packages/v2/verify-payment/`  
**Purpose:** Verify Razorpay payment and activate subscription

#### Postman Setup:
- **Method:** POST
- **URL:** `{{API_BASE}}/v2/verify-payment/`
- **Headers:** Default
- **Body:** Raw JSON

#### Test Case: Payment Verification
```json
{
  "razorpay_order_id": "order_QyS0J51dkl5RY2",
  "razorpay_payment_id": "pay_QyS0J51dkl5RY3",
  "razorpay_signature": "generated_signature_hash",
  "subscription_id": 7
}
```

**Curl Command:**
```bash
curl -X POST "http://127.0.0.1:8000/api/packages/v2/verify-payment/" \
  -H "Content-Type: application/json" \
  -d '{
    "razorpay_order_id": "order_QyS0J51dkl5RY2",
    "razorpay_payment_id": "pay_QyS0J51dkl5RY3",
    "razorpay_signature": "generated_signature_hash",
    "subscription_id": 7
  }'
```

**Expected Response (200 OK):**
```json
{
  "success": true,
  "message": "Payment verified successfully",
  "subscription_id": 7,
  "is_active": true,
  "payment_status": "completed"
}
```

---

### 5. Subscription Status Check API v2

**Endpoint:** `GET /api/packages/v2/subscription-status/{student_id}/`  
**Purpose:** Check current subscription status for a student

#### Postman Setup:
- **Method:** GET
- **URL:** `{{API_BASE}}/v2/subscription-status/11/`
- **Headers:** Default

**Curl Command:**
```bash
curl -X GET "http://127.0.0.1:8000/api/packages/v2/subscription-status/11/" \
  -H "Content-Type: application/json"
```

**Expected Response (200 OK):**
```json
{
  "student_id": 11,
  "has_active_subscription": true,
  "subscriptions": [
    {
      "id": 7,
      "package_name": "Updated Package Name",
      "package_type": "validity",
      "start_date": "2025-07-28",
      "end_date": "2026-02-28",
      "is_active": true,
      "final_price": 298.0
    }
  ]
}
```

---

## 🚨 Error Testing Scenarios

### 1. Invalid Student ID
```json
{
  "student": 99999,
  "package": 1
}
```

**Expected Response (404 Not Found):**
```json
{
  "error": "Student not found"
}
```

### 2. Invalid Package ID
```json
{
  "student": 11,
  "package": 99999
}
```

**Expected Response (404 Not Found):**
```json
{
  "error": "Package not found or not available"
}
```

### 3. Missing Required Fields
```json
{
  "student": 11
}
```

**Expected Response (400 Bad Request):**
```json
{
  "error": "Package ID is required"
}
```

### 4. Invalid Coupon Code
```json
{
  "student": 11,
  "package": 1,
  "coupon": "INVALID_COUPON"
}
```

**Expected Response (400 Bad Request):**
```json
{
  "error": "Invalid coupon code"
}
```

---

## 📊 Postman Collection Structure

### Collection: Subscription API v2 Testing
```
📁 Subscription API v2
├── 📁 Package Management
│   ├── GET Package List
│   └── GET Razorpay Config
├── 📁 Subscription Creation
│   ├── POST Basic Subscription
│   ├── POST Subscription with Coupon
│   ├── POST Subscription with Gift Card
│   └── POST Event Package Subscription
├── 📁 Payment Processing
│   ├── POST Verify Payment
│   └── GET Subscription Status
└── 📁 Error Scenarios
    ├── POST Invalid Student
    ├── POST Invalid Package
    ├── POST Missing Fields
    └── POST Invalid Coupon
```

---

## 🔍 Testing Checklist

### ✅ Pre-Test Setup
- [ ] Django server running on port 8000
- [ ] Database has test students and packages
- [ ] Razorpay test credentials configured
- [ ] Postman environment variables set

### ✅ Functional Tests
- [ ] Package listing returns all active packages
- [ ] Razorpay config returns valid test key
- [ ] Basic subscription creation works
- [ ] Coupon application works correctly
- [ ] Gift card redemption works
- [ ] Payment verification works
- [ ] Subscription status check works

### ✅ Error Handling Tests
- [ ] Invalid student ID returns 404
- [ ] Invalid package ID returns 404
- [ ] Missing fields return 400
- [ ] Invalid coupon returns 400
- [ ] Invalid gift card returns 400

### ✅ Integration Tests
- [ ] Orders appear on Razorpay dashboard
- [ ] Subscriptions created in database
- [ ] Invoices generated correctly
- [ ] Email notifications sent (check logs)
- [ ] FCM notifications sent (check logs)

---

## 🛠️ Quick Setup Instructions

### 1. Import Postman Collection
```bash
# Download the collection file
curl -O https://raw.githubusercontent.com/your-repo/Subscription_API_v2_Postman_Collection.json

# Import in Postman:
# File → Import → Upload Files → Select the JSON file
```

### 2. Set Environment Variables
Create a new environment in Postman with:
```
BASE_URL: http://127.0.0.1:8000
API_BASE: {{BASE_URL}}/api/packages
```

### 3. Run curl Tests
```bash
# Make the script executable
chmod +x curl_api_tests_v2.sh

# Run all tests
./curl_api_tests_v2.sh
```

## 🔄 Testing Workflow

### Step 1: Basic Functionality
1. Run "GET Package List" to see available packages
2. Run "GET Razorpay Config" to verify configuration
3. Run "POST Basic Subscription" to create a subscription

### Step 2: Advanced Features
1. Test coupon application
2. Test gift card redemption
3. Test event package subscriptions

### Step 3: Error Handling
1. Test with invalid student IDs
2. Test with invalid package IDs
3. Test with missing required fields

### Step 4: Integration Verification
1. Check Razorpay dashboard for created orders
2. Verify database entries for subscriptions
3. Check server logs for any errors

## 📊 Expected Response Times
- Package Listing: < 200ms
- Subscription Creation: < 1000ms
- Payment Verification: < 500ms
- Status Check: < 200ms

## 🔍 Debugging Tips

### Server Logs
Monitor Django server output for:
```
INFO === SUBSCRIPTION CREATION START ===
INFO Student found: username (ID: 11)
INFO Package found: Package Name (Type: validity, Price: ₹298.00)
INFO Final price after all discounts: ₹298.00
INFO Razorpay order created successfully: order_xyz123
INFO === SUBSCRIPTION CREATION SUCCESS ===
```

### Common Issues
1. **500 Error**: Check server logs for detailed error
2. **404 Error**: Verify student/package IDs exist
3. **400 Error**: Check request body format
4. **Connection Error**: Ensure Django server is running

## 📝 Notes for Testing

1. **Student IDs**: Use existing student IDs (11, 12, 13, etc.)
2. **Package IDs**: Use active package IDs (1, 3, 4, 5)
3. **Razorpay Orders**: Check dashboard at https://dashboard.razorpay.com/
4. **Logs**: Monitor Django server logs for detailed debugging
5. **Database**: Check subscription and invoice tables for created records

## 🎯 Success Criteria

- All API endpoints return expected status codes
- Razorpay orders are created successfully
- Subscriptions are saved to database
- Error scenarios are handled gracefully
- Comprehensive logging is available for debugging

## 📞 Support

If you encounter issues:
1. Check Django server logs first
2. Verify database has required test data
3. Ensure Razorpay test credentials are configured
4. Test with curl commands before using Postman
5. Check network connectivity to Razorpay APIs
